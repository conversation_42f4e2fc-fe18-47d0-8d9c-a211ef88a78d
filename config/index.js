/* eslint-disable @typescript-eslint/no-var-requires */
const fs = require('fs')
const path = require('path')
const paths = require('../build/paths')
const NODE_ENV = process.env.NODE_ENV
const PORT = process.env.PORT || 3001
const MOCK_PORT = process.env.MOCK_PORT || 4001
const baseConfig = require('../src/config/base.conf.js')
// pre 代理环境更常用，本地文件处理为 pre 代理
const target = 'https://gsc.pre.mioffice.cn'

module.exports = {
  build: {
    mode: NODE_ENV,
    assetsRoot: paths.distDir,
    assetsSubDirectory: './',
    assetsPublicPath: baseConfig.SITE_BASE,
    bundleAnalyzerReport: process.env.size,
    bundleSpeedTest: process.env.speed,
  },
  dev: {
    mode: NODE_ENV,
    assetsRoot: paths.distDir,
    assetsSubDirectory: './',
    assetsPublicPath: baseConfig.SITE_BASE,
    devServer: {
      host: 'localhost',
      port: PORT,
      open: true,
      historyApiFallback: true,
      allowedHosts: 'all',
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
      },
      setupMiddlewares: function (middlewares, devServer) {
        if (!devServer) {
          throw new Error('webpack-dev-server is not defined')
        }

        // 修改为你的文件名
        devServer.app?.get('/gsc-app/js/scm-track-sdk.js', function (_req, res) {
          res.set('content-type', 'application/javascript; charset=utf-8')
          res.send(fs.readFileSync(path.resolve('../', 'dist/gsc-app/js/scm-track-sdk.js')))
        })
        return middlewares
      },
      proxy: {
        '/shadow': {
          // 本地开发时，在这里你可以代理接口地址，轻松地进行跨域调试
          // 比如：测试环境接口【建议默认】、MApi 地址、或者后端联调本地 ip 地址等
          target,
          // pathRewrite: { '^/proxy': '' },
          changeOrigin: true,
          secure: false,
          // onProxyRes(proxyRes, req, res) {
          //   // 302 会被当做请求发出 导致无法正常重定向
          //   if (proxyRes.statusCode === 302) {
          //     proxyRes.statusCode = 200
          //   }
          // }
        },
        '/api/qms': {
          // 本地开发时，在这里你可以代理接口地址，轻松地进行跨域调试
          // 比如：测试环境接口【建议默认】、MApi 地址、或者后端联调本地 ip 地址等
          target,
          // pathRewrite: { '^/proxy': '' },
          changeOrigin: true,
          secure: false,
          // onProxyRes(proxyRes, req, res) {
          //   // 302 会被当做请求发出 导致无法正常重定向
          //   if (proxyRes.statusCode === 302) {
          //     proxyRes.statusCode = 200
          //   }
          // }
        },
        '/_aegis/': {
          target,
          changeOrigin: true,
        },
      },
    },
    apiMock: {
      host: 'localhost', // mock server running host
      port: MOCK_PORT, // mock server running port
      dataDir: paths.mocksDir, // mock data directory
      minDelay: 1000, // request Api delay (ms)
      apiBase: '/', // api url prefix path
      docPath: '/api-doc', // mock document url path, ensure only
    },
  },
  typescript: {
    // Dangerously ignore error even if your project has type errors when `ignoreTypeErrors` is `true`.
    ignoreTypeErrors: NODE_ENV === 'production',
  },
}
