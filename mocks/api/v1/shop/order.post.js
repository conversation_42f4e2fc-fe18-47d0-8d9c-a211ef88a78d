module.exports = {
  desc: '获取商城订单列表信息', // 标识该 api 的含义
  status: 200,
  data: {
    code: 200,
    message: 'Success',
    data: {
      columns: [
        {
          title: 'ID',
          dataKey: 'id',
          key: 'id',
          hide: true,
        },
        {
          title: '订单号',
          dataKey: 'orderId',
          key: 'orderId',
        },
        {
          title: '订单状态',
          dataKey: 'orderStatus',
          key: 'orderStatus',
          type: 'string',
        },
        {
          title: '订单日期',
          dataKey: 'orderDate',
          key: 'orderDate',
        },
        {
          title: '业务来源',
          dataKey: 'orderPlatform',
          key: 'orderPlatform',
          type: 'string',
        },
        {
          title: '运输方式',
          dataKey: 'orderDelivery',
          key: 'orderPlatform',
          type: 'string',
        },
        {
          title: '支付方式',
          dataKey: 'orderPayment',
          key: 'orderPayment',
          type: 'string',
        },
        {
          title: '总价',
          dataKey: 'orderTotal',
          key: 'orderTotal',
          type: 'number',
        },
        {
          title: '操作',
          width: 100,
          key: 'action',
        },
      ],
      pageInfo: {
        page: 1,
        total: 100,
        pageSize: 10,
      },
      list: [
        {
          id: 0,
          orderTotal: 5712.14,
          orderId: '118845759366',
          orderPlatform: '天猫旗舰店',
          orderDelivery: '顺丰',
          orderDate: '2017-03-17',
          orderPayment: '信用卡',
          orderStatus: '已支付',
        },
        {
          id: 1,
          orderTotal: 239.28,
          orderId: '169165233008',
          orderPlatform: '小米之家',
          orderDelivery: 'EMS',
          orderDate: '1975-07-06',
          orderPayment: '信用卡',
          orderStatus: '已支付',
        },
        {
          id: 2,
          orderTotal: 6063.42,
          orderId: '150344587200',
          orderPlatform: '小米商城',
          orderDelivery: 'EMS',
          orderDate: '2015-01-24',
          orderPayment: '信用卡',
          orderStatus: '配送中',
        },
        {
          id: 3,
          orderTotal: 6899.03,
          orderId: '117463763657',
          orderPlatform: '京东旗舰店',
          orderDelivery: '百世汇通',
          orderDate: '2009-06-29',
          orderPayment: '现金',
          orderStatus: '已收货',
        },
        {
          id: 4,
          orderTotal: 5197.96,
          orderId: '116727373646',
          orderPlatform: '小米商城',
          orderDelivery: '百世汇通',
          orderDate: '1984-07-12',
          orderPayment: '微信支付',
          orderStatus: '已取消',
        },
        {
          id: 5,
          orderTotal: 4131.26,
          orderId: '160895353281',
          orderPlatform: '京东旗舰店',
          orderDelivery: '如风达',
          orderDate: '1971-01-14',
          orderPayment: '信用卡',
          orderStatus: '已收货',
        },
        {
          id: 6,
          orderTotal: 7289.15,
          orderId: '125793936310',
          orderPlatform: '天猫旗舰店',
          orderDelivery: 'EMS',
          orderDate: '2005-06-20',
          orderPayment: '支付宝',
          orderStatus: '已取消',
        },
        {
          id: 7,
          orderTotal: 3634.44,
          orderId: '171166368685',
          orderPlatform: '小米之家',
          orderDelivery: 'EMS',
          orderDate: '1994-11-21',
          orderPayment: '银联',
          orderStatus: '配送中',
        },
        {
          id: 8,
          orderTotal: 5795.55,
          orderId: '125333645273',
          orderPlatform: '小米商城',
          orderDelivery: 'EMS',
          orderDate: '1978-12-11',
          orderPayment: '支付宝',
          orderStatus: '已支付',
        },
        {
          id: 9,
          orderTotal: 9276.35,
          orderId: '126701325456',
          orderPlatform: '小米商城',
          orderDelivery: 'EMS',
          orderDate: '2004-12-12',
          orderPayment: '信用卡',
          orderStatus: '配送中',
        },
      ],
    },
  },
}
