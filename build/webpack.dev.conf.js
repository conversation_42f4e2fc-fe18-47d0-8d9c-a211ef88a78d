/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path')
const paths = require('./paths')
// const webpack = require('webpack');
const { merge } = require('webpack-merge')
const HtmlWebpackPlugin = require('html-webpack-plugin')
// const FriendlyErrorsWebpackPlugin = require('friendly-errors-webpack-plugin');
// const semver = require('semver');
const baseWebpackConfig = require('./webpack.base.conf.js')
const config = require('../config').dev
const baseConfig = require(`../src/config/base.conf.js`)

const __MOCK__ = process.env.USE_API === 'mock'
const name = baseConfig.SITE_BASE?.replace(/(^\/)|(\/$)/g, '')
module.exports = merge(baseWebpackConfig, {
  mode: config.mode,
  stats: 'errors-only',
  output: {
    path: config.assetsRoot,
    filename: path.join(config.assetsSubDirectory, 'js/[name].js'),
    chunkFilename: path.join(config.assetsSubDirectory, 'js/[name].chunk.js'),
    publicPath: config.assetsPublicPath,
    pathinfo: false,
    library: `${name}-[name]`,
    libraryTarget: 'umd',
    chunkLoadingGlobal: `webpackJsonp_${name}`,
    globalObject: 'window',
  },
  devServer: {
    ...config.devServer,
    hot: true,
    client: {
      logging: 'error',
      overlay: true,
    },
    static: {
      watch: true,
      publicPath: config.assetsPublicPath,
      directory: config.assetsRoot,
    },
    setupMiddlewares: __MOCK__ ? require('@mi/api-mocker')(config.apiMock) : undefined,
  },
  plugins: [
    // new webpack.HotModuleReplacementPlugin(),
    new HtmlWebpackPlugin({
      template: paths.rootTemplate,
      templateParameters: {
        prefixUrl: path.join(config.assetsPublicPath, config.assetsSubDirectory),
      },
    }),
    // new FriendlyErrorsWebpackPlugin({
    //   compilationSuccessInfo: {
    //     messages: [
    //       `You application is running here 🔥 ${
    //         config.devServer.https ? 'https' : 'http'
    //       }://${config.devServer.host}:${config.devServer.port}`,
    //       __MOCK__ &&
    //         `Mock Document is running here 🌱 http://${config.apiMock.host}:${config.apiMock.port}${config.apiMock.docPath}`,
    //     ].filter(Boolean),
    //     notes: checkNodeVersion(),
    //   },
    // }),
  ],
})

// function checkNodeVersion() {
//   return semver.gte(process.version, '10.3.0')
//     ? []
//     : [
//         'Node version less than 10.3.0: https://wiki.n.miui.com/pages/viewpage.action?pageId=141942386',
//       ];
// }
