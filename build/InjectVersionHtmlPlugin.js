/* eslint-disable @typescript-eslint/no-var-requires */
const HtmlWebpackPlugin = require('html-webpack-plugin')
class InjectVersionHtmlPlugin {
  constructor(options = {}) {
    this.version = process.env.CI_COMMIT_TAG
    this.app = options.app
  }

  apply(compiler) {
    compiler.hooks.compilation.tap('InjectVersionHtmlPlugin', (compilation) => {
      HtmlWebpackPlugin.getHooks(compilation).alterAssetTags.tap(
        'InjectVersionHtmlPlugin',
        ({ assetTags }) => {
          assetTags.scripts.unshift({
            tagName: 'script',
            innerHTML: `console.log('%c ${this.app},tag:${this.version}', 'color:green;');window._RELEASE_ = "${this.version}"`,
          })
        }
      )
    })
  }
}

module.exports = InjectVersionHtmlPlugin
