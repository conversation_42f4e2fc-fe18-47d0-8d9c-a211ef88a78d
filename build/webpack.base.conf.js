/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path')
const paths = require('./paths')
const webpack = require('webpack')
const { ModuleFederationPlugin } = webpack.container
const CopyWebpackPlugin = require('copy-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin')
const { getAppEnvs } = require('../build/env')
const { ScmTrackInjectPlugin } = require('@mi/scm-track/webpack')
const __DEV__ = process.env.NODE_ENV === 'development'
const ENV = process.env.DEPLOY_ENV
const baseConfig = require('../config')

const config = baseConfig[__DEV__ ? 'dev' : 'build']
const regExp = /^(https?:\/\/)?[^/]+\//
// common function to get style loaders
const getStyleLoaders = () => {
  const loaders = [
    __DEV__ ? require.resolve('style-loader') : MiniCssExtractPlugin.loader,
    {
      loader: require.resolve('css-loader'),
      options: {
        importLoaders: 2,
        sourceMap: __DEV__,
      },
    },
    require.resolve('postcss-loader'),
    {
      loader: require.resolve('sass-loader'),
      options: {
        implementation: require('sass'),
        sassOptions: {
          includePaths: ['node_modules'],
        },
      },
    },
  ]

  return loaders
}

module.exports = {
  entry: paths.entry,
  devtool: process.env.sourceMap === 'none' ? false : process.env.sourceMap,
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx', '.json'],
    modules: ['node_modules'],
    alias: {
      '@': paths.srcDir,
    },
    fallback: {
      crypto: require.resolve('crypto-browserify'),
      stream: false,
    },
  },
  module: {
    rules: [
      {
        test: /\.(ts|tsx|js|jsx)$/,
        loader: require.resolve('babel-loader'),
        options: {
          cacheDirectory: true,
          // plugins: [__DEV__ && require.resolve('react-refresh/babel')].filter(
          //   Boolean
          // ),
        },

        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        use: [
          __DEV__ ? require.resolve('style-loader') : MiniCssExtractPlugin.loader,
          require.resolve('css-loader'),
        ],
      },
      {
        test: /\.(scss|sass)$/,
        use: getStyleLoaders(),
        exclude: /node_modules/,
      },
      {
        test: /\.svg$/i,
        type: 'asset/inline',
        resourceQuery: /url/, // *.svg?url
      },
      {
        test: /\.svg$/i,
        issuer: /\.[jt]sx?$/,
        resourceQuery: { not: [/url/] }, // exclude react component if *.svg?url
        use: [{ loader: '@svgr/webpack', options: { svgo: false } }],
      },
      {
        test: /\.(png|jpe?g|gif|webp)(\?.*)?$/,
        type: 'asset/inline',
      },
      {
        test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
        type: 'asset/inline',
      },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        type: 'asset/inline',
      },
    ],
  },
  plugins: [
    // About to issue：https://github.com/XiaoMi/hiui/issues/1976
    new webpack.IgnorePlugin({
      resourceRegExp: /^\.\/locale$/,
      contextRegExp: /moment$/,
    }),

    !baseConfig.typescript.ignoreTypeErrors &&
      new ForkTsCheckerWebpackPlugin({
        typescript: {
          configFile: paths.tsConfig,
        },
      }),
    new CopyWebpackPlugin({
      patterns: [
        {
          from: paths.staticDir,
          to: path.join(config.assetsSubDirectory, 'static'),
        },
      ],
    }),
    new webpack.DefinePlugin({
      ...getAppEnvs({
        __DEV__,
      }),
      __SENTRY_DEBUG__: false,
    }),
    new webpack.ProvidePlugin({
      process: 'process/browser.js',
    }),
    new ScmTrackInjectPlugin({
      env: ENV === 'local' ? 'test' : ENV, // test | pre | pro
      systemName: 'GSC', //  "GSC" | "R平台" | "OMS" | "BCS" | "MMC" | "MIP" | "SCM"
      secret: { token: 'bWVudS1wYWdlLWNvbmZpZw==' }, // 从IDaaS上获取
      hooks: {
        beforeTransform: (records) => {
          return records
            .filter(({ path }) => path && path.includes('gsc.mioffice.cn'))
            .map((record) => ({
              ...record,
              path: String.prototype.replace.call(record.path, regExp, ''),
            }))
        },
      },
      // 可选以下几个配置
      injectIifeSDK: false, // 自动插入IIFE版本的埋点SDK，建议打开
      injectOnetrack: false, // 自动插入onetrack SDK，也建议直接打开
      // injectFileName: 'scm-track-sdk.js', // 插入的IIFE版本的SDK和静态化的菜单信息的文件名称
      scriptsDestDir: 'js', // webpack输出文件时，js文件存放的目录，插件会自动解析，主动传入时不再自动解析
    }),
    new ModuleFederationPlugin({
      name: 'gsc_app',
      filename: 'js/gsc_app/remoteEntry.js',
      remotes: {
        // 引入基座远程模块，使用时import('workbench/xxx')导入
        // workbench: `workbench@${srcConfig.API_ORIGIN}/workbench-remoteEntry.js`,
        workbench: `promise new Promise(resolve => {
          const version = new Date().getTime()
          const remoteUrl = '/js/workbench-remoteEntry.js?version=' + version;
          const script = document.createElement('script');
          script.src = remoteUrl;
          script.onload = () => {
            const proxy = {
              get: (req) => window.workbench.get(req),
              init: (arg) => {
                try {
                  return window.workbench.init(arg);
                } catch(e) {
                  console.log('remote container already initialized');
                }
              }
            }
            resolve(proxy);
          };
          // 仅允许在GSC环境引入script
          if(/^gsc.(.*)(mioffice|mi).(cn|com)$/.test(window.location.hostname)) {
            document.head.appendChild(script);
          } else {
            resolve()
          }
        })`,
      },
      shared: {
        react: {
          singleton: true,
          version: '17.0.2',
          requiredVersion: '17.0.2',
        },
        'react-dom': {
          singleton: true,
          version: '17.0.2',
          requiredVersion: '17.0.2',
        },
        'react-router': {
          singleton: true,
          version: '6.3.0',
          requiredVersion: '6.3.0',
        },
        'react-router-dom': {
          singleton: true,
          version: '6.3.0',
          requiredVersion: '6.3.0',
        },
        'react-intl-universal': {
          singleton: true,
          version: '2.11.1',
          requiredVersion: '2.11.1',
        },
        redux: {
          singleton: true,
          version: '4.2.1',
          requiredVersion: '4.2.1',
        },
        'react-draggable': {
          singleton: true,
          version: '4.4.6',
          requiredVersion: '4.4.6',
        },
        'react-resizable': {
          singleton: true,
          version: '3.0.5',
          requiredVersion: '3.0.5',
        },
        'react-redux': {
          singleton: true,
          version: '8.1.2',
          requiredVersion: '8.1.2',
        },
      },
      // 解决module federation 引用远程模块报错问题，library的type为window，name为远程模块的名称
      library: { type: 'window', name: 'gsc_app' },
    }),
  ].filter(Boolean),
}
