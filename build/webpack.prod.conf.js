/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path')
const paths = require('./paths')
const { merge } = require('webpack-merge')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin')
const TerserPlugin = require('terser-webpack-plugin')
const CompressionPlugin = require('compression-webpack-plugin')
const InjectVersionHtmlPlugin = require('./InjectVersionHtmlPlugin')
const baseWebpackConfig = require('./webpack.base.conf.js')
const config = require('../config').build
const SentryCliPlugin = require('@sentry/webpack-plugin')
const { CleanWebpackPlugin } = require('clean-webpack-plugin')
const GenerateVersionWebpackPlugin = require('@mi/generate-version-webpack-plugin')
const zlib = require('zlib')
const baseConfig = require(`../src/config/base.conf.js`)

const name = baseConfig.SITE_BASE?.replace(/(^\/)|(\/$)/g, '')
// const publicPath = baseConfig.SITE_BASE
const webpackConfig = merge(baseWebpackConfig, {
  mode: config.mode,
  output: {
    path: config.assetsRoot,
    filename: path.join(config.assetsSubDirectory, 'js/[name].[chunkhash:8].js'),
    chunkFilename: path.join(config.assetsSubDirectory, 'js/[name].[chunkhash:8].chunk.js'),
    assetModuleFilename: 'assets/[hash][ext][query]',
    publicPath: config.assetsPublicPath,
    library: `${name}-[name]`,
    libraryTarget: 'umd',
    chunkLoadingGlobal: `webpackJsonp_${name}`,
    globalObject: 'window',
    clean: true,
  },
  bail: true,
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename],
    },
  },
  optimization: {
    minimize: true,
    minimizer: [
      new CssMinimizerPlugin(),
      new TerserPlugin({
        parallel: true,
        extractComments: false,
        terserOptions: {
          output: {
            beautify: false,
            comments: false,
          },
          compress: {
            warnings: false,
            drop_console: process.env.DEPLOY_ENV === 'pro',
            drop_debugger: true,
          },
        },
      }),
    ],
    splitChunks: {
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendor',
          chunks: 'all',
          priority: -30,
          reuseExistingChunk: true,
        },
        react: {
          test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
          name: 'react',
          chunks: 'all',
          priority: -20,
          reuseExistingChunk: true,
        },
        hiui: {
          test: /[\\/]node_modules[\\/](@hi-ui)[\\/].+/,
          name: 'hiui',
          chunks: 'all',
          priority: -10,
          reuseExistingChunk: true,
        },
      },
    },
    runtimeChunk: true,
  },
  plugins: [
    new InjectVersionHtmlPlugin({ app: 'gsc' }),
    new SentryCliPlugin({
      include: './dist',
      ignore: ['node_modules'],
      release: 'GSC-WEB-' + process.env.CI_COMMIT_TAG,
      urlPrefix: config.assetsPublicPath,
      setCommits: {
        auto: true,
        ignoreMissing: true,
      },
    }),
    new MiniCssExtractPlugin({
      filename: path.join(config.assetsSubDirectory, 'css/[name].[contenthash:8].css'),
      chunkFilename: path.join(config.assetsSubDirectory, 'css/[name].[contenthash:8].chunk.css'),
    }),
    new HtmlWebpackPlugin({
      inject: true,
      template: paths.rootTemplate,
      templateParameters: {
        prefixUrl: path.join(config.assetsPublicPath, config.assetsSubDirectory),
      },
      minify: {
        removeComments: true, // 移除注释
        collapseWhitespace: true, // 去除空格
        removeEmptyAttributes: true, // 去除空属性
      },
      meta: {
        version: process.env.CI_COMMIT_TAG,
      },
    }),
    new GenerateVersionWebpackPlugin({
      version: process.env.CI_COMMIT_TAG,
    }),
    new CleanWebpackPlugin({
      verbose: true, // 输出日志
      protectWebpackAssets: false, // 是否保护WebpackAssets被删除
      // 建构后删除map文件(测试环境可根据需求是否需要删除)
      cleanAfterEveryBuildPatterns: ['**/*.js.map'],
    }),
    new CompressionPlugin({
      filename: '[path][base].gz',
      algorithm: 'gzip',
    }),
    new CompressionPlugin({
      filename: '[path][base].br',
      algorithm: 'brotliCompress',
      compressionOptions: {
        params: {
          [zlib.constants.BROTLI_PARAM_QUALITY]: 11,
        },
      },
    }),
  ].filter(Boolean),
})

if (config.bundleAnalyzerReport) {
  const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
  webpackConfig.plugins.push(new BundleAnalyzerPlugin())
}

if (config.bundleSpeedTest) {
  const SpeedMeasurePlugin = require('speed-measure-webpack-plugin')
  const smp = new SpeedMeasurePlugin()

  smp.wrap(webpackConfig)
}

module.exports = webpackConfig
