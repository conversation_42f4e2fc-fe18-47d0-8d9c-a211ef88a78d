export const TODO_DETAIL_SEARCH = {
  tip: '970.4.1.1.27184',
  module: '待办明细',
  clicked_button_name: '查询',
}

export const TODO_DETAIL_RESET = {
  tip: '970.4.1.1.27185',
  module: '待办明细',
  clicked_button_name: '重置',
}

export const TODO_DETAIL_EXPORT = {
  tip: '970.4.1.1.27205',
  module: '待办明细',
  clicked_button_name: '导出',
}

export const TODO_DETAIL_TAB_CHANGE = {
  tip: '970.4.1.1.27186',
  module: '待办明细',
  clicked_button_name: '查看待办场景',
}

export const TODO_DASHBOARD_RESET = {
  tip: '970.3.2.1.27182',
  module: '待办看板',
  clicked_button_name: '重置',
}

export const TODO_DASHBOARD_SEARCH = {
  tip: '970.3.2.1.27181',
  module: '待办看板',
  clicked_button_name: '查询',
}

export const SHOW_TODO_DETAIL = {
  tip: '970.3.2.1.27183',
  module: '待办看板',
  clicked_button_name: '查看明细',
}

export const TODO_HOME_TYPE = {
  tip: '970.3.1.1.27179',
  module: '首页待办',
  clicked_button_name: '选择待办类型',
}

export const TODO_ITEM_TYPE = {
  tip: '970.3.1.1.27180',
  module: '首页待办',
  clicked_button_name: '选择待办场景',
}

export const TODO_EXPORT = {
  tip: '970.3.1.1.27204',
  module: '首页待办',
  clicked_button_name: '导出',
}

export const TODO_WHALE_TABLE_CEIL = {
  tip: '970.3.2.1.27349',
  module: '待办看板',
  clicked_button_name: '点击透视表部门项',
}

// GSC BI ADD
export const DASHBOARD_VIEW_START = {
  event: 'view_start',
  tip: '1118.1.1.1.25134',
}

export const DASHBOARD_VIEW_END = {
  event: 'view_end',
  tip: '1118.1.1.1.25135',
  ref_tip: '1118.1.1.1.25134',
}

export const DASHBOARD_GET_TOKEN = {
  event: 'view_end',
  tip: '1118.1.1.1.25169',
  ref_tip: '1118.1.1.1.25134',
}

export const DASHBOARD_LEAVE = {
  event: 'view_leave',
  tip: '1118.1.1.1.25404',
  ref_tip: '1118.1.1.1.25134',
}

// PBI dashboard
export const PBI_DASHBOARD = {
  event: 'performance',
  tip: '970.9.0.1.29252',
  ref_tip: '970.9.0.1.29252',
}

export const GSC_TRACK_DETAIL_URL = [
  // 页面埋点详情url
  'apps/gsc/bi-manage/update',
  'apps/gsc/todo-config/update',
  'apps/gsc/todo-config/update-notify',
]
export const GSC_TRACK_EDIT_URL = ['apps/gsc/manual-todo/edit', 'apps/gsc/update-announce']
