import { getStringStorage } from '@/utils/cache'

export * from './auth'

export const gscMenuPrefix = '/apps/gsc' // 菜单前缀
export const GSC_ROOT = '#gsc-root'
export const isLocal = process.env.DEPLOY_ENV === 'local'
export const isDev = process.env.DEPLOY_ENV === 'dev'
export const isTest = process.env.DEPLOY_ENV === 'test'
export const isProd = process.env.DEPLOY_ENV === 'pro'
export const isPre = process.env.DEPLOY_ENV === 'pre'

export const getEnv = () => {
  if (isLocal) return 'local'
  if (isDev) return 'dev'
  if (isTest) return 'test'
  if (isPre) return 'pre'
  if (isProd) return 'pro'
  return 'local'
}
export const env = getEnv()

export const GSC_AUTH = 'GSC_AUTH'
export const USERINFO = 'USER_INFO'
export const USERNAME = 'USERNAME'
export const DT_TOKEN = 'DT_TOKEN_' + process.env.DEPLOY_ENV

export const OFFLINE_URL = '/exception/offline'
export const DASHBOARD_BLANK_URL = '/exception/dashboard-blank'

export const TODO_DASHBOARD_LIST = isProd ? [25, 28] : [11, 49, 51]

export const isTodoDashboard = (id: number): boolean => TODO_DASHBOARD_LIST.includes(id)

export const isStoreVisDashboard = (id: number): boolean => {
  const matchedId = isProd ? 47 : 89
  return id === matchedId
}

export const isGSCWorkbench = /^gsc.(.*)mioffice.cn$/.test(location.host)

export const NOTIFY_UPLOAD_TYPE = 'NOTIFY_UPLOAD_TYPE'

export const GSC_HOME_URL_ARRAY = ['/apps/gsc/', '/apps/gsc']
// 米盾token过期时间3天
export const CAS_TOKEN_EXPIRE = 1000 * 60 * 60 * 24 * 2

export const LINK_VISIBLE_MAT_STORAGE_KEY = 'mat-visible-query'

export const PBI_TOKENS = 'PBI_CACHE_TOKENS'

export const FETCH_SUCCESS_CODES = [0, 2000]

export type EmptyFunctionType = () => void

export const FORM_VIEWS_CACHE = 'NEW_FORM_VIEWS_CACHE'
export const MATERIAL_QUICK_QUERY = 'MATERIAL_QUICK_QUERY' // 物料快查
export const DASHBOARD_MATERIAL = 'DASHBOARD_MATERIAL' // 原材料物流状态可视
export const MOVING_AVG_PRICE = 'MOVING_AVG_PRICE' // 原材料集团移动平均价
export const NEW_PROJECT_COST = 'NEW_PROJECT_COST' // 新项目成本
export const ESTIMATE_COST = 'ESTIMATE_COST' // 项目成本-原材料
export const PROJECT_COST = 'PROJECT_COST' // 项目成本
export const PROJECT_COST_NPC = 'PROJECT_COST_NPC' // 项目成本-NPC
export const TODO_LIST_PAGE = 'TODO_LIST_PAGE' // 待办明细
export const TODO_SEARCH_PAGE = 'TODO_SEARCH_PAGE' // 待办详情
export const MATERIAL_COST_REDUCTION = 'MATERIAL_COST_REDUCTION' // 成本降幅
export const TRACK_VISITOR = 'TRACK_VISITOR' // 使用人数
export const PROJECT_LIFE_COST = 'PROJECT_LIFE_COST' // 全周期项目成本
export const COSTED_BOM = 'COSTED_BOM' // 物料清单
export const NEW_PROJECT_COST_PRICE_MAINTENANCE = 'NEW_PROJECT_COST_PRICE_MAINTENANCE' // 价格维护
export const NEW_PROJECT_COST_PROJECT_LIST = 'NEW_PROJECT_COST_PROJECT_LIST' // 项目清单列表
export const NEW_PROJECT_COST_UPLOADER_LIST = 'NEW_PROJECT_COST_UPLOADER_LIST' // 项目品类维护人配置
export const NEW_PROJECT_COST_COST_LIST = 'NEW_PROJECT_COST_COST_LIST' // 成本清单列表
export const MENU_CLICK = 'MENU_CLICK' // 功能使用次数/人次
export const MENU_TRACK_PV = 'MENU_TRACK_PV' // 菜单使用次数
export const MENU_TRACK_UV = 'MENU_TRACK_UV' // 菜单使用人次
export const PERSON_ENTRANCE_RULE = 'PERSON_ENTRANCE_RULE' // 用户收口
export const QMS_AFTER_SALE_OVERVIEW = 'QMS_AFTER_SALE_OVERVIEW' // 售后概况
export const QMS_AFTER_SALE_FAULT = 'QMS_AFTER_SALE_FAULT' // 售后故障
export const QMS_AFTER_SALE_MATERIAL = 'QMS_AFTER_SALE_MATERIAL' // 售后换料
export const QMS_AFTER_SALE_SPECIAL = 'QMS_AFTER_SALE_SPECIAL' // 专项分析
export const QMS_MATERIAL_TRACE = 'QMS_MATERIAL_TRACE' // 物料追溯
export const NEW_PROJECT_COST_PURCHASE_CHANNEL = 'NEW_PROJECT_COST_PURCHASE_CHANNEL' // 采购渠道

export const MESSAGE_DURATION = 3000

// 迫不得已再考虑用
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type AnyType = any

export const GSC_TRACK_SUPPLIER_TYPES_CACHE = 'GSC_TRACK_SUPPLIER_TYPES_CACHE'
export const HEADER_TITLE_PORTAL = 'gsc-header__title'
export const HEADER_TOOLS_PORTAL = 'gsc-header__tools'
export const NEW_ALL_COST_PROJECT_CACHE = 'NEW_ALL_COST_PROJECT_CACHE'

export const ICON_COLOR = '#5F6A7A'

export enum TableColumnType {
  Fixed = 1, // 固定维度
  Hidden, // 隐藏维度
  Normal, // 普通维度
  Measure, // 指标
  HiddenMeasure, // 隐藏指标
  DynamicMeasure, // 动态指标
}

export const INTERNAL = 'INTERNAL'
export const EXTERNAL = 'EXTERNAL'

export const TRUE_OR_FALSE_STATUS = [
  { title: '是', id: true },
  { title: '否', id: false },
]

export const IMG_PREFIX = 'https://cdn.cnbj1.fds.api.mi-img.com/scm/gsc/images/'
export const QMS_IMG_PREFIX = 'https://cdn.cnbj1.fds.api.mi-img.com/scm/qms/'

export const COST_URLS = [
  '/apps/gsc/move-average-price', // 原材料价格
  '/apps/gsc/new-project-cost', // 新项目成本
  '/apps/gsc/metearial-cost/cvpn', // 原材料成本
  '/apps/gsc/predict-exchange-rate', // 汇率
  '/apps/gsc/all-life-cost', // 全生命周期成本
  '/apps/gsc/metearial-cost/project', // 项目成本
  '/apps/gsc/new-cost-reduction', // 成本降幅
  // '', // 预实对比分析，还没上线，路由不确定
  '/apps/gsc/dashboard/views?menuId=6560', // 预测价格波动明细
  '/apps/gsc/predict-upload', // 预测提报
]

export const EXCEPTION_403_URLS = ['/apps/gsc/exception/cost-403', '/apps/gsc/exception/403']

export const GSC_LANG = 'GSC_LANG'

export enum Language {
  zh = 'zh-CN',
  en = 'en-US',
}

export const getGSCLang = () => getStringStorage(GSC_LANG) || Language.zh
export const FORM_ITEM_CONFIG = {
  style: {
    width: '100%',
    minWidth: 196,
  },
  height: 260,
}
export const PROJECT_COST_EDIT = 'project_cost_biz_confirm'
export const PROJECT_COST_RELEASE = 'project_cost_bp_release'

export const UP_ICON_COLOR = 'rgb(244, 72, 92)'
export const DOWN_ICON_COLOR = 'rgb(0, 168, 84)'

export const NUM_AFTER_POINT = 4

export const isTabPage = 'isTabPage'
