import intl from 'react-intl-universal'
import { TableColumnItem } from '@hi-ui/table'
import { ellipses } from '@/components/ellipsis-tool'

const MATERIAL_CONFIG = {
  width: 180,
  render: (text) => ellipses(text),
}

// 一些常用的表头信息，方便不同页面使用
export const ISC_COLUMN_MAP = {
  orgTreeCode: {
    title: intl.get('组织编码'),
    dataKey: 'orgTreeCode',
    width: 100,
  },
  matCatLvl1Code: {
    title: intl.get('物料大类'),
    dataKey: 'matCatLvl1Code',
    ...MATERIAL_CONFIG,
  },
  matCatLvl2Code: {
    title: intl.get('物料中类'),
    dataKey: 'matCatLvl2Code',
    ...MATERIAL_CONFIG,
  },
  matCatLvl3Code: {
    title: intl.get('物料小类'),
    dataKey: 'matCatLvl3Code',
    ...MATERIAL_CONFIG,
  },
  cvpnCode: {
    title: 'CVPN',
    dataKey: 'cvpnCode',
    ...MATERIAL_CONFIG,
  },
  pnCode: {
    title: 'PN',
    dataKey: 'pnCode',
    ...MATERIAL_CONFIG,
  },
  mpnId: {
    title: 'MPNID',
    dataKey: 'mpnId',
    ...MATERIAL_CONFIG,
  },
} as Record<string, TableColumnItem>
