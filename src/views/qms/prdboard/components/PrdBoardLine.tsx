import React, { memo, useState, useMemo } from 'react'
import { BOARD_LINE_STYLE, DIMENSION_TYPES } from '../constant'
import NavLine, { ActiveNavBar } from './NavLine'
import { iconMap } from '../config'
import '../index.scss'

const PrdBoardLine = function ({ data }) {
  const { productLineName, deviceTypes, dimensions, defaultNavBar } = data

  const [activeNavBar, setActiveNavBar] = useState<ActiveNavBar>(defaultNavBar)

  const titleHandleLine = useMemo(() => {
    const NavBarProps = {
      deviceTypes,
      dimensions,
      activeNavBar,
      setActiveNavBar: (val) => {
        console.log('ddd ==> setActiveNavBar', val)
        setActiveNavBar(val)
      },
    }
    return (
      <div
        className={`h-32 w-full flex justify-between items-center pl-8`}
        style={{ backgroundColor: BOARD_LINE_STYLE.TITLE_BACKGROUNDCOLOR }}
      >
        <div
          className="flex items-center justify-center"
          style={{
            fontWeight: BOARD_LINE_STYLE.TITLE_FONT_WEIGHT,
            fontSize: BOARD_LINE_STYLE.TITLE_FONT_SIZE,
            color: BOARD_LINE_STYLE.TITLE_COLOR,
          }}
        >
          <img
            src={iconMap[productLineName]}
            alt={productLineName}
            style={{
              width: BOARD_LINE_STYLE.ICON_SIZE,
              height: BOARD_LINE_STYLE.ICON_SIZE,
              alignSelf: 'center',
              marginRight: BOARD_LINE_STYLE.ICON_MARGIN_RIGHT,
            }}
          />
          <span>{productLineName}</span>
        </div>

        <NavLine {...NavBarProps} />
      </div>
    )
  }, [productLineName, deviceTypes, dimensions, activeNavBar, setActiveNavBar])

  return (
    <div className="rounded-6 mb-6 prd-boardline">
      {titleHandleLine}
      <div className="w-full bg-white px-8 py-8">{data.children}</div>
    </div>
  )
}

export default memo(PrdBoardLine)
