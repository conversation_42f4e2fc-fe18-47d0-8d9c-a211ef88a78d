/* eslint-disable react/display-name */
import React from 'react'
import styled from 'styled-components'
import Tooltip from '@hi-ui/tooltip'
import Select from '@hi-ui/select'
import { InfoCircleOutlined } from '@hi-ui/icons'
import { BOARD_LINE_STYLE } from '../constant'
import type { PrdBoardProps } from '../PrdQuality'
import { defaultTitleSvg } from '@/views/qms/billboard/components/NavLine'

const NavBarContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  flex: 1;
`

const NavDivider = styled.div`
  flex: none;
  width: 2px;
  height: 20px;
  background-color: #dfe2e8;
  margin: ${(props) => (props.marginMore ? '0 12px' : '0 12px 0 6px')};
`

const NavButton = styled.div<{ active?: boolean }>`
  width: auto;
  height: 32px;
  display: flex;
  padding: 6px 12px;
  margin: 0 6px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: ${(props) => (props.active ? '1px solid #BDE2FF' : '1px solid #ebedf0')};
  background: ${(props) => (props.active ? '#E2F3FE' : '#fff')};
  color: ${(props) => (props.active ? '#237ffa' : '#5f6a7a;')};
  font-weight: ${(props) => (props.active ? '500' : '400;')};
  text-align: center;
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-family: 'PingFang SC';
  font-size: 14px;
  cursor: pointer;
  &:hover {
    color: #237ffa;
  }
`
export type ActiveNavBar = {
  deviceType: string
  region: string
  factory?: string
  project?: string
  date?: string
}

interface NavBarProps extends PrdBoardProps {
  activeNavBar: ActiveNavBar
  setActiveNavBar: (activeNavBar) => void
}

const NavLine: React.FC<NavBarProps> = ({
  deviceTypes,
  dimensions,
  activeNavBar,
  setActiveNavBar,
}) => {
  const dimensionsLength = dimensions.length
  const lastIndex = dimensionsLength - 1

  const handleClick = (type: Partial<ActiveNavBar>) => {
    setActiveNavBar({ ...activeNavBar, ...type })
  }

  return (
    <NavBarContainer>
      <div className="flex justify-start items-center">
        {Array.isArray(deviceTypes) &&
          deviceTypes.length > 1 &&
          deviceTypes.map((type, index) => (
            <NavButton
              key={`device-${type}-${index}`}
              active={activeNavBar.deviceType === type}
              onClick={() => handleClick({ deviceType: type })}
            >
              {defaultTitleSvg[type]?.({
                width: '16px',
                height: '16px',
              })}
              <span className="ml-3">{type}</span>
            </NavButton>
          ))}
      </div>
      <div className="flex">
        {dimensions.map((item, index) => {
          const list = item.list || []
          const tiledList = list.slice(0, 3)
          const moredList = list.slice(3).map((item) => ({ title: item, id: item }))
          const isActive = moredList.some((s) => s.id === activeNavBar[item.key])

          if (list.length === 0) return null
          return (
            <div key={item.key} className="flex justify-end items-center">
              {dimensionsLength > 1 && (
                <span
                  className="mr-4 flex items-center"
                  style={{
                    fontSize: BOARD_LINE_STYLE.LABEL_FONT_SIZE,
                    color: BOARD_LINE_STYLE.LABEL_COLOR,
                  }}
                >
                  {item.title}
                  {item?.tooltip && (
                    <Tooltip title={item.tooltip} trigger="hover">
                      <InfoCircleOutlined
                        style={{ fontSize: 14, marginLeft: 4, color: '#5F6A7A' }}
                      />
                    </Tooltip>
                  )}
                </span>
              )}
              {tiledList.map((s) => (
                <NavButton
                  key={s}
                  active={activeNavBar[item.key] === s}
                  onClick={() => handleClick({ [item.key]: s })}
                >
                  {s}
                </NavButton>
              ))}
              {moredList.length > 0 && (
                <Select
                  className={`prd-select-more ${isActive ? 'active' : ''}`}
                  data={moredList}
                  value={activeNavBar[item.key]}
                  clearable={false}
                  searchable
                  appearance="line"
                  style={{ width: '76px' }}
                  placeholder="更多"
                  optionWidth={160}
                  onChange={(value) => {
                    handleClick({ [item.key]: value })
                  }}
                />
              )}
              {index !== lastIndex && <NavDivider marginMore={moredList.length > 0} />}
            </div>
          )
        })}
      </div>
    </NavBarContainer>
  )
}

export default NavLine
