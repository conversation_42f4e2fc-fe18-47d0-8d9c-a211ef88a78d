/* eslint-disable react/display-name */
import React from 'react'
import styled from 'styled-components'

const NavBarContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  flex: 1;
`

const NavButton = styled.div<{ active?: boolean }>`
  width: auto;
  height: 32px;
  display: flex;
  padding: 6px 12px;
  margin: 0 6px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: ${(props) => (props.active ? '1px solid #BDE2FF' : '1px solid var(--Gray-G20, #ebedf0)')};
  background: ${(props) => (props.active ? '#E2F3FE' : '#fff')};
  color: ${(props) => (props.active ? '#237ffa' : '#5f6a7a;')};
  font-weight: ${(props) => (props.active ? '500' : '400;')};

  text-align: center;
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-family: 'PingFang SC';
  font-size: 14px;
  cursor: pointer;
  &:hover {
    color: #237ffa;
  }
`
export type ActiveNavBar = {
  deviceType: string
  region: string
  factory: string
  project: string
  date: string
}

interface NavBarProps {
  deviceTypes: string[]
  regions: string[]
  factorys: string[]
  projects: string[]
  setActiveNavBar: (activeNavBar) => void
  activeNavBar: ActiveNavBar
}

const NavLine: React.FC<NavBarProps> = ({
  deviceTypes,
  regions,
  activeNavBar,
  setActiveNavBar,
}) => {
  const handleClick = (type: Pick<ActiveNavBar, 'deviceType' | 'region'>) => {
    setActiveNavBar(type)
  }

  return (
    <NavBarContainer>
      <div className="flex justify-start items-center">
        {/* 只有一个选项就不展示了 */}
        {Array.isArray(deviceTypes) &&
          deviceTypes.length > 1 &&
          deviceTypes.map((type, index) => (
            <NavButton
              key={`device-${type}-${index}`}
              active={activeNavBar.deviceType === type}
              onClick={() => handleClick({ deviceType: type })}
            >
              {type}
            </NavButton>
          ))}
      </div>
      <div className="flex">
        <div className="flex justify-end items-center">
          {regions.map((region) => (
            <NavButton
              key={region}
              active={activeNavBar.region === region}
              onClick={() => {
                handleClick({ deviceType: activeNavBar.deviceType, region })
              }}
            >
              {region}
            </NavButton>
          ))}
        </div>
      </div>
    </NavBarContainer>
  )
}

export default NavLine
