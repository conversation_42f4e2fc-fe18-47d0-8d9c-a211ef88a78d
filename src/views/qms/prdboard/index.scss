@font-face {
  font-family: MiSans-Medium;
  src: url('https://cdn.cnbj1.fds.api.mi-img.com/scm/qms/work-bench/family/MiSans-Medium.ttf')
    format('truetype');
}

.prd-statistical-radio {
  padding: 2px;
  font-weight: normal;
  color: #5f6a7a;
  background-color: #f6f7f9;
  border-radius: 6px;

  .hi-v4-radio--type-button[data-checked] {
    box-sizing: border-box;
    color: #000;
    background-color: #fff;
    border-radius: 4px;
    font-weight: 500;
  }

  .hi-v4-radio--type-button::after {
    display: none;
  }
}

.prd-board {
  min-width: 1300px;
}

.prd-boardline {
  width: max-content;
  height: auto;
  min-width: 100%;
  background-color: rgb(255 255 255 / 65%);
  overflow: visible hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.prd-select-more {
  --hi-v4-color-gray-300: #ebedf0;
  --hi-v4-color-gray-700: #5f6a7a;

  &.active {
    --hi-v4-color-gray-300: #bde2ff;
    --hi-v4-color-gray-700: #237ffa;
    --hi-v4-color-static-white: #e2f3fe;
  }
}
