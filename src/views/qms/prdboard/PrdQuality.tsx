import React, { memo, useMemo } from 'react'
import { BOARD_LINE_STYLE, DIMENSION_TEXT } from './constant'
import { phoneIcon } from '@/views/qms/billboard/assets'

const productLineName = DIMENSION_TEXT.PHONE

const PrdQuality = function () {
  const titleHandleLine = useMemo(() => {
    return (
      <div
        className={`h-28 w-full flex justify-between items-center pl-8`}
        style={{ backgroundColor: BOARD_LINE_STYLE.TITLE_BACKGROUNDCOLOR }}
      >
        <div
          className="flex items-center justify-center"
          style={{
            fontWeight: BOARD_LINE_STYLE.TITLE_FONT_WEIGHT,
            fontSize: BOARD_LINE_STYLE.TITLE_FONT_SIZE,
            color: BOARD_LINE_STYLE.TITLE_COLOR,
          }}
        >
          <img
            src={phoneIcon}
            alt={productLineName}
            style={{
              width: BOARD_LINE_STYLE.ICON_SIZE,
              height: BOARD_LINE_STYLE.ICON_SIZE,
              alignSelf: 'center',
              marginRight: BOARD_LINE_STYLE.ICON_MARGIN_RIGHT,
            }}
          />
          <span>{productLineName}</span>
        </div>
      </div>
    )
  }, [])

  return (
    <div className="rounded-6 mb-6 prd-board__line">
      {titleHandleLine}
      <div className="w-full bg-white px-8 py-8">
        <div>MFG DI</div>
        <div>FPY&PY</div>
        <div>OBA</div>
      </div>
    </div>
  )
}

export default memo(PrdQuality)
