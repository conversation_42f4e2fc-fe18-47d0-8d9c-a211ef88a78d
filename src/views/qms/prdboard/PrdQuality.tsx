import React, { memo } from 'react'
import { DIMENSION_TYPES } from './constant'
import PrdBoardLine from './components/PrdBoardLine'
import type { ActiveNavBar } from './components/NavLine'

export interface PrdBoardProps {
  deviceTypes: string[]
  dimensions: DimensionProp[]
}

export interface BoardItemData extends PrdBoardProps {
  productLineName: string
  productLineId: number
  defaultNavBar: ActiveNavBar
  component: () => React.ReactNode
}

export interface DimensionProp {
  key: string
  title: string
  list: string[]
  tooltip?: string
}

const deviceTypes = ['手机', '平板']
const phoneDimensions = [
  {
    ...DIMENSION_TYPES.REGION,
    list: ['国内', '国际', '印度'],
  },
  {
    ...DIMENSION_TYPES.FACTORY,
    list: ['昌平', '蓝思', '华勤', '龙旗'],
  },
  {
    ...DIMENSION_TYPES.PROJECT,
    list: ['O1', 'O2', 'P1', 'P3', 'P4', 'N12', 'O16'],
  },
]

const ffrDimensions = [
  {
    ...DIMENSION_TYPES.REGION,
    list: ['中国大陆', '全球(除中国、印度)', '印度'],
  },
]

const data: BoardItemData[] = [
  {
    productLineName: DIMENSION_TYPES.PHONE.title,
    productLineId: 1,
    deviceTypes,
    dimensions: phoneDimensions,
    defaultNavBar: {
      deviceType: '手机',
      region: '国内',
      factory: '昌平',
      project: 'O1',
    },
    component: () => <div>MFG DI</div>,
  },
  {
    productLineName: DIMENSION_TYPES.FFR.title,
    productLineId: 2,
    deviceTypes,
    dimensions: ffrDimensions,
    defaultNavBar: {
      deviceType: '手机',
      region: '中国大陆',
    },
    component: () => <div>FFR</div>,
  },
]

const PrdQuality = function () {
  return (
    <>
      {data.map((item) => {
        return (
          <PrdBoardLine key={item.productLineId} data={item}>
            {item.component(item)}
          </PrdBoardLine>
        )
      })}
    </>
  )
}

export default memo(PrdQuality)
