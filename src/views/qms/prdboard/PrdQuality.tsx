import React, { memo, useState, useMemo } from 'react'
import { BOARD_LINE_STYLE, DIMENSION_TEXT } from './constant'
import { phoneIcon } from '@/views/qms/billboard/assets'
import NavLine, { ActiveNavBar } from './components/NavLine'

const productLineName = DIMENSION_TEXT.PHONE

const PrdQuality = function () {
  const [activeNavBar, setActiveNavBar] = useState<ActiveNavBar>({
    deviceType: '手机',
    region: '国内',
    factory: '',
    project: '',
    date: '',
  })

  const titleHandleLine = useMemo(() => {
    const NavBarProps = {
      deviceTypes: ['手机', '平板'],
      regions: ['国内', '国际', '印度'],
      factorys: [],
      projects: [],
      activeNavBar,
      setActiveNavBar,
    }
    return (
      <div
        className={`h-28 w-full flex justify-between items-center pl-8`}
        style={{ backgroundColor: BOARD_LINE_STYLE.TITLE_BACKGROUNDCOLOR }}
      >
        <div
          className="flex items-center justify-center"
          style={{
            fontWeight: BOARD_LINE_STYLE.TITLE_FONT_WEIGHT,
            fontSize: BOARD_LINE_STYLE.TITLE_FONT_SIZE,
            color: BOARD_LINE_STYLE.TITLE_COLOR,
          }}
        >
          <img
            src={phoneIcon}
            alt={productLineName}
            style={{
              width: BOARD_LINE_STYLE.ICON_SIZE,
              height: BOARD_LINE_STYLE.ICON_SIZE,
              alignSelf: 'center',
              marginRight: BOARD_LINE_STYLE.ICON_MARGIN_RIGHT,
            }}
          />
          <span>{productLineName}</span>
        </div>

        <NavLine {...NavBarProps} />
      </div>
    )
  }, [activeNavBar])

  return (
    <div className="rounded-6 mb-6 prd-board__line">
      {titleHandleLine}
      <div className="w-full bg-white px-8 py-8">
        <div>MFG DI</div>
        <div>FPY&PY</div>
        <div>OBA</div>
      </div>
    </div>
  )
}

export default memo(PrdQuality)
