import React, { useMemo, useState, useEffect } from 'react'
import Dayjs from 'dayjs'
import { microApp } from '@/utils/micro-app'
import CustomPortal from '@/components/custom-portal'
import { HEADER_TITLE_PORTAL } from '@/constants'
import { RadioGroup, RadioGroupProps } from '@hi-ui/radio'
import Loading from '@hi-ui/loading'
import PrdQuality from './PrdQuality'
import PrdMonitor from './PrdMonitor'
import {
  HEADER_TITLE,
  DATA_PRODUCTION_TIME_STYLE,
  DATA_PRODUCTION_TIME_TEXT,
  PLACEHOLDER_STR,
  FORMAT_DATETIME,
  STATISTICAL_PAGE,
  QUALITY_TEXT,
  MONITOR_TEXT,
} from './constant'
import './index.scss'

const QmsPrdBoard: React.FC = () => {
  const [etlTime, setEtlTime] = useState<string>('')
  const [statisticalType, setStatisticalType] = useState<string>('')

  // 可以通过外部配置来控制导航栏选项
  const navBarConfig = {
    deviceTypes: ['手机', '平板'],
    regions: ['国内', '国际', '印度'],
    factorys: ['昌平', '深圳', '东莞', 'xxx'],
    projects: ['O1', 'O2', 'P1', 'P3', 'P4', 'N12', 'O16'],
  }

  useEffect(() => {
    const { setOpenSubMenu } = microApp.getProps() as {
      setOpenSubMenu: (value: boolean) => void
    }

    if (setOpenSubMenu) {
      setOpenSubMenu(false)
    }

    // mock初始化数据
    const timer = setTimeout(() => {
      setEtlTime(Dayjs().format(FORMAT_DATETIME))
      setStatisticalType(STATISTICAL_PAGE.QUALITY)
    }, 1000)

    // 清理定时器
    return () => {
      clearTimeout(timer)
    }
  }, [])

  const memoTopBar = useMemo(() => {
    const statisticalData: RadioGroupProps['data'] = [
      {
        title: QUALITY_TEXT,
        id: STATISTICAL_PAGE.QUALITY,
      },
      {
        title: MONITOR_TEXT,
        id: STATISTICAL_PAGE.MONITOR,
      },
    ]

    const onChangeStatisticalType = (value) => {
      setStatisticalType(value)
      setEtlTime(Dayjs().format(FORMAT_DATETIME))
    }

    return (
      <div className="w-full flex justify-between align-center">
        <div className="flex align-center gap-8">
          {HEADER_TITLE}
          <div style={DATA_PRODUCTION_TIME_STYLE}>{`${DATA_PRODUCTION_TIME_TEXT}${
            etlTime ? Dayjs(etlTime).format(FORMAT_DATETIME) : PLACEHOLDER_STR
          }`}</div>
        </div>
        <div className="flex justify-end items-center">
          <RadioGroup
            className="prd-statistical-radio"
            value={statisticalType}
            type={'button'}
            data={statisticalData}
            onChange={onChangeStatisticalType}
          />
        </div>
      </div>
    )
  }, [etlTime, statisticalType])

  return (
    <div className="w-full h-full prd-board">
      {!etlTime ? (
        <div className="w-full h-full flex justify-center items-center">
          <Loading size="lg" />
        </div>
      ) : (
        <>
          <CustomPortal domId={HEADER_TITLE_PORTAL}>{memoTopBar}</CustomPortal>
          {statisticalType === STATISTICAL_PAGE.QUALITY ? (
            <PrdQuality navBarConfig={navBarConfig} />
          ) : (
            <PrdMonitor />
          )}
        </>
      )}
    </div>
  )
}

QmsPrdBoard.displayName = 'QmsPrdBoard'

export default QmsPrdBoard
