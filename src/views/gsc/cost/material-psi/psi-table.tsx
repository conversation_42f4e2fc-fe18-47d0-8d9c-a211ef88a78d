import intl from 'react-intl-universal'
import React, { memo, useCallback, useEffect, useMemo } from 'react'
import Table from '@hi-ui/table'
import Button, { ButtonGroup } from '@hi-ui/button'
import { usePagination, useRequest } from 'ahooks'
import Card from '@hi-ui/card'
import cx from 'classnames'
import { getCvpnCols, prefix, PSI_TABLE_TAB_CONFIG } from './constants'
import { useMaterialPsi } from '@/hooks/useMaterialPsi'
import { exportPsiList, getPsiList, PsiParams } from '@/api/cost/material-psi'
import { customMessage } from '@/utils/custom-message'
import message from '@hi-ui/message'
import { omit } from 'lodash'

const PsiTable = memo(() => {
  const { filter, tableTab, setTableTab } = useMaterialPsi()

  const {
    run: getList,
    data: listData,
    loading,
    pagination,
  } = usePagination(
    ({ current, pageSize }) => {
      const params = { ...filter, pageNum: current, pageSize, type: tableTab }
      return getPsiList(params as PsiParams)
    },
    { manual: true, refreshDeps: [filter, tableTab] }
  )

  const { run: exportList, loading: exportLoading } = useRequest(exportPsiList, {
    manual: true,
    onSuccess: (res) => {
      message.closeAll()
      if (res?.data) {
        customMessage(intl.get('导出成功'), 'success')
        window.open(res.data)
      } else {
        customMessage(
          intl.get('导出数据较多，下载链接生成中，生成完毕会在飞书通知您，请稍后关注飞书消息'),
          'info'
        )
      }
    },
    onError: () => {
      message.closeAll()
      customMessage(intl.get('导出失败'), 'error')
    },
  })

  const handleExport = useCallback(() => {
    message.open({
      type: 'info',
      title: intl.get('导出中，数据量较大，请稍等...'),
      autoClose: false,
    })
    const params = {
      ...filter,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      type: tableTab,
    }
    const finalParams = tableTab === 'mpn' ? params : omit(params, ['pnCodes', 'mpnIds'])
    exportList(finalParams as PsiParams)
  }, [exportList, filter, pagination, tableTab])

  useEffect(() => {
    if (Object.keys(filter).length) {
      getList({ current: 1, pageSize: 20 })
    }
  }, [filter, tableTab, getList])

  const cols = useMemo(
    () =>
      getCvpnCols(
        {
          title: '序号',
          dataKey: 'index',
          width: 80,
          render: (_text, _rowItem, index) =>
            index + 1 + pagination.pageSize * (pagination.current - 1),
        },
        tableTab
      ),
    [pagination, tableTab]
  )

  return (
    <>
      <Card
        className={`${prefix}-table `}
        extra={
          <Button type="primary" onClick={handleExport} disabled={exportLoading}>
            {intl.get('导出')}
          </Button>
        }
        title={
          <div className="p-1">
            <ButtonGroup>
              {PSI_TABLE_TAB_CONFIG.map((item) => (
                <Button
                  type={tableTab === item.id ? 'secondary' : 'default'}
                  key={item.id}
                  onClick={(event) => {
                    event.stopPropagation()
                    setTableTab(item.id as 'cvpn' | 'mpn')
                  }}
                  className={cx({ 'white-bg': tableTab !== item.id })}
                >
                  {item.title}
                </Button>
              ))}
            </ButtonGroup>
          </div>
        }
      >
        <div className="table-container">
          <Table
            columns={cols}
            data={listData?.list || []}
            loading={loading}
            size="sm"
            maxHeight={418}
            fieldKey="id"
            resizable
            fixedToColumn={{ left: 'fcstMonth' }}
            pagination={{
              total: pagination.total,
              current: pagination.current,
              pageSize: pagination.pageSize,
              showTotal: true,
              showJumper: true,
              pageSizeOptions: [5, 10, 20, 50],
              onPageSizeChange: (pageSize) => pagination.changePageSize(pageSize),
              onChange: (current, _, pageSize) => pagination.onChange(current, pageSize),
            }}
          />
        </div>
      </Card>
    </>
  )
})

PsiTable.displayName = 'PsiTable'
export default PsiTable
