import intl from 'react-intl-universal'
import { Form, FormHelpers, FormItem } from '@hi-ui/form'
import React, { memo, useCallback, useRef } from 'react'
import { PSI_FORM_VALUES, prefix } from './constants'
import Button from '@hi-ui/button'
import { calcConditions } from '../cost-reduction/config'
import { customMessage } from '@/utils/custom-message'
import { DownOutlined } from '@hi-ui/icons'
import MaterialQuerySection from './material-query-selection'
import TimeQuerySection from './time-query-selection'
import { Col, Row } from '@hi-ui/grid'
import Select from '@hi-ui/select'
import Card from '@hi-ui/card'
import { useMaterialPsi } from '@/hooks/useMaterialPsi'
import { useMount } from 'ahooks'
import dayjs from 'dayjs'

const PsiForm = memo(() => {
  const { conditionList, expanded, tableTab, setExpanded, setFilter } = useMaterialPsi()
  const commonSettings = { searchable: true, minWidth: 200, height: 260 }
  const formRef = useRef<FormHelpers>(null)
  const format = useCallback(
    (values) => {
      const { businessDate, ...rest } = values
      const [startDate, endDate] = businessDate
      return {
        ...rest,
        startDate: dayjs(startDate).startOf('month')?.valueOf(),
        endDate: dayjs(endDate).endOf('month')?.valueOf(),
        type: tableTab,
      }
    },
    [tableTab]
  )

  const handleSearch = useCallback(() => {
    formRef.current?.validate()?.then((values) => {
      const str = calcConditions(values)
      if (str) {
        customMessage(`${str}所选查询条件数量过多，请减少选择数量后查询`, 'info', 2500)
      } else {
        setFilter(format(values))
      }
    })
  }, [setFilter, format])

  useMount(() => {
    setFilter(format(PSI_FORM_VALUES))
  })

  return (
    <Card className={`${prefix}-query-form`}>
      <div className="form-section" style={{ height: !expanded ? 32 : 220 }}>
        <Form initialValues={PSI_FORM_VALUES} innerRef={formRef} labelWidth={82} showColon={false}>
          <TimeQuerySection conditionList={conditionList} formRef={formRef} />
          <div className="row-section">
            <div className="row-title">{intl.get('组织')}</div>
            <Row gutter rowGap={12}>
              <Col span={8}>
                <FormItem field="bizName" label={intl.get('业务线')}>
                  <Select data={conditionList.bizNames} {...commonSettings}></Select>
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem field="dept" label={intl.get('部门')}>
                  <Select data={conditionList.depts} {...commonSettings}></Select>
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem field="teamOwnerEn" label={intl.get('组长')}>
                  <Select data={conditionList.teamOwners} {...commonSettings}></Select>
                </FormItem>
              </Col>
            </Row>
          </div>
          <MaterialQuerySection conditionList={conditionList} />
        </Form>
      </div>
      <div>
        <Button appearance="link" onClick={() => setExpanded((prev) => !prev)}>
          {!expanded ? '展开' : '收起'}
          <DownOutlined
            className="transition-transform ml-2"
            color="#303030"
            style={{
              transform: !expanded ? 'rotateZ(0)' : 'rotateZ(180deg)',
            }}
          />
        </Button>
        <Button type="default" onClick={() => formRef.current?.reset()}>
          {intl.get('重置')}
        </Button>
        <Button type="secondary" onClick={handleSearch}>
          {intl.get('查询')}
        </Button>
      </div>
    </Card>
  )
})

PsiForm.displayName = 'PsiForm'
export default PsiForm
