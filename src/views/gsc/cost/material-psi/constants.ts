import intl from 'react-intl-universal'
import dayjs from 'dayjs'
import { ellipses } from '@/components/ellipsis-tool'

export const SETTINGS = {
  showCheckAll: true,
  searchable: true,
  virtual: true,
  height: 260,
}

const getCommonCols = (type) => {
  const isMpn = type === 'mpn'
  const purchaseLevelCol = isMpn
    ? [
        {
          title: intl.get('采购层级'),
          dataKey: 'purchaseLvlCode',
          width: 100,
        },
      ]
    : []

  const descCol = isMpn
    ? [
        {
          title: intl.get('物料描述'),
          dataKey: 'pnCodeDesc',
          width: 200,
          render: (text) => ellipses(text),
        },
      ]
    : []

  const commonCols = [
    {
      title: intl.get('分区'),
      dataKey: 'date',
      width: 120,
    },
    {
      title: intl.get('业务线'),
      dataKey: 'bizName',
      width: 120,
    },
    {
      title: intl.get('年月'),
      dataKey: 'fcstMonth',
      width: 100,
    },
    {
      title: isMpn ? 'mpn' : 'cvpn',
      dataKey: isMpn ? 'mpn' : 'cvpn',
      width: 200,
    },
    {
      title: isMpn ? 'mpn_id' : 'cvpn_code',
      dataKey: isMpn ? 'mpnId' : 'cvpnCode',
      width: 200,
    },
    ...purchaseLevelCol,
    {
      title: intl.get('移动平均价'),
      dataKey: isMpn ? 'maPriceMpn' : 'maPriceCvpn',
      width: 160,
    },
    {
      title: intl.get('期初数量'),
      dataKey: 'initialQty',
      width: 160,
    },
    {
      title: intl.get('期初金额'),
      dataKey: 'initialAmount',
      width: 160,
    },
    {
      title: intl.get('入库数量'),
      dataKey: 'inQty',
      width: 160,
    },
    {
      title: intl.get('入库金额'),
      dataKey: 'inAmount',
      width: 160,
    },
    {
      title: intl.get('出库数量'),
      dataKey: 'outQty',
      width: 160,
    },
    {
      title: intl.get('出库金额'),
      dataKey: 'outAmount',
      width: 160,
    },
    {
      title: intl.get('期末数量'),
      dataKey: 'endingQty',
      width: 160,
    },
    {
      title: intl.get('期末金额'),
      dataKey: 'endingAmount',
      width: 160,
    },
    {
      title: intl.get('物料大类'),
      dataKey: 'matCatLvl1Code',
      width: 200,
      render: (text) => ellipses(text),
    },
    {
      title: intl.get('物料中类'),
      dataKey: 'matCatLvl2Code',
      width: 200,
      render: (text) => ellipses(text),
    },
    {
      title: intl.get('物料小类'),
      dataKey: 'matCatLvl3Code',
      width: 200,
      render: (text) => ellipses(text),
    },
    ...descCol,
    {
      title: intl.get('成本组大类'),
      dataKey: 'costMatCatLvl1Code',
      width: 200,
      render: (text) => ellipses(text),
    },
    {
      title: intl.get('成本组中类'),
      dataKey: 'costMatCatLvl2Code',
      width: 200,
      render: (text) => ellipses(text),
    },
  ]
  return commonCols
}

export const getCvpnCols = (indexCol, type) => {
  const mpnCols = [
    ...getCommonCols('mpn'),
    {
      title: intl.get('品牌'),
      dataKey: 'brand',
      width: 200,
    },
    {
      title: intl.get('制造商简称'),
      dataKey: 'mfrCode',
      width: 200,
      render: (text) => ellipses(text),
    },
    {
      title: intl.get('制造商料号'),
      dataKey: 'spCode',
      width: 200,
      render: (text) => ellipses(text),
    },
    {
      title: intl.get('物料负责人'),
      dataKey: 'matOprNameCn',
      width: 200,
    },
    {
      title: intl.get('组长'),
      dataKey: 'teamOwner',
      width: 200,
    },
    {
      title: intl.get('部门'),
      dataKey: 'dept',
      width: 200,
      render: (text) => ellipses(text),
    },
  ]
  const cvpnCols = getCommonCols('cvpn')
  return [indexCol, ...(type === 'mpn' ? mpnCols : cvpnCols)]
}

export const getBomCols = (indexCol) => {
  const bomCols = [
    {
      title: intl.get('分区'),
      dataKey: 'date',
      width: 120,
    },
    {
      title: intl.get('计划版本'),
      dataKey: 'planVersion',
      width: 120,
    },
    {
      title: intl.get('年月'),
      dataKey: 'yearMonth',
      width: 100,
    },
    {
      title: intl.get('项目'),
      dataKey: 'pro',
      width: 100,
    },
    {
      title: 'sopcode',
      dataKey: 'sopcode',
      width: 200,
    },
    {
      title: intl.get('一级销售区域'),
      dataKey: 'siteCnName',
      width: 200,
    },
    {
      title: intl.get('一级销售区域code'),
      dataKey: 'siteEnName',
      width: 200,
    },
    {
      title: intl.get('二级销售区域'),
      dataKey: 'site1CnName',
      width: 200,
    },
    {
      title: intl.get('二级销售区域code'),
      dataKey: 'site1EnName',
      width: 200,
    },
    {
      title: intl.get('一级销售版本'),
      dataKey: 'verCnName',
      width: 160,
    },
    {
      title: intl.get('一级销售版本code'),
      dataKey: 'verEnName',
      width: 160,
    },
    {
      title: intl.get('二级销售版本'),
      dataKey: 'ver1CnName',
      width: 160,
    },
    {
      title: intl.get('二级销售版本code'),
      dataKey: 'ver1EnName',
      width: 160,
    },
    {
      title: intl.get('配置'),
      dataKey: 'config',
      width: 160,
    },
    {
      title: intl.get('产地'),
      dataKey: 'factorySite',
      width: 160,
    },
    {
      title: intl.get('产地code'),
      dataKey: 'factorySiteCode',
      width: 160,
    },
    {
      title: 'sopcode_desc',
      dataKey: 'sopcodeDesc',
      width: 220,
      render: (text) => ellipses(text),
    },
    {
      title: 'sku',
      dataKey: 'sku',
      width: 160,
    },
    {
      title: intl.get('含充/去充'),
      dataKey: 'chargeType',
      width: 160,
    },
    {
      title: intl.get('颜色'),
      dataKey: 'color',
      width: 160,
    },
    {
      title: '95',
      dataKey: 'mpnId',
      width: 160,
    },
    {
      title: intl.get('是否量产'),
      dataKey: 'isLc',
      width: 200,
      render: (text) => ellipses(text),
    },
    {
      title: intl.get('M+3内有无需求'),
      dataKey: 'hasDemand',
      width: 200,
      render: (text) => ellipses(text),
    },
    {
      title: intl.get('整机需求'),
      dataKey: 'heyp',
      width: 200,
      render: (text) => ellipses(text),
    },
    {
      title: intl.get('合议S'),
      dataKey: 's',
      width: 200,
      render: (text) => ellipses(text),
    },
    {
      title: intl.get('数据版本'),
      dataKey: 'date',
      width: 150,
      render: (text) => ellipses(text),
    },
  ]
  return [indexCol, ...bomCols]
}

export const prefix = 'material-psi'

export const TAB_CONFIG = [
  { id: 1, title: intl.get('进销存') },
  { id: 2, title: intl.get('合议P展BOM选择') },
]

export const PSI_TABLE_TAB_CONFIG = [
  { id: 'mpn', title: 'MPN' },
  { id: 'cvpn', title: 'CVPN' },
]

export const DEFAULT_DATE_RANGE = [
  dayjs().format('YYYY-MM'),
  dayjs().add(1, 'year').format('YYYY-MM'),
]

export const PSI_FORM_VALUES = {
  businessDate: DEFAULT_DATE_RANGE,
  date: null,
  bizName: intl.get('手机'),
  dept: null,
  teamOwnerEn: null,
  skus: [],
  cvpnCodes: [],
  pnCodes: [],
  mpnIds: [],
  matCatLvl1Codes: [],
  matCatLvl2Codes: [],
  matCatLvl3Codes: [],
  brandNames: [],
}

export const BOM_FORM_VALUES = {
  pros: [],
  sopcodes: [],
  skus: [],
  isLcs: [],
  hyTps: [],
  chargeTypes: [],
  date: '',
}
