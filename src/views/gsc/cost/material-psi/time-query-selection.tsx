import intl from 'react-intl-universal'
import React from 'react'
import { FormItem } from '@hi-ui/form'
import { Col, Row } from '@hi-ui/grid'
import Select from '@hi-ui/select'
import DatePicker from '@hi-ui/date-picker'
import { useMaterialPsi } from '@/hooks/useMaterialPsi'
import { useRequest } from 'ahooks'
import { getPsiConditions } from '@/api/cost/material-psi'
import { formatCondition } from '@/context/MaterialPsiContext'
import { SETTINGS } from './constants'

const TimeQuerySection = ({ conditionList, formRef }) => {
  const { setConditionList } = useMaterialPsi()

  const { runAsync: updateCondition } = useRequest((date) => getPsiConditions(date), {
    manual: true,
  })

  return (
    <div className="row-section">
      <div className="row-title">{intl.get('时间')}</div>
      <Row gutter rowGap={12}>
        <Col span={8}>
          <FormItem field="businessDate" label={intl.get('年月区间')}>
            <DatePicker type="monthrange" format="YYYY-MM" clearable={false} />
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem field="date" label={intl.get('数据版本')}>
            <Select
              data={conditionList.dates}
              {...SETTINGS}
              onChange={(date) => {
                formRef.current.setFieldValue('skus', [])
                formRef.current.setFieldValue('cvpnCodes', [])
                formRef.current.setFieldValue('pnCodes', [])
                formRef.current.setFieldValue('mpnIds', [])
                updateCondition(date).then((res) => {
                  setConditionList(formatCondition(res?.data || {}))
                })
              }}
            />
          </FormItem>
        </Col>
      </Row>
    </div>
  )
}

export default TimeQuerySection
