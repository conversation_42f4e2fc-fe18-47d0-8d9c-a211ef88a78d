.material-psi {
  .white-bg {
    background-color: #fff;
  }

  &-query-form {
    display: flex;
    background-color: #fff;
    border-radius: 4px;
    margin-top: 12px;
    margin-bottom: 12px;
    overflow: hidden;

    .hi-v4-card__body {
      display: flex;
      justify-content: space-between;
      width: 100%;

      .form-section {
        flex: 1;
        width: 0;
        margin-right: 12px;

        .row-section {
          display: flex;
          margin-bottom: 20px;

          .row-title {
            width: 70px;
            color: #303030;
            font-size: 14px;
            line-height: 32px;
            font-weight: 500;
          }

          .hi-v4-grid-row {
            flex: 1;
            width: 0;
          }
        }
      }

      .hi-v4-form-label {
        margin-bottom: 0;
      }
    }
  }

  &-table {
    margin: 12px 0;

    .rc-virtual-list-holder {
      overflow-x: hidden !important;
    }

    .hi-v4-table {
      overflow-x: hidden !important;
    }

    .quarter-header {
      color: #26f;
    }

    .month-header {
      color: #89adff;
    }

    .hi-v4-card__body {
      padding-bottom: 0;
    }

    .table-container {
      display: flex;

      .setting-col {
        background-color: #f5f7fa;
        border-left: 1px solid #dfe2e8;
        border-bottom: 1px solid #dfe2e8;
        display: flex;
        align-items: center;
      }

      .hi-v4-table-header-cell,
      .hi-v4-table-cell {
        padding: 12px;
      }
    }
  }
}
