import intl from 'react-intl-universal'
import React, { memo } from 'react'
import { FormItem } from '@hi-ui/form'
import { Col, Row } from '@hi-ui/grid'
import { CheckSelect } from '@hi-ui/check-select'
import { SETTINGS } from './constants'
import CustomSelect from './custom-select'

const MaterialQuerySection = memo<{ conditionList: AnyType }>(({ conditionList }) => {
  return (
    <div className="row-section">
      <div className="row-title">{intl.get('物料')}</div>
      <Row gutter rowGap={12}>
        <Col span={8}>
          <FormItem field="skus" label="SKU">
            <CustomSelect data={conditionList.skus || []} filedKey="sku" />
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem field="cvpnCodes" label="CVPN">
            <CustomSelect data={conditionList.cvpnCodes || []} filedKey="cvpn" />
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem field="pnCodes" label="PN">
            <CustomSelect data={conditionList.pnCodes || []} filedKey="pn" />
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem field="mpnIds" label="MPN">
            <CustomSelect data={conditionList.mpnIds || []} filedKey="mpn" />
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem field="matCatLvl1Codes" label={intl.get('物料大类')}>
            <CheckSelect data={conditionList.matCatLvl1Codes} {...SETTINGS}></CheckSelect>
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem field="matCatLvl2Codes" label={intl.get('物料中类')}>
            <CheckSelect data={conditionList.matCatLvl2Codes} {...SETTINGS}></CheckSelect>
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem field="matCatLvl3Codes" label={intl.get('物料小类')}>
            <CheckSelect data={conditionList.matCatLvl3Codes} {...SETTINGS}></CheckSelect>
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem field="brandNames" label={intl.get('品牌')}>
            <CheckSelect data={conditionList.brandNames} {...SETTINGS}></CheckSelect>
          </FormItem>
        </Col>
      </Row>
    </div>
  )
})
MaterialQuerySection.displayName = 'MaterialQuerySection'
export default MaterialQuerySection
