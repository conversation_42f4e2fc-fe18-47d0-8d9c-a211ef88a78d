import React, { FC } from 'react'
import { ErrorBoundary } from '@sentry/react'
import Button, { ButtonGroup } from '@hi-ui/button'
import cx from 'classnames'
import { useMaterialPsi } from '@/hooks/useMaterialPsi'
import { TAB_CONFIG, prefix } from './constants'
import CollegialView from './collegial-view'
import PsiView from './psi-view'
import './index.scss'

const PageView: FC = () => {
  const { tabId, setTabId } = useMaterialPsi()

  return (
    <div className={`${prefix}`}>
      <ErrorBoundary>
        <div className={`${prefix}-tab-list`}>
          <ButtonGroup>
            {TAB_CONFIG.map((item) => (
              <Button
                type={tabId === item.id ? 'secondary' : 'default'}
                className={cx({ 'white-bg': tabId !== item.id })}
                key={item.id}
                onClick={(e) => {
                  e.stopPropagation()
                  setTabId(item.id)
                }}
              >
                {item.title}
              </Button>
            ))}
          </ButtonGroup>
        </div>
        {tabId === 1 && <PsiView />}
        {tabId === 2 && <CollegialView />}
      </ErrorBoundary>
    </div>
  )
}
export default PageView
