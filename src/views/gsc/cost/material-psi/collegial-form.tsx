import intl from 'react-intl-universal'
import { Form, FormHelpers, FormItem } from '@hi-ui/form'
import React, { memo, useCallback, useRef } from 'react'
import { BOM_FORM_VALUES, SETTINGS } from './constants'
import Button from '@hi-ui/button'
import { calcConditions } from '../cost-reduction/config'
import { customMessage } from '@/utils/custom-message'
import { DownOutlined } from '@hi-ui/icons'
import { Col, Row } from '@hi-ui/grid'
import { useMaterialPsi } from '@/hooks/useMaterialPsi'
import { useMount } from 'ahooks'
import CheckSelect from '@hi-ui/check-select'
import './collegial-form.scss'
import Select from '@hi-ui/select'

const PsiForm = memo(() => {
  const { expanded, setExpanded, bomConditionList, setBomFilter } = useMaterialPsi()
  const formRef = useRef<FormHelpers>(null)

  const handleSearch = useCallback(() => {
    formRef.current?.validate()?.then((values) => {
      const str = calcConditions(values)
      if (str) {
        customMessage(`${str}所选查询条件数量过多，请减少选择数量后查询`, 'info', 2500)
      } else {
        setBomFilter({ ...values })
      }
    })
  }, [setBomFilter])

  useMount(() => {
    setBomFilter(BOM_FORM_VALUES)
  })

  return (
    <div className="collegial-form" style={{ height: expanded ? 186 : 70 }}>
      <Form initialValues={BOM_FORM_VALUES} innerRef={formRef} labelWidth={106} showColon={false}>
        <Row gutter rowGap={12}>
          <Col span={8}>
            <FormItem field="pros" label={intl.get('项目')}>
              <CheckSelect data={bomConditionList?.pros || []} {...SETTINGS}></CheckSelect>
            </FormItem>
          </Col>
          <Col span={8}>
            <FormItem field="sopcodes" label={intl.get('SOPCODE')}>
              <CheckSelect data={bomConditionList?.sopcodes || []} {...SETTINGS}></CheckSelect>
            </FormItem>
          </Col>
          <Col span={8}>
            <FormItem field="skus" label={intl.get('SKU')}>
              <CheckSelect data={bomConditionList?.skus || []} {...SETTINGS}></CheckSelect>
            </FormItem>
          </Col>
        </Row>

        <Row gutter rowGap={12}>
          <Col span={8}>
            <FormItem field="isLcs" label={intl.get('是否量产')}>
              <CheckSelect data={bomConditionList?.isLcs || []} {...SETTINGS}></CheckSelect>
            </FormItem>
          </Col>
          <Col span={8}>
            <FormItem field="hyTps" label={intl.get('合议P有无需求')}>
              <CheckSelect data={bomConditionList?.hyTps || []} {...SETTINGS}></CheckSelect>
            </FormItem>
          </Col>
          <Col span={8}>
            <FormItem field="chargeTypes" label={intl.get('含充/去充')}>
              <CheckSelect data={bomConditionList?.chargeTypes || []} {...SETTINGS}></CheckSelect>
            </FormItem>
          </Col>
        </Row>
        <Row gutter rowGap={12}>
          <Col span={8}>
            <FormItem field="date" label={intl.get('数据版本')}>
              <Select data={bomConditionList?.dates || []} {...SETTINGS}></Select>
            </FormItem>
          </Col>
        </Row>
      </Form>
      <div className="collegial-form__tools">
        <Button appearance="link" onClick={() => setExpanded((prev) => !prev)}>
          {!expanded ? '展开' : '收起'}
          <DownOutlined
            className="transition-transform ml-2"
            color="#303030"
            style={{
              transform: !expanded ? 'rotateZ(0)' : 'rotateZ(180deg)',
            }}
          />
        </Button>
        <Button type="default" onClick={() => formRef.current?.reset()}>
          {intl.get('重置')}
        </Button>
        <Button type="secondary" onClick={handleSearch}>
          {intl.get('查询')}
        </Button>
      </div>
    </div>
  )
})

PsiForm.displayName = 'PsiForm'
export default PsiForm
