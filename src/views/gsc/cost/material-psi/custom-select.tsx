import React, { CSSProperties, memo } from 'react'
import intl from 'react-intl-universal'
import CheckSelect from '@hi-ui/check-select'
import { Checkbox } from '@hi-ui/checkbox'
import message from '@hi-ui/message'
import Tooltip from '@hi-ui/tooltip'
import { useDebounceFn } from 'ahooks'
import { getFuzzyCondition } from '@/api/cost/material-psi'
import { formatInput } from '@/utils/input-format'

const CustomSelect = memo<{
  data: AnyType[]
  filedKey: string
  disabled?: boolean
  style?: CSSProperties
  [k: string]: AnyType
}>(({ data, filedKey, disabled = false, style, ...props }) => {
  const { run: debouncedFetch } = useDebounceFn(
    (input, resolve) => {
      const finalQueryArr = input ? formatInput(input) : []
      return getFuzzyCondition({
        contents: finalQueryArr.filter(Boolean),
        type: filedKey,
      })
        .then((res) => {
          return resolve(res?.data)
        })
        .catch((err) => {
          message.open({
            title: err.message || intl.get('搜索条件失败，请联系管理员'),
            type: 'error',
          })
          return resolve([])
        })
    },
    { wait: 200 }
  )
  return (
    <CheckSelect
      style={style}
      disabled={disabled}
      data={data}
      height={460}
      showCheckAll
      closeOnEsc
      fieldNames={{ id: 'value', title: 'name' }}
      dataSource={(keyword) => new Promise((resolve) => debouncedFetch(keyword, resolve))}
      searchable
      {...props}
      render={(item) => {
        const { title, checked, disabled } = item || {}
        return (
          <Tooltip title={title} trigger="hover" placement="right">
            <Checkbox checked={checked} disabled={disabled}>
              {title}
            </Checkbox>
          </Tooltip>
        )
      }}
    />
  )
})
CustomSelect.displayName = 'CustomSelect'
export default CustomSelect
