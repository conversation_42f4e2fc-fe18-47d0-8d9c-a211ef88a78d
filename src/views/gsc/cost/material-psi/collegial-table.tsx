import intl from 'react-intl-universal'
import React, { memo, useCallback, useEffect, useMemo } from 'react'
import Table from '@hi-ui/table'
import Button from '@hi-ui/button'
import { getBomCols, prefix } from './constants'
import Card from '@hi-ui/card'
import { useMaterialPsi } from '@/hooks/useMaterialPsi'
import { usePagination, useRequest } from 'ahooks'
import { exportBomList, getBomList } from '@/api/cost/material-psi'
import message from '@hi-ui/message'
import { customMessage } from '@/utils/custom-message'

const PsiTable = memo(() => {
  const { bomFilter } = useMaterialPsi()

  const { run: exportList, loading: exportLoading } = useRequest(exportBomList, {
    manual: true,
    onSuccess: (res) => {
      message.closeAll()
      if (res?.data) {
        customMessage(intl.get('导出成功'), 'success')
        window.open(res.data)
      } else {
        customMessage(
          intl.get('导出数据较多，下载链接生成中，生成完毕会在飞书通知您，请稍后关注飞书消息'),
          'info'
        )
      }
    },
    onError: () => {
      message.closeAll()
      customMessage(intl.get('导出失败'), 'error')
    },
  })

  const {
    run: getList,
    data: listData,
    loading,
    pagination,
  } = usePagination(
    ({ current, pageSize }) => {
      const params = {
        ...bomFilter,
        pageNum: current,
        pageSize,
      }
      return getBomList(params)
    },
    { manual: true, refreshDeps: [bomFilter] }
  )

  const cols = useMemo(
    () =>
      getBomCols({
        title: '序号',
        dataKey: 'index',
        width: 72,
        render: (_text, _rowItem, index) =>
          index + 1 + pagination.pageSize * (pagination.current - 1),
      }),
    [pagination]
  )

  useEffect(() => {
    if (Object.keys(bomFilter).length) {
      getList({ current: 1, pageSize: 20 })
    }
  }, [bomFilter, getList])

  const handleExport = useCallback(() => {
    message.open({
      type: 'info',
      title: intl.get('导出中，数据量较大，请稍等...'),
      autoClose: false,
    })
    const params = {
      ...bomFilter,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    }
    exportList(params)
  }, [exportList, bomFilter, pagination])

  return (
    <>
      <Card
        className={`${prefix}-table `}
        title={intl.get('明细')}
        extra={
          <Button type="primary" onClick={handleExport} disabled={exportLoading}>
            {intl.get('导出')}
          </Button>
        }
      >
        <div className="table-container">
          <Table
            columns={cols}
            data={listData?.list || []}
            loading={loading}
            size="sm"
            maxHeight={418}
            fieldKey="id"
            resizable
            fixedToColumn={{ left: 'pro' }}
            pagination={{
              total: pagination.total,
              current: pagination.current,
              pageSize: pagination.pageSize,
              showTotal: true,
              showJumper: true,
              pageSizeOptions: [5, 10, 20, 50],
              onPageSizeChange: (pageSize) => pagination.changePageSize(pageSize),
              onChange: (current, _, pageSize) => pagination.onChange(current, pageSize),
            }}
          />
        </div>
      </Card>
    </>
  )
})

PsiTable.displayName = 'PsiTable'
export default PsiTable
