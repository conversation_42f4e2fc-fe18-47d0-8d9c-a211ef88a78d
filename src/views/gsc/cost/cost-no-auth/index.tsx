import './index.scss'

import React, { FC, useCallback } from 'react'
import intl from 'react-intl-universal'

import { IMG_PREFIX } from '@/constants/index'
import { Button } from '@hi-ui/button'

const CostNoAuth: FC = () => {
  const LockImg = `${IMG_PREFIX}lock.svg`

  const handleToAccess = useCallback(
    () => window.open(`${location.origin}/apps/uc-admin/permission-apply`, '_blank'),
    []
  )

  return (
    <div className="auth-box">
      <img src={LockImg} alt="lock" />
      <div className="title-box">
        <p className="title">{intl.get('无权限')}</p>
        <div className="desc">
          <span>{intl.get('请点击下方按钮进行权限申请')}</span>
        </div>
        <Button type="primary" onClick={handleToAccess}>
          {intl.get('前往申请')}
        </Button>
      </div>
    </div>
  )
}
export default CostNoAuth
