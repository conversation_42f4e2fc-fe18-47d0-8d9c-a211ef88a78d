import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import FreeCombination from '../index'

// Mock react-intl-universal
vi.mock('react-intl-universal', () => ({
  default: {
    get: (key: string) => key,
    getHTML: (key: string) => key,
  },
}))

// Mock 子组件
vi.mock('../overall-cost', () => ({
  default: () => <div data-testid="overall-cost">OverallCost Component</div>,
}))

vi.mock('../../mix-cost', () => ({
  default: () => <div data-testid="mix-cost">MixCost Component</div>,
}))

describe('FreeCombination Component', () => {
  it('应该正确渲染组件', () => {
    render(<FreeCombination />)

    expect(screen.getByTestId('overall-cost')).toBeInTheDocument()
    expect(screen.getByTestId('mix-cost')).toBeInTheDocument()
  })

  it('应该渲染OverallCost和MixCost组件', () => {
    render(<FreeCombination />)

    const overallCost = screen.getByTestId('overall-cost')
    const mixCost = screen.getByTestId('mix-cost')

    expect(overallCost).toHaveTextContent('OverallCost Component')
    expect(mixCost).toHaveTextContent('MixCost Component')
  })

  it('应该使用Fragment作为根元素', () => {
    const { container } = render(<FreeCombination />)

    // Fragment 不会在DOM中创建额外的元素
    expect(container.firstChild).toBe(screen.getByTestId('overall-cost'))
  })
})
