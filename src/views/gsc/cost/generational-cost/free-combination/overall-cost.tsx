import React, { FC, useEffect, useState } from 'react'
import CustomCard from '../custom-card'
import intl from 'react-intl-universal'
import { IMG_PREFIX } from '@/constants'
import BaselineTable from '../baseline-table'
import { useGenerationalCost } from '@/hooks/useGenerationalCost'
import { CostCompare, TABLE_LINE_HEIGHT } from '../config'
import { useRequest } from 'ahooks'
import { getNodeCompareData } from '@/api/cost/generational-cost'
import { CompareType } from './config'
import { omit } from 'lodash'

const PieChartImg = `${IMG_PREFIX}Pie-chart.svg`

const OverallCost: FC = () => {
  const { filter, setBaseline } = useGenerationalCost()
  const [result, setResult] = useState<AnyType>(null)

  const { run: getList } = useRequest(getNodeCompareData, {
    manual: true,
    onSuccess: (res) => {
      setResult(res?.data)
    },
  })

  useEffect(() => {
    setBaseline('')
  }, [setBaseline])

  useEffect(() => {
    if (filter?.pageType) {
      let newFilter = { ...filter }
      if (newFilter?.pageType === CostCompare.FreeCombination) {
        newFilter = omit(newFilter, [
          'costMatCatLvl1Codes',
          'costMatCatLvl2Codes',
          'costMatCatLvl3Codes',
        ])
      }
      const params = { ...newFilter, type: CompareType.PROJECT_NAME }
      getList(params as AnyType)
    }
  }, [getList, filter])

  return (
    <CustomCard icon={<img src={PieChartImg} alt="PieChart" />} title={intl.get('成本对比')}>
      <BaselineTable result={result} baseline="" maxHeight={TABLE_LINE_HEIGHT * 5} />
    </CustomCard>
  )
}

export default OverallCost
