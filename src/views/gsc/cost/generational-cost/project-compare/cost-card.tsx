import React, { FC, useEffect, useMemo, useState } from 'react'
import CustomCard from '../custom-card'
import intl from 'react-intl-universal'
import { IMG_PREFIX } from '@/constants'
import { useGenerationalCost } from '@/hooks/useGenerationalCost'
import ProjectCard, { COLOR_MAP } from './project-card'
import { CURRENCY_SYMBOL_MAP, SPLIT_SYMBOL } from '../config'
import { useRequest } from 'ahooks'
import { getProjectCostCardData } from '@/api/cost/generational-cost'
import { omit } from 'lodash'
import Loading from '@hi-ui/loading'
import './cost-card.scss'

const PieChartImg = `${IMG_PREFIX}Pie-chart.svg`

let hash = 0
const projectMap = new Map()

const getColorByProjectName = (
  projectName: string
): { textColor: string; backgroundColor: string } => {
  const colorList = Object.values(COLOR_MAP)

  const projectColor = projectMap.get(projectName)
  if (projectColor) {
    return projectColor
  } else {
    const res = colorList[hash % colorList.length]
    projectMap.set(projectName, res)
    hash++
    return res
  }
}

const CostCard: FC = () => {
  const { baseline, setBaseline, filter } = useGenerationalCost()

  const [cardList, setCardList] = useState<AnyType[]>([])
  const { runAsync: getCardList, loading: cardLoading } = useRequest(getProjectCostCardData, {
    manual: true,
  })

  useEffect(() => {
    getCardList(omit(filter, ['pageType', 'costMatCatLvl1Codes']) as AnyType).then((res) => {
      setCardList(res?.data || [])
    })
  }, [filter, getCardList])

  useEffect(() => {
    if (cardList?.length > 0 && cardList?.[0]?.projectName) {
      setBaseline(
        `${cardList[0].projectName}${SPLIT_SYMBOL}${cardList[0].saleSite}${SPLIT_SYMBOL}${cardList[0].config}`
      )
    }
  }, [cardList, setBaseline])

  const baselineCost = useMemo(() => {
    const [projectName, saleSite, config] = baseline?.split(SPLIT_SYMBOL) || []
    return cardList.find(
      (item) =>
        item.projectName === projectName && item.saleSite === saleSite && item.config === config
    )?.cost
  }, [baseline, cardList])

  const cards = useMemo(() => {
    return cardList.map((project) => {
      const colorInfo = getColorByProjectName(project.projectName)
      return (
        <ProjectCard
          key={`${project.projectName}-${project.saleSite}-${project.config}`}
          baseline={baseline}
          baselineCost={baselineCost as number}
          textColor={colorInfo.textColor}
          backgroundColor={colorInfo.backgroundColor}
          currency={
            CURRENCY_SYMBOL_MAP[filter.exchangeRateType as keyof typeof CURRENCY_SYMBOL_MAP]
          }
          onClick={() =>
            setBaseline(
              `${project.projectName}${SPLIT_SYMBOL}${project.saleSite}${SPLIT_SYMBOL}${project.config}`
            )
          }
          config={project.config}
          saleSite={project.saleSite}
          projectName={project.projectName}
          cost={project?.cost}
        />
      )
    })
  }, [baseline, baselineCost, cardList, filter.exchangeRateType, setBaseline])

  return (
    <CustomCard icon={<img src={PieChartImg} alt="PieChart" />} title={intl.get('成本对比')}>
      <Loading visible={cardLoading}>
        <div className="project-list__container">{cards}</div>
      </Loading>
    </CustomCard>
  )
}

export default CostCard
