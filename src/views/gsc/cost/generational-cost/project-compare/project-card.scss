@import '@/common/styles/vars';

.project-card {
  padding: 15px 16px 12px;
  border-radius: 6px;
  border: 1px solid #dfe2e8;
  position: relative;
  cursor: pointer;
  min-height: 110px;

  &--baseline {
    background-color: #eff8ff;
    border-color: #237ffa;

    &::after {
      content: '';
      position: absolute;
      display: block;
      height: 26px;
      width: 28px;
      right: 0;
      bottom: 0;
      background-image: url('#{$img-url}Baseline-radius.svg');
      z-index: 1;
    }
  }

  &__content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;
    width: 100%;
  }

  &__left-content {
    display: inline-block;

    &-info {
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #5f6a7a;
    }

    &-currency {
      font-weight: 600;
      font-size: 14px;
      line-height: 20px;
      color: #1a1f28;
      margin-right: 3px;
    }

    &-cost {
      font-weight: 600;
      font-size: 22px;
      line-height: 36px;
      color: #1f2733;
    }
  }

  &__right-content {
    display: inline-block;
    max-width: 100px;
    padding: 8px 10px;
    font-weight: 600;
    font-size: 18px;
    line-height: 20px;
    border-radius: 10px;
    position: relative;
    top: -6px;
  }

  &__footer {
    &-text {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 100%;
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      color: #9299a6;
    }

    &-diff {
      font-weight: 500;
      font-size: 12px;
      line-height: 20px;
    }
  }

  &__baseline-info {
    display: inline-block;
  }
}
