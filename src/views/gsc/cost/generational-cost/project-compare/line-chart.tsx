import { getEcoBomCostDetailChart } from '@/api/eco/bom-cost'
import { NUM_AFTER_POINT } from '@/constants'
import { CHART_SALE_Y, CHART_PURCHASE_Y, CHART_X, CHART_Y } from '@/views/gsc/eco/bom-cost/config'
import { Chart } from '@antv/g2'
import { useRequest } from 'ahooks'
import React, { FC, useEffect, useRef, useState } from 'react'

const colors = ['#14CA64', '#237FFA', '#FFC540', '#BE8CF1', '#FF7A75']

const LineChart: FC = () => {
  const domRef = useRef<HTMLDivElement>(null)
  const chartRef = useRef<AnyType>(null)
  const [chartData, setChartData] = useState<AnyType[]>([])

  useRequest(() => getEcoBomCostDetailChart({ matNo95: '950118000010', sku: 'BHR8970CN' }), {
    onSuccess: (res) => {
      const chartArr = [] as { date: string; costType: string; cost }[]
      res?.data?.forEach((item) => {
        const { saleBomCost, purcBomCost, date } = item
        chartArr.push({
          date: String(date),
          costType: CHART_SALE_Y,
          cost:
            saleBomCost || saleBomCost === 0 ? Number(saleBomCost?.toFixed(NUM_AFTER_POINT)) : null,
        })
        chartArr.push({
          date: String(date),
          costType: CHART_PURCHASE_Y,
          cost:
            purcBomCost || purcBomCost === 0 ? Number(purcBomCost?.toFixed(NUM_AFTER_POINT)) : null,
        })
      })
      setChartData(chartArr)
    },
  })

  useEffect(() => {
    if (!chartRef.current && domRef.current) {
      chartRef.current = new Chart({
        container: domRef.current,
        autoFit: true,
        height: 216,
      })
      chartRef.current.scale({
        [CHART_X]: {
          range: [0.02, 0.98],
        },
        [CHART_Y]: {
          nice: true,
          min: 0,
        },
      })

      chartRef.current.tooltip({
        showCrosshairs: true, // 展示 Tooltip 辅助线
        shared: true,
      })

      chartRef.current
        .line()
        .position(`${CHART_X}*${CHART_Y}`)
        .color('costType', colors)
        .shape('smooth')
        .label(`${CHART_Y}`)

      chartRef.current
        .point()
        .position(`${CHART_X}*${CHART_Y}`)
        .color('costType', colors)
        .shape('circle')
        .label(`${CHART_Y}`)

      chartRef.current.axis(CHART_Y, {
        grid: {
          line: {
            style: {
              stroke: '#EBEDF0',
              lineWidth: 1,
            },
          },
        },
      })
      chartRef.current.axis(CHART_X, {
        line: {
          style: {
            stroke: '#EBEDF0',
            lineWidth: 2,
          },
        },
      })
      chartRef.current.legend({
        position: 'top',
        marker: { symbol: 'hyphen' },
      })
    }
  }, [])

  useEffect(() => {
    if (chartRef.current) {
      chartRef.current.data([...chartData])
      chartRef.current.render()
    }
  }, [chartData])

  return <div ref={domRef}></div>
}

export default LineChart
