import React, { FC, useMemo } from 'react'
import './project-card.scss'
import { CaretUpFilled } from '@hi-ui/icons'
import { isNumber } from 'lodash'
import { ellipses } from '@/components/ellipsis-tool'
import { EmptyFunctionType } from '@/types/type'
import { PERCENT_DECIMALS, SPLIT_SYMBOL } from '../config'
import { absOptimized } from '@/utils/number'

interface ProjectCardProps {
  baseline: string
  baselineCost: number | null
  config: string
  saleSite: string
  projectName: string
  textColor: string
  backgroundColor: string
  currency: string
  cost: number | null
  onClick: EmptyFunctionType
}

export const COLOR_MAP = {
  0: { textColor: '#FF5959', backgroundColor: '#FFF2EF' },
  1: { textColor: '#14CA64', backgroundColor: '#EEFFF2' },
  2: { textColor: '#FF9900', backgroundColor: '#FEFAE0' },
  3: { textColor: '#A889FE', backgroundColor: '#F5EEFF' },
  4: { textColor: '#4D88FF', backgroundColor: 'rgba(77, 136, 255, 0.1)' },
  5: { textColor: 'rgba(234, 112, 185, 1)', backgroundColor: 'rgba(234, 112, 185, 0.1)' },
  6: { textColor: 'rgba(99, 114, 255, 1)', backgroundColor: 'rgba(99, 114, 255, 0.1)' },
  7: { textColor: 'rgba(17, 208, 197, 1)', backgroundColor: 'rgba(101, 209, 203, 0.1)' },
}

const ProjectCard: FC<ProjectCardProps> = (props) => {
  const {
    baseline,
    baselineCost,
    config,
    saleSite,
    projectName,
    textColor,
    backgroundColor,
    currency,
    cost,
    onClick,
  } = props

  const [baselineProjectName, baselineSaleSite, baselineConfig] =
    baseline?.split(SPLIT_SYMBOL) || []
  const matchBaseline =
    projectName === baselineProjectName &&
    saleSite === baselineSaleSite &&
    config === baselineConfig

  let icon: AnyType | null = null
  let diff = ''
  const color = (cost as number) > (baselineCost as number) ? '#FF5959' : '#14CA64'
  if (isNumber(baselineCost) && isNumber(cost) && cost !== baselineCost && baselineCost !== 0) {
    icon = (
      <CaretUpFilled
        color={color}
        style={{
          transform:
            (cost as number) > (baselineCost as number) ? 'rotate(0deg)' : 'rotate(180deg)',
          marginLeft: 4,
        }}
      />
    )
    diff = `${Math.abs(
      ((((cost as number) - baselineCost) as number) / baselineCost) * 100
    ).toFixed(PERCENT_DECIMALS)}%`
  }

  const info = useMemo(() => {
    return `${config}（${saleSite}）`
  }, [config, saleSite])

  return (
    <div
      className={`project-card ${matchBaseline ? 'project-card--baseline' : ''}`}
      onClick={onClick}
      data-testid="hi-ui-card"
    >
      <div className="project-card__content">
        <div className="project-card__left-content">
          <div className="project-card__left-content-info">{ellipses(info)}</div>
          <div>
            <span className="project-card__left-content-currency">{currency}</span>
            <span className="project-card__left-content-cost">{absOptimized(cost as number)}</span>
          </div>
        </div>
        <div
          className="project-card__right-content"
          style={{
            backgroundColor,
            color: textColor,
          }}
        >
          {ellipses(projectName)}
        </div>
      </div>
      <div className="project-card__footer">
        <div className="project-card__footer-text">
          <div className="project-card__baseline-info">
            {ellipses(
              matchBaseline
                ? '基准项目（其余项目对比口径）'
                : `较 ${baselineProjectName} ${baselineConfig}（${baselineSaleSite}）对比`
            )}
          </div>
          {icon}
          <div className="project-card__footer-diff" style={{ color }}>
            {diff}
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProjectCard
