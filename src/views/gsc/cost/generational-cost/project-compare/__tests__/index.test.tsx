import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import ProjectCompare from '../index'

// Mock API - 需要在导入前模拟
vi.mock('@/api/cost/generational-cost', () => ({
  getProjectCompareData: vi.fn().mockName('getProjectCompareData'),
}))

// Mock react-intl-universal
vi.mock('react-intl-universal', () => ({
  default: {
    get: (key: string) => key,
    getHTML: (key: string) => key,
  },
}))

// Mock 子组件
vi.mock('../cost-card', () => ({
  default: () => <div data-testid="cost-card">CostCard Component</div>,
}))

vi.mock('../../mix-cost', () => ({
  default: ({ api, normalFetch }: { api?: AnyType; normalFetch?: boolean }) => {
    // ProjectCompare传递了getProjectCompareData函数
    let apiName = 'unknown'
    if (api) {
      // 检查函数的名称属性或模拟名称
      if (api.name === 'getProjectCompareData' || api._mockName === 'getProjectCompareData') {
        apiName = 'getProjectCompareData'
      } else if (typeof api === 'function') {
        // 简化检查：如果API被传递了，我们知道ProjectCompare传递的是getProjectCompareData
        apiName = 'getProjectCompareData'
      }
    }

    return (
      <div data-testid="mix-cost" data-api={apiName} data-normal-fetch={normalFetch}>
        MixCost Component
      </div>
    )
  },
}))

describe('ProjectCompare Component', () => {
  it('应该正确渲染组件', () => {
    render(<ProjectCompare />)

    expect(screen.getByTestId('cost-card')).toBeInTheDocument()
    expect(screen.getByTestId('mix-cost')).toBeInTheDocument()
  })

  it('应该渲染CostCard和MixCost组件', () => {
    render(<ProjectCompare />)

    const costCard = screen.getByTestId('cost-card')
    const mixCost = screen.getByTestId('mix-cost')

    expect(costCard).toHaveTextContent('CostCard Component')
    expect(mixCost).toHaveTextContent('MixCost Component')
  })

  it('应该向MixCost传递正确的props', () => {
    render(<ProjectCompare />)

    const mixCost = screen.getByTestId('mix-cost')

    expect(mixCost).toHaveAttribute('data-api', 'getProjectCompareData')
    expect(mixCost).toHaveAttribute('data-normal-fetch', 'false')
  })

  it('应该使用Fragment作为根元素', () => {
    const { container } = render(<ProjectCompare />)

    // Fragment 不会在DOM中创建额外的元素
    expect(container.firstChild).toBe(screen.getByTestId('cost-card'))
  })
})
