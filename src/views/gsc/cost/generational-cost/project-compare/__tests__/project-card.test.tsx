import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import ProjectCard, { COLOR_MAP } from '../project-card'

// Mock react-intl-universal
vi.mock('react-intl-universal', () => ({
  default: {
    get: vi.fn((key) => key),
  },
}))

// Mock @hi-ui/card
vi.mock('@hi-ui/card', () => ({
  default: ({ children, onClick, style, className }: AnyType) => (
    <div data-testid="hi-ui-card" onClick={onClick} style={style} className={className}>
      {children}
    </div>
  ),
}))

describe('ProjectCard Component', () => {
  const defaultProps = {
    projectName: 'Test Project',
    saleSite: 'Test Site',
    config: 'Test Config',
    cost: 1000,
    currency: '¥',
    textColor: '#1890ff',
    backgroundColor: '#f6ffed',
    baseline: 'Test Project@@Test Site@@Test Config',
    baselineCost: 1000,
    onClick: vi.fn(),
  }

  it('应该正确渲染ProjectCard组件', () => {
    render(<ProjectCard {...defaultProps} />)

    expect(screen.getByTestId('hi-ui-card')).toBeInTheDocument()
    expect(screen.getByText('Test Project')).toBeInTheDocument()
    expect(screen.getByText('Test Config（Test Site）')).toBeInTheDocument()
  })

  it('应该正确显示成本信息', () => {
    render(<ProjectCard {...defaultProps} />)

    expect(screen.getByText('¥')).toBeInTheDocument()
    expect(screen.getByText('1,000')).toBeInTheDocument()
  })

  it('应该处理点击事件', () => {
    const mockOnClick = vi.fn()
    render(<ProjectCard {...defaultProps} onClick={mockOnClick} />)

    const card = screen.getByTestId('hi-ui-card')
    fireEvent.click(card)

    expect(mockOnClick).toHaveBeenCalled()
  })

  it('应该应用正确的颜色样式', () => {
    render(<ProjectCard {...defaultProps} />)

    const rightContent = screen.getByText('Test Project').closest('.project-card__right-content')
    expect(rightContent).toHaveStyle({
      backgroundColor: 'rgb(246, 255, 237)',
      color: 'rgb(24, 144, 255)',
    })
  })

  it('应该正确处理基准值比较', () => {
    const props = {
      ...defaultProps,
      cost: 1200,
      baselineCost: 1000,
    }

    render(<ProjectCard {...props} />)

    expect(screen.getByText('¥')).toBeInTheDocument()
    expect(screen.getByText('1,200')).toBeInTheDocument()
  })

  it('应该正确显示基准值指示器', () => {
    const props = {
      ...defaultProps,
      baseline: 'Test Project@@Test Site@@Test Config',
    }

    render(<ProjectCard {...props} />)

    expect(screen.getByTestId('hi-ui-card')).toBeInTheDocument()
  })

  it('应该正确格式化成本显示', () => {
    const props = {
      ...defaultProps,
      cost: 1234567.89,
    }

    render(<ProjectCard {...props} />)

    expect(screen.getByText('¥')).toBeInTheDocument()
    expect(screen.getByText('1,234,567.89')).toBeInTheDocument()
  })

  it('应该正确处理不同货币符号', () => {
    const props = {
      ...defaultProps,
      currency: '$',
      cost: 1000,
    }

    render(<ProjectCard {...props} />)

    expect(screen.getByText('$')).toBeInTheDocument()
    expect(screen.getByText('1,000')).toBeInTheDocument()
  })

  it('应该正确处理零成本', () => {
    const props = {
      ...defaultProps,
      cost: 0,
    }

    render(<ProjectCard {...props} />)

    expect(screen.getByText('¥')).toBeInTheDocument()
    expect(screen.getByText('0')).toBeInTheDocument()
  })

  it('应该正确处理负成本', () => {
    const props = {
      ...defaultProps,
      cost: -1000,
    }

    render(<ProjectCard {...props} />)

    expect(screen.getByText('¥')).toBeInTheDocument()
    expect(screen.getByText('-1,000')).toBeInTheDocument()
  })

  it('应该导出正确的COLOR_MAP', () => {
    expect(COLOR_MAP).toBeDefined()
    expect(typeof COLOR_MAP).toBe('object')

    Object.values(COLOR_MAP).forEach((color) => {
      expect(color).toHaveProperty('textColor')
      expect(color).toHaveProperty('backgroundColor')
    })
  })
})
