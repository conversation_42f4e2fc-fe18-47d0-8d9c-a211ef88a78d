import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import CostCard from '../cost-card'
import { GenerationalCostProvider } from '@/context/GenerationalCost'

// Mock react-intl-universal
vi.mock('react-intl-universal', () => ({
  default: {
    get: vi.fn((key) => key),
  },
}))

// Mock @hi-ui/loading
vi.mock('@hi-ui/loading', () => ({
  default: ({ children, visible }: { children: React.ReactNode; visible: boolean }) => (
    <div data-testid="loading" data-visible={visible}>
      {children}
    </div>
  ),
}))

// Mock constants
vi.mock('@/constants', () => ({
  IMG_PREFIX: '/img/',
}))

// Mock ahooks with a simple mock that can be controlled later
vi.mock('ahooks', () => ({
  useRequest: vi.fn(),
}))

// Mock API
vi.mock('@/api/cost/generational-cost', () => ({
  getProjectCostCardData: vi.fn(),
}))

// Mock hooks
vi.mock('@/hooks/useGenerationalCost', () => ({
  useGenerationalCost: vi.fn(() => ({
    baseline: 'project1',
    setBaseline: vi.fn(),
    filter: {
      pageType: 'projectCompare',
      exchangeRateType: 'CNY',
    },
  })),
}))

// Mock custom components
vi.mock('../../custom-card', () => ({
  default: ({ children, title }: AnyType) => (
    <div data-testid="custom-card" data-title={title}>
      {children}
    </div>
  ),
}))

// Mock ProjectCard
vi.mock('../project-card', () => ({
  default: ({ projectName, cost }: AnyType) => (
    <div data-testid="project-card" data-project-name={projectName} data-cost={cost}>
      Project: {projectName}, Cost: {cost}
    </div>
  ),
  COLOR_MAP: {},
}))

// Mock lodash
vi.mock('lodash', () => ({
  omit: vi.fn((obj) => obj),
}))

// Mock config
vi.mock('../../config', () => ({
  CURRENCY_SYMBOL_MAP: {
    CNY: '¥',
  },
  SPLIT_SYMBOL: '@@',
}))

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <GenerationalCostProvider>{children}</GenerationalCostProvider>
)

describe('CostCard Component', () => {
  beforeEach(async () => {
    vi.clearAllMocks()
    // Get the mocked useRequest and set default behavior
    const { useRequest } = await import('ahooks')
    vi.mocked(useRequest).mockReturnValue({
      runAsync: vi.fn().mockResolvedValue({
        data: [],
      }),
      loading: false,
    } as AnyType)
  })

  it('应该正确渲染CostCard组件', () => {
    render(
      <TestWrapper>
        <CostCard />
      </TestWrapper>
    )

    expect(screen.getByTestId('custom-card')).toBeInTheDocument()
  })

  it('应该显示loading组件', async () => {
    // 为这个测试设置loading为true
    const { useRequest } = await import('ahooks')
    vi.mocked(useRequest).mockReturnValue({
      runAsync: vi.fn().mockResolvedValue({ data: [] }),
      loading: true,
    } as AnyType)

    render(
      <TestWrapper>
        <CostCard />
      </TestWrapper>
    )

    // 检查loading组件是否存在且visible为true
    const loadingElement = screen.getByTestId('loading')
    expect(loadingElement).toBeInTheDocument()
    expect(loadingElement).toHaveAttribute('data-visible', 'true')
  })
})
