import React, { FC, useEffect, useMemo, useRef, useState } from 'react'
import intl from 'react-intl-universal'
import { useGenerationalCost } from '@/hooks/useGenerationalCost'
import { IMG_PREFIX } from '@/constants'
import { useRequest } from 'ahooks'
import { getNodeCompareData } from '@/api/cost/generational-cost'
import CustomTab from '@/components/custom-tab'
import { Loading } from '@hi-ui/loading'
import { CompareType, CostCompare, SPLIT_SYMBOL, TAB_LIST, TABLE_LINE_HEIGHT } from './config'
import CustomCard from './custom-card'
import BaselineTable from './baseline-table'
import { omit } from 'lodash'

const RelationImg = `${IMG_PREFIX}Relation.svg`

interface MixCostProps {
  api?: (data: AnyType) => Promise<AnyType>
  tabList?: AnyType[]
  cardTitle?: string
  normalFetch?: boolean
  isCategory?: boolean
}

const MixCost: FC<MixCostProps> = (props) => {
  const {
    api = getNodeCompareData,
    tabList = TAB_LIST,
    cardTitle,
    normalFetch = true,
    isCategory = false,
  } = props
  const { baseline, setBaseline, filter } = useGenerationalCost()
  const [result, setResult] = useState<AnyType>(null)
  const [currentTab, setCurrentTab] = useState<CompareType>(tabList[0].tabId)
  const tableBaseline = baseline?.includes(SPLIT_SYMBOL)
    ? baseline?.split(SPLIT_SYMBOL)?.[0] || ''
    : baseline

  const resultRef = useRef<AnyType>(null)

  useEffect(() => {
    resultRef.current = result
  }, [result])

  useEffect(() => {
    resultRef.current = null
  }, [filter])

  const tabTitle = useMemo(() => {
    return tabList.find((item) => item.tabId === currentTab)?.tabTitle
  }, [currentTab, tabList])

  const { run: getList, loading } = useRequest(api, {
    manual: true,
    onSuccess: (res) => {
      setResult(res?.data)
    },
  })

  useEffect(() => {
    let paramsForApi: AnyType | undefined

    // 检查是否满足基于 filter.pageType 和 baseline 的处理条件
    const canProcessWithBaseline = filter?.pageType && baseline

    if (canProcessWithBaseline && baseline.includes(SPLIT_SYMBOL) && !normalFetch) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const [_projectName, saleSite, config] = baseline.split(SPLIT_SYMBOL)
      if (config) {
        paramsForApi = {
          ...filter,
          type: currentTab,
          saleSites: [saleSite],
          configs: [config],
        }
      }
      // 如果 config 不存在，paramsForApi 保持 undefined，此分支不会调用 getList
    } else if (
      (canProcessWithBaseline && normalFetch && filter?.pageType !== CostCompare.FreeCombination) ||
      (filter?.pageType === CostCompare.FreeCombination && !baseline)
    ) {
      // 此分支满足以下任一条件时执行:
      // 1. canProcessWithBaseline 为 true，但 baseline 不包含 SPLIT_SYMBOL
      //    (即 canProcessWithBaseline 为 true，且外层 if 的第一个条件为 false)
      // 2. canProcessWithBaseline 为 false，但 normalFetch 为 true
      paramsForApi = { ...filter, type: currentTab }
    }

    if (paramsForApi && !resultRef.current) {
      if (!isCategory) {
        paramsForApi = omit(paramsForApi, [
          'costMatCatLvl1Codes',
          'costMatCatLvl2Codes',
          'costMatCatLvl3Codes',
        ])
      }
      getList(paramsForApi as AnyType)
    }
  }, [baseline, currentTab, filter, getList, isCategory, normalFetch])

  const baselineExtra = useMemo(() => {
    if (baseline?.includes(SPLIT_SYMBOL)) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const [_, saleSite, config] = baseline?.split(SPLIT_SYMBOL) || []
      return `${SPLIT_SYMBOL}${saleSite}${SPLIT_SYMBOL}${config}`
    } else {
      return ''
    }
  }, [baseline])

  return (
    <CustomCard
      icon={<img src={RelationImg} alt="Relation" />}
      title={cardTitle || `${intl.get('成本')}${tabTitle}${intl.get('对比')}`}
      extra={
        <CustomTab
          tabItems={tabList}
          currentTab={currentTab}
          setCurrentTab={(tab) => {
            resultRef.current = null
            setCurrentTab(tab as CompareType)
          }}
          position="right"
        />
      }
    >
      <Loading visible={loading} delay={200}>
        <BaselineTable
          result={result}
          baseline={tableBaseline}
          baselineExtra={baselineExtra}
          setBaseline={setBaseline}
          maxHeight={TABLE_LINE_HEIGHT * 11}
        />
      </Loading>
    </CustomCard>
  )
}

export default MixCost
