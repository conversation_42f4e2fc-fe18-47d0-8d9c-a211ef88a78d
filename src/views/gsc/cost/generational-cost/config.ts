import intl from 'react-intl-universal'

export const GENERATIONAL_COST_VIEW_FUNCTION_CODE = 'generation_cost_view'
export const TABLE_LINE_HEIGHT = 41
export const SPLIT_SYMBOL = '@@'
export const COST_DECIMALS = 2
export const PERCENT_DECIMALS = 2
export const CHART_TEXT_COLOR = '#5F6A7A'
export const CHART_AXIS_COLOR = '#EBEDF0'
export const BAR_COLOR = '#4A9EFF'
export const CHART_HEIGHT = 322

export const getCommonChartConfig = (lineWidth = 0) => ({
  line: {
    style: {
      stroke: CHART_AXIS_COLOR,
      lineWidth,
    },
  },
  label: {
    autoRotate: true,
    autoHide: false,
    style: {
      fill: CHART_TEXT_COLOR,
    },
  },
})

export const COMMON_CHART_TOOLTIP_CONFIG = {
  domStyles: {
    'g2-tooltip-title': {
      color: '#000000E5',
      fontSize: 14,
      fontWeight: 500,
      letterSpacing: '0px',
    },
    'g2-tooltip-name': {
      color: CHART_TEXT_COLOR,
    },
    'g2-tooltip-value': {
      color: '#1F2733',
      fontSize: 12,
      fontWeight: 600,
      letterSpacing: '0px',
    },
  },
}

export enum CostCompare {
  Project = 'projectCompare',
  Node = 'stageCompare',
  Category = 'categoryCompare',
  FreeCombination = 'customCompare',
}

export const COST_COMPARE_OPTIONS = [
  {
    id: CostCompare.Project,
    title: intl.get('项目对比'),
  },
  {
    id: CostCompare.Node,
    title: intl.get('节点对比'),
  },
  {
    id: CostCompare.Category,
    title: intl.get('品类对比'),
  },
  {
    id: CostCompare.FreeCombination,
    title: intl.get('自由组合对比'),
  },
]

export const GENERATIONAL_INIT_VALUES = {
  pageType: '',
  costMatCatLvl1Codes: '',
  costMatCatLvl2Codes: '',
  costMatCatLvl3Codes: '',
  projectNames: [],
  saleSites: [],
  configs: [],
  phaseStages: [],
  exchangeRateType: 'CNY',
  tableType: 'CVPN',
  date: '',
}

export const LAST_GENERATION_FILTER_KEY = 'lastGenerationFilter'

const checkMsgMap = {
  costMatCatLvl1Codes: intl.get('请选择成本大类'),
  projectNames: intl.get('请选择项目'),
  saleSites: intl.get('请选择区域'),
  configs: intl.get('请选择配置'),
  phaseStages: intl.get('请选择项目节点'),
}

export const checkFormValues = (values: Record<string, string | string[]>): string => {
  let checkMsg = ''
  Object.entries(values).forEach(([key, value]) => {
    if (value?.length === 0) {
      checkMsg = checkMsgMap[key]
    }
  })

  return checkMsg
}

export const ALWAYS_ARRAY_KEYS = [
  'costMatCatLvl1Codes',
  'costMatCatLvl2Codes',
  'costMatCatLvl3Codes',
  'projectNames',
  'configs',
  'saleSites',
  'phaseStages',
]

export const toAlwaysArray = (value: string | string[]) => {
  return Array.isArray(value) ? value : value ? [value] : []
}

export const toAlwaysArrayOptions = (value: string | string[]) => {
  return Array.isArray(value)
    ? value.map((item) => ({ name: item, value: item }))
    : value
      ? [{ name: value, value }]
      : []
}

export const formatValues = (values: Record<string, string | string[]>) => {
  const {
    costMatCatLvl1Codes,
    costMatCatLvl2Codes,
    costMatCatLvl3Codes,
    projectNames,
    configs,
    saleSites,
    phaseStages,
    ...rest
  } = values
  return {
    costMatCatLvl1Codes: toAlwaysArray(costMatCatLvl1Codes),
    costMatCatLvl2Codes: toAlwaysArray(costMatCatLvl2Codes),
    costMatCatLvl3Codes: toAlwaysArray(costMatCatLvl3Codes),
    projectNames: toAlwaysArray(projectNames),
    configs: toAlwaysArray(configs),
    saleSites: toAlwaysArray(saleSites),
    phaseStages: toAlwaysArray(phaseStages),
    ...rest,
  }
}

export const DYNAMIC_TAG_MAP = {
  [CostCompare.Project]: {
    projectNames: 'CheckSelect',
    saleSites: 'CheckSelect',
    configs: 'CheckSelect',
    phaseStages: 'Select',
  },
  [CostCompare.Node]: {
    projectNames: 'Select',
    saleSites: 'CheckSelect',
    configs: 'CheckSelect',
    phaseStages: 'CheckSelect',
  },
  [CostCompare.Category]: {
    projectNames: 'CheckSelect',
    saleSites: 'Select',
    configs: 'Select',
    phaseStages: 'Select',
  },
  [CostCompare.FreeCombination]: {
    projectNames: 'CheckSelect',
    saleSites: 'CheckSelect',
    configs: 'CheckSelect',
    phaseStages: 'CheckSelect',
  },
} as Record<CostCompare, Record<string, 'CheckSelect' | 'Select'>>

export const formatDynamicValues = (
  currentValues: Record<string, string | string[]>,
  tagMap: Record<string, 'CheckSelect' | 'Select'>
) => {
  const newValues = {}
  for (const key in tagMap) {
    if (tagMap[key] === 'CheckSelect') {
      newValues[key] = toAlwaysArray(currentValues[key])
    } else if (tagMap[key] === 'Select') {
      /**
       * 两种情况继承值
       * 1. 多选切为单选，数组长度刚好为 1
       * 2. 单选切单选，直接继承
       * */
      newValues[key] =
        Array.isArray(currentValues[key]) &&
        currentValues[key].length === 1 &&
        currentValues[key][0]
          ? currentValues[key][0]
          : !Array.isArray(currentValues[key]) && currentValues[key]
            ? currentValues[key]
            : ''
    }
  }
  return newValues
}

const COLUMN_ICON_WIDTH = 16

const COLUMN_WIDTH_OBJ = {
  costMatCatLvl1Code: 120 + COLUMN_ICON_WIDTH,
  costMatCatLvl2Code: 140 + COLUMN_ICON_WIDTH,
  costMatCatLvl3Code: 160 + COLUMN_ICON_WIDTH,
  projectName: 80 + COLUMN_ICON_WIDTH,
  saleSite: 90 + COLUMN_ICON_WIDTH,
  config: 120 + COLUMN_ICON_WIDTH,
  releaseMonth: 90 + COLUMN_ICON_WIDTH,
}

export const getColumnsByType = (allColumns: AnyType[], types: string[]) =>
  allColumns
    ?.filter((item) => types.includes(item?.type))
    ?.map((column) => {
      const { key, value } = column
      return {
        code: key,
        name: value,
        width: COLUMN_WIDTH_OBJ[key] || 100,
      }
    }) || []

export const CURRENCY_OPTIONS = [
  { name: 'CNY', value: '人民币' },
  { name: 'USD', value: '美元' },
  { name: 'INR', value: '印度卢比' },
  { name: 'IDR', value: '印尼盾' },
]

export const TABLE_TYPE_OPTIONS = [
  { name: 'MPN', value: 'MPN' },
  { name: 'CVPN', value: 'CVPN' },
]

export const CURRENCY_SYMBOL_MAP = {
  CNY: '¥',
  USD: '$',
  INR: '₹',
  IDR: 'Rps',
}

export enum CompareType {
  PROJECT_NAME = 'projectName',
  COST_MAT_CAT_LVL1_CODE = 'costMatCatLvl1Code',
  COST_MAT_CAT_LVL2_CODE = 'costMatCatLvl2Code',
  COST_MAT_CAT_LVL3_CODE = 'costMatCatLvl3Code',
}

export const TAB_LIST = [
  { tabId: CompareType.COST_MAT_CAT_LVL1_CODE, tabTitle: intl.get('大类') },
  { tabId: CompareType.COST_MAT_CAT_LVL2_CODE, tabTitle: intl.get('中类') },
  { tabId: CompareType.COST_MAT_CAT_LVL3_CODE, tabTitle: intl.get('小类') },
]

export const RADIO_ID = 'gsc-generational-cost__radio'
export const TABLE_BASELINE_ID = 'gsc-generational-cost__baseline'
export const SHADOW_BOX_WRAPPER_ID = 'tour-box__wrapper'
