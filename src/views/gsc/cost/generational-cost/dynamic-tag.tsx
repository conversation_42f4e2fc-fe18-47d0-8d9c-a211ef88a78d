import React, { ComponentType } from 'react'
import CheckSelect from '@hi-ui/check-select'
import Select from '@hi-ui/select'

// 添加所有需要的组件映射
const COMPONENT_MAP: Record<string, ComponentType<AnyType>> = {
  CheckSelect,
  Select,
}

interface Props {
  tag: string
  children?: React.ReactNode
  [key: string]: AnyType
}

function DynamicTag({ tag, children, ...rest }: Props): JSX.Element {
  const Component = COMPONENT_MAP[tag] || tag
  const finalProps = { ...rest }
  let showCheckAll = {}

  if (tag === 'CheckSelect') {
    // 如果 value 未定义、为 null，或者不是数组，则将其设置为空数组
    if (!finalProps.value) {
      finalProps.value = []
    } else if (!Array.isArray(finalProps.value) && finalProps.value) {
      finalProps.value = [finalProps.value]
    }
    showCheckAll = { showCheckAll: true }
  } else if (tag === 'Select') {
    if (!finalProps.value) {
      finalProps.value = ''
    } else if (Array.isArray(finalProps.value) && finalProps.value.length === 1) {
      finalProps.value = finalProps.value[0]
    }
  }

  return (
    <Component searchable {...finalProps} {...showCheckAll}>
      {children}
    </Component>
  )
}

DynamicTag.displayName = 'DynamicTag'
export default DynamicTag
