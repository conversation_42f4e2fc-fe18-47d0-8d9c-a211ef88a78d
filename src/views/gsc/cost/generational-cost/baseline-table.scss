@import '@/common/styles/vars';

.baseline-table__target-column {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    display: block;
    height: 26px;
    width: 26px;
    right: -12px;
    bottom: -10px;
    background-image: url('#{$img-url}Baseline.svg');
    z-index: 1;
  }
}

.baseline-table__empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
}

.baseline-table__column-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  width: 100%;
}

.baseline-table__column-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.baseline-table__sort-icons {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 22px;
  justify-content: space-between;
  z-index: 2;
}

.baseline-table__sort-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 11px;
  cursor: pointer;
  position: relative;
  border-radius: 2px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgb(35 127 250 / 10%);
  }

  svg {
    position: absolute;
    width: 14px;
    height: 14px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.baseline-table__dimension-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  width: 100%;
}
