import React, { <PERSON> } from 'react'
import { ErrorBoundary } from '@sentry/react'
import { GenerationalCostProvider } from '@/context/GenerationalCost'
import { useAuth } from '@/hooks/useAuth'
import { Exception403 } from '@/views/exception/403'
import { GENERATIONAL_COST_VIEW_FUNCTION_CODE } from './config'
import View from './view'

const GenerationalCost: FC = () => {
  const { functionCodes } = useAuth()

  const isAuthorized = functionCodes.includes(GENERATIONAL_COST_VIEW_FUNCTION_CODE)

  return (
    <ErrorBoundary>
      <GenerationalCostProvider>
        {isAuthorized ? <View /> : <Exception403 />}
      </GenerationalCostProvider>
    </ErrorBoundary>
  )
}

export default GenerationalCost
