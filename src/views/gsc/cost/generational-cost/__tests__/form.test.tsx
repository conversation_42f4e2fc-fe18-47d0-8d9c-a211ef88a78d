import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import userEvent from '@testing-library/user-event'
import GenerationalForm from '../form'
import { CostCompare } from '../config'

// 基础Mock - 尽可能简化
vi.mock('react-intl-universal', () => ({
  default: {
    get: vi.fn((key: string) => key),
  },
}))

vi.mock('@/hooks/useGenerationalCost', () => ({
  useGenerationalCost: () => ({
    expanded: false,
    setFilter: vi.fn(),
    setExpanded: vi.fn(),
    setSearched: vi.fn(),
    baseline: null,
  }),
}))

vi.mock('@/hooks/useExport', () => ({
  useExport: () => ({
    handleTableExport: vi.fn(),
    loading: false,
  }),
}))

vi.mock('ahooks', () => ({
  useRequest: () => ({
    run: vi.fn(),
    loading: false,
  }),
}))

vi.mock('@/api/cost/generational-cost', () => ({
  getGenerationalCostConditions: vi.fn(),
  exportGenerationalCost: vi.fn(),
}))

vi.mock('@/utils/custom-message', () => ({
  customMessage: vi.fn(),
}))

vi.mock('@/constants', () => ({
  HEADER_TOOLS_PORTAL: 'header-tools-portal',
}))

vi.mock('@/utils/array', () => ({
  uniqueObjectArray: vi.fn((arr) => arr),
}))

vi.mock('lodash', () => ({
  omit: vi.fn((obj) => obj),
}))

vi.mock('@sentry/react', () => ({
  captureException: vi.fn(),
}))

// Mock 子组件
vi.mock('../dynamic-tag', () => ({
  default: ({ children, ...props }: AnyType) => (
    <div data-testid="dynamic-tag" {...props}>
      {children}
    </div>
  ),
}))

vi.mock('./tour', () => ({
  Tour: ({ children }: AnyType) => <div data-testid="tour-component">{children}</div>,
}))

vi.mock('@/components/custom-portal', () => ({
  default: ({ children }: AnyType) => <div data-testid="custom-portal">{children}</div>,
}))

// Mock localStorage
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: vi.fn(() => null),
    setItem: vi.fn(),
    removeItem: vi.fn(),
  },
  writable: true,
})

describe('GenerationalForm 组件测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('基础渲染测试', () => {
    it('应该成功渲染组件', () => {
      render(<GenerationalForm cacheFormValues={null} />)

      // 基础渲染检查
      expect(screen.getByText('成本对比')).toBeInTheDocument()
      expect(screen.getByText('查询')).toBeInTheDocument()
      expect(screen.getByText('重置')).toBeInTheDocument()
    })

    it('应该渲染必要的表单字段', () => {
      render(<GenerationalForm cacheFormValues={null} />)

      // 检查主要表单字段
      expect(screen.getByText('成本对比')).toBeInTheDocument()
      expect(screen.getByText('查询')).toBeInTheDocument()
      expect(screen.getByText('重置')).toBeInTheDocument()
      expect(screen.getByText('项目对比')).toBeInTheDocument()
      expect(screen.getByText('节点对比')).toBeInTheDocument()
    })

    it('应该在传入缓存值时正确渲染', () => {
      const cacheValues = {
        pageType: CostCompare.Project,
        currency: 'CNY',
      }

      render(<GenerationalForm cacheFormValues={cacheValues} />)

      expect(screen.getByText('成本对比')).toBeInTheDocument()
      expect(screen.getByText('查询')).toBeInTheDocument()
    })
  })

  describe('表单交互测试', () => {
    it('应该响应对比维度选择', async () => {
      render(<GenerationalForm cacheFormValues={null} />)

      // 查找对比维度选择器
      const projectRadio = screen.getByText('项目对比')
      expect(projectRadio).toBeInTheDocument()

      // 尝试点击选择
      await userEvent.click(projectRadio)
      await waitFor(() => {
        expect(projectRadio).toBeInTheDocument()
      })
    })

    it('应该响应品类对比选择', async () => {
      render(<GenerationalForm cacheFormValues={null} />)

      // 查找品类对比选择器
      const categoryRadio = screen.getByText('品类对比')
      expect(categoryRadio).toBeInTheDocument()

      // 尝试点击选择
      await userEvent.click(categoryRadio)
      await waitFor(() => {
        expect(categoryRadio).toBeInTheDocument()
      })
    })

    it('应该响应查询按钮点击', async () => {
      render(<GenerationalForm cacheFormValues={null} />)

      const searchButton = screen.getByText('查询')
      expect(searchButton).toBeInTheDocument()

      await userEvent.click(searchButton)

      // 验证按钮仍然存在
      expect(searchButton).toBeInTheDocument()
    })

    it('应该响应重置按钮点击', async () => {
      render(<GenerationalForm cacheFormValues={null} />)

      const resetButton = screen.getByText('重置')
      expect(resetButton).toBeInTheDocument()

      await userEvent.click(resetButton)

      // 验证按钮仍然存在
      expect(resetButton).toBeInTheDocument()
    })
  })

  describe('条件渲染测试', () => {
    it('应该根据pageType显示不同的字段', () => {
      const { rerender } = render(
        <GenerationalForm cacheFormValues={{ pageType: CostCompare.Project }} />
      )

      // 验证基础字段存在
      expect(screen.getByText('成本对比')).toBeInTheDocument()

      // 重新渲染为品类维度
      rerender(<GenerationalForm cacheFormValues={{ pageType: CostCompare.Category }} />)

      // 验证基础字段仍然存在
      expect(screen.getByText('成本对比')).toBeInTheDocument()
    })

    it('应该显示导出按钮当pageType被选择时', () => {
      render(<GenerationalForm cacheFormValues={{ pageType: CostCompare.Project }} />)

      // 检查导出按钮
      const exportButton = screen.getByText('导出')
      expect(exportButton).toBeInTheDocument()
    })
  })

  describe('表单验证测试', () => {
    it('应该验证必填字段', async () => {
      render(<GenerationalForm cacheFormValues={null} />)

      const searchButton = screen.getByText('查询')
      await userEvent.click(searchButton)

      // 验证表单验证逻辑被触发
      expect(searchButton).toBeInTheDocument()
    })
  })

  describe('缓存功能测试', () => {
    it('应该正确处理localStorage缓存', () => {
      const mockGetItem = vi.fn(() =>
        JSON.stringify({
          pageType: CostCompare.Project,
          currency: 'USD',
        })
      )

      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: mockGetItem,
          setItem: vi.fn(),
          removeItem: vi.fn(),
        },
        writable: true,
      })

      render(<GenerationalForm cacheFormValues={null} />)

      // 验证组件渲染
      expect(screen.getByText('成本对比')).toBeInTheDocument()
    })

    it('应该在缓存值变更时更新表单', () => {
      const cacheValues = {
        pageType: CostCompare.Project,
        currency: 'USD',
      }

      const { rerender } = render(<GenerationalForm cacheFormValues={cacheValues} />)

      // 更新缓存值
      rerender(
        <GenerationalForm
          cacheFormValues={{
            pageType: CostCompare.Category,
            currency: 'EUR',
          }}
        />
      )

      // 验证组件仍然正常渲染
      expect(screen.getByText('成本对比')).toBeInTheDocument()
    })
  })

  describe('内部功能测试', () => {
    it('应该正确处理查询逻辑', async () => {
      render(<GenerationalForm cacheFormValues={null} />)

      const searchButton = screen.getByText('查询')
      await userEvent.click(searchButton)

      // 验证按钮点击不会出错
      expect(searchButton).toBeInTheDocument()
    })

    it('应该正确处理重置逻辑', async () => {
      render(<GenerationalForm cacheFormValues={null} />)

      const resetButton = screen.getByText('重置')
      await userEvent.click(resetButton)

      // 验证按钮点击不会出错
      expect(resetButton).toBeInTheDocument()
    })
  })

  describe('导出功能测试', () => {
    it('应该显示导出按钮', () => {
      render(<GenerationalForm cacheFormValues={{ pageType: CostCompare.Project }} />)

      const exportButton = screen.getByText('导出')
      expect(exportButton).toBeInTheDocument()
    })

    it('应该响应导出按钮点击', async () => {
      render(<GenerationalForm cacheFormValues={{ pageType: CostCompare.Project }} />)

      const exportButton = screen.getByText('导出')
      await userEvent.click(exportButton)

      // 验证按钮仍然存在
      expect(exportButton).toBeInTheDocument()
    })
  })

  describe('边界情况测试', () => {
    it('应该处理空的cacheFormValues', () => {
      render(<GenerationalForm cacheFormValues={null} />)

      expect(screen.getByText('成本对比')).toBeInTheDocument()
    })

    it('应该处理undefined的cacheFormValues', () => {
      render(<GenerationalForm cacheFormValues={undefined as AnyType} />)

      expect(screen.getByText('成本对比')).toBeInTheDocument()
    })

    it('应该处理空对象的cacheFormValues', () => {
      render(<GenerationalForm cacheFormValues={{}} />)

      expect(screen.getByText('成本对比')).toBeInTheDocument()
    })
  })

  describe('组件状态测试', () => {
    it('应该正确管理表单状态', () => {
      render(<GenerationalForm cacheFormValues={null} />)

      // 验证基础状态
      expect(screen.getByText('成本对比')).toBeInTheDocument()
      expect(screen.getByText('查询')).toBeInTheDocument()
      expect(screen.getByText('重置')).toBeInTheDocument()
    })

    it('应该在加载状态时显示相应UI', () => {
      render(<GenerationalForm cacheFormValues={null} />)

      // 检查加载状态下的UI
      expect(screen.getByText('成本对比')).toBeInTheDocument()
    })
  })
})
