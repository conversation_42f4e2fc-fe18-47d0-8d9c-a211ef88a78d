import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import CustomCard from '../custom-card'

// 模拟依赖
vi.mock('@hi-ui/tooltip', () => ({
  default: ({ children, title }) => (
    <div data-testid="tooltip-component" data-title={title}>
      {children}
    </div>
  ),
}))

vi.mock('@hi-ui/icons', () => ({
  InfoCircleOutlined: ({ className, size, color }) => (
    <span data-testid="info-circle-icon" className={className} data-size={size} data-color={color}>
      InfoCircleIcon
    </span>
  ),
}))

describe('CustomCard 组件', () => {
  it('应该正确渲染标题', () => {
    render(<CustomCard title="测试标题">内容</CustomCard>)
    expect(screen.getByText('测试标题')).toBeInTheDocument()
  })

  it('应该正确渲染子元素内容', () => {
    render(<CustomCard title="标题">测试内容</CustomCard>)
    expect(screen.getByText('测试内容')).toBeInTheDocument()
  })

  it('应该在提供图标时渲染图标', () => {
    const icon = <span data-testid="test-icon">图标</span>
    render(
      <CustomCard title="标题" icon={icon}>
        内容
      </CustomCard>
    )
    expect(screen.getByTestId('test-icon')).toBeInTheDocument()
  })

  it('应该在不提供图标时不渲染图标容器', () => {
    render(<CustomCard title="标题">内容</CustomCard>)
    const iconContainers = document.querySelectorAll('.custom-card__icon')
    expect(iconContainers.length).toBe(0)
  })

  it('应该在提供额外内容时渲染额外内容', () => {
    const extra = <button data-testid="extra-button">额外按钮</button>
    render(
      <CustomCard title="标题" extra={extra}>
        内容
      </CustomCard>
    )
    expect(screen.getByTestId('extra-button')).toBeInTheDocument()
  })

  it('应该在不提供额外内容时不渲染额外内容容器', () => {
    render(<CustomCard title="标题">内容</CustomCard>)
    const extraContainers = document.querySelectorAll('.custom-card__header-right')
    expect(extraContainers.length).toBe(0)
  })

  it('应该在提供tip时渲染自定义提示', () => {
    const tip = <span data-testid="custom-tip">自定义提示</span>
    render(
      <CustomCard title="标题" tip={tip}>
        内容
      </CustomCard>
    )
    expect(screen.getByTestId('custom-tip')).toBeInTheDocument()
  })

  it('应该在提供tooltipTitle时渲染默认的提示图标和Tooltip', () => {
    render(
      <CustomCard title="标题" tooltipTitle="提示内容">
        内容
      </CustomCard>
    )
    expect(screen.getByTestId('info-circle-icon')).toBeInTheDocument()
    expect(screen.getByTestId('tooltip-component')).toBeInTheDocument()
    expect(screen.getByTestId('tooltip-component').getAttribute('data-title')).toBe('提示内容')
  })

  it('应该同时渲染多个属性（综合测试）', () => {
    const icon = <span data-testid="test-icon">图标</span>
    const extra = <span data-testid="extra-content">额外内容</span>

    render(
      <CustomCard title="综合测试" icon={icon} extra={extra} tooltipTitle="提示文本">
        测试子内容
      </CustomCard>
    )

    expect(screen.getByText('综合测试')).toBeInTheDocument()
    expect(screen.getByTestId('test-icon')).toBeInTheDocument()
    expect(screen.getByTestId('extra-content')).toBeInTheDocument()
    expect(screen.getByTestId('info-circle-icon')).toBeInTheDocument()
    expect(screen.getByTestId('tooltip-component')).toBeInTheDocument()
    expect(screen.getByText('测试子内容')).toBeInTheDocument()
  })

  it('应该在同时提供tip和tooltipTitle时优先使用tip', () => {
    const tip = <span data-testid="custom-tip">自定义提示</span>
    render(
      <CustomCard title="标题" tip={tip} tooltipTitle="提示内容">
        内容
      </CustomCard>
    )

    expect(screen.getByTestId('custom-tip')).toBeInTheDocument()

    // 确保默认的tooltip没有被渲染
    const tooltipComponents = screen.queryAllByTestId('tooltip-component')
    expect(tooltipComponents.length).toBe(0)
  })

  it('应该正确应用CSS类名', () => {
    render(<CustomCard title="标题">内容</CustomCard>)

    expect(document.querySelector('.custom-card__wrapper')).toBeInTheDocument()
    expect(document.querySelector('.custom-card__header')).toBeInTheDocument()
    expect(document.querySelector('.custom-card__header-left')).toBeInTheDocument()
    expect(document.querySelector('.custom-card__title')).toBeInTheDocument()
  })
})
