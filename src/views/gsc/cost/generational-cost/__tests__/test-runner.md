# GenerationalCost 单测运行指南

## 📋 测试覆盖范围

本测试套件为 GenerationalCost 模块提供了全面的单元测试覆盖，包括：

### 🎯 核心组件测试

- **入口组件** (`index.tsx`) - ErrorBoundary、Context Provider
- **视图组件** (`view.tsx`) - 条件渲染、localStorage缓存、useMemo优化
- **Context** (`GenerationalCost.tsx`) - 状态管理、多组件共享、边界情况

### 🔧 工具函数测试

- **配置文件** (`config.ts`) - 常量定义、工具函数、图表配置
- **Hooks** (`useGenerationalCost.ts`) - Context消费、类型安全、性能优化

## 🚀 运行测试

### 运行所有测试

```bash
npm run test
```

### 运行特定测试文件

```bash
# 测试视图组件
npm run test view.test.tsx

# 测试Context
npm run test GenerationalCostContext.test.tsx

# 测试配置
npm run test config.test.ts

# 测试Hooks
npm run test useGenerationalCost.test.ts
```

### 生成覆盖率报告

```bash
npm run test:coverage
```

## 📊 测试覆盖目标

- **行覆盖率**: ≥95%
- **函数覆盖率**: 100%
- **分支覆盖率**: ≥90%
- **语句覆盖率**: ≥95%

## 🧪 测试类别

### 1. 基础功能测试

- 组件正常渲染
- Props传递和处理
- 状态初始化

### 2. 交互测试

- 用户操作响应
- 表单提交处理
- 按钮点击事件

### 3. 状态管理测试

- Context状态更新
- 多组件状态共享
- 状态持久化

### 4. 边界情况测试

- 空值处理
- 错误处理
- 异常情况

### 5. 性能测试

- 组件重渲染优化
- 内存泄漏防护
- Hook性能

### 6. 集成测试

- 组件间协作
- API调用模拟
- 数据流测试

## 🔍 关键测试点

### View组件

- ✅ 不同pageType的条件渲染
- ✅ localStorage缓存处理
- ✅ useMemo依赖优化
- ✅ 错误边界处理

### Context

- ✅ 初始状态设置
- ✅ 状态更新机制
- ✅ 多消费者共享
- ✅ 类型安全性

### Config

- ✅ 常量定义正确性
- ✅ 工具函数逻辑
- ✅ 数据格式化
- ✅ 表单验证

### Hooks

- ✅ Context正确消费
- ✅ 引用稳定性
- ✅ 类型推导
- ✅ 性能优化

## 🛠️ Mock策略

### 外部依赖Mock

- `@sentry/react` - 错误监控
- `react-intl-universal` - 国际化
- `@hi-ui/*` - UI组件库
- `ahooks` - React Hooks库

### API Mock

- `@/api/cost/generational-cost` - 成本API
- 请求状态模拟（loading, success, error）

### 工具函数Mock

- `@/utils/*` - 公共工具函数
- `localStorage` - 本地存储

## 📝 测试最佳实践

### 1. 测试命名

- 使用描述性的测试名称
- 中文描述测试意图
- 分组相关测试用例

### 2. 断言策略

- 验证DOM元素存在性
- 检查函数调用参数
- 确认状态变化

### 3. 数据隔离

- 每个测试独立运行
- beforeEach清理状态
- Mock函数重置

### 4. 异步处理

- 使用waitFor等待异步操作
- 正确处理Promise
- 避免测试竞态条件

## 🐛 常见问题

### 1. Mock不生效

确保Mock声明在测试文件顶部，在import之前

### 2. 异步测试失败

使用async/await和waitFor处理异步操作

### 3. Context Provider缺失

确保测试组件被正确的Provider包装

### 4. TypeScript错误

使用类型断言或any类型处理复杂类型

## 📈 持续改进

- 定期review测试覆盖率
- 更新测试用例适配新功能
- 优化测试性能
- 完善边界情况测试
