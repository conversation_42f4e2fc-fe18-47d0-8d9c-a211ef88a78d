import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import Empty from '../empty'

// 模拟国际化库
vi.mock('react-intl-universal', () => ({
  default: {
    get: vi.fn((key) => key), // 简单返回传入的键名作为翻译文本
  },
}))

// 模拟EmptyState组件
vi.mock('@hi-ui/empty-state', () => {
  const EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL = 'empty-state-image-url'

  return {
    default: ({ size, title, indicator, className }) => (
      <div
        data-testid="empty-state-component"
        data-size={size}
        data-title={title}
        data-indicator={indicator}
        className={className}
      >
        {title}
      </div>
    ),
    EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL,
  }
})

describe('Empty 组件', () => {
  it('应该正确渲染EmptyState组件', () => {
    render(<Empty />)
    const emptyState = screen.getByTestId('empty-state-component')
    expect(emptyState).toBeInTheDocument()
  })

  it('应该使用正确的大小属性', () => {
    render(<Empty />)
    const emptyState = screen.getByTestId('empty-state-component')
    expect(emptyState.getAttribute('data-size')).toBe('xl')
  })

  it('应该使用正确的标题文本', () => {
    render(<Empty />)
    const emptyState = screen.getByTestId('empty-state-component')
    expect(emptyState.getAttribute('data-title')).toBe(
      '暂无数据，请选择成本对比类型，点击 “查询” 获取数据。'
    )
  })

  it('应该使用无数据的彩色指示图', () => {
    render(<Empty />)
    const emptyState = screen.getByTestId('empty-state-component')
    expect(emptyState.getAttribute('data-indicator')).toBe('empty-state-image-url')
  })

  it('应该应用正确的CSS类名', () => {
    render(<Empty />)
    const emptyState = screen.getByTestId('empty-state-component')
    expect(emptyState.className).toBe('generational-cost__empty')
  })

  it('应该显示翻译后的文本内容', () => {
    render(<Empty />)
    expect(
      screen.getByText('暂无数据，请选择成本对比类型，点击 “查询” 获取数据。')
    ).toBeInTheDocument()
  })
})
