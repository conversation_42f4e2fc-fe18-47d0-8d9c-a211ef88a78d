import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import DynamicTag from '../dynamic-tag'

// 模拟依赖
vi.mock('@hi-ui/check-select', () => ({
  default: ({ value, showCheckAll, children }) => (
    <div data-testid="check-select-component" data-value={JSON.stringify(value)}>
      CheckSelect Component
      {showCheckAll && <span data-testid="show-check-all">Show All Checkbox</span>}
      {children}
    </div>
  ),
}))

vi.mock('@hi-ui/select', () => ({
  default: ({ value, children }) => (
    <div data-testid="select-component" data-value={JSON.stringify(value)}>
      Select Component
      {children}
    </div>
  ),
}))

describe('DynamicTag Component', () => {
  it('应该根据tag属性渲染CheckSelect组件', () => {
    render(<DynamicTag tag="CheckSelect" data={[]} />)
    expect(screen.getByTestId('check-select-component')).toBeInTheDocument()
  })

  it('应该根据tag属性渲染Select组件', () => {
    render(<DynamicTag tag="Select" data={[]} />)
    expect(screen.getByTestId('select-component')).toBeInTheDocument()
  })

  describe('CheckSelect模式', () => {
    it('应该显示全选按钮', () => {
      render(<DynamicTag tag="CheckSelect" data={[]} />)
      expect(screen.getByTestId('show-check-all')).toBeInTheDocument()
    })

    it('应该将null或undefined值转换为空数组', () => {
      render(<DynamicTag tag="CheckSelect" data={[]} value={null} />)
      const component = screen.getByTestId('check-select-component')
      expect(component.getAttribute('data-value')).toBe('[]')
    })

    it('应该将字符串值转换为单元素数组', () => {
      render(<DynamicTag tag="CheckSelect" data={[]} value="option1" />)
      const component = screen.getByTestId('check-select-component')
      expect(component.getAttribute('data-value')).toBe('["option1"]')
    })

    it('应该保持数组值不变', () => {
      render(<DynamicTag tag="CheckSelect" data={[]} value={['option1', 'option2']} />)
      const component = screen.getByTestId('check-select-component')
      expect(component.getAttribute('data-value')).toBe('["option1","option2"]')
    })
  })

  describe('Select模式', () => {
    it('应该将null或undefined值转换为空字符串', () => {
      render(<DynamicTag tag="Select" data={[]} value={null} />)
      const component = screen.getByTestId('select-component')
      expect(component.getAttribute('data-value')).toBe('""')
    })

    it('应该将单元素数组转换为字符串', () => {
      render(<DynamicTag tag="Select" data={[]} value={['option1']} />)
      const component = screen.getByTestId('select-component')
      expect(component.getAttribute('data-value')).toBe('"option1"')
    })

    it('应该保持非数组值不变', () => {
      render(<DynamicTag tag="Select" data={[]} value="option1" />)
      const component = screen.getByTestId('select-component')
      expect(component.getAttribute('data-value')).toBe('"option1"')
    })

    it('应该处理多元素数组', () => {
      // 注意：在Select模式下，多元素数组不会被特殊处理
      render(<DynamicTag tag="Select" data={[]} value={['option1', 'option2']} />)
      const component = screen.getByTestId('select-component')
      expect(component.getAttribute('data-value')).toBe('["option1","option2"]')
    })
  })

  it('应该将其他属性传递给底层组件', () => {
    const onOpen = vi.fn()
    render(<DynamicTag tag="Select" data={[]} onOpen={onOpen} value="test" />)

    expect(screen.getByTestId('select-component')).toBeInTheDocument()
    // 注意：由于我们不能真正地测试属性传递（因为我们模拟了组件），
    // 这个测试只是检查组件是否正确渲染
  })
})
