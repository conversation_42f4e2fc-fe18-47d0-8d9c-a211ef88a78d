import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import View from '../view'
import { GenerationalCostContext } from '@/context/GenerationalCost'
import { CostCompare } from '../config'

// Mock dependencies
vi.mock('@sentry/react', () => ({
  captureException: vi.fn(),
}))

vi.mock('react-intl-universal', () => ({
  default: {
    get: vi.fn((key) => key),
  },
}))

// Mock child components
vi.mock('../form', () => ({
  default: ({ cacheFormValues }) => (
    <div data-testid="generational-form" data-cache={JSON.stringify(cacheFormValues)}>
      Form Component
    </div>
  ),
}))

vi.mock('../empty', () => ({
  default: () => <div data-testid="empty-component">Empty Component</div>,
}))

vi.mock('../node-compare', () => ({
  default: () => <div data-testid="node-compare">Node Compare</div>,
}))

vi.mock('../project-compare', () => ({
  default: () => <div data-testid="project-compare">Project Compare</div>,
}))

vi.mock('../category-compare', () => ({
  default: () => <div data-testid="category-compare">Category Compare</div>,
}))

vi.mock('../free-combination', () => ({
  default: () => <div data-testid="free-compare">Free Compare</div>,
}))

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

describe('View Component', () => {
  let mockContextValue

  beforeEach(() => {
    vi.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)

    mockContextValue = {
      filter: {},
      setFilter: vi.fn(),
      expanded: true,
      setExpanded: vi.fn(),
      searched: false,
      setSearched: vi.fn(),
      baseline: '',
      setBaseline: vi.fn(),
    }
  })

  const renderWithContext = (contextValue = mockContextValue) => {
    return render(
      <GenerationalCostContext.Provider value={contextValue}>
        <View />
      </GenerationalCostContext.Provider>
    )
  }

  describe('基础渲染测试', () => {
    it('应该渲染表单组件', () => {
      renderWithContext()
      expect(screen.getByTestId('generational-form')).toBeInTheDocument()
    })

    it('应该在未搜索时渲染空状态组件', () => {
      renderWithContext()
      expect(screen.getByTestId('empty-component')).toBeInTheDocument()
    })

    it('应该在没有pageType时渲染空状态组件', () => {
      const contextValue = {
        ...mockContextValue,
        searched: true,
        filter: {},
      }
      renderWithContext(contextValue)
      expect(screen.getByTestId('empty-component')).toBeInTheDocument()
    })
  })

  describe('不同对比类型渲染测试', () => {
    it('应该渲染项目对比组件', () => {
      const contextValue = {
        ...mockContextValue,
        searched: true,
        filter: { pageType: CostCompare.Project },
      }
      renderWithContext(contextValue)
      expect(screen.getByTestId('project-compare')).toBeInTheDocument()
      expect(screen.queryByTestId('empty-component')).not.toBeInTheDocument()
    })

    it('应该渲染节点对比组件', () => {
      const contextValue = {
        ...mockContextValue,
        searched: true,
        filter: { pageType: CostCompare.Node },
      }
      renderWithContext(contextValue)
      expect(screen.getByTestId('node-compare')).toBeInTheDocument()
      expect(screen.queryByTestId('empty-component')).not.toBeInTheDocument()
    })

    it('应该渲染品类对比组件', () => {
      const contextValue = {
        ...mockContextValue,
        searched: true,
        filter: { pageType: CostCompare.Category },
      }
      renderWithContext(contextValue)
      expect(screen.getByTestId('category-compare')).toBeInTheDocument()
      expect(screen.queryByTestId('empty-component')).not.toBeInTheDocument()
    })

    it('应该渲染自由组合对比组件', () => {
      const contextValue = {
        ...mockContextValue,
        searched: true,
        filter: { pageType: CostCompare.FreeCombination },
      }
      renderWithContext(contextValue)
      expect(screen.getByTestId('free-compare')).toBeInTheDocument()
      expect(screen.queryByTestId('empty-component')).not.toBeInTheDocument()
    })

    it('应该在无效pageType时渲染空状态', () => {
      const contextValue = {
        ...mockContextValue,
        searched: true,
        filter: { pageType: 'invalid-type' },
      }
      renderWithContext(contextValue)
      expect(screen.getByTestId('empty-component')).toBeInTheDocument()
    })
  })

  describe('localStorage缓存处理测试', () => {
    it('应该正确处理有效的localStorage缓存', () => {
      const cacheData = {
        pageType: CostCompare.Project,
        projectNames: ['test-project'],
      }
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(cacheData))

      renderWithContext()

      const form = screen.getByTestId('generational-form')
      expect(form).toHaveAttribute('data-cache', JSON.stringify(cacheData))
    })

    it('应该正确处理空的localStorage缓存', () => {
      mockLocalStorage.getItem.mockReturnValue('{}')

      renderWithContext()

      const form = screen.getByTestId('generational-form')
      expect(form).toHaveAttribute('data-cache', 'null')
    })

    it('应该正确处理无效的localStorage缓存', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid-json')

      // 应该不抛出错误
      expect(() => renderWithContext()).not.toThrow()

      const form = screen.getByTestId('generational-form')
      expect(form).toHaveAttribute('data-cache', 'null')
    })

    it('应该正确处理null的localStorage缓存', () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      renderWithContext()

      const form = screen.getByTestId('generational-form')
      expect(form).toHaveAttribute('data-cache', 'null')
    })
  })

  describe('useMemo依赖测试', () => {
    it('应该在filter变化时重新计算compareContent', () => {
      const { rerender } = renderWithContext()
      expect(screen.getByTestId('empty-component')).toBeInTheDocument()

      // 更新filter
      const newContextValue = {
        ...mockContextValue,
        searched: true,
        filter: { pageType: CostCompare.Project },
      }

      rerender(
        <GenerationalCostContext.Provider value={newContextValue}>
          <View />
        </GenerationalCostContext.Provider>
      )

      expect(screen.getByTestId('project-compare')).toBeInTheDocument()
      expect(screen.queryByTestId('empty-component')).not.toBeInTheDocument()
    })

    it('应该在searched变化时重新计算compareContent', () => {
      const contextValue = {
        ...mockContextValue,
        searched: false,
        filter: { pageType: CostCompare.Project },
      }

      const { rerender } = renderWithContext(contextValue)
      expect(screen.getByTestId('empty-component')).toBeInTheDocument()

      // 更新searched
      const newContextValue = {
        ...contextValue,
        searched: true,
      }

      rerender(
        <GenerationalCostContext.Provider value={newContextValue}>
          <View />
        </GenerationalCostContext.Provider>
      )

      expect(screen.getByTestId('project-compare')).toBeInTheDocument()
      expect(screen.queryByTestId('empty-component')).not.toBeInTheDocument()
    })
  })

  describe('边界情况测试', () => {
    it('应该处理filter为null的情况', () => {
      const contextValue = {
        ...mockContextValue,
        searched: true,
        filter: null,
      }

      expect(() => renderWithContext(contextValue)).not.toThrow()
      expect(screen.getByTestId('empty-component')).toBeInTheDocument()
    })

    it('应该处理filter为undefined的情况', () => {
      const contextValue = {
        ...mockContextValue,
        searched: true,
        filter: undefined,
      }

      expect(() => renderWithContext(contextValue)).not.toThrow()
      expect(screen.getByTestId('empty-component')).toBeInTheDocument()
    })
  })
})
