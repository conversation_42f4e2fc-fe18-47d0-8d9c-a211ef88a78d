import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import MixCost from '../mix-cost'
import { GenerationalCostProvider } from '@/context/GenerationalCost'

// Mock react-intl-universal
vi.mock('react-intl-universal', () => ({
  default: {
    get: (key: string) => key,
    getHTML: (key: string) => key,
  },
}))

// Mock constants
vi.mock('@/constants', () => ({
  IMG_PREFIX: '/img/',
}))

// Mock ahooks
vi.mock('ahooks', () => ({
  useRequest: vi.fn(() => ({
    run: vi.fn(),
    loading: false,
  })),
}))

// Mock API functions
vi.mock('@/api/cost/generational-cost', () => ({
  getNodeCompareData: vi.fn(),
}))

// Mock hooks
vi.mock('@/hooks/useGenerationalCost', () => ({
  useGenerationalCost: vi.fn(() => ({
    baseline: 'project1',
    setBaseline: vi.fn(),
    filter: {
      pageType: 'projectCompare',
    },
  })),
}))

// Mock lodash
vi.mock('lodash', () => ({
  omit: vi.fn((obj) => obj),
}))

// Mock @hi-ui/loading
vi.mock('@hi-ui/loading', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  Loading: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}))

// Mock config
vi.mock('./config', () => ({
  CompareType: {
    PROJECT_NAME: 'projectName',
  },
  SPLIT_SYMBOL: '@@',
  TAB_LIST: [{ tabId: 'projectName', tabTitle: '项目' }],
  TABLE_LINE_HEIGHT: 41,
}))

// Mock child components
vi.mock('./custom-card', () => ({
  default: ({ children, title }: AnyType) => (
    <div data-testid="custom-card" data-title={title}>
      {children}
    </div>
  ),
}))

vi.mock('@/components/custom-tab', () => ({
  default: () => <div data-testid="custom-tab">Custom Tab</div>,
}))

vi.mock('./baseline-table', () => ({
  default: () => <div data-testid="baseline-table">Baseline Table</div>,
}))

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <GenerationalCostProvider>{children}</GenerationalCostProvider>
)

describe('MixCost Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染MixCost组件', async () => {
    render(
      <TestWrapper>
        <MixCost />
      </TestWrapper>
    )

    await waitFor(() => {
      expect(screen.getByTestId('custom-card')).toBeInTheDocument()
    })
  })

  it('应该正确渲染子组件', async () => {
    render(
      <TestWrapper>
        <MixCost />
      </TestWrapper>
    )

    await waitFor(() => {
      expect(screen.getByTestId('custom-tab')).toBeInTheDocument()
      expect(screen.getByTestId('baseline-table')).toBeInTheDocument()
    })
  })

  it('应该正确传递自定义标题', async () => {
    render(
      <TestWrapper>
        <MixCost cardTitle="自定义标题" />
      </TestWrapper>
    )

    await waitFor(() => {
      const card = screen.getByTestId('custom-card')
      expect(card).toHaveAttribute('data-title', '自定义标题')
    })
  })
})
