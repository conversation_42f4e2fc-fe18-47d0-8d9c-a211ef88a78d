import { describe, it, expect, vi } from 'vitest'
import {
  checkFormValues,
  toAlwaysArray,
  toAlwaysArrayOptions,
  formatValues,
  formatDynamicValues,
  DYNAMIC_TAG_MAP,
  CostCompare,
  COST_COMPARE_OPTIONS,
  GENERATIONAL_INIT_VALUES,
  ALWAYS_ARRAY_KEYS,
  TABLE_LINE_HEIGHT,
  SPLIT_SYMBOL,
  COST_DECIMALS,
  PERCENT_DECIMALS,
  getCommonChartConfig,
  COMMON_CHART_TOOLTIP_CONFIG,
  LAST_GENERATION_FILTER_KEY,
} from '../config'

// 模拟react-intl-universal
vi.mock('react-intl-universal', () => ({
  default: {
    get: vi.fn((key) => key),
  },
}))

describe('GenerationalCost Config', () => {
  describe('常量定义测试', () => {
    it('应该定义正确的常量值', () => {
      expect(TABLE_LINE_HEIGHT).toBe(41)
      expect(SPLIT_SYMBOL).toBe('@@')
      expect(COST_DECIMALS).toBe(2)
      expect(PERCENT_DECIMALS).toBe(2)
      expect(LAST_GENERATION_FILTER_KEY).toBe('lastGenerationFilter')
    })

    it('应该定义正确的CostCompare枚举', () => {
      expect(CostCompare.Project).toBe('projectCompare')
      expect(CostCompare.Node).toBe('stageCompare')
      expect(CostCompare.Category).toBe('categoryCompare')
      expect(CostCompare.FreeCombination).toBe('customCompare')
    })

    it('应该定义正确的初始值', () => {
      expect(GENERATIONAL_INIT_VALUES).toEqual({
        pageType: '',
        costMatCatLvl1Codes: '',
        costMatCatLvl2Codes: '',
        costMatCatLvl3Codes: '',
        projectNames: [],
        saleSites: [],
        configs: [],
        phaseStages: [],
        exchangeRateType: 'CNY',
        tableType: 'CVPN',
        date: '',
      })
    })

    it('应该定义正确的数组字段键', () => {
      expect(ALWAYS_ARRAY_KEYS).toEqual([
        'costMatCatLvl1Codes',
        'costMatCatLvl2Codes',
        'costMatCatLvl3Codes',
        'projectNames',
        'configs',
        'saleSites',
        'phaseStages',
      ])
    })
  })

  describe('COST_COMPARE_OPTIONS测试', () => {
    it('应该包含所有对比选项', () => {
      expect(COST_COMPARE_OPTIONS).toHaveLength(4)

      const optionIds = COST_COMPARE_OPTIONS.map((option) => option.id)
      expect(optionIds).toContain(CostCompare.Project)
      expect(optionIds).toContain(CostCompare.Node)
      expect(optionIds).toContain(CostCompare.Category)
      expect(optionIds).toContain(CostCompare.FreeCombination)
    })

    it('应该有正确的标题', () => {
      COST_COMPARE_OPTIONS.forEach((option) => {
        expect(option.title).toBeTruthy()
        expect(typeof option.title).toBe('string')
      })
    })
  })

  describe('checkFormValues函数测试', () => {
    it('应该在所有字段都有值时返回空字符串', () => {
      const values = {
        costMatCatLvl1Codes: ['cat1'],
        projectNames: ['project1'],
        saleSites: ['site1'],
        configs: ['config1'],
        phaseStages: ['stage1'],
      }

      expect(checkFormValues(values)).toBe('')
    })

    it('应该在costMatCatLvl1Codes为空时返回错误信息', () => {
      const values = {
        costMatCatLvl1Codes: [],
        projectNames: ['project1'],
        saleSites: ['site1'],
        configs: ['config1'],
        phaseStages: ['stage1'],
      }

      expect(checkFormValues(values)).toBe('请选择成本大类')
    })

    it('应该在projectNames为空时返回错误信息', () => {
      const values = {
        costMatCatLvl1Codes: ['cat1'],
        projectNames: [],
        saleSites: ['site1'],
        configs: ['config1'],
        phaseStages: ['stage1'],
      }

      expect(checkFormValues(values)).toBe('请选择项目')
    })
  })

  describe('toAlwaysArray函数测试', () => {
    it('应该将字符串转换为数组', () => {
      expect(toAlwaysArray('test')).toEqual(['test'])
    })

    it('应该保持数组不变', () => {
      expect(toAlwaysArray(['test1', 'test2'])).toEqual(['test1', 'test2'])
    })

    it('应该将空字符串转换为空数组', () => {
      expect(toAlwaysArray('')).toEqual([])
    })
  })

  describe('toAlwaysArrayOptions函数测试', () => {
    it('应该将字符串转换为选项数组', () => {
      expect(toAlwaysArrayOptions('test')).toEqual([{ name: 'test', value: 'test' }])
    })

    it('应该将字符串数组转换为选项数组', () => {
      expect(toAlwaysArrayOptions(['test1', 'test2'])).toEqual([
        { name: 'test1', value: 'test1' },
        { name: 'test2', value: 'test2' },
      ])
    })

    it('应该将空字符串转换为空数组', () => {
      expect(toAlwaysArrayOptions('')).toEqual([])
    })
  })

  describe('formatValues函数测试', () => {
    it('应该将指定字段转换为数组', () => {
      const input = {
        costMatCatLvl1Codes: 'cat1',
        costMatCatLvl2Codes: ['cat2'],
        projectNames: 'project1',
        saleSites: [],
        configs: 'config1',
        phaseStages: ['stage1'],
        pageType: 'project',
        date: '2023-12-01',
      }

      const result = formatValues(input)

      expect(result).toEqual({
        costMatCatLvl1Codes: ['cat1'],
        costMatCatLvl2Codes: ['cat2'],
        costMatCatLvl3Codes: [],
        projectNames: ['project1'],
        saleSites: [],
        configs: ['config1'],
        phaseStages: ['stage1'],
        pageType: 'project',
        date: '2023-12-01',
      })
    })
  })

  describe('DYNAMIC_TAG_MAP测试', () => {
    it('应该为每个对比类型定义标签映射', () => {
      expect(DYNAMIC_TAG_MAP[CostCompare.Project]).toBeDefined()
      expect(DYNAMIC_TAG_MAP[CostCompare.Node]).toBeDefined()
      expect(DYNAMIC_TAG_MAP[CostCompare.Category]).toBeDefined()
      expect(DYNAMIC_TAG_MAP[CostCompare.FreeCombination]).toBeDefined()
    })

    it('应该为项目对比定义正确的标签', () => {
      const projectMap = DYNAMIC_TAG_MAP[CostCompare.Project]
      expect(projectMap.projectNames).toBe('CheckSelect')
      expect(projectMap.saleSites).toBe('CheckSelect')
      expect(projectMap.configs).toBe('CheckSelect')
      expect(projectMap.phaseStages).toBe('Select')
    })
  })

  describe('formatDynamicValues函数测试', () => {
    it('应该根据标签映射格式化值', () => {
      const currentValues = {
        projectNames: ['project1', 'project2'],
        saleSites: 'site1',
        configs: ['config1'],
        phaseStages: ['stage1'], // 修改为只有一个元素的数组
      }

      const tagMap = {
        projectNames: 'CheckSelect' as const,
        saleSites: 'Select' as const,
        configs: 'CheckSelect' as const,
        phaseStages: 'Select' as const,
      }

      const result = formatDynamicValues(currentValues, tagMap)

      expect(result).toHaveProperty('projectNames', ['project1', 'project2'])
      expect(result).toHaveProperty('saleSites', 'site1')
      expect(result).toHaveProperty('configs', ['config1'])
      expect(result).toHaveProperty('phaseStages', 'stage1')
    })

    it('应该处理多元素数组转换为Select类型时返回空字符串', () => {
      const currentValues = {
        phaseStages: ['stage1', 'stage2'], // 多个元素的数组
      }

      const tagMap = {
        phaseStages: 'Select' as const,
      }

      const result = formatDynamicValues(currentValues, tagMap)

      expect(result).toHaveProperty('phaseStages', '') // 应该返回空字符串
    })
  })

  describe('图表配置测试', () => {
    it('应该返回正确的通用图表配置', () => {
      const config = getCommonChartConfig()

      expect(config.line.style.stroke).toBe('#EBEDF0')
      expect(config.line.style.lineWidth).toBe(0)
      expect(config.label.autoRotate).toBe(true)
      expect(config.label.autoHide).toBe(false)
      expect(config.label.style.fill).toBe('#5F6A7A')
    })

    it('应该支持自定义线宽', () => {
      const config = getCommonChartConfig(2)

      expect(config.line.style.lineWidth).toBe(2)
    })

    it('应该定义正确的Tooltip配置', () => {
      expect(COMMON_CHART_TOOLTIP_CONFIG.domStyles).toBeDefined()
      expect(COMMON_CHART_TOOLTIP_CONFIG.domStyles['g2-tooltip-title']).toBeDefined()
      expect(COMMON_CHART_TOOLTIP_CONFIG.domStyles['g2-tooltip-name']).toBeDefined()
      expect(COMMON_CHART_TOOLTIP_CONFIG.domStyles['g2-tooltip-value']).toBeDefined()
    })
  })
})
