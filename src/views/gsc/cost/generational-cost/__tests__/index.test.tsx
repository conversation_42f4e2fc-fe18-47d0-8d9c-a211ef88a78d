import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import GenerationalCost from '../index'

// 模拟子组件
vi.mock('../view', () => ({
  default: () => <div data-testid="view-component">View Component</div>,
}))

// 模拟ErrorBoundary
vi.mock('@sentry/react', () => ({
  ErrorBoundary: ({ children }) => <div data-testid="error-boundary">{children}</div>,
}))

// 模拟Context Provider
vi.mock('@/context/GenerationalCost', () => ({
  GenerationalCostProvider: ({ children }) => (
    <div data-testid="generational-cost-provider">{children}</div>
  ),
}))

// 模拟Exception403组件
vi.mock('@/views/exception/403', () => ({
  Exception403: () => <div data-testid="exception-403">403 Exception</div>,
}))

// 模拟useAuth钩子
vi.mock('@/hooks/useAuth', () => ({
  useAuth: vi.fn(() => ({
    functionCodes: ['generation_cost_view'], // 包含所需权限
  })),
}))

// 模拟权限常量
vi.mock('../config', () => ({
  GENERATIONAL_COST_VIEW_FUNCTION_CODE: 'generation_cost_view',
}))

describe('GenerationalCost Component', () => {
  it('应该正确渲染ErrorBoundary组件', () => {
    render(<GenerationalCost />)
    expect(screen.getByTestId('error-boundary')).toBeInTheDocument()
  })

  it('应该正确渲染GenerationalCostProvider组件', () => {
    render(<GenerationalCost />)
    expect(screen.getByTestId('generational-cost-provider')).toBeInTheDocument()
  })

  it('应该在Provider内部渲染View组件', () => {
    render(<GenerationalCost />)
    const provider = screen.getByTestId('generational-cost-provider')
    expect(provider).toContainElement(screen.getByTestId('view-component'))
  })
})
