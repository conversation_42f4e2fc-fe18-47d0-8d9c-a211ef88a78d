# GenerationalCost 单元测试套件

## 📋 概述

本目录包含了 GenerationalCost 模块的完整单元测试套件，旨在确保代码质量和功能稳定性。

## 🗂️ 测试文件结构

```
__tests__/
├── view.test.tsx                    # 视图组件测试
├── config.test.ts                  # 配置文件测试
├── GenerationalCostContext.test.tsx # Context测试
├── test-runner.md                  # 测试运行指南
└── README.md                       # 本文档
```

```
../
├── __tests__/useGenerationalCost.test.ts # Hooks测试
```

## 🎯 测试覆盖目标

### 核心组件

- ✅ **View组件** - 条件渲染、缓存处理、性能优化
- ✅ **Context** - 状态管理、共享机制、类型安全
- ✅ **Config** - 常量定义、工具函数、数据处理
- ✅ **Hooks** - Context消费、引用稳定、性能测试

### 功能覆盖

- ✅ **基础渲染** - 组件正常显示和交互
- ✅ **状态管理** - Context状态更新和共享
- ✅ **数据处理** - 表单验证、格式化、缓存
- ✅ **错误处理** - 边界情况、异常捕获
- ✅ **性能优化** - 重渲染控制、内存管理

## 🧪 测试策略

### 1. 组件测试

- **渲染测试**: 验证组件正常渲染
- **交互测试**: 模拟用户操作和事件
- **条件渲染**: 测试不同状态下的显示逻辑
- **Props测试**: 验证属性传递和处理

### 2. 状态管理测试

- **初始状态**: 验证Context初始值
- **状态更新**: 测试setter函数功能
- **状态共享**: 验证多组件间状态同步
- **持久化**: 测试localStorage集成

### 3. 工具函数测试

- **纯函数**: 测试输入输出关系
- **边界值**: 验证极端情况处理
- **类型转换**: 测试数据格式化逻辑
- **配置验证**: 确保常量定义正确

### 4. 性能测试

- **重渲染控制**: 验证React.memo和useMemo
- **内存泄漏**: 测试组件卸载清理
- **引用稳定**: 确保函数引用不变
- **异步处理**: 测试Promise和异步操作

## 🛠️ Mock设计

### 外部依赖Mock

```typescript
// UI组件库
vi.mock('@hi-ui/button', () => ({
  default: ({ children, onClick, type, loading }) => (
    <button data-testid={`button-${type}`} onClick={onClick} disabled={loading}>
      {loading ? 'Loading...' : children}
    </button>
  ),
}))

// 国际化
vi.mock('react-intl-universal', () => ({
  default: { get: vi.fn((key) => key) },
}))

// 错误监控
vi.mock('@sentry/react', () => ({
  ErrorBoundary: ({ children }) => <div data-testid="error-boundary">{children}</div>,
  captureException: vi.fn(),
}))
```

### API Mock

```typescript
// 成本API
vi.mock('@/api/cost/generational-cost', () => ({
  getGenerationalCostConditions: vi.fn(),
  getProjectCompareData: vi.fn(),
  exportGenerationalCost: vi.fn(),
}))

// ahooks
vi.mock('ahooks', () => ({
  useRequest: vi.fn(() => ({
    run: vi.fn(),
    loading: false,
    data: null,
    error: null,
  })),
}))
```

### 浏览器API Mock

```typescript
// localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage })

// DOM API
global.IntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
}))
```

## 📊 测试覆盖率

### 目标指标

- **行覆盖率**: ≥95%
- **函数覆盖率**: 100%
- **分支覆盖率**: ≥90%
- **语句覆盖率**: ≥95%

### 关键测试点

1. **所有组件渲染路径**
2. **所有用户交互场景**
3. **所有状态变更逻辑**
4. **所有错误处理分支**
5. **所有工具函数逻辑**

## 🚀 运行测试

### 快速开始

```bash
# 安装依赖
npm install

# 运行所有测试
npm run test

# 运行特定测试
npm run test view.test.tsx

# 生成覆盖率报告
npm run test:coverage
```

### CI/CD集成

```yaml
# .github/workflows/test.yml
- name: Run Tests
  run: npm run test:coverage

- name: Upload Coverage
  uses: codecov/codecov-action@v1
```

## 🔍 质量保证

### 代码审查检查点

- [ ] 测试覆盖率达标
- [ ] 关键路径有测试
- [ ] 边界情况已覆盖
- [ ] Mock策略合理
- [ ] 测试性能良好

### 持续改进

- 定期review测试用例
- 更新Mock适配新版本
- 优化测试执行速度
- 完善错误场景覆盖

## 📝 贡献指南

### 添加新测试

1. 确定测试范围和目标
2. 选择合适的测试类型
3. 编写清晰的测试用例
4. 确保Mock配置正确
5. 验证测试覆盖率

### 测试命名规范

- 使用描述性名称
- 中文描述测试意图
- 分组相关测试用例
- 遵循AAA模式（Arrange-Act-Assert）

### 最佳实践

- 保持测试独立性
- 避免测试间依赖
- 使用合适的断言
- 处理异步操作
- 清理测试状态

## 📚 参考资料

- [Vitest文档](https://vitest.dev/)
- [Testing Library文档](https://testing-library.com/)
- [React测试最佳实践](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
- [Mock策略指南](https://jestjs.io/docs/manual-mocks)
