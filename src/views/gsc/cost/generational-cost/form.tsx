import React, { memo, ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import intl from 'react-intl-universal'
import { useRequest } from 'ahooks'
import { ellipses } from '@/components/ellipsis-tool'
import Button from '@hi-ui/button'
import { CheckSelectMergedItem } from '@hi-ui/check-select'
import { Form, FormHelpers, FormItem } from '@hi-ui/form'
import { Col, Row } from '@hi-ui/grid'
import { useGenerationalCost } from '@/hooks/useGenerationalCost'
import { DownloadOutlined, DownOutlined } from '@hi-ui/icons'
import { genHeight } from '@/utils/form-utils'
import { RadioGroup } from '@hi-ui/radio'
import {
  ALWAYS_ARRAY_KEYS,
  checkFormValues,
  CompareType,
  COST_COMPARE_OPTIONS,
  CostCompare,
  CURRENCY_OPTIONS,
  DYNAMIC_TAG_MAP,
  formatDynamicValues,
  formatValues,
  GENERATIONAL_INIT_VALUES,
  LAST_GENERATION_FILTER_KEY,
  RADIO_ID,
  SPLIT_SYMBOL,
  TABLE_TYPE_OPTIONS,
  toAlwaysArrayOptions,
} from './config'
import Select, { SelectMergedItem } from '@hi-ui/select'
import DynamicTag from './dynamic-tag'
import { customMessage } from '@/utils/custom-message'
import {
  exportGenerationalCost,
  getGenerationalCostConditions,
  IConditionProps,
} from '@/api/cost/generational-cost'
import { uniqueObjectArray } from '@/utils/array'
import { omit } from 'lodash'
import * as Sentry from '@sentry/react'
import CustomPortal from '@/components/custom-portal'
import { HEADER_TOOLS_PORTAL } from '@/constants'
import { useExport } from '@/hooks/useExport'
import { Tour } from './tour'

import './form.scss'

interface GenerationalFormProps {
  cacheFormValues: Record<string, string | string[]> | null
}

const GenerationalForm = memo(({ cacheFormValues }: GenerationalFormProps) => {
  const { expanded, setFilter, setExpanded, setSearched, baseline } = useGenerationalCost()
  const formRef = useRef<FormHelpers>(null)
  const [conditions, setConditions] = useState<CheckSelectMergedItem[] | SelectMergedItem[]>()
  const [formValues, setFormValues] = useState<AnyType>(cacheFormValues || GENERATIONAL_INIT_VALUES)
  const [tourVisible, setTourVisible] = useState<boolean>(true)
  const cacheInit = useRef(false)

  useEffect(() => {
    if (cacheFormValues && !cacheInit.current) {
      setFilter(formatValues(cacheFormValues))
      setSearched(true)
      setExpanded(false)
      // 确保有缓存时只初始化一次
      cacheInit.current = true
    }
  }, [cacheFormValues, setExpanded, setFilter, setSearched])

  useEffect(() => {
    const currentValues = formRef.current?.getFieldsValue()
    const tagMap = DYNAMIC_TAG_MAP[formValues.pageType as CostCompare]
    const newValues = formatDynamicValues(currentValues, tagMap)
    setFormValues({ ...currentValues, ...newValues })
    formRef.current?.setFieldsValue({ ...currentValues, ...newValues })
  }, [formValues.pageType])

  const { run: getConditions, loading } = useRequest(getGenerationalCostConditions, {
    manual: true,
    onSuccess(res) {
      setConditions(res?.data || [])
    },
  })

  const onOpen = useCallback(
    (field: string) => {
      getConditions({
        ...omit(formatValues(formValues), 'pageType', field),
        searchName: field,
        [field]: ALWAYS_ARRAY_KEYS.includes(field) || Array.isArray(formValues?.[field]) ? [] : '',
      } as IConditionProps)
    },
    [formValues, getConditions]
  )

  const commonSettings = useMemo(() => {
    return {
      loading,
      fieldNames: { id: 'name', title: 'value' },
    }
  }, [loading])

  const items = useMemo(() => {
    const isCategoryDimension = formValues?.pageType === CostCompare.Category
    const values = formRef.current?.getFieldsValue()
    const {
      costMatCatLvl1Codes,
      costMatCatLvl2Codes,
      costMatCatLvl3Codes,
      projectNames,
      configs,
      saleSites,
      phaseStages,
      date,
    } = values || {}
    const categoryItems = isCategoryDimension
      ? [
          <FormItem
            field="costMatCatLvl1Codes"
            label={ellipses(intl.get('成本大类'), true)}
            key="costMatCatLvl1Codes"
          >
            <Select
              data={uniqueObjectArray([
                ...((conditions as SelectMergedItem[]) || []),
                ...toAlwaysArrayOptions(costMatCatLvl1Codes),
              ])}
              searchable
              onOpen={() => onOpen('costMatCatLvl1Codes')}
              {...commonSettings}
            ></Select>
          </FormItem>,
          <FormItem
            field="costMatCatLvl2Codes"
            label={ellipses(intl.get('成本中类'))}
            key="costMatCatLvl2Codes"
          >
            <Select
              data={uniqueObjectArray([
                ...((conditions as SelectMergedItem[]) || []),
                ...toAlwaysArrayOptions(costMatCatLvl2Codes),
              ])}
              searchable
              onOpen={() => onOpen('costMatCatLvl2Codes')}
              {...commonSettings}
            ></Select>
          </FormItem>,
          <FormItem
            field="costMatCatLvl3Codes"
            label={ellipses(intl.get('成本小类'))}
            key="costMatCatLvl3Codes"
          >
            <Select
              data={uniqueObjectArray([
                ...((conditions as SelectMergedItem[]) || []),
                ...toAlwaysArrayOptions(costMatCatLvl3Codes),
              ])}
              searchable
              onOpen={() => onOpen('costMatCatLvl3Codes')}
              {...commonSettings}
            ></Select>
          </FormItem>,
        ]
      : []

    return [
      ...categoryItems,
      <FormItem field="projectNames" label={ellipses(intl.get('项目'), true)} key="projectNames">
        <DynamicTag
          tag={DYNAMIC_TAG_MAP[formValues.pageType as CostCompare]?.projectNames || 'CheckSelect'}
          data={uniqueObjectArray([
            ...((conditions as SelectMergedItem[]) || []),
            ...toAlwaysArrayOptions(projectNames),
          ])}
          onOpen={() => onOpen('projectNames')}
          {...commonSettings}
        ></DynamicTag>
      </FormItem>,
      <FormItem field="saleSites" label={ellipses(intl.get('区域'), true)} key="saleSites">
        <DynamicTag
          tag={DYNAMIC_TAG_MAP[formValues.pageType as CostCompare]?.saleSites || 'CheckSelect'}
          data={uniqueObjectArray([
            ...((conditions as SelectMergedItem[]) || []),
            ...toAlwaysArrayOptions(saleSites),
          ])}
          onOpen={() => onOpen('saleSites')}
          {...commonSettings}
        ></DynamicTag>
      </FormItem>,
      <FormItem field="configs" label={ellipses(intl.get('配置'), true)} key="configs">
        <DynamicTag
          tag={DYNAMIC_TAG_MAP[formValues.pageType as CostCompare]?.configs || 'CheckSelect'}
          data={uniqueObjectArray([
            ...((conditions as SelectMergedItem[]) || []),
            ...toAlwaysArrayOptions(configs),
          ])}
          onOpen={() => onOpen('configs')}
          {...commonSettings}
        ></DynamicTag>
      </FormItem>,
      <FormItem field="phaseStages" label={ellipses(intl.get('项目节点'), true)} key="phaseStages">
        <DynamicTag
          tag={DYNAMIC_TAG_MAP[formValues.pageType as CostCompare]?.phaseStages || 'CheckSelect'}
          data={uniqueObjectArray([
            ...((conditions as SelectMergedItem[]) || []),
            ...toAlwaysArrayOptions(phaseStages),
          ])}
          onOpen={() => onOpen('phaseStages')}
          {...commonSettings}
        ></DynamicTag>
      </FormItem>,
      <FormItem field="exchangeRateType" label={ellipses(intl.get('币种'))} key="exchangeRateType">
        <Select
          data={uniqueObjectArray([
            ...((conditions as SelectMergedItem[]) || []),
            ...CURRENCY_OPTIONS,
          ])}
          searchable
          clearable={false}
          onOpen={() => onOpen('exchangeRateType')}
          {...commonSettings}
        ></Select>
      </FormItem>,
      <FormItem field="tableType" label={ellipses(intl.get('数据维度'), true)} key="tableType">
        <Select
          data={uniqueObjectArray([
            // ...((conditions as SelectMergedItem[]) || []),
            ...TABLE_TYPE_OPTIONS,
          ])}
          searchable
          clearable={false}
          // onOpen={() => onOpen('tableType')}
          {...commonSettings}
        ></Select>
      </FormItem>,
      <FormItem field="date" label={ellipses(intl.get('数据版本'))} key="date">
        <Select
          data={uniqueObjectArray([
            ...((conditions as SelectMergedItem[]) || []),
            ...toAlwaysArrayOptions(date),
          ])}
          searchable
          onOpen={() => onOpen('date')}
          {...commonSettings}
        ></Select>
      </FormItem>,
    ]
  }, [commonSettings, conditions, formValues.pageType, onOpen])

  const lines = useMemo(() => {
    return Math.ceil(items.length / 3) + 1
  }, [items.length])

  const handleReset = useCallback(() => {
    formRef.current?.setFieldsValue(GENERATIONAL_INIT_VALUES)
    setFormValues(GENERATIONAL_INIT_VALUES)
    setSearched(false)
  }, [setSearched])

  const check = useCallback((params: Record<string, string | string[]>) => {
    const checkMsg = checkFormValues(params)
    if (checkMsg) {
      customMessage(checkMsg, 'warning')
      return false
    }
    return true
  }, [])

  const handleSearch = useCallback(() => {
    const values = formRef.current?.getFieldsValue()
    const { pageType, costMatCatLvl1Codes, projectNames, configs, saleSites, phaseStages } = values

    if (!pageType) {
      customMessage(intl.get('请选择成本对比类型'), 'warning')
      return
    }
    const catObj = pageType === CostCompare.Category ? { costMatCatLvl1Codes } : {}
    if (!check({ phaseStages, configs, saleSites, projectNames, ...catObj } as AnyType)) {
      return
    }

    if (pageType === CostCompare.Node && configs?.length > 5) {
      customMessage(intl.get('节点对比时最多只能选择 5 个配置'), 'warning')
      return
    }

    setFilter(formatValues(values))
    setSearched(true)

    try {
      localStorage.setItem(LAST_GENERATION_FILTER_KEY, JSON.stringify(values))
    } catch (err) {
      Sentry.captureException(err)
    }
  }, [check, setFilter, setSearched])

  const exportParams = useMemo(() => {
    const isCategoryDimension = formValues?.pageType === CostCompare.Category
    let params = {}
    if (baseline?.includes(SPLIT_SYMBOL)) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const [_projectName, saleSite, config] = baseline?.split(SPLIT_SYMBOL) || []
      params = {
        ...formatValues(formValues),
        type: CompareType.COST_MAT_CAT_LVL1_CODE,
        saleSites: [saleSite],
        configs: [config],
      }
    } else {
      params = formatValues(formValues)
    }
    if (!isCategoryDimension) {
      params = omit(params, 'costMatCatLvl1Codes', 'costMatCatLvl2Codes', 'costMatCatLvl3Codes')
    }
    return params
  }, [formValues, baseline])

  const { handleTableExport, loading: exportLoading } = useExport(
    exportParams,
    exportGenerationalCost
  )

  const exportCheck = useCallback(() => {
    const {
      pageType,
      costMatCatLvl1Codes,
      costMatCatLvl2Codes,
      costMatCatLvl3Codes,
      projectNames,
      configs,
      saleSites,
      phaseStages,
    } = formValues

    const catObj =
      pageType === CostCompare.Category
        ? { costMatCatLvl1Codes, costMatCatLvl2Codes, costMatCatLvl3Codes }
        : {}
    if (!check({ phaseStages, configs, saleSites, projectNames, ...catObj } as AnyType)) {
      return
    }

    handleTableExport()
  }, [formValues, check, handleTableExport])

  return (
    <div className="generational-cost__form">
      <div
        className="generational-cost__container"
        style={{ height: expanded ? genHeight(1) : genHeight(lines) }}
      >
        <Form
          innerRef={formRef}
          initialValues={cacheFormValues || GENERATIONAL_INIT_VALUES}
          labelWidth={80}
          labelPlacement="right"
          showColon={false}
          onValuesChange={(_changeValue, allValues) => {
            setFormValues(allValues)
          }}
        >
          <Row gutter rowGap={0}>
            <Col span={24}>
              <FormItem field="pageType" label={ellipses(intl.get('成本对比'), true)}>
                <RadioGroup
                  data={COST_COMPARE_OPTIONS}
                  id={RADIO_ID}
                  onChange={() => setExpanded(false)}
                />
              </FormItem>
            </Col>
          </Row>
          {items
            .map((item, index) => (
              <Col span={8} key={`${item.props.field}${index}`}>
                {item}
              </Col>
            ))
            .reduce((a: ReactNode[], b, idx, arr) => {
              if (idx % 3 === 0)
                a.push(
                  <Row gutter rowGap={0} key={`${b.props.field}${idx}`}>
                    {arr.slice(idx, idx + 3)}
                  </Row>
                )
              return a
            }, [])}
        </Form>
      </div>
      <div className="form-button__container">
        <div className="w-max">
          <Button appearance="link" onClick={() => setExpanded((expanded) => !expanded)}>
            {expanded ? intl.get('展开') : intl.get('收起')}
            <DownOutlined
              className="transition-transform ml-2"
              color="#9d9d9f"
              style={{
                transform: expanded ? 'rotateZ(0)' : 'rotateZ(180deg)',
              }}
            />
          </Button>
          <Button type="default" onClick={handleReset}>
            {intl.get('重置')}
          </Button>
          <Button type="secondary" onClick={handleSearch}>
            {intl.get('查询')}
          </Button>
        </div>
      </div>
      {!formValues?.pageType ? null : (
        <CustomPortal domId={HEADER_TOOLS_PORTAL}>
          <Button
            type="primary"
            loading={exportLoading}
            onClick={exportCheck}
            icon={<DownloadOutlined />}
          >
            {intl.get('导出')}
          </Button>
        </CustomPortal>
      )}
      {!cacheFormValues && tourVisible && <Tour close={() => setTourVisible(false)} />}
    </div>
  )
})

GenerationalForm.displayName = 'GenerationalForm'
export default GenerationalForm
