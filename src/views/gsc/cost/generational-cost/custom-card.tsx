import React, { memo, ReactNode } from 'react'
import './custom-card.scss'
import Tooltip from '@hi-ui/tooltip'
import { InfoCircleOutlined } from '@hi-ui/icons'

interface CustomCardProps {
  title: string
  icon?: ReactNode
  extra?: ReactNode
  tip?: ReactNode
  tooltipTitle?: string
  children: ReactNode
}

const CustomCard = memo<CustomCardProps>(({ title, icon, extra, tip, tooltipTitle, children }) => {
  return (
    <div className="custom-card__wrapper" data-testid="custom-card" data-title={title}>
      <div className="custom-card__header">
        <div className="custom-card__header-left">
          {icon && <div className="custom-card__icon">{icon}</div>}
          <div className="custom-card__title">{title}</div>
          {tip ||
            (tooltipTitle && (
              <Tooltip title={tooltipTitle} placement="top-end">
                <InfoCircleOutlined className="cursor-pointer" size={16} color="#929AA6" />
              </Tooltip>
            ))}
        </div>
        {extra && <div className="custom-card__header-right">{extra}</div>}
      </div>
      {children}
    </div>
  )
})

CustomCard.displayName = 'CustomCard'
export default CustomCard
