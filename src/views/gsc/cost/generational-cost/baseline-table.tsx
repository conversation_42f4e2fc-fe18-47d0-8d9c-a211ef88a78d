import React, { FC, useCallback, useEffect, useMemo, useState } from 'react'
import intl from 'react-intl-universal'
import EmptyState, { EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL } from '@hi-ui/empty-state'
import { CaretUpFilled, CaretDownFilled, FilterOutlined } from '@hi-ui/icons'
import CheckSelect from '@hi-ui/check-select'
import {
  buildDrillTree,
  buildRecordMatrix,
  convertDrillTreeToCrossTree,
  CrossTable,
} from 'ali-react-table/dist/ali-react-table-pivot'
import { GenerationalCostBaseTable } from './base-table'
import { createAggregateFunction } from 'dvt-aggregation'
import { isNumber } from 'lodash'
import Loading from '@hi-ui/loading'
import { SetType } from '@/types/type'
import { COST_DECIMALS, getColumnsByType, TABLE_BASELINE_ID } from './config'
import { formatDisplayNumber } from '@/utils/number'

import './baseline-table.scss'
import { ellipses } from '@/components/ellipsis-tool'

const topCodes = []

// 排序状态: 0-无排序, 1-升序, 2-降序
type SortState = 0 | 1 | 2

interface BaselineTableProps {
  result: Record<string, AnyType> | null
  baseline?: string
  setBaseline?: SetType<string>
  maxHeight?: number
  baselineExtra?: string
}

const BaselineTable: FC<BaselineTableProps> = (props) => {
  const { result, baseline, setBaseline, maxHeight = 480, baselineExtra } = props
  const [{ finalData, isLoading }, setState] = useState({
    isLoading: true,
    finalData: [] as AnyType[],
  })
  const [tableList, setTableList] = useState<AnyType[]>([])
  const [leftCodes, setLeftCodes] = useState<string[]>([])
  const [newDimensions, setNewDimensions] = useState<AnyType[]>([])
  const [newIndicators, setNewIndicators] = useState<AnyType[]>([])
  const [baselineMap, setBaselineMap] = useState<Record<string, string>>({})
  // 排序状态管理
  const [sortState, setSortState] = useState<Record<string, SortState>>({})
  const [sortedData, setSortedData] = useState<AnyType[]>([])
  // 筛选状态管理
  const [filterState, setFilterState] = useState<Record<string, string[]>>({})
  const [filteredData, setFilteredData] = useState<AnyType[]>([])

  const handleResult = useCallback((result) => {
    const { columnList = [], rowPage } = result || {}
    const { data = [] } = rowPage || {}
    const calcDimensions = getColumnsByType(columnList, ['1', '3'])
    const newBaselineMap = {}
    const calcIndicators = getColumnsByType(columnList, ['6']).map((column, index) => {
      newBaselineMap[column.name] = `a${index + 1}`
      return { ...column, width: 160, expression: `SUM(a${index + 1})`, align: 'right' as const }
    })
    setNewDimensions(calcDimensions)
    setNewIndicators(calcIndicators)
    setBaselineMap(newBaselineMap)
    setTableList(data)
  }, [])

  useEffect(() => {
    if (result && Object.keys(result).length) {
      setSortState({})
      setFilterState({})
      handleResult(result)
    }
  }, [handleResult, result])

  // 生成筛选选项（基于其他维度筛选后的数据）
  const getFilterOptions = useCallback(
    (dimensionCode: string) => {
      // 获取除当前维度外的其他筛选条件
      const otherFilters = Object.entries(filterState).filter(
        ([code, values]) => code !== dimensionCode && values.length > 0
      )

      // 如果没有其他筛选条件，使用原始数据
      let sourceData = finalData

      // 如果有其他筛选条件，先应用这些筛选
      if (otherFilters.length > 0) {
        sourceData = finalData.filter((item) => {
          return otherFilters.every(([code, selectedValues]) => {
            const itemValue = String(item[code] || '')
            return selectedValues.includes(itemValue)
          })
        })
      }

      // 从筛选后的数据中提取当前维度的唯一值
      const uniqueValues = new Set<string>()
      sourceData.forEach((item) => {
        const value = item[dimensionCode]
        if (value !== null && value !== undefined && value !== '') {
          uniqueValues.add(String(value))
        }
      })

      return Array.from(uniqueValues).map((value) => ({
        title: value,
        id: value,
      }))
    },
    [finalData, filterState]
  )

  const dimensions = useMemo(() => {
    return newDimensions.map((key) => {
      const width = key?.width || 100
      const hasFilter = filterState[key.code]?.length > 0

      return {
        ...key,
        width,
        render: (a) => ellipses(a?.value),
        name: (
          <div className="baseline-table__dimension-header">
            <span>{key.name}</span>
            <CheckSelect
              style={{ width: 'auto' }}
              optionWidth={200}
              placeholder={intl.get('筛选')}
              searchable
              clearable
              data={getFilterOptions(key.code)}
              value={filterState[key.code] || []}
              onChange={(selectedValues) => {
                setFilterState((prev) => ({
                  ...prev,
                  [key.code]: selectedValues || [],
                }))
              }}
              customRender={
                <FilterOutlined
                  style={{
                    color: hasFilter ? '#237ffa' : 'rgb(201, 206, 214)',
                    fontSize: '14px',
                    cursor: 'pointer',
                  }}
                />
              }
            />
          </div>
        ),
      }
    })
  }, [newDimensions, getFilterOptions, filterState])

  useEffect(() => {
    setLeftCodes(dimensions.map((i) => i.code) as AnyType)
  }, [dimensions])

  // 排序函数
  const handleSort = useCallback((indicatorName: string, direction: 'up' | 'down') => {
    setSortState((prev) => {
      const currentSort = prev[indicatorName] || 0
      let newSort: SortState

      if (direction === 'up') {
        // 点击上箭头的逻辑
        if (currentSort === 0) {
          newSort = 1 // 原序 -> 升序
        } else if (currentSort === 1) {
          newSort = 0 // 升序 -> 原序
        } else {
          newSort = 1 // 降序 -> 升序（跨方向点击）
        }
      } else {
        // 点击下箭头的逻辑
        if (currentSort === 0) {
          newSort = 2 // 原序 -> 降序
        } else if (currentSort === 2) {
          newSort = 0 // 降序 -> 原序
        } else {
          newSort = 2 // 升序 -> 降序（跨方向点击）
        }
      }

      // 清除其他列的排序状态，只保留当前列的排序
      if (newSort !== 0) {
        return { [indicatorName]: newSort }
      } else {
        return {}
      }
    })
  }, [])

  // 应用筛选到数据
  useEffect(() => {
    if (finalData.length === 0) {
      setFilteredData([])
      return
    }

    // 检查是否有激活的筛选
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const activeFilters = Object.entries(filterState).filter(([_, values]) => values.length > 0)

    if (activeFilters.length === 0) {
      setFilteredData(finalData)
      return
    }

    // 应用筛选
    const filtered = finalData.filter((item) => {
      return activeFilters.every(([dimensionCode, selectedValues]) => {
        const itemValue = String(item[dimensionCode] || '')
        return selectedValues.includes(itemValue)
      })
    })

    setFilteredData(filtered)
  }, [finalData, filterState])

  // 应用排序到筛选后的数据
  useEffect(() => {
    if (filteredData.length === 0) {
      setSortedData([])
      return
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const activeSort = Object.entries(sortState).find(([_, state]) => state !== 0)
    if (!activeSort) {
      setSortedData(filteredData)
      return
    }

    const [sortColumn, sortDirection] = activeSort
    const sortedIndicator = newIndicators.find((indicator) => indicator.name === sortColumn)
    if (!sortedIndicator) {
      setSortedData(filteredData)
      return
    }

    const sortedResult = [...filteredData].sort((a, b) => {
      const aValue = Number(a[sortedIndicator.code]) || 0
      const bValue = Number(b[sortedIndicator.code]) || 0

      if (sortDirection === 1) {
        // 升序
        return aValue - bValue
      } else {
        // 降序
        return bValue - aValue
      }
    })

    setSortedData(sortedResult)
  }, [filteredData, sortState, newIndicators])

  const visibleIndicators = useMemo(
    () =>
      newIndicators.map((indicator, idx) => {
        const currentSortState = sortState[indicator.name] || 0
        return {
          ...indicator,
          code: `a${idx + 1}`,
          expression: `SUM(a${idx + 1})`,
          name: (
            <div
              className={`baseline-table__column-header ${
                indicator.name === baseline ? 'baseline-table__target-column' : ''
              }`}
              id={indicator.name === baseline ? TABLE_BASELINE_ID : ''}
            >
              <span
                onClick={() => baseline && setBaseline?.(`${indicator.name}${baselineExtra || ''}`)}
                className={`baseline-table__column-title ${baseline ? 'cursor-pointer' : ''}`}
              >
                {indicator.name}
              </span>
              <div className="baseline-table__sort-icons">
                <span
                  className="baseline-table__sort-icon"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleSort(indicator.name, 'up')
                  }}
                >
                  <CaretUpFilled
                    size={12}
                    color={currentSortState === 1 ? '#237ffa' : 'rgb(201, 206, 214)'}
                  />
                </span>
                <span
                  className="baseline-table__sort-icon"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleSort(indicator.name, 'down')
                  }}
                >
                  <CaretDownFilled
                    size={12}
                    color={currentSortState === 2 ? '#237ffa' : 'rgb(201, 206, 214)'}
                  />
                </span>
              </div>
            </div>
          ),
        }
      }) || [],
    [baseline, baselineExtra, newIndicators, setBaseline, sortState, handleSort]
  )

  const leftDrillTree = useMemo(() => {
    return buildDrillTree(sortedData, leftCodes as AnyType, {
      includeTopWrapper: true,
    })
  }, [sortedData, leftCodes])

  const leftTreeRoot = useMemo(
    () =>
      convertDrillTreeToCrossTree(leftDrillTree, {
        indicators: null as AnyType,
      })?.[0],
    [leftDrillTree]
  )

  const topDrillTree = useMemo(
    () => buildDrillTree(sortedData, topCodes, { includeTopWrapper: true }),
    [sortedData]
  )

  const topTreeRoot = useMemo(
    () => convertDrillTreeToCrossTree(topDrillTree, { indicators: visibleIndicators })[0],
    [topDrillTree, visibleIndicators]
  )

  const dimMap = useMemo(() => new Map(dimensions.map((dim) => [dim.code, dim])), [dimensions])

  const formatTableList = useMemo(() => {
    const res = tableList.map((item) => {
      const arr = Object.keys(item)
      const keys = newIndicators.map((i) => i.code) || []
      const shouldSetDefaultArr = keys.filter((key) => !arr.includes(key as string))

      let extraObj = {}
      if (shouldSetDefaultArr.length !== 0) {
        extraObj = shouldSetDefaultArr.reduce((a, b) => {
          a[b] = null
          return a
        }, {})
      }
      const temp = { ...item, ...extraObj }
      newIndicators.forEach((deconstructIndicator, idx) => {
        temp[`a${idx + 1}`] =
          typeof temp[deconstructIndicator.code] === 'number'
            ? temp[deconstructIndicator.code]
            : '-'
      })
      return temp
    })
    return res
  }, [newIndicators, tableList])

  useEffect(() => {
    setState({ finalData: formatTableList, isLoading: false })
    setFilteredData(formatTableList)
    setSortedData(formatTableList)
  }, [formatTableList, setState])

  const aggregate = useMemo(
    () => createAggregateFunction(visibleIndicators as AnyType),
    [visibleIndicators]
  )

  const matrix = useMemo(() => {
    if (leftCodes.length > 0 && leftDrillTree && topDrillTree) {
      return buildRecordMatrix({
        data: sortedData,
        leftCodes,
        topCodes,
        aggregate,
        prebuiltLeftTree: leftDrillTree,
        prebuiltTopTree: topDrillTree,
      })
    } else {
      return null
    }
  }, [aggregate, sortedData, leftCodes, leftDrillTree, topDrillTree])

  return (
    <div data-testid="baseline-table">
      {isLoading ? (
        <Loading content="">
          <div style={{ height: maxHeight }}></div>
        </Loading>
      ) : sortedData.length === 0 ? (
        <EmptyState
          className="baseline-table__empty-state"
          style={{ height: maxHeight }}
          title={intl.get('暂无数据')}
          indicator={EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL}
        />
      ) : (
        matrix && (
          <CrossTable
            className="bordered"
            style={{ maxHeight, width: '100%', overflow: 'overlay' }}
            BaseTableComponent={GenerationalCostBaseTable}
            defaultColumnWidth={100}
            useVirtual={true}
            leftMetaColumns={leftCodes.map((code) => dimMap.get(code))}
            leftTree={leftTreeRoot.children || []}
            topTree={topTreeRoot.children || []}
            getValue={(leftNode, topNode) => {
              const leftKey = leftNode.data.dataKey
              const topKey = topNode.data.dataKey
              const r = matrix?.get(leftKey)?.get(topKey)
              if (!r) return '-'
              const value = r[topNode.data.indicator.code]
              if (baseline && topNode.code !== baselineMap[baseline]) {
                const baselineValue = matrix?.get(leftKey)?.get('key:@total@')?.[
                  baselineMap[baseline]
                ]
                let icon: string | null = null
                let diff = ''
                const color = value > baselineValue ? '#FF5959' : '#14CA64'
                if (value !== baselineValue && isNumber(baselineValue) && isNumber(value)) {
                  icon = value > baselineValue ? '+' : '-'
                  diff = String(Math.abs(value - baselineValue).toFixed(2))
                }
                return (
                  <>
                    <div
                      style={{
                        height: 0,
                        position: 'relative',
                        bottom: icon ? '8px' : '9px',
                      }}
                    >
                      {formatDisplayNumber(value, COST_DECIMALS)}
                    </div>
                    <div style={{ color, fontSize: 12, position: 'relative', top: '10px' }}>
                      {icon}
                      {diff}
                    </div>
                  </>
                )
              }
              return formatDisplayNumber(value, COST_DECIMALS)
            }}
          />
        )
      )}
    </div>
  )
}

export default BaselineTable
