import React from 'react'
import { render } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { Tour } from '../index'

// 在测试文件开头直接模拟IntersectionObserver
class MockIntersectionObserver {
  private callback: IntersectionObserverCallback
  private options: IntersectionObserverInit

  constructor(callback: IntersectionObserverCallback, options?: IntersectionObserverInit) {
    this.callback = callback
    this.options = options || {}
  }

  observe(element: Element) {
    // 立即触发回调，模拟元素可见
    setTimeout(() => {
      this.callback(
        [
          {
            target: element,
            isIntersecting: true,
            intersectionRatio: 0.9,
            boundingClientRect: element.getBoundingClientRect(),
            intersectionRect: element.getBoundingClientRect(),
            rootBounds: null,
            time: Date.now(),
          } as IntersectionObserverEntry,
        ],
        this as AnyType
      )
    }, 0)
  }

  unobserve() {
    // Mock unobserve
  }

  disconnect() {
    // Mock disconnect
  }
}

// 替换全局IntersectionObserver
Object.defineProperty(window, 'IntersectionObserver', {
  writable: true,
  configurable: true,
  value: MockIntersectionObserver,
})

Object.defineProperty(global, 'IntersectionObserver', {
  writable: true,
  configurable: true,
  value: MockIntersectionObserver,
})

const mockClose = vi.fn()

describe('Tour Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()

    // Mock DOM elements
    const mockElement = {
      getBoundingClientRect: () => ({
        top: 100,
        left: 100,
        right: 200,
        bottom: 150,
        width: 100,
        height: 50,
      }),
    }

    vi.spyOn(document, 'getElementById').mockReturnValue(mockElement as AnyType)
  })

  afterEach(() => {
    vi.restoreAllMocks()
    vi.useRealTimers()
  })

  it('应该正确渲染组件', () => {
    const { container } = render(<Tour close={mockClose} />)
    expect(container).toBeInTheDocument()
  })

  it('应该正确初始化IntersectionObserver', () => {
    render(<Tour close={mockClose} />)

    // 推进定时器以触发filterVisibleSteps中的setTimeout(500ms)
    vi.advanceTimersByTime(600)

    // 检查IntersectionObserver是否被使用
    expect(window.IntersectionObserver).toBeDefined()
  })

  it('应该接收close回调函数', () => {
    render(<Tour close={mockClose} />)
    expect(mockClose).toEqual(expect.any(Function))
  })

  it('应该正确处理DOM元素查询', () => {
    render(<Tour close={mockClose} />)
    expect(document.getElementById).toHaveBeenCalled()
  })

  it('应该正确处理getBoundingClientRect调用', () => {
    render(<Tour close={mockClose} />)
    // 组件应该正常渲染而不报错
    expect(true).toBe(true)
  })
})
