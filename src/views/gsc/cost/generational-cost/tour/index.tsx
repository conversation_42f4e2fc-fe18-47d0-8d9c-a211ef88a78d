import React, { FC, ReactNode, useCallback, useEffect, useMemo, useState } from 'react'
import intl from 'react-intl-universal'
import Button from '@hi-ui/button'
import { RADIO_ID, SHADOW_BOX_WRAPPER_ID, TABLE_BASELINE_ID } from '../config'
import './index.scss'

export const GUIDE_MAP = {
  radioGroup: {
    title: () => intl.get('成本对比'),
    description: () => (
      <>
        <div className="mb-6">
          {intl.get('请选择成本对比方式，不同对比方式重点展示会有差异，谢谢！')}
        </div>
        <div>{intl.get('1. 项目对比：支持多个项目成本对比。')}</div>
        <div>{intl.get('2. 节点对比：支持项目全生命周期各节点成本对比。')}</div>
        <div>{intl.get('3. 品类对比：支持多项目成本品类对比。')}</div>
        <div>{intl.get('4. 自由组合对比：支持项目/区域/配置/节点等各纬度组合对比。')}</div>
      </>
    ),
  },
  tableBaseline: {
    title: () => intl.get('基准值切换提示'),
    description: () =>
      intl.get(
        '带对勾标识的为基准值，各列数据依此计算。您可按需切换基准值列，轻松对比不同维度数据。'
      ),
  },
}

export const X_TOUR_PADDING = 10
export const Y_TOUR_PADDING = 12

export interface StepItem {
  title: string
  description: string | ReactNode
  target: string[]
}

interface Props {
  close: () => void
}

export const Tour: FC<Props> = ({ close }) => {
  const [currentStep, setCurrentStep] = useState<number>(0)
  const [shadowStyle, setShadowStyle] = useState<Record<string, string | number>>({})
  const [boxStyle, setBoxStyle] = useState<Record<string, string | number>>({})
  const windowSize = { width: window.innerWidth, height: window.innerHeight }
  const [filteredSteps, setFilteredSteps] = useState<StepItem[]>([])

  const boxWrapperDom = document.getElementById(SHADOW_BOX_WRAPPER_ID)
  const radioDom = document.getElementById(RADIO_ID)
  const baselineDom = document.getElementById(TABLE_BASELINE_ID)

  const checkElementVisibility = (elementId: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const element = document.getElementById(elementId)
      if (!element) {
        resolve(false)
        return
      }

      const checkObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            resolve(entry.intersectionRatio > 0.8)
          })
          checkObserver.disconnect()
        },
        { threshold: 0.8 }
      )

      checkObserver.observe(element)
    })
  }

  // 过滤 steps 的函数
  const filterVisibleSteps = useCallback(async (steps: StepItem[]): Promise<StepItem[]> => {
    const visibleSteps = await Promise.all(
      steps.map(async (step) => {
        // 处理单个或多个 target
        const targets = Array.isArray(step.target) ? step.target : [step.target]

        // 检查所有 target 是否都可见
        const visibilityResults = await Promise.all(
          targets.map((targetId) => checkElementVisibility(targetId))
        )

        // 只有当所有 target 都可见时，才保留这个 step
        return visibilityResults.every((isVisible) => isVisible) ? step : null
      })
    )

    return visibleSteps.filter((step): step is StepItem => step !== null)
  }, [])

  const tourSteps = useMemo(() => {
    const steps: StepItem[] = []

    radioDom &&
      steps.push({
        title: GUIDE_MAP.radioGroup.title(),
        description: GUIDE_MAP.radioGroup.description(),
        target: [RADIO_ID],
      })

    baselineDom &&
      steps.push({
        title: GUIDE_MAP.tableBaseline.title(),
        description: GUIDE_MAP.tableBaseline.description(),
        target: [TABLE_BASELINE_ID],
      })

    return steps
  }, [baselineDom, radioDom])

  const stopItem = useMemo(() => filteredSteps[currentStep], [filteredSteps, currentStep])

  useEffect(() => {
    if (stopItem && stopItem.target[0]) {
      const target = stopItem.target
      const firstRect = document.getElementById(target[0])?.getBoundingClientRect()
      const boxWrapperRect = boxWrapperDom?.getBoundingClientRect()
      if (!firstRect) {
        close()
        return
      }
      const lastRect =
        target.length > 1
          ? document.getElementById(target[target.length - 1])?.getBoundingClientRect()
          : null
      setShadowStyle({
        top: firstRect.top - Y_TOUR_PADDING,
        left: firstRect.left - X_TOUR_PADDING,
        width: firstRect.width + X_TOUR_PADDING * 2,
        height: (lastRect || firstRect).bottom - firstRect.top + Y_TOUR_PADDING * 2,
      })
      const isVerticalWealthy =
        windowSize.height - firstRect.bottom >= (boxWrapperRect?.height || 150) + Y_TOUR_PADDING * 2

      const isHorizontalWealthy =
        windowSize.width - firstRect.right >= (boxWrapperRect?.width || 310) + X_TOUR_PADDING * 2

      const newBoxStyle = {
        top: isVerticalWealthy
          ? firstRect.top + firstRect.height + Y_TOUR_PADDING
          : firstRect.top - (boxWrapperRect?.height || 150) - Y_TOUR_PADDING,
        left: isHorizontalWealthy
          ? firstRect.left + firstRect.width + X_TOUR_PADDING
          : firstRect.left - (boxWrapperRect?.width || 310) - X_TOUR_PADDING,
      }
      setBoxStyle(newBoxStyle)
    }
  }, [stopItem, windowSize.height, windowSize.width, boxWrapperDom, close])

  useEffect(() => {
    const timer = setTimeout(() => {
      // 当 tourSteps 更新时，过滤出可见的步骤
      filterVisibleSteps(tourSteps).then((visibleSteps) => {
        setFilteredSteps(visibleSteps)
      })
    }, 500)
    return () => clearTimeout(timer)
  }, [filterVisibleSteps, tourSteps])

  return (
    <>
      {stopItem?.title && (
        <div className="tour">
          <div className="tour__guidance-box" style={shadowStyle} />
          <div className="tour__box-wrapper" style={boxStyle} id={SHADOW_BOX_WRAPPER_ID}>
            <p className="tour__title">👋 {stopItem?.title}</p>
            <p className="tour__description">{stopItem?.description}</p>
            <div className="tour__foot-wrapper">
              <div>
                {currentStep + 1}/{filteredSteps.length}
              </div>
              <div>
                {currentStep === filteredSteps.length - 1 ? (
                  <Button type="primary" onClick={close}>
                    {intl.get('知道了')}
                  </Button>
                ) : (
                  <>
                    <Button appearance="link" onClick={close}>
                      {intl.get('跳过')}
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => {
                        setCurrentStep((pre) => pre + 1)
                      }}
                    >
                      {intl.get('下一步')}
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
