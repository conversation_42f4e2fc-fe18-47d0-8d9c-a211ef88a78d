import React, { ReactNode, useMemo } from 'react'
import * as Sentry from '@sentry/react'
import GenerationalForm from './form'
import { useGenerationalCost } from '@/hooks/useGenerationalCost'
import Empty from './empty'
import NodeCompare from './node-compare'
import { CostCompare, LAST_GENERATION_FILTER_KEY } from './config'
import ProjectCompare from './project-compare'
import CategoryCompare from './category-compare'
import FreeCompare from './free-combination'

const View = () => {
  let lastGenerationFilter = null as Record<string, string | string[]> | null
  const { filter, searched } = useGenerationalCost()

  try {
    const filterCache = JSON.parse(localStorage.getItem(LAST_GENERATION_FILTER_KEY) || '{}')
    if (Object.keys(filterCache).length) {
      lastGenerationFilter = filterCache
    }
  } catch (err) {
    Sentry.captureException(err)
  }

  const compareContent = useMemo(() => {
    if (!filter?.pageType || !searched) return <Empty />
    let content = null as ReactNode
    switch (filter?.pageType) {
      case CostCompare.Project:
        content = <ProjectCompare />
        break
      case CostCompare.Node:
        content = <NodeCompare />
        break
      case CostCompare.Category:
        content = <CategoryCompare />
        break
      case CostCompare.FreeCombination:
        content = <FreeCompare />
        break
      default:
        content = <Empty />
    }

    return content
  }, [filter, searched])

  return (
    <>
      <GenerationalForm cacheFormValues={lastGenerationFilter} />
      {compareContent}
    </>
  )
}

export default View
