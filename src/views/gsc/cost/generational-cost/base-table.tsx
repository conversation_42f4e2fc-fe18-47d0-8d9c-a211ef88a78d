import React from 'react'
import { BaseTable, BaseTableProps, Classes } from 'ali-react-table'
import styled from 'styled-components'
import cx from 'classnames'

export const StyledBaseTable = styled(BaseTable)`
  --font-size: 14px;
  --font-weight: 400;
  --row-height: 41px;
  --header-row-height: 41px;

  --lock-shadow: rgba(0, 0, 0, 0.2) 0 0 10px 0px;
  --border-color: #ebedf0;
  --color: rgba(0, 0, 0, 0.85);
  --bgcolor: white;
  --hover-bgcolor: #f2f4f7;
  --header-color: #1f2733;
  --header-bgcolor: #f5f7fa;

  td {
    transition: background 0.3s;
  }

  th {
    font-weight: 500;
  }

  .${Classes.lockShadowMask} {
    .${Classes.lockShadow} {
      transition: box-shadow 0.3s;
    }
  }

  &:not(.bordered) {
    --cell-border-vertical: none;
    --header-cell-border-vertical: none;

    thead > tr.first th {
      border-top: none;
    }
  }
` as unknown as typeof BaseTable

export const GenerationalCostBaseTable = React.forwardRef<BaseTable, BaseTableProps>(
  (props, ref) => {
    return <StyledBaseTable ref={ref} className={cx(props.className)} {...props} />
  }
)

GenerationalCostBaseTable.displayName = 'GenerationalCostBaseTable'
