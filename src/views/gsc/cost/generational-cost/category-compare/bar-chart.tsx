import { useGenerationalCost } from '@/hooks/useGenerationalCost'
import { Chart } from '@antv/g2'
import React, { FC, useEffect, useRef, useState } from 'react'
import {
  BAR_COLOR,
  CHART_AXIS_COLOR,
  CHART_HEIGHT,
  CHART_TEXT_COLOR,
  COMMON_CHART_TOOLTIP_CONFIG,
  COST_DECIMALS,
} from '../config'
import { roundToDecimal } from '@/utils/number'

const X_KEY = 'projectName'
const Y_KEY = 'cost'

// 定义数字缩写辅助函数
const SI_POSTFIXES = [
  { value: 1, symbol: '' },
  { value: 1e3, symbol: 'K' },
  { value: 1e6, symbol: 'M' },
  { value: 1e9, symbol: 'B' },
  { value: 1e12, symbol: 'T' },
]

function abbreviateNumber(number: number | null | undefined, decimals = 2): string {
  if (number === null || number === undefined || typeof number !== 'number' || isNaN(number)) {
    return ''
  }
  if (number === 0) {
    return '0'
  }

  const isNegative = number < 0
  const absNumber = Math.abs(number)

  const tier = SI_POSTFIXES.slice()
    .reverse()
    .find((postfix) => absNumber >= postfix.value)

  if (!tier) {
    return (isNegative ? '-' : '') + absNumber.toFixed(decimals)
  }

  const scaledValue = absNumber / tier.value
  // 对于K, M等，如果缩放后小于10且有小数，则保留小数，否则尽量整数
  const effectiveDecimals =
    tier.symbol === '' || (scaledValue < 10 && scaledValue % 1 !== 0) ? decimals : 0

  return (isNegative ? '-' : '') + scaledValue.toFixed(effectiveDecimals) + tier.symbol
}

const BarChart: FC<{ result: AnyType }> = ({ result }) => {
  const { filter } = useGenerationalCost()
  const domRef = useRef<HTMLDivElement>(null)
  const chartRef = useRef<AnyType>(null)
  const [chartData, setChartData] = useState<AnyType[]>([])
  const filterRef = useRef<AnyType>(filter)

  useEffect(() => {
    filterRef.current = filter
  }, [filter])

  useEffect(() => {
    const { columnList = [], rowPage } = result || {}
    const { data = [] } = rowPage || {}
    const finalIndex = [
      columnList?.findIndex((item) => item?.key === 'costMatCatLvl3Code'),
      columnList?.findIndex((item) => item?.key === 'costMatCatLvl2Code'),
      columnList?.findIndex((item) => item?.key === 'costMatCatLvl1Code'),
    ].find((index) => index !== -1 && index !== undefined)
    const nodeList = [] as AnyType[]

    columnList.forEach((item, index) => {
      if (index > finalIndex) {
        nodeList.push(item)
      }
    })

    const newChartData = data.reduce((a, b) => {
      nodeList.forEach((item) => {
        a.push({
          [X_KEY]: item.value,
          [Y_KEY]:
            b[item.key] === 0
              ? 0
              : b[item.key] && typeof b[item.key] === 'number'
                ? roundToDecimal(b[item.key], COST_DECIMALS)
                : null,
        })
      })
      return a
    }, [])
    setChartData(newChartData)
  }, [result])

  useEffect(() => {
    if (!chartRef.current && domRef.current) {
      chartRef.current = new Chart({
        container: domRef.current,
        autoFit: true,
        height: CHART_HEIGHT,
      })

      chartRef.current.tooltip(COMMON_CHART_TOOLTIP_CONFIG)

      chartRef.current.axis(X_KEY, {
        tickLine: null,
      })

      chartRef.current.interaction('active-region')
      chartRef.current
        .interval()
        .position(X_KEY + '*' + Y_KEY)
        .size(16)
        .color(BAR_COLOR)

      chartRef.current.legend(false)

      chartRef.current.axis(Y_KEY, {
        grid: {
          line: {
            style: {
              stroke: CHART_AXIS_COLOR,
              lineWidth: 1,
            },
          },
        },
        label: {
          autoRotate: true,
          autoHide: false,
          style: {
            fill: CHART_TEXT_COLOR,
          },
        },
      })
      chartRef.current.axis(X_KEY, {
        label: {
          autoRotate: true,
          autoHide: false,
          style: {
            fill: CHART_TEXT_COLOR,
          },
        },
      })
    }
  }, [filter?.configs, filter?.saleSites])

  useEffect(() => {
    if (chartRef.current) {
      let maxYForScale

      if (chartData && chartData.length > 0) {
        const validYValues = chartData
          .map((item) => item[Y_KEY])
          .filter((y) => typeof y === 'number' && y !== null)
        if (validYValues.length > 0) {
          const currentMaxY = Math.max(...validYValues, 0)
          maxYForScale = currentMaxY * 1.1
          if (maxYForScale === 0 && currentMaxY === 0 && validYValues.every((v) => v === 0)) {
            maxYForScale = 10
          } else if (currentMaxY < 0) {
            maxYForScale = currentMaxY * 0.9
            if (maxYForScale === 0 && currentMaxY !== 0) maxYForScale = undefined
          }
        } else {
          maxYForScale = undefined
        }
      } else {
        maxYForScale = undefined
      }

      chartRef.current.scale(Y_KEY, {
        nice: true,
        max: maxYForScale,
        alias: `${filterRef.current?.configs?.[0]}（${filterRef.current?.saleSites?.[0]}）`,
      })

      chartRef.current.annotation().clear(true)
      chartRef.current.data(chartData)
      chartData.forEach((item) => {
        if (typeof item[Y_KEY] === 'number' && item[Y_KEY] !== null) {
          chartRef.current.annotation().text({
            position: [item[X_KEY], item[Y_KEY]],
            content: abbreviateNumber(item[Y_KEY], 2),
            style: {
              textAlign: 'center',
              fill: CHART_TEXT_COLOR,
            },
            offsetY: -10,
          })
        }
      })
      chartRef.current.render()
    }
  }, [chartData])

  return <div ref={domRef}></div>
}

export default BarChart
