import React, { FC, useEffect, useMemo, useState } from 'react'
import CustomCard from '../custom-card'
import intl from 'react-intl-universal'
import { IMG_PREFIX } from '@/constants'

import BaselineTable from '../baseline-table'
import { useGenerationalCost } from '@/hooks/useGenerationalCost'
import { TABLE_LINE_HEIGHT, CompareType } from '../config'
import { useRequest } from 'ahooks'
import { getProjectCompareData } from '@/api/cost/generational-cost'
import BarChart from './bar-chart'

const DataMonitorImg = `${IMG_PREFIX}Data-monitor.svg`

const BigCost: FC = () => {
  const { filter } = useGenerationalCost()
  const [result, setResult] = useState<AnyType>(null)

  const isChartEmpty = useMemo(() => {
    return result?.rowPage?.data?.length === 0
  }, [result])

  const { run: getList } = useRequest(getProjectCompareData, {
    manual: true,
    onSuccess: (res) => {
      setResult(res?.data)
    },
  })

  useEffect(() => {
    if (filter?.pageType) {
      let categoryType = CompareType.COST_MAT_CAT_LVL1_CODE

      if (filter?.costMatCatLvl2Codes?.length > 0) {
        categoryType = CompareType.COST_MAT_CAT_LVL2_CODE
      }

      if (filter?.costMatCatLvl3Codes?.length > 0) {
        categoryType = CompareType.COST_MAT_CAT_LVL3_CODE
      }

      const params = { ...filter, type: categoryType }
      getList(params as AnyType)
    }
  }, [getList, filter])

  return (
    <CustomCard
      icon={<img src={DataMonitorImg} alt="DataMonitor" />}
      title={intl.get('成本趋势对比')}
    >
      {!isChartEmpty && <BarChart result={result} />}
      <div className="mt-10">
        <BaselineTable result={result} baseline="" maxHeight={TABLE_LINE_HEIGHT * 5} />
      </div>
    </CustomCard>
  )
}

export default BigCost
