import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import CategoryCompare from '../index'

// 模拟BigCost组件
vi.mock('../big-cost', () => ({
  default: vi.fn(() => <div data-testid="big-cost">BigCost组件</div>),
}))

describe('CategoryCompare组件', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该成功渲染主要组件', () => {
    render(<CategoryCompare />)

    // 检查BigCost组件是否渲染
    const bigCostElement = screen.getByTestId('big-cost')
    expect(bigCostElement).toBeInTheDocument()
    expect(bigCostElement.textContent).toBe('BigCost组件')
  })

  it('应该使用Fragment作为根元素包装', () => {
    const { container } = render(<CategoryCompare />)

    // Fragment不会创建额外的DOM节点
    expect(container.firstChild).toBe(screen.getByTestId('big-cost'))
  })

  it('应该不包含任何CSS类或样式', () => {
    const { container } = render(<CategoryCompare />)

    // 验证根节点没有额外的包装元素
    expect(container.children).toHaveLength(1)
  })

  it('应该正确导入所需的依赖', () => {
    // 验证组件能够正常实例化
    expect(() => <CategoryCompare />).not.toThrow()
  })

  it('应该在组件卸载时不产生内存泄漏', () => {
    const { unmount } = render(<CategoryCompare />)

    expect(() => unmount()).not.toThrow()
  })

  it('应该在多次渲染时保持一致性', () => {
    const { rerender } = render(<CategoryCompare />)

    expect(screen.getByTestId('big-cost')).toBeInTheDocument()

    rerender(<CategoryCompare />)

    expect(screen.getByTestId('big-cost')).toBeInTheDocument()
  })

  it('应该处理空属性情况', () => {
    const props = {}
    expect(() => render(<CategoryCompare {...props} />)).not.toThrow()
  })

  it('应该处理undefined属性情况', () => {
    const props = {}
    expect(() => render(<CategoryCompare {...props} />)).not.toThrow()
  })

  it('应该确保组件类型为函数组件', () => {
    expect(typeof CategoryCompare).toBe('function')
    expect(CategoryCompare.prototype?.render).toBeUndefined()
  })

  it('应该正确处理React开发工具显示名称', () => {
    const component = CategoryCompare as AnyType
    expect(component.displayName || component.name).toBeTruthy()
  })
})
