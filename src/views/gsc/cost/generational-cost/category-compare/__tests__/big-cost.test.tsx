import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import BigCost from '../big-cost'

// 使用vi.hoisted来避免变量提升问题
const { mockRun, mockUseRequest, mockUseGenerationalCost, mockGetProjectCompareData } = vi.hoisted(
  () => ({
    mockRun: vi.fn(),
    mockUseRequest: vi.fn(),
    mockUseGenerationalCost: vi.fn(),
    mockGetProjectCompareData: vi.fn(),
  })
)

// 模拟所有外部依赖
vi.mock('@/hooks/useGenerationalCost', () => ({
  useGenerationalCost: mockUseGenerationalCost,
}))

vi.mock('ahooks', () => ({
  useRequest: mockUseRequest,
}))

vi.mock('@/api/cost/generational-cost', () => ({
  getProjectCompareData: mockGetProjectCompareData,
}))

vi.mock('react-intl-universal', () => ({
  default: {
    get: vi.fn((key: string) => key),
  },
}))

vi.mock('@/constants', () => ({
  IMG_PREFIX: 'https://test.com/',
}))

// 模拟G2图表库以避免ES模块兼容性问题
vi.mock('@antv/g2', () => ({
  Chart: vi.fn().mockImplementation(() => ({
    render: vi.fn(),
    destroy: vi.fn(),
    clear: vi.fn(),
    data: vi.fn(),
    scale: vi.fn(),
    interval: vi.fn(() => ({
      position: vi.fn(() => ({
        size: vi.fn(() => ({
          color: vi.fn(),
        })),
      })),
    })),
    axis: vi.fn(),
    tooltip: vi.fn(),
    interaction: vi.fn(),
    legend: vi.fn(),
    annotation: vi.fn(() => ({
      clear: vi.fn(),
      text: vi.fn(),
    })),
  })),
}))

// 不模拟custom-card，让它使用真实组件
// vi.mock('../custom-card', () => ({ ... }))

vi.mock('../baseline-table', () => ({
  default: vi.fn((props) => (
    <div data-testid="baseline-table" data-props={JSON.stringify(props)}>
      BaselineTable组件
    </div>
  )),
}))

vi.mock('./bar-chart', () => ({
  default: vi.fn((props) => (
    <div data-testid="bar-chart" data-props={JSON.stringify(props)}>
      BarChart组件
    </div>
  )),
}))

vi.mock('../config', () => ({
  TABLE_LINE_HEIGHT: 40,
  CompareType: {
    COST_MAT_CAT_LVL1_CODE: 'costMatCatLvl1Code',
    COST_MAT_CAT_LVL2_CODE: 'costMatCatLvl2Code',
    COST_MAT_CAT_LVL3_CODE: 'costMatCatLvl3Code',
  },
}))

describe('BigCost组件', () => {
  const mockFilter = {
    pageType: 'test',
    costMatCatLvl1Codes: [] as string[],
    costMatCatLvl2Codes: [] as string[],
    costMatCatLvl3Codes: [] as string[],
    configs: ['测试配置'],
    saleSites: ['测试站点'],
  }

  beforeEach(() => {
    vi.clearAllMocks()

    // 设置默认mock返回值
    mockUseRequest.mockReturnValue({
      run: mockRun,
      loading: false,
      error: null,
      data: null,
    })

    mockUseGenerationalCost.mockReturnValue({
      filter: mockFilter,
    })
  })

  it('应该处理API错误情况', () => {
    mockUseRequest.mockReturnValue({
      run: mockRun,
      loading: false,
      error: 'API错误',
      data: null,
    })

    expect(() => render(<BigCost />)).not.toThrow()
  })

  it('应该处理加载状态', () => {
    mockUseRequest.mockReturnValue({
      run: mockRun,
      loading: true,
      error: null,
      data: null,
    })

    expect(() => render(<BigCost />)).not.toThrow()
  })

  it('应该在组件卸载时不产生错误', () => {
    const { unmount } = render(<BigCost />)

    expect(() => unmount()).not.toThrow()
  })

  it('应该正确处理多层级分类代码优先级', async () => {
    const filterWithAllLevels = {
      ...mockFilter,
      costMatCatLvl1Codes: ['lvl1'],
      costMatCatLvl2Codes: ['lvl2'],
      costMatCatLvl3Codes: ['lvl3'],
    }

    mockUseGenerationalCost.mockReturnValue({
      filter: filterWithAllLevels,
    })

    render(<BigCost />)

    // 应该使用最高优先级的lvl3
    await waitFor(() => {
      expect(mockRun).toHaveBeenCalledWith({
        ...filterWithAllLevels,
        type: 'costMatCatLvl3Code',
      })
    })
  })

  it('应该处理空数组分类代码', async () => {
    const filterWithEmptyArrays = {
      ...mockFilter,
      costMatCatLvl1Codes: [],
      costMatCatLvl2Codes: [],
      costMatCatLvl3Codes: [],
    }

    mockUseGenerationalCost.mockReturnValue({
      filter: filterWithEmptyArrays,
    })

    render(<BigCost />)

    // 应该使用默认的lvl1
    await waitFor(() => {
      expect(mockRun).toHaveBeenCalledWith({
        ...filterWithEmptyArrays,
        type: 'costMatCatLvl1Code',
      })
    })
  })

  it('应该正确处理null值情况', () => {
    mockUseGenerationalCost.mockReturnValue({
      filter: null,
    })

    expect(() => render(<BigCost />)).not.toThrow()
  })

  it('应该正确处理undefined值情况', () => {
    mockUseGenerationalCost.mockReturnValue({
      filter: undefined,
    })

    expect(() => render(<BigCost />)).not.toThrow()
  })

  it('应该正确处理缺失costMatCatLvl属性的情况', async () => {
    const filterWithoutCostMatCat = {
      pageType: 'test',
      configs: ['测试配置'],
      saleSites: ['测试站点'],
    }

    mockUseGenerationalCost.mockReturnValue({
      filter: filterWithoutCostMatCat,
    })

    render(<BigCost />)

    // 应该使用默认的lvl1
    await waitFor(() => {
      expect(mockRun).toHaveBeenCalledWith({
        ...filterWithoutCostMatCat,
        type: 'costMatCatLvl1Code',
      })
    })
  })

  it('应该正确处理filter变化导致的重新渲染', () => {
    const { rerender, container } = render(<BigCost />)

    // 改变filter
    const newFilter = {
      ...mockFilter,
      pageType: 'new-test',
    }

    mockUseGenerationalCost.mockReturnValue({
      filter: newFilter,
    })

    rerender(<BigCost />)

    expect(container.querySelector('.custom-card__wrapper')).toBeInTheDocument()
  })

  it('应该正确处理IMG_PREFIX常量', () => {
    render(<BigCost />)

    const img = screen.getByAltText('DataMonitor')
    expect(img).toHaveAttribute('src', expect.stringContaining('https://test.com/'))
  })

  it('应该正确处理intl.get国际化', () => {
    render(<BigCost />)

    expect(screen.getByText('成本趋势对比')).toBeInTheDocument()
  })
})
