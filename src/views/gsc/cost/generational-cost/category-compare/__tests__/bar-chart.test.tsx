import React from 'react'
import { render } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import BarChart from '../bar-chart'

// 使用vi.hoisted来避免变量提升问题
const {
  mockRender,
  mockData,
  mockScale,
  mockClear,
  mockText,
  mockChart,
  mockFilter,
  mockUseGenerationalCost,
} = vi.hoisted(() => {
  const mockRender = vi.fn()
  const mockData = vi.fn()
  const mockScale = vi.fn()
  const mockClear = vi.fn()
  const mockText = vi.fn()
  const mockInterval = vi.fn(() => ({
    position: vi.fn(() => ({
      size: vi.fn(() => ({
        color: vi.fn(),
      })),
    })),
  }))
  const mockAxis = vi.fn()
  const mockTooltip = vi.fn()
  const mockInteraction = vi.fn()
  const mockLegend = vi.fn()
  const mockAnnotation = vi.fn(() => ({
    clear: mockClear,
    text: mockText,
  }))

  const mockChart = {
    render: mockRender,
    data: mockData,
    scale: mockScale,
    interval: mockInterval,
    axis: mockAxis,
    tooltip: mockTooltip,
    interaction: mockInteraction,
    legend: mockLegend,
    annotation: mockAnnotation,
  }

  const mockFilter = {
    configs: ['测试配置'],
    saleSites: ['测试站点'],
  }

  const mockUseGenerationalCost = vi.fn(() => ({
    filter: mockFilter,
  }))

  return {
    mockRender,
    mockData,
    mockScale,
    mockClear,
    mockText,
    mockInterval,
    mockAxis,
    mockTooltip,
    mockInteraction,
    mockLegend,
    mockAnnotation,
    mockChart,
    mockFilter,
    mockUseGenerationalCost,
  }
})

// 模拟G2库
vi.mock('@antv/g2', () => ({
  Chart: vi.fn(() => mockChart),
}))

vi.mock('@/hooks/useGenerationalCost', () => ({
  useGenerationalCost: mockUseGenerationalCost,
}))

vi.mock('../config', () => ({
  BAR_COLOR: '#1890ff',
  CHART_AXIS_COLOR: '#d9d9d9',
  CHART_HEIGHT: 400,
  CHART_TEXT_COLOR: '#595959',
  COMMON_CHART_TOOLTIP_CONFIG: {
    showTitle: false,
    showMarkers: false,
  },
  COST_DECIMALS: 2,
}))

vi.mock('@/utils/number', () => ({
  roundToDecimal: vi.fn((num, decimals) => Number(num.toFixed(decimals))),
}))

describe('BarChart组件', () => {
  const mockResult = {
    columnList: [
      { key: 'costMatCatLvl3Code', value: '测试三级分类' },
      { key: 'costMatCatLvl2Code', value: '测试二级分类' },
      { key: 'costMatCatLvl1Code', value: '测试一级分类' },
      { key: 'project1', value: '项目1' },
      { key: 'project2', value: '项目2' },
    ],
    rowPage: {
      data: [
        {
          project1: 1000,
          project2: 2000,
        },
      ],
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()

    // 重置所有mock函数
    mockRender.mockClear()
    mockData.mockClear()
    mockScale.mockClear()
    mockClear.mockClear()
    mockText.mockClear()

    mockUseGenerationalCost.mockReturnValue({
      filter: mockFilter,
    })
  })

  it('应该正确处理数据并生成图表数据', () => {
    render(<BarChart result={mockResult} />)

    // 图表应该会被调用来渲染数据
    expect(mockData).toHaveBeenCalled()
  })

  it('应该正确处理空结果', () => {
    const emptyResult = null

    expect(() => render(<BarChart result={emptyResult} />)).not.toThrow()
  })

  it('应该正确处理空数据', () => {
    const emptyDataResult = {
      columnList: [],
      rowPage: {
        data: [],
      },
    }

    expect(() => render(<BarChart result={emptyDataResult} />)).not.toThrow()
  })

  it('应该正确处理缺失rowPage的结果', () => {
    const resultWithoutRowPage = {
      columnList: [{ key: 'project1', value: '项目1' }],
    }

    expect(() => render(<BarChart result={resultWithoutRowPage} />)).not.toThrow()
  })

  it('应该正确处理缺失columnList的结果', () => {
    const resultWithoutColumnList = {
      rowPage: {
        data: [{ project1: 1000 }],
      },
    }

    expect(() => render(<BarChart result={resultWithoutColumnList} />)).not.toThrow()
  })

  it('应该正确处理数字值为0的情况', () => {
    const resultWithZero = {
      columnList: [
        { key: 'costMatCatLvl1Code', value: '测试一级分类' },
        { key: 'project1', value: '项目1' },
      ],
      rowPage: {
        data: [{ project1: 0 }],
      },
    }

    expect(() => render(<BarChart result={resultWithZero} />)).not.toThrow()
  })

  it('应该正确处理数字值为null的情况', () => {
    const resultWithNull = {
      columnList: [
        { key: 'costMatCatLvl1Code', value: '测试一级分类' },
        { key: 'project1', value: '项目1' },
      ],
      rowPage: {
        data: [{ project1: null }],
      },
    }

    expect(() => render(<BarChart result={resultWithNull} />)).not.toThrow()
  })

  it('应该正确处理数字值为非数字的情况', () => {
    const resultWithNonNumber = {
      columnList: [
        { key: 'costMatCatLvl1Code', value: '测试一级分类' },
        { key: 'project1', value: '项目1' },
      ],
      rowPage: {
        data: [{ project1: 'not a number' }],
      },
    }

    expect(() => render(<BarChart result={resultWithNonNumber} />)).not.toThrow()
  })

  it('应该正确处理filter变化', () => {
    const { rerender } = render(<BarChart result={mockResult} />)

    const newFilter = {
      configs: ['新配置'],
      saleSites: ['新站点'],
    }

    mockUseGenerationalCost.mockReturnValue({
      filter: newFilter,
    })

    rerender(<BarChart result={mockResult} />)

    expect(mockScale).toHaveBeenCalled()
  })

  it('应该正确处理组件卸载', () => {
    const { unmount } = render(<BarChart result={mockResult} />)

    expect(() => unmount()).not.toThrow()
  })

  it('应该正确处理result变化', () => {
    const { rerender } = render(<BarChart result={mockResult} />)

    const newResult = {
      columnList: [
        { key: 'costMatCatLvl1Code', value: '新分类' },
        { key: 'newProject', value: '新项目' },
      ],
      rowPage: {
        data: [{ newProject: 3000 }],
      },
    }

    rerender(<BarChart result={newResult} />)

    // 数据应该被更新
    expect(mockData).toHaveBeenCalled()
  })

  it('应该正确找到finalIndex', () => {
    const resultWithMultipleLevels = {
      columnList: [
        { key: 'costMatCatLvl3Code', value: '三级' },
        { key: 'costMatCatLvl2Code', value: '二级' },
        { key: 'costMatCatLvl1Code', value: '一级' },
        { key: 'project1', value: '项目1' },
      ],
      rowPage: {
        data: [{ project1: 1000 }],
      },
    }

    expect(() => render(<BarChart result={resultWithMultipleLevels} />)).not.toThrow()
  })

  it('应该正确处理只有一级分类的情况', () => {
    const resultWithOnlyLvl1 = {
      columnList: [
        { key: 'costMatCatLvl1Code', value: '一级' },
        { key: 'project1', value: '项目1' },
      ],
      rowPage: {
        data: [{ project1: 1000 }],
      },
    }

    expect(() => render(<BarChart result={resultWithOnlyLvl1} />)).not.toThrow()
  })

  it('应该正确处理没有分类代码的情况', () => {
    const resultWithoutCatCode = {
      columnList: [
        { key: 'project1', value: '项目1' },
        { key: 'project2', value: '项目2' },
      ],
      rowPage: {
        data: [{ project1: 1000, project2: 2000 }],
      },
    }

    expect(() => render(<BarChart result={resultWithoutCatCode} />)).not.toThrow()
  })

  it('应该正确处理边界情况：空字符串值', () => {
    const resultWithEmptyString = {
      columnList: [
        { key: 'costMatCatLvl1Code', value: '' },
        { key: 'project1', value: '项目1' },
      ],
      rowPage: {
        data: [{ project1: 1000 }],
      },
    }

    expect(() => render(<BarChart result={resultWithEmptyString} />)).not.toThrow()
  })

  it('应该正确处理边界情况：undefined值', () => {
    const resultWithUndefined = {
      columnList: [
        { key: 'costMatCatLvl1Code', value: undefined },
        { key: 'project1', value: '项目1' },
      ],
      rowPage: {
        data: [{ project1: undefined }],
      },
    }

    expect(() => render(<BarChart result={resultWithUndefined} />)).not.toThrow()
  })

  it('应该正确处理圆形引用', () => {
    const circularResult: AnyType = {
      columnList: [{ key: 'project1', value: '项目1' }],
      rowPage: {
        data: [{ project1: 1000 }],
      },
    }

    // 创建圆形引用
    circularResult.self = circularResult

    expect(() => render(<BarChart result={circularResult} />)).not.toThrow()
  })

  it('应该正确处理深层嵌套的数据结构', () => {
    const deepResult = {
      columnList: [
        {
          key: 'project1',
          value: '项目1',
          nested: {
            deep: {
              value: 'test',
            },
          },
        },
      ],
      rowPage: {
        data: [
          {
            project1: 1000,
            nested: {
              value: 'test',
            },
          },
        ],
      },
    }

    expect(() => render(<BarChart result={deepResult} />)).not.toThrow()
  })

  it('应该正确处理filter为null的情况', () => {
    mockUseGenerationalCost.mockReturnValue({
      filter: null as AnyType,
    })

    expect(() => render(<BarChart result={mockResult} />)).not.toThrow()
  })

  it('应该正确处理filter为undefined的情况', () => {
    mockUseGenerationalCost.mockReturnValue({
      filter: undefined as AnyType,
    })

    expect(() => render(<BarChart result={mockResult} />)).not.toThrow()
  })

  it('应该正确处理filter缺失configs的情况', () => {
    mockUseGenerationalCost.mockReturnValue({
      filter: {
        configs: ['配置'],
        saleSites: ['站点'],
      },
    })

    expect(() => render(<BarChart result={mockResult} />)).not.toThrow()
  })

  it('应该正确处理filter缺失saleSites的情况', () => {
    mockUseGenerationalCost.mockReturnValue({
      filter: {
        configs: ['配置'],
        saleSites: ['站点'],
      },
    })

    expect(() => render(<BarChart result={mockResult} />)).not.toThrow()
  })
})

// 单独测试数字缩写函数
describe('abbreviateNumber函数行为测试', () => {
  // 由于abbreviateNumber函数在组件内部，我们通过组件行为来测试

  it('应该正确处理0值', () => {
    const result = {
      columnList: [
        { key: 'costMatCatLvl1Code', value: '分类' },
        { key: 'project1', value: '项目1' },
      ],
      rowPage: {
        data: [{ project1: 0 }],
      },
    }

    expect(() => render(<BarChart result={result} />)).not.toThrow()
  })

  it('应该正确处理大数值', () => {
    const result = {
      columnList: [
        { key: 'costMatCatLvl1Code', value: '分类' },
        { key: 'project1', value: '项目1' },
      ],
      rowPage: {
        data: [{ project1: 1000000 }],
      },
    }

    expect(() => render(<BarChart result={result} />)).not.toThrow()
  })

  it('应该正确处理负数', () => {
    const result = {
      columnList: [
        { key: 'costMatCatLvl1Code', value: '分类' },
        { key: 'project1', value: '项目1' },
      ],
      rowPage: {
        data: [{ project1: -1000 }],
      },
    }

    expect(() => render(<BarChart result={result} />)).not.toThrow()
  })

  it('应该正确处理小数', () => {
    const result = {
      columnList: [
        { key: 'costMatCatLvl1Code', value: '分类' },
        { key: 'project1', value: '项目1' },
      ],
      rowPage: {
        data: [{ project1: 123.456 }],
      },
    }

    expect(() => render(<BarChart result={result} />)).not.toThrow()
  })

  it('应该正确处理NaN值', () => {
    const result = {
      columnList: [
        { key: 'costMatCatLvl1Code', value: '分类' },
        { key: 'project1', value: '项目1' },
      ],
      rowPage: {
        data: [{ project1: NaN }],
      },
    }

    expect(() => render(<BarChart result={result} />)).not.toThrow()
  })

  it('应该正确处理Infinity值', () => {
    const result = {
      columnList: [
        { key: 'costMatCatLvl1Code', value: '分类' },
        { key: 'project1', value: '项目1' },
      ],
      rowPage: {
        data: [{ project1: Infinity }],
      },
    }

    expect(() => render(<BarChart result={result} />)).not.toThrow()
  })
})
