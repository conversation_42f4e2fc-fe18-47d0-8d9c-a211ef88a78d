.generational-cost__form {
  display: flex;

  .form-line__description {
    background-color: #fff;
    border-radius: 6px 0 0;
    padding: 12px 0 0 16px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-end;
    gap: 12px;
    overflow: hidden;

    .description-item {
      color: #303030;
      font-size: 14px;
      font-weight: 500;
      line-height: 32px;
      width: max-content;
    }

    .description-item.description-item--hidden {
      color: transparent;
    }
  }

  .generational-cost__container {
    display: flex;
    overflow: hidden;
    background-color: #fff;
    padding: 12px 16px 0;
    border-radius: 6px 0 0 6px;
    width: 100%;

    .hi-v4-form {
      flex: 1;
      width: 0;
      min-width: 0;

      .hi-v4-form-label {
        width: 100%;
        margin-bottom: 12px;

        .hi-v4-form-label__control {
          width: 100%;
        }
      }
    }
  }

  .form-button__container {
    background-color: #fff;
    padding: 12px 16px 12px 4px;
    border-radius: 0 6px 6px 0;
  }
}
