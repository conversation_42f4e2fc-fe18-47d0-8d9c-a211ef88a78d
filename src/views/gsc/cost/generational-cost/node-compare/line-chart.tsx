import { getNodeCompareData } from '@/api/cost/generational-cost'
import { useGenerationalCost } from '@/hooks/useGenerationalCost'
import { Chart } from '@antv/g2'
import { useRequest } from 'ahooks'
import React, { FC, useEffect, useRef, useState } from 'react'
import {
  CHART_HEIGHT,
  CHART_TEXT_COLOR,
  COMMON_CHART_TOOLTIP_CONFIG,
  CompareType,
  COST_DECIMALS,
  getCommonChartConfig,
} from '../config'
import { roundToDecimal } from '@/utils/number'
import { omit } from 'lodash'

const colors = ['#14CA64', '#237FFA', '#FFC540', '#BE8CF1', '#FF7A75']
const X_KEY = 'node'
const Y_KEY = 'cost'
const TYPE = 'type'
const INIT_BASELINE = 'Charter'
const ALL_LIFE_CYCLE_KEY = 'lifecycleCost'

const LineChart: FC = () => {
  const { filter, setBaseline } = useGenerationalCost()
  const domRef = useRef<HTMLDivElement>(null)
  const chartRef = useRef<AnyType>(null)
  const [chartData, setChartData] = useState<AnyType[]>([])

  useEffect(() => {
    setBaseline(filter?.phaseStages?.[0] || INIT_BASELINE)
  }, [filter?.phaseStages, setBaseline])

  const { run: getList } = useRequest(getNodeCompareData, {
    manual: true,
    onSuccess: (res) => {
      const { columnList = [], rowPage } = res?.data || {}
      const { data = [] } = rowPage || {}
      const charterIndex = columnList?.findIndex((item) => item?.value === INIT_BASELINE)
      const nodeList = [] as AnyType[]

      columnList.forEach((item, index) => {
        if (index >= charterIndex && item.key !== ALL_LIFE_CYCLE_KEY) {
          nodeList.push(item)
        }
      })

      const newChartData = data.reduce((a, b) => {
        nodeList.forEach((item) => {
          a.push({
            [TYPE]: `${b.projectName} ${b.config}（${b.saleSite}）`,
            [X_KEY]: item.value,
            [Y_KEY]:
              b[item.key] === 0
                ? 0
                : b[item.key]
                  ? roundToDecimal(b[item.key], COST_DECIMALS)
                  : null,
          })
        })
        return a
      }, [])

      setChartData(newChartData)
    },
  })

  useEffect(() => {
    let newFilter = { ...filter, type: CompareType.PROJECT_NAME }
    newFilter = omit(newFilter, [
      'costMatCatLvl1Codes',
      'costMatCatLvl2Codes',
      'costMatCatLvl3Codes',
    ]) as AnyType
    getList(newFilter as AnyType)
  }, [getList, filter])

  useEffect(() => {
    if (!chartRef.current && domRef.current) {
      chartRef.current = new Chart({
        container: domRef.current,
        autoFit: true,
        height: CHART_HEIGHT,
      })
      chartRef.current.scale({
        [X_KEY]: {
          nice: true,
        },
        [Y_KEY]: {
          nice: true,
          min: 0,
        },
      })

      chartRef.current.tooltip({
        ...COMMON_CHART_TOOLTIP_CONFIG,
        showCrosshairs: true,
        shared: true,
      })
      chartRef.current.line().position(`${X_KEY}*${Y_KEY}`).color(TYPE, colors).shape('smooth')
      chartRef.current.axis(Y_KEY, getCommonChartConfig())
      chartRef.current.axis(X_KEY, getCommonChartConfig(1.5))
      chartRef.current.legend({
        position: 'top',
        marker: { symbol: 'hyphen' },
        itemName: {
          style: {
            fill: CHART_TEXT_COLOR,
          },
        },
      })
    }
  }, [])

  useEffect(() => {
    if (chartRef.current) {
      chartRef.current.data(chartData)
      chartRef.current.render()
    }
  }, [chartData])

  return <div ref={domRef}></div>
}

export default LineChart
