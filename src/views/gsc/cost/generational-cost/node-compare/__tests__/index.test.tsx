import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import NodeCompare from '../index'

// 由于需要hoisting，先移除模拟变量的声明
// Mock API - 需要在导入前模拟
vi.mock('@/api/cost/generational-cost', () => ({
  getNodeCompareData: vi.fn().mockName('getNodeCompareData'),
}))

// Mock react-intl-universal
vi.mock('react-intl-universal', () => ({
  default: {
    get: (key: string) => key,
    getHTML: (key: string) => key,
  },
}))

// Mock 子组件
vi.mock('../overall-cost', () => ({
  default: () => <div data-testid="overall-cost">OverallCost Component</div>,
}))

vi.mock('../../mix-cost', () => ({
  default: ({ api, normalFetch }: { api?: AnyType; normalFetch?: boolean }) => {
    // NodeCompare没有传递API，所以MixCost使用默认的getNodeCompareData
    // 由于没有传递API prop，这里的api应该是undefined
    const apiName = api ? 'unknown' : 'getNodeCompareData'

    return (
      <div data-testid="mix-cost" data-api={apiName} data-normal-fetch={normalFetch || true}>
        MixCost Component
      </div>
    )
  },
}))

describe('NodeCompare Component', () => {
  it('应该正确渲染组件', () => {
    render(<NodeCompare />)

    expect(screen.getByTestId('overall-cost')).toBeInTheDocument()
    expect(screen.getByTestId('mix-cost')).toBeInTheDocument()
  })

  it('应该渲染OverallCost和MixCost组件', () => {
    render(<NodeCompare />)

    const overallCost = screen.getByTestId('overall-cost')
    const mixCost = screen.getByTestId('mix-cost')

    expect(overallCost).toHaveTextContent('OverallCost Component')
    expect(mixCost).toHaveTextContent('MixCost Component')
  })

  it('应该向MixCost传递正确的props', () => {
    render(<NodeCompare />)

    const mixCost = screen.getByTestId('mix-cost')

    // NodeCompare没有传递API，所以MixCost使用默认的getNodeCompareData
    expect(mixCost).toHaveAttribute('data-api', 'getNodeCompareData')
    // NodeCompare没有传递normalFetch，所以使用默认值true
    expect(mixCost).toHaveAttribute('data-normal-fetch', 'true')
  })

  it('应该使用Fragment作为根元素', () => {
    const { container } = render(<NodeCompare />)

    // Fragment 不会在DOM中创建额外的元素
    expect(container.firstChild).toBe(screen.getByTestId('overall-cost'))
  })

  it('应该按正确顺序渲染组件', () => {
    const { container } = render(<NodeCompare />)

    const children = Array.from(container.childNodes)

    expect(children[0]).toHaveAttribute('data-testid', 'overall-cost')
    expect(children[1]).toHaveAttribute('data-testid', 'mix-cost')
  })
})
