import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import OverallCost from '../overall-cost'
import { GenerationalCostProvider } from '@/context/GenerationalCost'

// Mock react-intl-universal
vi.mock('react-intl-universal', () => ({
  default: {
    get: vi.fn((key) => key),
  },
}))

// Mock constants
vi.mock('@/constants', () => ({
  IMG_PREFIX: '/img/',
}))

// Mock ahooks
vi.mock('ahooks', () => ({
  useRequest: vi.fn(() => ({
    run: vi.fn(),
  })),
}))

// Mock API
vi.mock('@/api/cost/generational-cost', () => ({
  getNodeCompareData: vi.fn(),
}))

// Mock hooks
vi.mock('@/hooks/useGenerationalCost', () => ({
  useGenerationalCost: vi.fn(() => ({
    baseline: 'project1',
    setBaseline: vi.fn(),
    filter: {
      pageType: 'stageCompare',
    },
  })),
}))

// Mock lodash
vi.mock('lodash', () => ({
  omit: vi.fn((obj) => obj),
}))

// Mock config
vi.mock('../../config', () => ({
  CompareType: {
    PROJECT_NAME: 'projectName',
  },
  TABLE_LINE_HEIGHT: 41,
}))

// Mock child components
vi.mock('../../custom-card', () => ({
  default: ({ children, title }: AnyType) => (
    <div data-testid="custom-card" data-title={title}>
      {children}
    </div>
  ),
}))

vi.mock('../line-chart', () => ({
  default: () => <div data-testid="line-chart">LineChart Component</div>,
}))

vi.mock('../../baseline-table', () => ({
  default: () => <div data-testid="baseline-table">BaselineTable Component</div>,
}))

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <GenerationalCostProvider>{children}</GenerationalCostProvider>
)

describe('OverallCost Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染OverallCost组件', async () => {
    render(
      <TestWrapper>
        <OverallCost />
      </TestWrapper>
    )

    await waitFor(() => {
      expect(screen.getByTestId('custom-card')).toBeInTheDocument()
    })
  })

  it('应该在有数据时显示LineChart', async () => {
    render(
      <TestWrapper>
        <OverallCost />
      </TestWrapper>
    )

    await waitFor(() => {
      expect(screen.getByTestId('line-chart')).toBeInTheDocument()
    })
  })

  it('应该正确渲染BaselineTable', async () => {
    render(
      <TestWrapper>
        <OverallCost />
      </TestWrapper>
    )

    await waitFor(() => {
      const table = screen.getByTestId('baseline-table')
      expect(table).toBeInTheDocument()
    })
  })
})
