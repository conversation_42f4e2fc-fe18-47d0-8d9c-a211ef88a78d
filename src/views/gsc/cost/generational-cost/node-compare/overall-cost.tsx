import React, { FC, useEffect, useMemo, useState } from 'react'
import CustomCard from '../custom-card'
import intl from 'react-intl-universal'
import { IMG_PREFIX } from '@/constants'
import LineChart from './line-chart'
import BaselineTable from '../baseline-table'
import { useGenerationalCost } from '@/hooks/useGenerationalCost'
import { CompareType, TABLE_LINE_HEIGHT } from '../config'
import { useRequest } from 'ahooks'
import { getNodeCompareData } from '@/api/cost/generational-cost'
import { omit } from 'lodash'

const PieChartImg = `${IMG_PREFIX}Pie-chart.svg`

const OverallCost: FC = () => {
  const { baseline, setBaseline, filter } = useGenerationalCost()
  const [result, setResult] = useState<AnyType>(null)

  const isChartEmpty = useMemo(() => {
    return result?.rowPage?.data?.length === 0
  }, [result])

  const { run: getList } = useRequest(getNodeCompareData, {
    manual: true,
    onSuccess: (res) => {
      setResult(res?.data)
    },
  })

  useEffect(() => {
    if (filter?.pageType) {
      let newFilter = { ...filter, type: CompareType.PROJECT_NAME }

      newFilter = omit(newFilter, [
        'costMatCatLvl1Codes',
        'costMatCatLvl2Codes',
        'costMatCatLvl3Codes',
      ]) as AnyType

      getList(newFilter as AnyType)
    }
  }, [getList, filter])

  return (
    <CustomCard icon={<img src={PieChartImg} alt="PieChart" />} title={intl.get('成本对比')}>
      {!isChartEmpty && <LineChart />}
      <div className="mt-10">
        <BaselineTable
          result={result}
          baseline={baseline}
          setBaseline={setBaseline}
          maxHeight={TABLE_LINE_HEIGHT * 5}
        />
      </div>
    </CustomCard>
  )
}

export default OverallCost
