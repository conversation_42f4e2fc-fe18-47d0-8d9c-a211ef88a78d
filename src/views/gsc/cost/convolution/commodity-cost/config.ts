import dayjs from 'dayjs'

/**
 * 默认查询条件
 */
export const DEFAULT_COMMODITY_COST_CONDITIONS = {
  projectNames: [],
  configs: [],
  regions: [],
  bizNames: [],
  sopCodes: [],
  periods: [dayjs().format('YYYYMM'), dayjs().add(11, 'month').format('YYYYMM')],
  currency: 'CNY',
  goodsCostVersion: '',
  materialCostVersion: '',
  mdsPlanVersions: [],
  psiVersions: [],
  productCostVersions: [],
  materialCostVersions: [],
}

/**
 * 货币单位映射
 */
export const CURRENCY_MAP = {
  CNY: '人民币',
  USD: '美元',
  EUR: '欧元',
  GBP: '英镑',
}

/**
 * 最新版本缓存key
 */
export const COMMODITY_COST_NEWEST_VERSION = 'commodityCostNewestVersion'

/**
 * 最新版本缓存key
 */
export const MATERIAL_COST_NEWEST_VERSION = 'materialCostNewestVersion'

export const COMMODITY_COST_FUNCTION_CODE = 'new_project_cost_rolling_view'
