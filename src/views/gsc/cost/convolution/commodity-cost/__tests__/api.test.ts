import { describe, it, expect, vi, beforeEach } from 'vitest'
import * as requestModule from '@/utils/request'
import {
  fetchGoodsCostAggCondition,
  fetchGoodsCostAggList,
  exportGoodsCostAgg,
  fetchGoodsCostCondition,
  fetchGoodsCostList,
  exportGoodsCostData,
  fetchProductCostCondition,
  fetchProductCostList,
  exportProductCostData,
  dynamicApiConfig,
} from '../api'

// Mock request module
vi.mock('@/utils/request', () => ({
  request: vi.fn(),
}))

describe('commodity-cost API模块', () => {
  const mockParams = {
    projectNames: ['项目A'],
    configs: ['配置1'],
    regions: ['区域1'],
    currency: 'CNY',
    pageNum: 1,
    pageSize: 10,
  }

  const mockResponse = {
    code: 2000,
    msg: 'success',
    success: true,
    data: {
      rowPage: {
        data: [{ id: 1, name: 'test' }],
        total: 100,
      },
      columnList: [],
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(requestModule.request).mockResolvedValue(mockResponse)
  })

  describe('商品成本聚合API', () => {
    it('fetchGoodsCostAggCondition 应该正确调用请求', async () => {
      await fetchGoodsCostAggCondition(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/new-project-cost-rolling/goodsCostAggCondition',
        method: 'POST',
        data: mockParams,
      })
    })

    it('fetchGoodsCostAggList 应该正确调用请求并格式化返回数据', async () => {
      const result = await fetchGoodsCostAggList(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/new-project-cost-rolling/goodsCostAggList',
        method: 'POST',
        data: mockParams,
      })

      expect(result).toEqual({
        list: [{ id: 1, name: 'test' }],
        total: 100,
        columnList: [],
      })
    })

    it('exportGoodsCostAgg 应该正确调用请求', async () => {
      await exportGoodsCostAgg(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/new-project-cost-rolling/goodsCostAggExport',
        method: 'POST',
        data: mockParams,
      })
    })
  })

  describe('商品成本API', () => {
    it('fetchGoodsCostCondition 应该正确调用请求', async () => {
      await fetchGoodsCostCondition(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/new-project-cost-rolling/goodsCostCondition',
        method: 'POST',
        data: mockParams,
      })
    })

    it('fetchGoodsCostList 应该正确调用请求并格式化返回数据', async () => {
      const result = await fetchGoodsCostList(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/new-project-cost-rolling/goodsCostList',
        method: 'POST',
        data: mockParams,
      })

      expect(result).toEqual({
        list: [{ id: 1, name: 'test' }],
        total: 100,
        columnList: [],
      })
    })

    it('exportGoodsCostData 应该正确调用请求', async () => {
      await exportGoodsCostData(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/new-project-cost-rolling/goodsCostExport',
        method: 'POST',
        data: mockParams,
      })
    })
  })

  describe('产品成本API', () => {
    it('fetchProductCostCondition 应该正确调用请求', async () => {
      await fetchProductCostCondition(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/new-project-cost-rolling/productCostCondition',
        method: 'POST',
        data: mockParams,
      })
    })

    it('fetchProductCostList 应该正确调用请求并格式化返回数据', async () => {
      const result = await fetchProductCostList(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/new-project-cost-rolling/productCostList',
        method: 'POST',
        data: mockParams,
      })

      expect(result).toEqual({
        list: [{ id: 1, name: 'test' }],
        total: 100,
        columnList: [],
      })
    })

    it('exportProductCostData 应该正确调用请求', async () => {
      await exportProductCostData(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/new-project-cost-rolling/productCostExport',
        method: 'POST',
        data: mockParams,
      })
    })
  })

  describe('dynamicApiConfig', () => {
    it('应该包含正确的API配置', () => {
      expect(dynamicApiConfig.goodsCost).toEqual({
        condition: fetchGoodsCostAggCondition,
        list: fetchGoodsCostAggList,
        export: exportGoodsCostAgg,
      })

      expect(dynamicApiConfig.materialCost).toEqual({
        condition: fetchGoodsCostCondition,
        list: fetchGoodsCostList,
        export: exportGoodsCostData,
      })

      expect(dynamicApiConfig.productCost).toEqual({
        condition: fetchProductCostCondition,
        list: fetchProductCostList,
        export: exportProductCostData,
      })
    })
  })
})
