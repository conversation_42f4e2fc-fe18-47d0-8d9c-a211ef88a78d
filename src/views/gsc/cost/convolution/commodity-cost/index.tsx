import React, { useState } from 'react'
import intl from 'react-intl-universal'
import dayjs from 'dayjs'
import { PageGeneratorConfig, OperationType } from '@/views/page-generator/types'
import {
  dynamicApiConfig,
  fetchGoodsCostAggCondition,
  fetchGoodsCostAggList,
  fetchProductCostCondition,
} from './api'
import { useGenerator } from '@/views/page-generator'
import {
  COMMODITY_COST_FUNCTION_CODE,
  COMMODITY_COST_NEWEST_VERSION,
  CURRENCY_MAP,
  DEFAULT_COMMODITY_COST_CONDITIONS,
  MATERIAL_COST_NEWEST_VERSION,
} from './config'
import { ellipses } from '@/components/ellipsis-tool'
import { useAsyncEffect } from 'ahooks'
import { implAsyncCache } from '@/utils/cache'
import MigrationPage from '@/views/gsc/pages/migration-view'
import { useAuth } from '@/hooks/useAuth'
import { absOptimized } from '@/utils/number'
import { CONVOLUTION_DIGITS } from '../config'

const CommodityCost: React.FC = () => {
  const { functionCodes } = useAuth()
  const isAuthed = functionCodes.includes(COMMODITY_COST_FUNCTION_CODE)

  const [newestGoodsVersion, setNewestGoodsVersion] = useState<string | null>(null)
  const [newestMaterialVersion, setNewestMaterialVersion] = useState<string | null>(null)

  useAsyncEffect(async () => {
    const goodsResult = await implAsyncCache({
      key: COMMODITY_COST_NEWEST_VERSION,
      api: fetchGoodsCostAggCondition,
      apiParams: {
        ...DEFAULT_COMMODITY_COST_CONDITIONS,
        field: 'goodsCostVersion',
      },
    })
    const materialResult = await implAsyncCache({
      key: MATERIAL_COST_NEWEST_VERSION,
      api: fetchProductCostCondition,
      apiParams: {
        ...DEFAULT_COMMODITY_COST_CONDITIONS,
        field: 'materialCostVersion',
      },
    })
    const newGoodsVersion = goodsResult?.data?.[0]?.value || null
    const newMaterialVersion = materialResult?.data?.[0]?.value || null
    setNewestGoodsVersion(newGoodsVersion)
    setNewestMaterialVersion(newMaterialVersion)
  }, [])

  const pageConfig: PageGeneratorConfig = {
    pageKey: 'commodityCost',
    pageExtra: null,
    formConfig: {
      conditionField: 'field',
      fetchConditionFormatter: (params) => {
        const { periods, ...rest } = params

        return {
          ...rest,
          periodStart: periods?.[0] ? dayjs(periods[0]).format('YYYYMM') : null,
          periodEnd: periods?.[1] ? dayjs(periods[1]).format('YYYYMM') : null,
        }
      },
      fieldNames: { id: 'value', title: 'name' },
      initValues: {
        ...DEFAULT_COMMODITY_COST_CONDITIONS,
        goodsCostVersion: newestGoodsVersion,
        materialCostVersion: newestMaterialVersion,
      },
      labelWidth: 110,
      hasGroupTitle: true,
      labelPlacement: 'right',
      showColon: false,
      colNum: 3,
      items: {
        [intl.get('项目信息')]: [
          {
            field: 'projectNames',
            label: intl.get('项目'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'configs',
            label: intl.get('配置'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'regions',
            label: intl.get('区域'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'bizNames',
            label: intl.get('业务线'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'sopCodes',
            label: 'SOP_CODE',
            formItemType: 'CheckSelect',
            extra: {
              optionWidth: 360,
              searchable: true,
              showCheckAll: true,
            },
          },
        ],
        [intl.get('基础选项')]: [
          {
            field: 'periods',
            label: ellipses(intl.get('成本预估区间'), true),
            formItemType: 'DatePicker',
            extra: {
              clearable: false,
              type: 'monthrange',
              format: 'YYYYMM',
            },
          },
          {
            field: 'currency',
            label: ellipses(intl.get('货币单位'), true),
            formItemType: 'Select',
            extra: {
              searchable: false,
              clearable: false,
              staticData: [
                { value: 'CNY', name: intl.get('人民币') },
                { value: 'USD', name: intl.get('美元') },
              ],
            },
          },
          {
            field: 'goodsCostVersion',
            label: ellipses(intl.get('商品成本版本'), true),
            formItemType: 'Select',
            extra: {
              clearable: false,
              searchable: true,
              initData: [
                {
                  value: newestGoodsVersion as string,
                  name: newestGoodsVersion as string,
                },
              ],
            },
          },
          {
            field: 'materialCostVersion',
            label: ellipses(intl.get('材料成本版本'), true),
            formItemType: 'Select',
            extra: {
              clearable: false,
              searchable: true,
              initData: [
                {
                  value: newestMaterialVersion as string,
                  name: newestMaterialVersion as string,
                },
              ],
            },
          },
          {
            field: 'materialCostVersions',
            label: ellipses(intl.get('材料成本版本')),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'mdsPlanVersions',
            label: ellipses(intl.get('MDS计划版本')),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'psiVersions',
            label: intl.get('合议PSI版本'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'productCostVersions',
            label: intl.get('生产成本版本'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
        ],
      },
    },
    tableConfig: {
      fieldKey: 'id',
      showAutoIndex: true,
      fetchListApi: fetchGoodsCostAggList,
      tools: {
        operations: [
          OperationType.COLUMN_FREEZE,
          OperationType.COLUMN_SETTING,
          OperationType.FULL_SCREEN,
        ],
        operationExtra: [],
        tab: {
          tabItems: [
            {
              tabId: 'goodsCost',
              tabTitle: intl.get('商品成本-全科目'),
            },
            {
              tabId: 'materialCost',
              tabTitle: intl.get('商品成本-原材料'),
            },
            {
              tabId: 'productCost',
              tabTitle: intl.get('材料成本-原材料'),
            },
          ],
          fieldMap: {
            goodsCost: ['mdsPlanVersions', 'materialCostVersion', 'materialCostVersions'],
            materialCost: ['mdsPlanVersions', 'materialCostVersion', 'productCostVersions'],
            productCost: ['goodsCostVersion', 'materialCostVersions'],
          },
        },
      },
      tableExtra: [],
      defaultPageNum: 1,
      defaultPageSize: 20,
      enhanceColumns: (cols) => {
        return cols.map((col) => {
          if (col.dataKey === 'currency') {
            return { ...col, render: (value) => CURRENCY_MAP[value] }
          } else if (['config', 'region', 'color'].includes(col.dataKey as string)) {
            return { ...col, render: (v) => v || '-' }
          } else if (
            [
              'goodsCost',
              'processingFee',
              'moldFee',
              'customsDutiesFee',
              'excessLossExpenses',
              'softwareAlgorithmFee',
              'otherPatentFee',
              'productCost',
            ].includes(col.dataKey as string)
          ) {
            return {
              ...col,
              width: 120,
              align: 'right',
              render: (v) => absOptimized(v, CONVOLUTION_DIGITS),
            }
          } else if (col.dataKey === 'exchangeRate') {
            return { ...col, width: 72, align: 'right' }
          } else if (col.dataKey === 'odmFee') {
            return { ...col, width: 170, align: 'right', render: (v) => absOptimized(v) }
          } else if (
            ['costMatCatLvl1Code', 'costMatCatLvl2Code', 'costMatCatLvl3Code'].includes(
              col.dataKey as string
            )
          ) {
            return { ...col, render: (v) => ellipses(v) }
          } else {
            return col
          }
        })
      },
    },
    dynamicApiConfig,
  }

  const { page } = useGenerator(pageConfig)

  if (!isAuthed) {
    return <MigrationPage desc={intl.get('暂无权限')} />
  }

  return newestGoodsVersion && newestMaterialVersion ? (
    page
  ) : (
    <MigrationPage desc={intl.get('没有商品成本版本或材料成本版本，请联系管理员')} />
  )
}

export default CommodityCost
