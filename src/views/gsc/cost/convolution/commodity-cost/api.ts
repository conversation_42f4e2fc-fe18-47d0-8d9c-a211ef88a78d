import { request } from '@/utils/request'
import { formatPaginationData } from '@/views/page-generator/utils/pagination'

const apiPrefix = '/new-project-cost-rolling'

export interface GoodsCostAggParams {
  /** 项目名称列表 */
  projectNames?: string[]
  /** 配置列表 */
  configs?: string[]
  /** 区域列表 */
  regions?: string[]
  /** 业务线列表 */
  bizNames?: string[]
  /** SOP代码列表 */
  sopCodes?: string[]
  /** 货币单位 */
  currency?: string
  /** 商品成本版本 */
  goodsCostVersion?: string
  /** 合议PSI版本列表 */
  psiVersions?: string[]
  /** 生产成本版本列表 */
  productionCostVersions?: string[]
  /** 成本预估开始周期 */
  periodStart?: string
  /** 成本预估结束周期 */
  periodEnd?: string
  /** 字段名 */
  field?: string
  /** 日期时间戳 */
  date?: number
  /** 用户名 */
  userName?: string
  /** 页码 */
  pageNum?: number
  /** 每页条数 */
  pageSize?: number
}

/**
 * 获取商品成本聚合条件选项
 * @param params 查询参数
 * @returns Promise 包含条件选项结果
 */
export async function fetchGoodsCostAggCondition(params: GoodsCostAggParams) {
  return request({
    url: `${apiPrefix}/goodsCostAggCondition`,
    method: 'POST',
    data: params,
  })
}

/**
 * 获取商品成本聚合列表数据
 * @param params 查询参数
 * @returns Promise 包含列表数据结果
 */
export async function fetchGoodsCostAggList(params: GoodsCostAggParams) {
  const res = await request({
    url: `${apiPrefix}/goodsCostAggList`,
    method: 'POST',
    data: params,
  })
  return formatPaginationData(res?.data)
}

/**
 * 导出商品成本聚合数据
 * @param params 导出参数
 * @returns Promise 包含导出文件链接
 */
export async function exportGoodsCostAgg(params: GoodsCostAggParams) {
  return request({
    url: `${apiPrefix}/goodsCostAggExport`,
    method: 'POST',
    data: params,
  })
}

/**
 * 获取商品成本条件选项
 * @param params 查询参数
 * @returns Promise 包含条件选项结果
 */
export async function fetchGoodsCostCondition(params: GoodsCostAggParams) {
  return request({
    url: `${apiPrefix}/goodsCostCondition`,
    method: 'POST',
    data: params,
  })
}

/**
 * 获取商品成本列表数据
 * @param params 查询参数
 * @returns Promise 包含列表数据结果
 */
export async function fetchGoodsCostList(params: GoodsCostAggParams) {
  const res = await request({
    url: `${apiPrefix}/goodsCostList`,
    method: 'POST',
    data: params,
  })
  return formatPaginationData(res?.data)
}

/**
 * 导出商品成本数据
 * @param params 导出参数
 * @returns Promise 包含导出文件链接
 */
export async function exportGoodsCostData(params: GoodsCostAggParams) {
  return request({
    url: `${apiPrefix}/goodsCostExport`,
    method: 'POST',
    data: params,
  })
}

/**
 * 获取商品成本条件选项
 * @param params 查询参数
 * @returns Promise 包含条件选项结果
 */
export async function fetchProductCostCondition(params: GoodsCostAggParams) {
  return request({
    url: `${apiPrefix}/productCostCondition`,
    method: 'POST',
    data: params,
  })
}

/**
 * 获取商品成本列表数据
 * @param params 查询参数
 * @returns Promise 包含列表数据结果
 */
export async function fetchProductCostList(params: GoodsCostAggParams) {
  const res = await request({
    url: `${apiPrefix}/productCostList`,
    method: 'POST',
    data: params,
  })
  return formatPaginationData(res?.data)
}

/**
 * 导出商品成本数据
 * @param params 导出参数
 * @returns Promise 包含导出文件链接
 */
export async function exportProductCostData(params: GoodsCostAggParams) {
  return request({
    url: `${apiPrefix}/productCostExport`,
    method: 'POST',
    data: params,
  })
}

export const dynamicApiConfig = {
  goodsCost: {
    condition: fetchGoodsCostAggCondition,
    list: fetchGoodsCostAggList,
    export: exportGoodsCostAgg,
  },
  materialCost: {
    condition: fetchGoodsCostCondition,
    list: fetchGoodsCostList,
    export: exportGoodsCostData,
  },
  productCost: {
    condition: fetchProductCostCondition,
    list: fetchProductCostList,
    export: exportProductCostData,
  },
}
