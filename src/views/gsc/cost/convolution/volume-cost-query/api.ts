import { request } from '@/utils/request'
import { formatPaginationData, PaginationResponse } from '@/views/page-generator/utils/pagination'

const apiPrefix = '/cost-rolling'

export interface CostRollingConditionParams {
  goodsCostVersions?: string[]
  psiVersions?: string[]
  productionCostVersions?: string[]
  goodsCostMin?: number
  goodsCostMax?: number
  field?: string
  date?: number
  projectNames?: string[]
  configs?: string[]
  regions?: string[]
  saleSites?: string[]
  bizNames?: string[]
  sopCodes?: string[]
  skus?: string[]
  currency?: string
  periods?: string[]
  userName?: string
  pageNum?: number
  pageSize?: number
}

export interface CostRollingListResponse {
  total: number
  list: CostRollingItem[]
}

export interface CostRollingItem {
  goodsCostVersion: string
  psiVersion: string
  productionCostVersion: string
  businessLine: string
  project: string
  config: string
  region: string
  color: string
  sku: string
  month: string
}

/**
 * 获取成本卷积条件
 * @param params 查询参数
 * @returns Promise 包含查询结果
 */
export async function fetchCostRollingCondition(params?: Partial<CostRollingConditionParams>) {
  return request({
    url: `${apiPrefix}/result/condition`,
    method: 'POST',
    data: params || {},
  })
}

/**
 * 获取成本卷积结果列表
 * @param params 查询参数
 * @returns Promise 包含查询结果
 */
export async function fetchCostRollingList(params: CostRollingConditionParams) {
  const res = await request<PaginationResponse>({
    url: `${apiPrefix}/result/list`,
    method: 'POST',
    data: params,
  })
  return formatPaginationData(res?.data)
}

/**
 * 导出表格
 * @param params 查询参数
 * @returns Promise 包含查询结果
 */
export async function exportVolumeCostTable(params: CostRollingConditionParams) {
  return request({
    url: `${apiPrefix}/result/export`,
    method: 'POST',
    data: params,
  })
}
