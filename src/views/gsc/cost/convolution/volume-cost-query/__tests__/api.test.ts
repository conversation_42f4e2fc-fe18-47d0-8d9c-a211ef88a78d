import { describe, it, expect, vi, beforeEach } from 'vitest'
import * as requestModule from '@/utils/request'
import { fetchCostRollingCondition, fetchCostRollingList, exportVolumeCostTable } from '../api'

// Mock request module
vi.mock('@/utils/request', () => ({
  request: vi.fn(),
}))

describe('volume-cost-query API模块', () => {
  const mockParams = {
    projectNames: ['项目A'],
    configs: ['配置1'],
    regions: ['区域1'],
    currency: 'CNY',
    goodsCostVersions: ['v1'],
    psiVersions: ['p1'],
    productionCostVersions: ['pc1'],
    pageNum: 1,
    pageSize: 10,
  }

  const mockResponse = {
    code: 2000,
    msg: 'success',
    success: true,
    data: {
      rowPage: {
        data: [{ id: 1, name: 'test' }],
        total: 100,
      },
      columnList: [],
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(requestModule.request).mockResolvedValue(mockResponse)
  })

  describe('fetchCostRollingCondition', () => {
    it('应该正确调用请求，带参数', async () => {
      await fetchCostRollingCondition(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/cost-rolling/result/condition',
        method: 'POST',
        data: mockParams,
      })
    })

    it('应该正确调用请求，不带参数', async () => {
      await fetchCostRollingCondition()

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/cost-rolling/result/condition',
        method: 'POST',
        data: {},
      })
    })
  })

  describe('fetchCostRollingList', () => {
    it('应该正确调用请求并格式化返回数据', async () => {
      const result = await fetchCostRollingList(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/cost-rolling/result/list',
        method: 'POST',
        data: mockParams,
      })

      expect(result).toEqual({
        list: [{ id: 1, name: 'test' }],
        total: 100,
        columnList: [],
      })
    })
  })

  describe('exportVolumeCostTable', () => {
    it('应该正确调用请求', async () => {
      await exportVolumeCostTable(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/cost-rolling/result/export',
        method: 'POST',
        data: mockParams,
      })
    })
  })
})
