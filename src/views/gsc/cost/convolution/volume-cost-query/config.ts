import dayjs from 'dayjs'
import intl from 'react-intl-universal'

export const CURRENCY_MAP = {
  CNY: intl.get('人民币'),
  USD: intl.get('美元'),
}

export const VOLUME_NEWEST_VERSION = 'volume_newest_version'

export const DEFAULT_VOLUME_CONDITIONS = {
  projectNames: [],
  configs: [],
  regions: [],
  bizNames: [],
  sopCodes: [],
  skus: [],
  periods: [dayjs().format('YYYYMM'), dayjs().add(11, 'month').format('YYYYMM')],
  currency: 'CNY',
  goodsCostVersion: '',
  psiVersions: [],
  productionCostVersions: [],
}
