import React, { useState } from 'react'
import intl from 'react-intl-universal'
import { PageGeneratorConfig, OperationType } from '@/views/page-generator/types'
import { exportVolumeCostTable, fetchCostRollingCondition, fetchCostRollingList } from './api'
import { useGenerator } from '@/views/page-generator'
import dayjs from 'dayjs'
import { TableColumnItem } from '@hi-ui/table'
import { CURRENCY_MAP, DEFAULT_VOLUME_CONDITIONS, VOLUME_NEWEST_VERSION } from './config'
import { customMessage } from '@/utils/custom-message'
import { ellipses } from '@/components/ellipsis-tool'
import { useAsyncEffect } from 'ahooks'
import { implAsyncCache } from '@/utils/cache'
import MigrationPage from '@/views/gsc/pages/migration-view'
import { absOptimized } from '@/utils/number'
import { CONVOLUTION_DIGITS } from '../config'

const VolumeCostQuery: React.FC = () => {
  const [newestVersion, setNewestVersion] = useState<string | null>(null)

  useAsyncEffect(async () => {
    const res = await implAsyncCache({
      key: VOLUME_NEWEST_VERSION,
      api: fetchCostRollingCondition,
      apiParams: {
        ...DEFAULT_VOLUME_CONDITIONS,
        field: 'goodsCostVersion',
      },
    })

    const newVersion = res?.data?.[0]?.value || null
    setNewestVersion(newVersion)
  }, [])

  const pageConfig: PageGeneratorConfig = {
    pageKey: 'volumeCostQuery',
    pageExtra: null,
    formConfig: {
      fetchConditionApi: fetchCostRollingCondition,
      conditionField: 'field',
      fetchConditionFormatter: (params) => {
        const { periods, ...rest } = params

        return {
          ...rest,
          periodStart: periods?.[0] ? dayjs(periods[0]).format('YYYYMM') : null,
          periodEnd: periods?.[1] ? dayjs(periods[1]).format('YYYYMM') : null,
        }
      },
      fieldNames: { id: 'value', title: 'name' },
      initValues: { ...DEFAULT_VOLUME_CONDITIONS, goodsCostVersion: newestVersion },
      labelWidth: 110,
      hasGroupTitle: true,
      filterKey: 'ROLLING_COST_QUERY',
      labelPlacement: 'right',
      showColon: false,
      colNum: 3,
      items: {
        [intl.get('项目信息')]: [
          {
            field: 'projectNames',
            label: intl.get('项目'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'configs',
            label: intl.get('配置'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'regions',
            label: intl.get('区域'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'bizNames',
            label: intl.get('业务线'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'sopCodes',
            label: 'SOP_CODE',
            formItemType: 'CheckSelect',
            extra: {
              optionWidth: 360,
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'skus',
            label: 'SKU',
            formItemType: 'CheckSelect',
            extra: {
              optionWidth: 200,
              searchable: true,
              showCheckAll: true,
            },
          },
        ],
        [intl.get('基础选项')]: [
          {
            field: 'periods',
            label: ellipses(intl.get('成本预估区间'), true),
            formItemType: 'DatePicker',
            extra: {
              clearable: false,
              type: 'monthrange',
              format: 'YYYYMM',
            },
          },
          {
            field: 'currency',
            label: ellipses(intl.get('货币单位'), true),
            formItemType: 'Select',
            extra: {
              searchable: false,
              clearable: false,
              staticData: [
                { value: 'CNY', name: intl.get('人民币') },
                { value: 'USD', name: intl.get('美元') },
              ],
            },
          },
          {
            field: 'goodsCostVersion',
            label: ellipses(intl.get('商品成本版本'), true),
            formItemType: 'Select',
            extra: {
              clearable: false,
              searchable: true,
              initData: [
                {
                  value: newestVersion as string,
                  name: newestVersion as string,
                },
              ],
            },
          },
          {
            field: 'psiVersions',
            label: intl.get('合议PSI版本'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'productionCostVersions',
            label: intl.get('生产成本版本'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
        ],
      },
    },
    tableConfig: {
      fieldKey: 'id',
      showAutoIndex: true,
      fetchListApi: fetchCostRollingList,
      tools: {
        operations: [
          OperationType.COLUMN_FREEZE,
          OperationType.COLUMN_SETTING,
          OperationType.FULL_SCREEN,
        ],
        handleTableExport: async (filter?: Record<string, AnyType>) => {
          const params = filter || {}
          const res = await exportVolumeCostTable({ ...params })
          if (res?.data) {
            window.open(res?.data)
            customMessage(intl.get('导出成功'), 'success')
          } else {
            customMessage(intl.get('导出失败'), 'error')
          }
        },
        operationExtra: [],
      },
      tableExtra: [],
      defaultPageNum: 1,
      defaultPageSize: 20,
      defaultLeftFreezeKey: 'productionCostVersion',
      enhanceColumns: (cols: TableColumnItem[]) => {
        return cols.map((col) => {
          if (col.dataKey === 'currency') {
            return { ...col, render: (value) => CURRENCY_MAP[value] }
          } else if (['config', 'region', 'color'].includes(col.dataKey as string)) {
            return { ...col, render: (v) => v || '-' }
          } else if (['goodsCost'].includes(col.dataKey as string)) {
            return {
              ...col,
              width: 120,
              align: 'right',
              render: (v) => absOptimized(v, CONVOLUTION_DIGITS),
            }
          } else if (col.dataKey === 'exchangeRate') {
            return { ...col, width: 72, align: 'right' }
          } else {
            return col
          }
        })
      },
      isRowSelection: false,
      patchOperate: () => {
        return []
      },
    },
  }
  const { page } = useGenerator(pageConfig)

  return newestVersion ? page : <MigrationPage desc={intl.get('没有商品成本版本，请联系管理员')} />
}

export default VolumeCostQuery
