import { request } from '@/utils/request'
import { formatPaginationData, PaginationResponse } from '@/views/page-generator/utils/pagination'

const apiPrefix = '/cost-rolling'

export interface CostRollingUploadParams {
  projectNames?: string[]
  configs?: string[]
  regions?: string[]
  saleSites?: string[]
  bizNames?: string[]
  sopCodes?: string[]
  skus?: string[]
  currency?: string
  periods?: string[]
  userName?: string
  pageNum?: number
  pageSize?: number
}

export interface CostRollingUploadResponse {
  total: number
  list: CostRollingUploadItem[]
}

export interface CostRollingUploadItem {
  period: string
  bizName: string
  project: string
  config: string
  region: string
  color: string
  sku: string
  stockQty: number
  stockAmount: number
  deleteStockAmount: number
}

/**
 * 获取成本上传列表
 * @param params 查询参数
 * @returns Promise 包含查询结果
 */
export async function fetchCostRollingUploadList(params: CostRollingUploadParams) {
  const res = await request<PaginationResponse>({
    url: `${apiPrefix}/upload/list`,
    method: 'POST',
    data: params,
  })
  return formatPaginationData(res?.data)
}

/**
 * 获取成本上传条件列表
 * @param params 查询参数
 * @returns Promise 包含查询结果
 */
export async function fetchCostRollingUploadCondition(params?: Partial<CostRollingUploadParams>) {
  return request({
    url: `${apiPrefix}/upload/condition`,
    method: 'POST',
    data: params || {},
  })
}

/**
 * 获取成本上传模板
 * @returns Promise 包含查询结果
 */
export async function getCostRollingTemplate() {
  return request({
    url: `${apiPrefix}/template`,
    method: 'GET',
  })
}

/**
 * 上传成本上传模板
 * @param params 上传参数
 * @returns Promise 包含查询结果
 */
export async function uploadRollingCost(params: { objectName: string }) {
  return request({
    url: `${apiPrefix}/upload`,
    method: 'POST',
    data: params,
  })
}

/**
 * 导出成本上传表格
 * @param params 查询参数
 * @returns Promise 包含查询结果
 */
export async function exportRollingTable(params: CostRollingUploadParams) {
  return request({
    url: `${apiPrefix}/upload/export`,
    method: 'POST',
    data: params,
  })
}

/**
 * 批量删除成本上传模板
 * @param params 删除参数
 * @returns Promise 包含查询结果
 */
export async function batchDeleteRollingCost(params: { ids: string[] }) {
  return request({
    url: `${apiPrefix}/upload/delete`,
    method: 'POST',
    data: params,
  })
}
