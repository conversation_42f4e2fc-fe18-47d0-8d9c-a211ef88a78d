import React from 'react'
import intl from 'react-intl-universal'
import dayjs from 'dayjs'
import { TableColumnItem } from '@hi-ui/table'
import { PageGeneratorConfig, OperationType } from '@/views/page-generator/types'
import { useGenerator } from '@/views/page-generator'
import {
  batchDeleteRollingCost,
  exportRollingTable,
  fetchCostRollingUploadCondition,
  fetchCostRollingUploadList,
  getCostRollingTemplate,
  uploadRollingCost,
} from './api'
import { HEADER_TOOLS_PORTAL } from '@/constants'
import ExcelOpButtons from '@/components/excel-op-buttons'
import { CURRENCY_MAP, DEFAULT_COST_MANAGEMENT_CONDITIONS } from './config'
import { customMessage } from '@/utils/custom-message'
import { ellipses } from '@/components/ellipsis-tool'
import { absOptimized } from '@/utils/number'
import { CONVOLUTION_DIGITS } from '../config'
import Button from '@hi-ui/button'
import { useRequest } from 'ahooks'
import PopConfirm from '@hi-ui/pop-confirm'

const CostManagement: React.FC = () => {
  const { runAsync: batchDelete, loading: deleting } = useRequest(batchDeleteRollingCost, {
    manual: true,
  })

  const pageConfig: PageGeneratorConfig = {
    pageKey: 'costManagement',
    pageExtra: {
      domId: HEADER_TOOLS_PORTAL,
      dom: (
        <ExcelOpButtons
          category="COST_ROLLING_UPLOAD"
          uploadKey="objectName"
          downloadAPI={getCostRollingTemplate}
          uploadAPI={uploadRollingCost}
        >
          {[intl.get('下载模板'), intl.get('上传商品库存数据')]}
        </ExcelOpButtons>
      ),
    },
    formConfig: {
      fetchConditionApi: fetchCostRollingUploadCondition,
      conditionField: 'field',
      fetchConditionFormatter: (params) => {
        const { periods, ...rest } = params
        return {
          ...rest,
          periodStart: periods?.[0] ? dayjs(periods[0]).format('YYYYMM') : null,
          periodEnd: periods?.[1] ? dayjs(periods[1]).format('YYYYMM') : null,
        }
      },
      fieldNames: { id: 'value', title: 'name' },
      initValues: DEFAULT_COST_MANAGEMENT_CONDITIONS,
      labelWidth: 100,
      hasGroupTitle: true,
      filterKey: 'ACT_COST_MANAGE',
      labelPlacement: 'right',
      showColon: false,
      colNum: 3,
      items: {
        [intl.get('项目信息')]: [
          {
            field: 'projectNames',
            label: intl.get('项目'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'configs',
            label: intl.get('配置'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'regions',
            label: intl.get('区域'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'bizNames',
            label: intl.get('业务线'),
            formItemType: 'CheckSelect',
            extra: {
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'sopCodes',
            label: 'SOP_CODE',
            formItemType: 'CheckSelect',
            extra: {
              optionWidth: 360,
              searchable: true,
              showCheckAll: true,
            },
          },
          {
            field: 'skus',
            label: 'SKU',
            formItemType: 'CheckSelect',
            extra: {
              optionWidth: 200,
              searchable: true,
              showCheckAll: true,
            },
          },
        ],
        [intl.get('基础选项')]: [
          {
            field: 'periods',
            label: ellipses(intl.get('时间区间'), true),
            formItemType: 'DatePicker',
            extra: {
              type: 'monthrange',
              format: 'YYYYMM',
              clearable: false,
            },
          },
          {
            field: 'currency',
            label: ellipses(intl.get('货币单位'), true),
            formItemType: 'Select',
            extra: {
              searchable: false,
              clearable: false,
              staticData: [
                { value: 'CNY', name: intl.get('人民币') },
                { value: 'USD', name: intl.get('美元') },
              ],
            },
          },
        ],
      },
    },
    tableConfig: {
      fieldKey: 'id',
      fetchListApi: fetchCostRollingUploadList,
      showAutoIndex: true,
      tools: {
        operations: [
          OperationType.COLUMN_FREEZE,
          OperationType.COLUMN_SETTING,
          OperationType.FULL_SCREEN,
        ],
        handleTableExport: async (filter?: Record<string, AnyType>) => {
          const params = filter || {}
          const res = await exportRollingTable({ ...params })
          if (res?.data) {
            window.open(res?.data)
            customMessage(intl.get('导出成功'), 'success')
          } else {
            customMessage(intl.get('导出失败'), 'error')
          }
        },
        operationExtra: [],
      },
      tableExtra: [],
      defaultPageNum: 1,
      defaultPageSize: 20,
      defaultLeftFreezeKey: 'period',
      defaultRightFreezeKey: 'stockAmount',
      enhanceColumns: (cols: TableColumnItem[]) => {
        return cols.map((col) => {
          if (col.dataKey === 'currency') {
            return { ...col, render: (value) => CURRENCY_MAP[value] }
          } else if (
            ['bizName', 'projectName', 'config', 'region', 'color'].includes(col.dataKey as string)
          ) {
            return { ...col, render: (v) => v || '-' }
          } else if (
            [
              'inventoryBalance',
              'excludedRetainedBalance',
              'changpingAdjustment',
              'finalInventoryBalance',
            ].includes(col.dataKey as string)
          ) {
            return {
              ...col,
              align: 'right',
              render: (v) => absOptimized(v, CONVOLUTION_DIGITS),
            }
          } else if ((col.dataKey as string) === 'exchangeRate') {
            return { ...col, width: 80, align: 'right' }
          } else if ((col.dataKey as string) === 'inventoryQuantity') {
            return { ...col, align: 'right', render: (v) => absOptimized(v, 0) }
          } else {
            return col
          }
        })
      },
      isRowSelection: true,
      patchOperate: (selectedRowKeys, refresh) => {
        return (
          <PopConfirm
            title={intl.get('确认删除？')}
            onConfirm={() => {
              batchDelete({ ids: selectedRowKeys })
                .then(() => {
                  customMessage(intl.get('删除成功'), 'success')
                  refresh()
                })
                .catch(() => {
                  customMessage(intl.get('删除失败'), 'error')
                })
            }}
          >
            <Button type="primary" disabled={selectedRowKeys?.length === 0} loading={deleting}>
              {intl.get('批量删除')}
            </Button>
          </PopConfirm>
        )
      },
    },
  }

  const { page } = useGenerator(pageConfig)

  return page
}

export default CostManagement
