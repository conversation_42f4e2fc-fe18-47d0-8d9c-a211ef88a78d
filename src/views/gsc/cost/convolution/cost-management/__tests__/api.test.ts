import { describe, it, expect, vi, beforeEach } from 'vitest'
import * as requestModule from '@/utils/request'
import {
  fetchCostRollingUploadList,
  fetchCostRollingUploadCondition,
  getCostRollingTemplate,
  uploadRollingCost,
  exportRollingTable,
  batchDeleteRollingCost,
} from '../api'

// Mock request module
vi.mock('@/utils/request', () => ({
  request: vi.fn(),
}))

describe('cost-management API模块', () => {
  const mockParams = {
    projectNames: ['项目A'],
    configs: ['配置1'],
    regions: ['区域1'],
    currency: 'CNY',
    pageNum: 1,
    pageSize: 10,
  }

  const mockResponse = {
    code: 2000,
    msg: 'success',
    success: true,
    data: {
      rowPage: {
        data: [{ id: 1, name: 'test' }],
        total: 100,
      },
      columnList: [],
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(requestModule.request).mockResolvedValue(mockResponse)
  })

  describe('fetchCostRollingUploadList', () => {
    it('应该正确调用请求并格式化返回数据', async () => {
      const result = await fetchCostRollingUploadList(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/cost-rolling/upload/list',
        method: 'POST',
        data: mockParams,
      })

      expect(result).toEqual({
        list: [{ id: 1, name: 'test' }],
        total: 100,
        columnList: [],
      })
    })
  })

  describe('fetchCostRollingUploadCondition', () => {
    it('应该正确调用请求，带参数', async () => {
      await fetchCostRollingUploadCondition(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/cost-rolling/upload/condition',
        method: 'POST',
        data: mockParams,
      })
    })

    it('应该正确调用请求，不带参数', async () => {
      await fetchCostRollingUploadCondition()

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/cost-rolling/upload/condition',
        method: 'POST',
        data: {},
      })
    })
  })

  describe('getCostRollingTemplate', () => {
    it('应该正确调用请求', async () => {
      await getCostRollingTemplate()

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/cost-rolling/template',
        method: 'GET',
      })
    })
  })

  describe('uploadRollingCost', () => {
    it('应该正确调用请求', async () => {
      const params = { objectName: 'test.xlsx' }
      await uploadRollingCost(params)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/cost-rolling/upload',
        method: 'POST',
        data: params,
      })
    })
  })

  describe('exportRollingTable', () => {
    it('应该正确调用请求', async () => {
      await exportRollingTable(mockParams)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/cost-rolling/upload/export',
        method: 'POST',
        data: mockParams,
      })
    })
  })

  describe('batchDeleteRollingCost', () => {
    it('应该正确调用请求', async () => {
      const params = { ids: ['1', '2', '3'] }
      await batchDeleteRollingCost(params)

      expect(requestModule.request).toHaveBeenCalledWith({
        url: '/cost-rolling/upload/delete',
        method: 'POST',
        data: params,
      })
    })
  })
})
