import intl from 'react-intl-universal'
import React, { memo, useCallback, useMemo, useEffect } from 'react'
import Table, { TableColumnItem } from '@hi-ui/table'
import Modal from '@hi-ui/modal'
import Pagination from '@hi-ui/pagination'
import Button from '@hi-ui/button'
import { customMessage } from '@/utils/custom-message'
import dayjs from 'dayjs'
import { usePagination } from 'ahooks'
import { getExportList, ExportListPropsType } from '@/api/cost/cost-reduction'
import { EXPOERT_TYPE, EXPORT_STATUS } from './config'
import './index.scss'
import { ellipses } from '@/components/ellipsis-tool'
export const ExportListModal = memo<{ visible: boolean; onCancel: () => void }>(
  ({ visible, onCancel }) => {
    const {
      data: listResult,
      run: getList,
      pagination,
      loading: tableLoading,
    } = usePagination(
      ({ current, pageSize }) => {
        return getExportList({
          type: EXPOERT_TYPE,
          pageNum: current,
          pageSize,
        } as ExportListPropsType)
      },
      {
        manual: true,
        refreshDeps: [],
        defaultPageSize: 20,
      }
    )
    useEffect(() => {
      visible && getList({ pageSize: pagination.pageSize, current: 1 })
    }, [visible, getList, pagination.pageSize])
    const handleExport = useCallback((url: string) => {
      if (url) {
        window.open(url)
        customMessage('导出成功', 'success', 2500)
      } else {
        customMessage('导出链接失效', 'error', 2500)
      }
    }, [])
    const tableData = useMemo(
      () =>
        (listResult?.list || []).map((key: Record<string, string>, index: number) => ({
          id: key?.createTime + index,
          ...key,
        })),
      [listResult]
    )
    const columns = useMemo<TableColumnItem[]>(
      () => [
        {
          title: ellipses(intl.get('名称')),
          dataKey: 'name',
        },
        {
          title: ellipses(intl.get('状态')),
          dataKey: 'status',
          render: (text) => EXPORT_STATUS[text],
        },
        {
          title: ellipses(intl.get('创建时间')),
          dataKey: 'createTime',
          render: (text) => dayjs(text).format('YYYY-MM-DD'),
        },
        {
          title: ellipses(intl.get('操作')),
          dataKey: 'options',
          render: (text, row) => (
            <Button
              type="primary"
              appearance="link"
              disabled={row.status !== 2}
              onClick={() => handleExport(row.url)}
            >
              {intl.get('下载')}
            </Button>
          ),
        },
      ],
      [handleExport]
    )
    return (
      <Modal
        title={intl.get('单据明细下载')}
        footer={null}
        visible={visible}
        onCancel={onCancel}
        width={'70%'}
      >
        <div className="modal-table-wrapper">
          <Table fieldKey="id" columns={columns} data={tableData} loading={tableLoading} />
        </div>
        <div className="flex justify-end w-full mt-6">
          <Pagination
            showTotal
            showJumper
            pageSizeOptions={[10, 20, 30, 40, 50]}
            pageSize={pagination?.pageSize || 10}
            total={pagination?.total || 0}
            current={pagination?.current || 0}
            onPageSizeChange={(pageSize) => pagination.changePageSize(pageSize)}
            onChange={(current, _, pageSize) => pagination.onChange(current, pageSize)}
          />
        </div>
      </Modal>
    )
  }
)
ExportListModal.displayName = 'ExportListModal'
