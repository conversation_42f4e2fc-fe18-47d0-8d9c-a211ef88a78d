.cost-reduction-wrapper {
  height: 100%;

  .query-wrapper {
    background-color: #fff;
    border-radius: 6px 6px 0 0;
    padding: 12px 16px;
  }

  .table-wrapper {
    background-color: #fff;
    border-radius: 0 0 6px 6px;
    padding: 18px 16px 12px;
  }
}

.table-wrapper.expand {
  .bordered {
    overflow: overlay !important;
    height: calc(100vh - 345px) !important;
  }

  .loading,
  .empty-state {
    height: calc(100vh - 345px);
  }
}

.table-wrapper.no-expand {
  .bordered {
    overflow: overlay !important;
    height: calc(100vh - 215px) !important;
  }

  .loading,
  .empty-state {
    height: calc(100vh - 218px);
  }
}

.table-wrapper.fullscreen-wrapper {
  .bordered {
    overflow: overlay !important;
    height: calc(100vh - 156px) !important;
  }
}

.modal-table-wrapper {
  .hi-v4-table {
    height: 100%;

    .hi-v4-table__wrapper {
      height: 100%;

      .hi-v4-table-body {
        overflow: overlay;
        height: 350px;
      }
    }
  }
}

.cost-reduction-footer-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 56px;
  background-color: #fff;
  box-shadow: 0 -4px 4px rgb(0 0 0 / 5%);
}

.voucher-container {
  overflow: hidden;
}

.page-footer {
  margin-top: 12px;
}

.border-line {
  width: 100%;
  height: 1px;
  background: rgb(235 237 240);
}

.ellipsis-col {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.update-time {
  margin-left: 12px;
  font-size: 14px;
}
