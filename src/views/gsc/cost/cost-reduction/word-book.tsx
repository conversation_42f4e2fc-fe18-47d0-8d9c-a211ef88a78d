import intl from 'react-intl-universal'
import React, { memo } from 'react'
import Table from '@hi-ui/table'
import { cx } from '@mi/sc-ui-common'
import { ellipses } from '@/components/ellipsis-tool'

export const workBookColumns = [
  {
    title: ellipses(intl.get('指标名称')),
    dataKey: 'inDicatorName',
    width: 200,
  },
  {
    title: ellipses(intl.get('公式')),
    dataKey: 'formula',
    width: 300,
  },
  {
    title: ellipses(intl.get('颗粒度')),
    dataKey: 'graininess',
    width: 200,
  },
  {
    title: ellipses(intl.get('数据来源')),
    dataKey: 'dataSource',
    width: 200,
  },
  {
    title: ellipses(intl.get('考核维度')),
    dataKey: 'assessmentDimensions',
    width: 150,
  },
  {
    title: ellipses(intl.get('计算逻辑')),
    dataKey: 'calculationLogic',
    width: 700,
  },
  {
    title: ellipses(intl.get('指标说明')),
    dataKey: 'indicatorDesc',
    width: 300,
  },
]

export const workBookData = [
  {
    key: 1,
    inDicatorName: `成本降幅目标达成率：
    （含材料、加工费、ODM成本）`,
    formula: intl.get('(Σ入库数量*入库单价-Σ入库数量*基准单价)/入库数量*基准单价'),
    graininess: intl.get('月+MPNID/SKU'),
    dataSource: intl.get('SAP入库单据、MRP peeging(预估)'),
    assessmentDimensions: intl.get('品类、小组'),
    calculationLogic: `CVPN基准价/SKU的基准价
    1.优先取上个年度Q4最后入库月均价，当上个年度Q4无入库时取当年第一笔入库均价
    2.剔除试产订单和退货对基准价影响


    1、材料费
    1）材料成本降幅=（∑入库数量*入库单价-∑入库数量*CVPN基准价）/∑入库数量*CVPN基准价
    2）CVPN基准价
    第一步、取CVPN前一年Q4最后入库月入库均价；剔除试产
    第二步、如果前一年Q4无入库，取CVPN当年首笔入库价为基准价

    3）入库单价
    实际部分：实际已入库凭证的物料单价
    预估部分：资源维护MPN的未来月入库价
    4）入库数量
    实际部分：实际已入库凭证入库数量
    预估部分：
    内存：资源维护的未来月入库数量
    新项目：资源维护成本物料清单，新项目复用料BOM用量*未来月整机数量；（复用料包括：屏）
    其他：56天内，MRP 配料（DPS）物料净需求；56天外，MRP备料（MDS）物料净需求

    2、代采费、加工费
    1）代采费、加工费降幅=（∑入库数量*入库单价-∑入库数量*SKU基准价）/∑入库数量* SKU基准价
    2） SKU基准价
    第一步、取 SKU前一年Q4最后入库月入库均价；
    第二步、如果前一年Q4无入库，取 SKU当年首笔入库价为基准价
    3）入库单价：实际采购订单价
    4）入库入库：实际采购入库数量
    `,
    indicatorDesc: `1、币种：人民币 未税
    2、降本金额为负数，代表降；降本金额为正数，代表涨。`,
  },
]

// eslint-disable-next-line react/display-name
const WordBook = memo<{ expanded: boolean }>(({ expanded }) => {
  return (
    <div className={cx('cost-work-data', { expanded })}>
      <Table data={workBookData} columns={workBookColumns} bordered></Table>
    </div>
  )
})

export default WordBook
