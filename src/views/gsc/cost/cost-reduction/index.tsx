import intl from 'react-intl-universal'
import React, { FC, useEffect, useRef } from 'react'
import { useSafeState, useFullscreen } from 'ahooks'
import cx from 'classnames'
import { ErrorBoundary } from '@sentry/react'
import QueryForm from './QueryForm'
import ReductionTable from './ReductionTable'
import { CostReductionProvider } from '@/context/CostReductionContext'
import './index.scss'
import { getCostReductionTime } from '@/api/cost/cost-reduction'
import { HEADER_TOOLS_PORTAL } from '@/constants'
import Portal from '@/components/portal'

const portalRoot = document.getElementById(HEADER_TOOLS_PORTAL) || document.body
const MatCostReduction: FC = () => {
  const fullscreenDomRef = useRef(null)
  const [isFullscreen, { toggleFullscreen }] = useFullscreen(fullscreenDomRef)
  const [expanded, setExpanded] = useSafeState<boolean>(false) // 控制表单折叠与否
  const [updateTime, setUpdateTime] = useSafeState<string>('')

  useEffect(() => {
    getCostReductionTime().then((res) => {
      setUpdateTime(res.data)
    })
  }, [setUpdateTime])

  return (
    <div className="cost-reduction-wrapper">
      <CostReductionProvider>
        <ErrorBoundary>
          <Portal container={portalRoot}>
            <div className="update-time">
              {intl.get('数据更新时间：')}
              {updateTime}
            </div>
          </Portal>
          <div className="query-wrapper">
            <QueryForm expanded={expanded} setExpanded={setExpanded} />
          </div>
        </ErrorBoundary>
        <div className="border-line"></div>
        <ErrorBoundary>
          <div
            className={cx('table-wrapper', {
              expand: expanded,
              'no-expand': !expanded,
              'fullscreen-wrapper': isFullscreen,
            })}
            ref={fullscreenDomRef}
          >
            <ReductionTable
              toggleFullscreen={toggleFullscreen}
              isFullscreen={isFullscreen}
              expanded={expanded}
            />
          </div>
        </ErrorBoundary>
      </CostReductionProvider>
    </div>
  )
}
export default MatCostReduction
