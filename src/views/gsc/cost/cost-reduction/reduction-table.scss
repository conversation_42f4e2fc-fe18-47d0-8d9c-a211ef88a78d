@import '@/common/styles/vars';

.cr-table-tools {
  align-items: center;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  height: 32px;
  margin-bottom: 12px;

  .unit {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #929aa6;
  }

  .buttons {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;

    .option {
      height: 32px;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f2f4f7;
      color: #1f2733;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      padding: 0 10px;

      &:last-of-type {
        margin-right: 18px;
      }

      &::after {
        content: '';
        position: absolute;
        width: 1px;
        height: 16px;
        background-color: #dfe2e8;
        right: 0;
        top: 8px;
      }

      &:hover {
        cursor: pointer;
      }
    }

    .option-left {
      border-radius: 4px 0 0 4px;
    }

    .option-right {
      border-radius: 0 4px 4px 0;
    }

    .option.active {
      color: #237ffa;
      background-color: #e2f3fe;
    }
  }

  .no-dropdown-buttons {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-left: 12px;

    & > i {
      width: 32px;
      height: 32px;
      cursor: pointer;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      color: #5f6a7a;

      &:hover {
        background: #f2f4f7;
      }
    }
  }
}

.bordered {
  width: 100%;
  overflow: auto;
  font-size: 14px !important;

  th {
    background-color: rgb(245 247 250) !important;
    padding: 9px 16px !important;
    border-top: none !important;
    text-align: center !important;

    &:first-of-type {
      border-left: none !important;
    }

    &:last-of-type {
      border-right: none !important;
    }
  }

  tr {
    td {
      word-wrap: break-word;
      word-break: break-all;

      &:first-of-type {
        border-left: none !important;
      }

      &:last-of-type {
        border-right: none !important;
      }
    }
  }

  .cell-wapper {
    display: inline-flex;
    width: 100%;
    justify-content: center;

    .center {
      text-align: left;
    }
  }

  .ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}

.supports-expand.bordered {
  tr {
    td {
      text-align: center;
    }

    &.first {
      td {
        text-align: left;
      }
    }
  }
}

.bold {
  font-weight: bold;
  margin-top: 2px;
}

.footer-wrapper {
  align-items: center;
  background-color: #fff;
  bottom: 0;
  box-shadow: 0 -4px 4px #0000000d;
  box-sizing: border-box;
  height: 56px;
  left: 0;
  padding: 0 24px;
  position: absolute;
  width: 100%;
  display: flex;
  justify-content: flex-end;

  .hi-v4-picker {
    width: 125px !important;
  }
}

.doller-icon {
  background-image: url('#{$img-url}doller_icon.svg');
  background-repeat: no-repeat;
  width: 16px;
  height: 16px;
  display: inline-block;
}

.percent_icon {
  background-image: url('#{$img-url}percent_icon.svg');
  background-repeat: no-repeat;
  background-size: 80%;
  background-position: center;
  width: 16px;
  height: 16px;
  display: inline-block;
}

.rmb-icon {
  background-image: url('#{$img-url}rmb_icon.svg');
  background-repeat: no-repeat;
  width: 16px;
  height: 16px;
  display: inline-block;
}

.cost-work-data {
  height: calc(100vh - 210px);

  .hi-v4-table-body {
    white-space: pre-line;
    height: calc(100vh - 260px);
  }
}

.warehouse-voucher {
  height: calc(100vh - 250px);

  .hi-v4-table-body {
    height: calc(100vh - 300px);
  }
}

.cost-work-data.expanded {
  height: calc(100vh - 340px);

  .hi-v4-table-body {
    height: calc(100vh - 390px);
  }
}

.warehouse-voucher.expanded {
  height: calc(100vh - 340px);

  .hi-v4-table-body {
    height: calc(100vh - 430px);
  }
}
