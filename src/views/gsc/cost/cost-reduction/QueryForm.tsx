import intl from 'react-intl-universal'
import React, { CSSProperties, memo, useCallback, useEffect, useRef } from 'react'
import { useSafeState, useMount, useUpdateEffect } from 'ahooks'
import * as Sentry from '@sentry/react'
import { Form, FormHelpers, FormItem } from '@hi-ui/form'
import { Col, Row } from '@hi-ui/grid'
import Button from '@hi-ui/button'
import { DownOutlined } from '@hi-ui/icons'
import DatePicker from '@hi-ui/date-picker'
import dayjs from 'dayjs'
import { DEFAULT_DATE_RANGE, calcConditions } from './config'
import { getConditions } from '@/api/cost/cost-reduction'
import { customMessage } from '@/utils/custom-message'
import { MATERIAL_COST_REDUCTION, FORM_VIEWS_CACHE } from '@/constants'
import { useCostReduction } from '@/hooks/useCostReduction'
import { SetType } from '@/context/CostReductionContext'
import FormView from '@/components/form-view'
import * as API from '@/api/gsc/form-view'
import './query-form.scss'
import { isEmpty, isString } from 'lodash'
import message from '@hi-ui/message'
import CheckSelect from '@hi-ui/check-select'
import Select from '@hi-ui/select'
import { ellipses } from '@/components/ellipsis-tool'
import { FORM_WIDTH } from '@/utils/select-data'

const QueryForm = memo<{ expanded: boolean; setExpanded: SetType<boolean> }>(
  ({ expanded, setExpanded }) => {
    const { setCostFilter, dateType, queryConditions, setQueryConditions } = useCostReduction()

    const formRef = useRef<FormHelpers>(null)
    const rowGapStyle = useRef<CSSProperties | undefined>({ minWidth: 200 }) // grid布局内容宽度
    const formEleHeightRef = useRef<number>(33) // 表单默认高度
    const commonSettings = {
      searchable: true,
      showCheckAll: true,
      style: rowGapStyle.current,
      height: 260,
    }
    const initQueryCondition = useRef<Record<string, Array<string> | string>>({
      businessDate: DEFAULT_DATE_RANGE,
      sourcingOprNameCns: [],
      projectNames: [],
      purchasePatternDescs: [],
      depts: [],
      deptOwners: [],
      teamOwners: [],
      subjects: [intl.get('材料费')],
      subProjectNames: [],
      mpnIds: [],
      pnCodes: [],
      date: '',
    })
    const [formValue, setFormValue] = useSafeState<Record<string, Array<string> | string>>({
      ...initQueryCondition.current,
    })
    const [dateSelectedValue, setDateSelectedValue] = useSafeState<Date | string>(
      DEFAULT_DATE_RANGE[0]
    )
    const [dataString, setDateString] = useSafeState<string[] | string | null>(DEFAULT_DATE_RANGE)

    const formatFormValues = useCallback(
      (data) => {
        let { businessDate, ...left } = data
        if (!Array.isArray(businessDate) || !Array.from(businessDate).length) {
          businessDate = DEFAULT_DATE_RANGE
        }
        if (!isString(businessDate[0])) {
          businessDate = businessDate.map((key) => dayjs(key).format('YYYY-MM'))
        }
        formRef.current?.setFieldsValue({ ...data, businessDate })
        setFormValue({ ...data, businessDate })
        const startDate = dayjs(businessDate?.[0])
          .startOf('month')
          ?.valueOf()
        const endDate = dayjs(businessDate?.[1])
          .endOf('month')
          ?.valueOf()
        const derivedConditions = {
          ...left,
          startDate,
          endDate,
        }
        Object.keys(derivedConditions).forEach((key) => {
          const value = data[key]
          if (Array.isArray(value) && value?.length === queryConditions[key]?.length) {
            derivedConditions[key] = []
          }
        })
        return derivedConditions
      },
      [queryConditions, setFormValue]
    )
    useMount(() => {
      try {
        const cache = localStorage.getItem(FORM_VIEWS_CACHE) || '[]'
        const content =
          JSON.parse(cache)?.find((item) => item?.pageType === MATERIAL_COST_REDUCTION)?.content ||
          ''
        let config
        if (content) {
          const contentConfig = JSON.parse(content)
          const formConfig = contentConfig?.formConfig || contentConfig
          config = formConfig
        } else {
          const { formConfig } = { formConfig: initQueryCondition.current }
          config = formConfig
        }
        setCostFilter(formatFormValues(config))
      } catch (err) {
        Sentry.captureException(err)
      }
    })
    useUpdateEffect(() => {
      const values = formRef.current?.getFieldsValue()
      if (!values || !Object.keys(values)?.length) return
      const derivedConditions = formatFormValues(values)
      setCostFilter(derivedConditions)
    }, [dateType])

    const handleSearch = useCallback(() => {
      formRef.current?.validate()?.then((values) => {
        const str = calcConditions(values)
        if (str) {
          customMessage(`${str}所选查询条件数量过多，请减少选择数量后查询`, 'info', 2500)
        } else {
          setCostFilter?.(formatFormValues(values))
        }
      })
    }, [setCostFilter, formatFormValues])

    const getCondition = useCallback(async () => {
      const startDate = dayjs(dataString?.[0])
        ?.startOf('day')
        ?.valueOf()
      const endDate = dayjs(dataString?.[1])
        ?.endOf('day')
        ?.valueOf()

      await getConditions({ startDate, endDate })
        .then((res) => {
          const data = res?.data || {}
          if (Object.keys(data)?.length) {
            const conditions = {}
            Object.keys(data).forEach((key) => {
              conditions[key] = data[key].map((item) => {
                return {
                  id: item?.value,
                  title: item?.name,
                }
              })
            })
            res?.data && setQueryConditions(conditions)
          }
        })
        .catch((err) => {
          message.open({
            title: err?.message || intl.get('搜索条件失败，请联系管理员'),
            type: 'error',
          })
        })
    }, [dataString, setQueryConditions])

    useEffect(() => {
      getCondition()
    }, [getCondition])

    return (
      <div className="query_form_wrapper">
        <div className="query-top-search">
          <div
            className="query-top-form transition-[height]"
            style={{
              height: !expanded ? formEleHeightRef.current : 166,
              width: FORM_WIDTH,
            }} // 256
          >
            <Form
              innerRef={formRef}
              initialValues={initQueryCondition.current}
              labelWidth="105"
              labelPlacement="right"
              showColon={false}
              onValuesChange={(_changedValues, allValues) => {
                let { businessDate, ...left } = allValues
                if (!isEmpty(businessDate) && !isString(businessDate[0])) {
                  businessDate = businessDate.map((key) => dayjs(key).format('YYYY-MM'))
                }
                setFormValue({ ...left, businessDate })
              }}
            >
              <Row gutter rowGap={0}>
                <Col span={8}>
                  <FormItem field="businessDate" label={ellipses(intl.get('业务日期'))}>
                    <DatePicker
                      type="monthrange"
                      format="YYYY-MM"
                      onChange={(_date, dateStr) => {
                        setDateString(dateStr)
                      }}
                      disabledDate={(currentDate) => {
                        if (dateType === 1 || !dateSelectedValue) return false
                        const selectYear = parseInt(dayjs(dateSelectedValue).format('YYYY'), 10)
                        const currentYear = parseInt(dayjs(currentDate).format('YYYY'), 10)
                        return selectYear !== currentYear
                      }}
                      onSelect={(val, isCompleted) => {
                        setDateSelectedValue(isCompleted ? '' : val)
                      }}
                      {...commonSettings}
                    />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem field="depts" label={ellipses(intl.get('部门'))}>
                    <CheckSelect data={queryConditions?.depts} {...commonSettings} />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem field="deptOwners" label={ellipses(intl.get('部门负责人'))}>
                    <CheckSelect data={queryConditions?.deptOwners} {...commonSettings} />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem field="sourcingOprNameCns" label={ellipses(intl.get('资源负责人'))}>
                    <CheckSelect data={queryConditions?.sourcingOprNameCns} {...commonSettings} />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem field="teamOwners" label={ellipses(intl.get('小组负责人'))}>
                    <CheckSelect data={queryConditions?.teamOwners} {...commonSettings} />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem field="purchasePatternDescs" label={ellipses(intl.get('采购渠道'))}>
                    <CheckSelect data={queryConditions?.purchasePatternDescs} {...commonSettings} />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem field="subjects" label={ellipses(intl.get('科目'))}>
                    <CheckSelect data={queryConditions?.subjects} {...commonSettings} />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem field="projectNames" label={ellipses(intl.get('项目'))}>
                    <CheckSelect data={queryConditions?.projectNames} {...commonSettings} />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem field="subProjectNames" label={ellipses(intl.get('二级项目'))}>
                    <CheckSelect data={queryConditions?.subProjectNames} {...commonSettings} />
                  </FormItem>
                </Col>
                {dateType === 2 && (
                  <Col span={8}>
                    <FormItem field="mpnIds" label="MPN-ID">
                      <CheckSelect data={queryConditions?.mpnIds} {...commonSettings} />
                    </FormItem>
                  </Col>
                )}
                {dateType === 2 && (
                  <Col span={8}>
                    <FormItem field="pnCodes" label="PN">
                      <CheckSelect data={queryConditions?.pnCodes} {...commonSettings} />
                    </FormItem>
                  </Col>
                )}
                <Col span={8}>
                  <FormItem field="date" label={ellipses(intl.get('数据版本'))}>
                    <Select data={queryConditions.dates} {...commonSettings} />
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </div>
          <div className="ml-3 pl-3">
            <div
              className={
                intl.getInitOptions().currentLocale === 'en-US'
                  ? 'w-125 pt-0.5 pr-0.5'
                  : 'w-104 pt-0.5 pr-0.5'
              }
            >
              <Button appearance="link" onClick={() => setExpanded((prev) => !prev)}>
                {!expanded ? intl.get('展开') : intl.get('收起')}
                <DownOutlined
                  className="transition-transform ml-2"
                  color="#9d9d9f"
                  style={{
                    transform: !expanded ? 'rotateZ(0)' : 'rotateZ(180deg)',
                  }}
                />
              </Button>
              <Button
                type="default"
                onClick={() => {
                  formRef.current?.reset()
                  setFormValue(initQueryCondition.current)
                }}
              >
                {intl.get('重置')}
              </Button>
              <Button type="secondary" onClick={handleSearch} className="search-button">
                {intl.get('查询')}
              </Button>
              <FormView
                api={API}
                filter={formValue}
                pageType={MATERIAL_COST_REDUCTION}
                onSearch={setCostFilter}
                clickFormatter={formatFormValues}
              />
            </div>
          </div>
        </div>
      </div>
    )
  }
)
QueryForm.displayName = 'QueryForm'
export default QueryForm
