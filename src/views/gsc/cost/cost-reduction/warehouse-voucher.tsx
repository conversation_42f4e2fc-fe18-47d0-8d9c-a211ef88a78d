import Table from '@hi-ui/table'
import React, { memo, useEffect, useState } from 'react'
import * as API from '@/api/cost/cost-reduction'
import { useCostReduction } from '@/hooks/useCostReduction'
import Pagination from '@hi-ui/pagination'
import { usePagination } from 'ahooks'
import { FetchCostListType } from '@/api/cost/cost-reduction'
import { cx } from '@mi/sc-ui-common'
import { Tag } from '@hi-ui/tag'
import { colWidth } from './config'

export const WarehouseVoucher = memo<{ expanded: boolean }>(({ expanded }) => {
  const { costFilter, dateType, isAscSort } = useCostReduction()
  const [first, setFirst] = useState<boolean>(true)
  const [columnList, setColumnList] = useState([])

  const {
    data: listResult,
    run: getList,
    pagination,
    loading: tableLoading,
  } = usePagination(
    ({ current, pageSize }) => {
      const params = {
        ...costFilter,
        type: dateType,
        pageNum: current,
        pageSize,
        createDayAsc: isAscSort,
      }
      return API.getWarehouseTable(params as FetchCostListType)
    },
    {
      manual: true,
      refreshDeps: [costFilter, dateType, isAscSort],
      defaultPageSize: 20,
      onSuccess: (res) => {
        const tableColumns = res.columnList?.map((column) => {
          const { key, value, type } = column
          if (key === 'source') {
            return {
              dataKey: key,
              title: value,
              width: colWidth[key],
              type,
              render: (text) => {
                return <Tag type={text === '实际' ? 'success' : 'primary'}>{text}</Tag>
              },
            }
          }
          return {
            dataKey: key,
            title: value,
            width: colWidth[key] || 150,
            type,
            render: (text) => {
              return (
                <span title={text} className="ellipsis-col">
                  {text}
                </span>
              )
            },
          }
        })
        setColumnList(tableColumns)
      },
    }
  )

  useEffect(() => {
    if (first) {
      setFirst(false)
    } else {
      Object.keys(costFilter).length && getList({ current: 1, pageSize: 20 })
    }
  }, [costFilter, first, getList, isAscSort])

  return (
    <div className={cx('warehouse-voucher', { expanded })}>
      <Table
        columns={columnList}
        data={listResult?.list || []}
        loading={tableLoading}
        size="sm"
      ></Table>

      <div className="cost-reduction-footer-wrapper">
        <div className="flex justify-end w-full pl-13 pr-13">
          <Pagination
            showTotal
            showJumper
            pageSizeOptions={[10, 20, 30, 40, 50]}
            pageSize={pagination?.pageSize || 10}
            total={pagination?.total || 0}
            current={pagination?.current || 0}
            onPageSizeChange={(pageSize) => pagination.changePageSize(pageSize)}
            onChange={(current, _, pageSize) => pagination.onChange(current, pageSize)}
          />
        </div>
      </div>
    </div>
  )
})

WarehouseVoucher.displayName = 'WarehouseVoucher'
export default WarehouseVoucher
