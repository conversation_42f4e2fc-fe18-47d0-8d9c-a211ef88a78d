import intl from 'react-intl-universal'
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { memo, useEffect, useMemo, useState, useCallback } from 'react'
import {
  buildDrillTree,
  buildRecordMatrix,
  convertDrillTreeToCrossTree,
  CrossTable,
  LeftCrossTreeNode,
  TopCrossTreeNode,
} from 'ali-react-table/pivot'
import cx from 'classnames'
import { usePagination, useSafeState, useRequest, useMount } from 'ahooks'
import {
  getCostList,
  FetchCostListType,
  exportCostRedution,
  exportWarehouseTable,
} from '@/api/cost/cost-reduction'
import { getColumnConfig, saveColumnConfig } from '@/api/gsc/bom-properties'
import { createAggregateFunction } from 'dvt-aggregation'
import {
  MoveOutlined,
  DirectionDownOutlined,
  DownloadOutlined,
  SettingOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  ArrowDownOutlined,
  ArrowUpOutlined,
} from '@hi-ui/icons'
import { message } from '@hi-ui/message'
import { customMessage } from '@/utils/custom-message'
import { thousandSeparator } from '@/utils/thousandSeparator'
import Loading from '@hi-ui/loading'
import EmptyState, { EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL } from '@hi-ui/empty-state'
import Button from '@hi-ui/button'
import { isEmpty, isNumber, isObject } from 'lodash'
import { CrossTableColumn } from '@/context/CostReductionContext'
import { useCostReduction } from '@/hooks/useCostReduction'
import { WebsiteBaseTable } from '@/components/website-base-table'
import TableSort from '@/components/table-sort'
import SettingDrawer from '@/components/multi-setting-drawer'
import { ExportListModal } from './ExportListModal'
import { PRICE_ICON_COLOR, UP_ICON_COLOR, DOWN_ICON_COLOR } from './config'
import { EmptyFunctionType, ResponseType } from '@/types/type'
import { AnyType, MATERIAL_COST_REDUCTION } from '@/constants'
import './reduction-table.scss'
import WarehouseVoucher from './warehouse-voucher'
import Tooltip from '@hi-ui/tooltip'
import WordBook from './word-book'

const ReductionTable = memo<{
  toggleFullscreen: EmptyFunctionType
  isFullscreen: boolean
  expanded: boolean
}>(({ toggleFullscreen, isFullscreen = false, expanded = false }) => {
  const {
    data,
    totals,
    isLoading,
    costFilter,
    dateType,
    dimensions,
    dimString,
    indicators,
    hiddenColKeys,
    sortedColKeys,
    disabledColkeys,
    isAscSort,
    setHiddenColKeys,
    setSortColKeys,
    setDateType,
    assignFetchedResult,
    setTableListState,
    setIsAscSort,
  } = useCostReduction()
  const [columnConfig, setColumnConfig] = useSafeState<Record<string, string | number>>({})
  const [amountShow] = useSafeState<boolean>(false)
  const [modalVisible, setModalVisible] = useSafeState<boolean>(false)
  const [drawerVisible, setDrawerVisible] = useSafeState<boolean>(false)
  const [leftCodes, onChangeLeftCodes] = useSafeState<string[]>([])
  const [topCodes] = useSafeState<string[]>([])
  const [sortTableShow, setSortTableShow] = useSafeState<boolean>(false)
  const [sortFilters, setSortFilters] = useSafeState<Record<string, AnyType>>({})
  const [supportsExpand, setSupportsExpand] = useSafeState<boolean>(true) // 是否支持表格展开
  const { run: getSettledList, pagination } = usePagination(
    ({ current, pageSize, sortFilter, leftCode }) => {
      const fieldsSearch = sortFilter ?? { findFields: leftCode ?? leftCodes }
      return getCostList({
        ...costFilter,
        ...fieldsSearch,
        mpnIds: undefined,
        pnCodes: undefined,
        type: dateType,
        pageNum: current,
        pageSize,
      } as FetchCostListType)
    },
    {
      manual: true,
      refreshDeps: [costFilter, dateType],
      defaultPageSize: 30000,
      onBefore: () => {
        setTableListState({ data: [], totals: {}, isLoading: true })
      },
      onSuccess: (res) => {
        assignFetchedResult(res)
      },
    }
  )
  const { run: exportSettled } = useRequest((params) => exportCostRedution(params), {
    manual: true,
    onSuccess: (res) => {
      message.closeAll()
      const url = res?.data || ''
      if (url) {
        window.open(url)
        customMessage('导出成功', 'success', 2500)
      } else {
        customMessage('导出链接失效', 'error', 2500)
      }
    },
    onError: (err) => {
      message.closeAll()
      customMessage((err as ResponseType)?.msg || '导出失败', 'error', 2500)
    },
  })

  const { run: downloadWarehouseTable } = useRequest((params) => exportWarehouseTable(params), {
    manual: true,
    onSuccess: (res) => {
      message.closeAll()
      const urlList = res?.data || []

      if (urlList?.length) {
        urlList?.forEach((url) => {
          window.open(url)
        })
        customMessage('导出成功', 'success', 2500)
      } else {
        customMessage('导出链接失效', 'error', 2500)
      }
    },
    onError: (err) => {
      message.closeAll()
      customMessage((err as ResponseType)?.msg || '导出失败', 'error', 2500)
    },
  })

  useEffect(() => {
    const newLeftCodes: string[] =
      dateType === 4
        ? ['subject', 'matCat']
        : (sortedColKeys[0] ?? dimString).filter((item: string) => !hiddenColKeys.includes(item))
    onChangeLeftCodes(newLeftCodes)
  }, [dimString, sortedColKeys, hiddenColKeys, dateType, onChangeLeftCodes])
  const sortTableData = useCallback(
    ({ sortKey, keyCodes }: { sortKey?: string; keyCodes?: string[] }) => {
      if ([3, 4].includes(dateType)) {
        const sortFilter: { findFields: string[]; sort?: string } = {
          findFields: keyCodes ?? leftCodes,
        }
        if (sortKey) {
          sortFilter.sort = sortKey
        }

        getSettledList({
          sortFilter,
          pageSize: pagination.pageSize,
          current: 1,
        })
        setSortFilters(sortFilter)
      }
    },
    [leftCodes, dateType, getSettledList, pagination.pageSize, setSortFilters]
  )

  const detailClickExport = useCallback(() => {
    setModalVisible((prev: boolean) => !prev)
  }, [setModalVisible])
  const visibleIndicators = useMemo(() => {
    const amountShowRender = (cell: Record<string, string | number> | string | number) => {
      return isObject(cell) ? (
        <div className="cell-wapper">
          {['1', '2']?.includes(cell?.dataType as string) ? (
            <div className="center">{thousandSeparator(Number(cell?.showValue).toFixed(2))}</div>
          ) : ['3']?.includes(cell?.dataType as string) ? (
            <div className="center">
              {`${((cell?.percent as number) ?? 0).toFixed(2)}%`}
              {((cell?.percent as number) ?? 0) > 0 ? (
                <ArrowUpOutlined color={UP_ICON_COLOR} size={18} />
              ) : (
                <ArrowDownOutlined color={DOWN_ICON_COLOR} size={18} />
              )}
            </div>
          ) : (
            <div className="center">{cell?.showValue}</div>
          )}
        </div>
      ) : isNumber(cell) ? (
        <div className="cell-wapper">
          <div className="center">{thousandSeparator(cell.toFixed(2))}</div>
        </div>
      ) : cell?.includes('%') ? (
        <div className="cell-wapper">
          <div className="center">
            {cell}
            {(parseFloat(cell) ?? 0) > 0 ? (
              <ArrowUpOutlined color={UP_ICON_COLOR} size={18} />
            ) : (
              <ArrowDownOutlined color={DOWN_ICON_COLOR} size={18} />
            )}
          </div>
        </div>
      ) : (
        cell
      )
    }

    const viewTableCellRender = (
      a: undefined | Record<string, string>,
      b: undefined | LeftCrossTreeNode,
      c: undefined | TopCrossTreeNode
    ) => {
      const singleRow = /加工费|代采费/.test(b?.key ?? '')
      const row = b?.value
        ? data?.find((item) => item?.matCat === b?.value)
        : data?.find((item) =>
            singleRow ? b?.key.includes(item.subject) : item?.matCat === b?.value
          )
      const cell: Record<string, string | number> | string | number = row?.[c?.code ?? ''] ?? ''
      return amountShowRender(cell)
    }

    const departmentTableCellRender = (
      a: undefined | Record<string, string>,
      b: undefined | LeftCrossTreeNode,
      c: undefined | (TopCrossTreeNode & { dataType: string })
    ) => {
      let reduceRmbAmtSum, baselineRmbAmtSum, percent
      const code = c?.code ?? ''
      let showValue: string | number = ''
      const dataType = c?.dataType ?? ''
      if (b?.value === '总计') {
        showValue = totals[code] ?? (['1', '2'].includes(dataType) ? 0 : '')
        percent = totals[code]?.percent ?? (['1', '2'].includes(dataType) ? 0 : '')
      } else {
        const dataPath = b?.data?.dataPath ?? []
        const dataPathString = dataPath.join(',')
        const sliceLeftCodes = leftCodes?.slice(0, dataPath.length)
        const codeLists = data
          ?.filter((item) => {
            return sliceLeftCodes.map((key) => item[key])?.join(',') === dataPathString
          })
          .map((key) => key[code])
        if (isObject(codeLists[0])) {
          reduceRmbAmtSum = codeLists
            .map((key) => key?.reduceRmbAmtSum)
            .reduceRight((a, b) => a + b, 0) // 降本金额
          baselineRmbAmtSum = codeLists
            .map((key) => key?.baselineRmbAmtSum)
            .reduceRight((a, b) => a + b, 0) // 基准金额
          percent = baselineRmbAmtSum === 0 ? 0 : (reduceRmbAmtSum / baselineRmbAmtSum) * 100
        } else if (isNumber(codeLists[0])) {
          showValue = codeLists.reduceRight((a, b) => a + b, 0)
        } else {
          return ''
        }
      }
      return amountShowRender({
        percent,
        dataType,
        key: c?.value ?? '',
        showValue,
      })
    }
    const newIndicators = indicators
      .filter((key: CrossTableColumn) => !hiddenColKeys.includes(key?.code ?? ''))
      .sort((a, b) => {
        return (
          (sortedColKeys[1] ?? []).indexOf(a?.code ?? '') -
          (sortedColKeys[1] ?? []).indexOf(b?.code ?? '')
        )
      })
      .map((i) => {
        return {
          ...i,
          title:
            sortTableShow && dateType !== 4 ? (
              <TableSort
                code={i.code}
                name={i.name}
                sortCode={sortFilters?.sort ?? ''}
                setSort={(sortKey) => {
                  if (!isEmpty(sortKey)) {
                    sortKey.splice(1, 0, amountShow ? 'amount' : 'percent')
                  }
                  sortTableData({ sortKey: sortKey.join('.') })
                }}
              />
            ) : undefined,
          render: dateType === 4 ? viewTableCellRender : departmentTableCellRender,
        }
      })
    return newIndicators
  }, [
    dateType,
    amountShow,
    indicators,
    leftCodes,
    sortFilters,
    sortTableShow,
    sortedColKeys,
    data,
    totals,
    hiddenColKeys,
    sortTableData,
  ])
  const [leftExpandKeys, onChangeLeftExpandKeys] = useState<string[]>([])
  const dimMap = useMemo(
    () =>
      new Map(
        dimensions.filter((item: CrossTableColumn) => !item.hidden).map((dim) => [dim.code, dim])
      ),
    [dimensions]
  )
  const leftExpandKeySet = useMemo(() => new Set(leftExpandKeys), [leftExpandKeys])
  const leftDrillTree = useMemo(() => {
    return buildDrillTree(data, leftCodes, {
      includeTopWrapper: true,
      isExpand: !supportsExpand ? undefined : (key) => leftExpandKeySet.has(key),
    })
  }, [data, leftCodes, leftExpandKeySet, supportsExpand])
  const [leftTreeRoot] = useMemo(() => {
    return convertDrillTreeToCrossTree(leftDrillTree, {
      indicators: null as AnyType,
      supportsExpand,
      expandKeys: leftExpandKeys,
      generateSubtotalNode: (drillNode) => {
        return drillNode.path.length === 0 && dateType !== 4
          ? { position: 'start' as const, value: intl.get('总计') }
          : null
      },
      onChangeExpandKeys: onChangeLeftExpandKeys,
    })
  }, [leftDrillTree, leftExpandKeys, supportsExpand, dateType, onChangeLeftExpandKeys])
  const [topExpandKeys, onChangeTopExpandKeys] = useState<string[]>([])
  const topExpandKeySet = useMemo(() => new Set(topExpandKeys), [topExpandKeys])
  const topDrillTree = useMemo(() => {
    return buildDrillTree(data, topCodes, {
      includeTopWrapper: true,
      isExpand: !supportsExpand ? undefined : (key) => topExpandKeySet.has(key),
    })
  }, [data, topCodes, supportsExpand, topExpandKeySet])
  const [topTreeRoot] = useMemo(() => {
    return convertDrillTreeToCrossTree(topDrillTree, {
      indicators: visibleIndicators as AnyType,
      supportsExpand,
      expandKeys: topExpandKeys,
      onChangeExpandKeys: onChangeTopExpandKeys,
    })
  }, [topDrillTree, visibleIndicators, supportsExpand, topExpandKeys, onChangeTopExpandKeys])
  const aggregate = useMemo(() => {
    return createAggregateFunction(visibleIndicators)
  }, [visibleIndicators])
  const matrix = useMemo(() => {
    if (data.length > 0 && leftCodes.length > 0 && leftDrillTree && topDrillTree) {
      return buildRecordMatrix({
        data,
        leftCodes,
        topCodes,
        aggregate,
        prebuiltLeftTree: leftDrillTree,
        prebuiltTopTree: topDrillTree,
      })
    } else {
      return null
    }
  }, [aggregate, data, leftCodes, topCodes, leftDrillTree, topDrillTree])
  const drawerColums = useMemo(
    () => [
      dimensions.map((key: CrossTableColumn) => ({ title: key.name, dataKey: key.code })),
      indicators.map((key: CrossTableColumn) => ({ title: key.name, dataKey: key.code })),
    ],
    [dimensions, indicators]
  )
  const handleExport = useCallback(() => {
    message.open({ title: intl.get('导出中...'), type: 'info', autoClose: false })
    if ([3, 4].includes(dateType)) {
      exportSettled({ ...costFilter, findFields: leftCodes, type: dateType, isExport: true })
    } else if (dateType === 2) {
      downloadWarehouseTable({ ...costFilter, type: dateType, createDayAsc: isAscSort })
    }
  }, [costFilter, dateType, downloadWarehouseTable, exportSettled, isAscSort, leftCodes])
  const headerSave = useCallback(
    async (newColumnOrder, delColumn) => {
      try {
        const jsonArgs = JSON.stringify({
          sortedColKeys: newColumnOrder,
          hiddenColKeys: delColumn,
        })
        await saveColumnConfig({
          id: columnConfig.id,
          pageType: MATERIAL_COST_REDUCTION,
          content: jsonArgs,
        })
        message.open({ title: intl.get('维度列顺序配置成功'), type: 'success' })
      } catch (err) {
        message.open({
          title:
            (err as ResponseType)?.message ||
            (err as ResponseType)?.msg ||
            '列顺序配置失败，请联系管理员',
          type: 'error',
        })
      }
    },
    [columnConfig]
  )
  const headerSort = useCallback(
    (keyCodes = []) => {
      if (keyCodes.length === 1) {
        sortTableData({ keyCodes })
        !sortTableShow && setSortTableShow(true)
        !supportsExpand && dateType === 3 && setSupportsExpand(true)
      }
      if (keyCodes.length > 1) {
        Object.keys(costFilter).length &&
          getSettledList({
            pageSize: pagination.pageSize,
            current: 1,
            leftCode: keyCodes,
          })
        setSortTableShow(false)
        setSortFilters({})
      }
    },
    [
      sortTableData,
      sortTableShow,
      setSortTableShow,
      supportsExpand,
      dateType,
      setSupportsExpand,
      costFilter,
      getSettledList,
      pagination.pageSize,
      setSortFilters,
    ]
  )
  const sortedCol = useCallback((colKeys: string[] = [], hiddenKeys: string[] = []) => {
    return colKeys
      .filter((item: string) => !hiddenKeys.includes(item))
      .sort((a, b) => {
        if (a < b) return -1
        if (a > b) return 1
        return 0
      })
  }, [])
  const onSetColKeysChange = useCallback(
    (sortedKeys: string[][], hiddenKeys: string[]) => {
      const keyCodes: string[] = sortedCol([...sortedKeys[0]] ?? [], hiddenKeys)
      if (keyCodes.length === 0) {
        message.open({ title: intl.get('维度列至少保留一个'), type: 'error' })
      } else {
        setSortColKeys(sortedKeys)
        setHiddenColKeys(hiddenKeys)
        headerSave(sortedKeys[0], hiddenKeys)
        const keyColCodes: string[] = sortedCol(sortedColKeys[0] ?? [], hiddenColKeys)
        if (keyCodes.join(',') !== keyColCodes.join(',')) {
          headerSort(keyCodes)
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [hiddenColKeys, sortedColKeys, setSortColKeys, setHiddenColKeys, headerSave, headerSort]
  )
  useEffect(() => {
    if (sortTableShow) {
      const keyCodes: string[] = (sortedColKeys[0] ?? []).filter(
        (item: string) => !hiddenColKeys.includes(item)
      )
      headerSort(keyCodes)
    } else if ([3, 4].includes(dateType)) {
      Object.keys(costFilter).length &&
        getSettledList({ pageSize: pagination.pageSize, current: 1 })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [costFilter])
  useMount(() => {
    const fn = async () => {
      let content, item
      try {
        const columnRes = await getColumnConfig()
        if (columnRes?.data?.length !== 0 && !!columnRes?.data[0]?.content) {
          item = columnRes?.data?.find((key) => key.pageType === MATERIAL_COST_REDUCTION)
          content = JSON.parse(item?.content ?? '{}')
          localStorage.setItem('COLUMN_CONFIG', JSON.stringify(columnRes?.data))
        } else {
          const cache = localStorage.getItem('COLUMN_CONFIG') ?? '[]'
          item = JSON.parse(cache).find((key) => key.pageType === MATERIAL_COST_REDUCTION)
          content = JSON.parse(item?.content ?? '{}')
        }
        const { sortedColKeys, hiddenColKeys } = content
        sortedColKeys &&
          setSortColKeys((pre) => {
            const newSort = [...pre]
            newSort[0] = sortedColKeys
            return newSort
          })
        hiddenColKeys && setHiddenColKeys(hiddenColKeys)
        const keyCodes: string[] = (sortedColKeys ?? []).filter(
          (key: string) => !hiddenColKeys.includes(key)
        )
        if (keyCodes.length === 1) {
          if (dateType === 3) {
            sortTableData({ keyCodes })
          } else {
            setSortFilters({ findFields: keyCodes })
          }
          !sortTableShow && setSortTableShow(true)
          !supportsExpand && dateType === 3 && setSupportsExpand(true)
        }
        setColumnConfig(item ?? {})
      } catch (err) {
        message.open({
          title:
            (err as ResponseType)?.message ||
            (err as ResponseType)?.msg ||
            intl.get('请求异常，请联系管理员'),
          type: 'error',
        })
      }
    }
    fn()
  })

  // 清空掉手动展开折叠的行
  const clearExpandKeys = () => {
    onChangeLeftExpandKeys([])
    onChangeTopExpandKeys([])
  }

  const clickExpend = useCallback(() => {
    setSupportsExpand((prev: boolean) => !prev)
    clearExpandKeys()
  }, [setSupportsExpand])

  const changeDateType = (type) => () => {
    setDrawerVisible(false)
    setDateType(type)
  }

  const clickSettingVisible = useCallback(() => setDrawerVisible(true), [setDrawerVisible])
  const closeSettingVisible = useCallback(() => setDrawerVisible(false), [setDrawerVisible])

  const tableShow = useMemo(
    () => visibleIndicators.length > 0 && data.length > 0 && matrix,
    [visibleIndicators, data, matrix]
  )
  const downloadShow = useMemo(() => !sortTableShow && dateType !== 4, [sortTableShow, dateType])

  useEffect(() => {
    dateType === 3 && setSupportsExpand(true)
    dateType === 4 && setSupportsExpand(false)
    clearExpandKeys()
  }, [dateType, setSupportsExpand])

  return (
    <div>
      <div className="cr-table-tools">
        <div className="buttons">
          {isFullscreen ? (
            [3, 4].includes(dateType) && (
              <span className="unit">
                {intl.get('单位：CNY')}
                {dateType === 4 && '(亿)'}
              </span>
            )
          ) : (
            <>
              <span
                className={cx('option', 'option-left', { active: dateType === 4 })}
                onClick={changeDateType(4)}
              >
                {intl.get('器件')}
              </span>
              <span
                className={cx('option', { active: dateType === 3 })}
                onClick={changeDateType(3)}
              >
                {intl.get('人员')}
              </span>
              <span
                className={cx('option', { active: dateType === 2 })}
                onClick={changeDateType(2)}
              >
                {intl.get('入库明细')}
              </span>
              <span
                className={cx('option', 'option-right', { active: dateType === 1 })}
                onClick={changeDateType(1)}
              >
                {intl.get('指标词典')}
              </span>
              <div className="no-dropdown-buttons">
                {tableShow ? (
                  <>
                    {dateType === 3 && (
                      <Tooltip title={intl.get('字段设置')}>
                        <i onClick={clickSettingVisible}>
                          <SettingOutlined color={PRICE_ICON_COLOR} />
                        </i>
                      </Tooltip>
                    )}

                    {[2, 3, 4].includes(dateType) && (
                      <Tooltip title={intl.get('下载')}>
                        <i onClick={handleExport}>
                          <DownloadOutlined color={PRICE_ICON_COLOR} />
                        </i>
                      </Tooltip>
                    )}
                    <Tooltip title={intl.get('进入全屏')}>
                      <i onClick={toggleFullscreen}>
                        <FullscreenOutlined color={PRICE_ICON_COLOR} />
                      </i>
                    </Tooltip>
                    {[3, 4].includes(dateType) && downloadShow && (
                      <i onClick={clickExpend}>
                        {supportsExpand ? (
                          <Tooltip title={intl.get('收起')}>
                            <MoveOutlined color={PRICE_ICON_COLOR} />
                          </Tooltip>
                        ) : (
                          <Tooltip title={intl.get('展开')}>
                            <DirectionDownOutlined color={PRICE_ICON_COLOR} />
                          </Tooltip>
                        )}
                      </i>
                    )}
                    {dateType === 2 &&
                      (isAscSort ? (
                        <Tooltip title={intl.get('降序')}>
                          <ArrowDownOutlined
                            onClick={() => setIsAscSort(false)}
                            color={PRICE_ICON_COLOR}
                          />
                        </Tooltip>
                      ) : (
                        <Tooltip title={intl.get('升序')}>
                          <ArrowUpOutlined
                            onClick={() => setIsAscSort(true)}
                            color={PRICE_ICON_COLOR}
                          />
                        </Tooltip>
                      ))}
                  </>
                ) : null}
              </div>
            </>
          )}
        </div>
        {isFullscreen ? (
          <Tooltip title={intl.get('退出全屏')} placement="left">
            <Button
              icon={<FullscreenExitOutlined />}
              appearance="link"
              onClick={toggleFullscreen}
            ></Button>
          </Tooltip>
        ) : (
          <div>
            {[3, 4].includes(dateType) ? (
              <>
                <span className="unit ml-6">
                  {intl.get('共')} {data.length || 0} {intl.get('条')}
                </span>
                <span className="unit ml-6">
                  {intl.get('单位：CNY')}
                  {dateType === 4 && '(亿)'}
                </span>
              </>
            ) : null}
          </div>
        )}
      </div>
      {tableShow ? (
        <>
          {dateType === 2 ? (
            <div className="voucher-container">
              <WarehouseVoucher expanded={expanded} />
            </div>
          ) : dateType === 1 ? (
            <WordBook expanded={expanded} />
          ) : (
            <CrossTable
              className={cx('bordered', { 'supports-expand': !supportsExpand })}
              BaseTableComponent={WebsiteBaseTable}
              isLoading={isLoading}
              defaultColumnWidth={100}
              useVirtual={true}
              leftMetaColumns={leftCodes.map((code) => dimMap.get(code)) as AnyType}
              leftTree={leftTreeRoot.children as AnyType}
              leftTotalNode={leftTreeRoot} // 当 leftTree 为空时，leftTotalNode 用于渲染总计行
              topTree={topTreeRoot.children as AnyType}
              getValue={(leftNode, topNode) => {
                const record = matrix?.get(leftNode.data.dataKey)?.get(topNode.data.dataKey)
                if (record == null) {
                  return '-'
                }
                const indicator = leftNode.data?.indicator ?? topNode.data?.indicator
                return record[indicator.code]
              }}
              render={(value, leftNode, topNode) => {
                const render = leftNode?.data?.indicator?.render ?? topNode.data?.indicator?.render
                return render ? render(value, leftNode, topNode) : value
              }}
            />
          )}
        </>
      ) : isLoading ? (
        <Loading content="">
          <div className="loading"></div>
        </Loading>
      ) : (
        <EmptyState
          className="empty-state mt-6"
          title={intl.get('暂无数据')}
          indicator={EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL}
        />
      )}
      <SettingDrawer
        visible={drawerVisible}
        onClose={closeSettingVisible}
        drawerProps={{ width: 400, title: intl.get('字段设置') }}
        columns={drawerColums}
        checkDisabledColKeys={disabledColkeys}
        hiddenColKeys={hiddenColKeys}
        sortedColKeys={sortedColKeys}
        onSetColKeysChange={onSetColKeysChange}
      />
      <ExportListModal visible={modalVisible} onCancel={detailClickExport} />
    </div>
  )
})
ReductionTable.displayName = 'ReductionTable'
export default ReductionTable
