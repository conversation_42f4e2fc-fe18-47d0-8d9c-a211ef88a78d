import intl from 'react-intl-universal'
import dayjs from 'dayjs'

export const PRICE_ICON_COLOR = '#5F6A7A'
export const UP_ICON_COLOR = 'rgb(244, 72, 92)'
export const DOWN_ICON_COLOR = 'rgb(0, 168, 84)'
export const CostSearchType = [
  'CVPN',
  'PN',
  'MPN_ID',
  'PN_CODE_DESC',
  'BRAND_NAME',
  'SOURCING_OPR_NAME_CN',
  'PURCHASE_USER_NAME_CN',
  'PROJECT_NAME',
  'ODM_MGR_DISPLAY_NAME',
  'PURCHASE_PATTERN_DESC',
  'DEPT',
  'TEAM_OWNER',
  'SUBJECT',
  'SUB_PROJECT_NAME',
  'DEPT_OWNER',
  'PN_CODE',
]
export const EXPOERT_TYPE = 'FLCV_COST_REDUCTION'
export const DEFAULT_DATE_RANGE = [
  // dayjs().subtract(1, 'month').format('YYYY-MM-DD'), // 测试用
  dayjs().startOf('year')?.format('YYYY-MM'),
  dayjs().endOf('year').format('YYYY-MM'),
]
export const PURCHASE_WAYS = [
  { id: intl.get(intl.get('小米直采')), title: '小米直采' },
  { id: intl.get(intl.get('海外小米本采')), title: '海外小米本采' },
  { id: intl.get(intl.get('ODM代采')), title: 'ODM代采' },
  { id: intl.get(intl.get('ODM自采自用')), title: 'ODM自采自用' },
  { id: intl.get(intl.get('EMS本采')), title: 'EMS本采' },
  { id: intl.get(intl.get('不涉及')), title: '不涉及' },
]
export const SUBJECT_WAYS = [
  { id: intl.get(intl.get('材料费')), title: '材料费' },
  { id: intl.get(intl.get('加工费')), title: '加工费' },
  { id: intl.get(intl.get('代采费')), title: '代采费' },
]
export const MAX_QUERY_LENGTH = 2000
export const EXPORT_STATUS = {
  0: intl.get('任务创建'),
  1: intl.get('进行中'),
  2: intl.get('完成'),
  3: intl.get('任务取消'),
  4: intl.get('任务异常'),
}
export enum FilterNameKey {
  categoryList = '品类',
  brandNames = '品牌',
  businessDate = '业务日期',
  cvpnCodes = 'CVPN',
  pnCodes = 'PN',
  mpnIds = 'MPN',
  pnCodeDescs = '物料描述',
  sourcingOprNameCns = '资源负责人',
  purchasePatternDescs = '采购渠道',
  depts = '部门',
  teamOwners = '小组负责人',
  subjects = '科目',
  projectNames = '项目',
  subProjectNames = '二级项目',
  catLvl3Names = '系列',
  catLvl4Names = '产品品牌',
  catLvl5Names = '机型',
  configs = '配置',
}
export const calcConditions = (priceFilter) => {
  const { cvpnCodes = [], pnCodes = [], mpnIds = [] } = priceFilter || {}
  const isNotValid = {
    CVPN: cvpnCodes.length > MAX_QUERY_LENGTH,
    PN: pnCodes.length > MAX_QUERY_LENGTH,
    'MPN-ID': mpnIds.length > MAX_QUERY_LENGTH,
  }
  if (isNotValid.CVPN || isNotValid.PN || isNotValid['MPN-ID']) {
    return ['CVPN', 'PN', 'MPN-ID'].filter((i) => isNotValid[i]).join(' ')
  } else {
    return ''
  }
}

export const colWidth = {
  source: 120,
  postingDay: 120,
  createDay: 120,
  postingMonth: 120,
  cvpnCode: 150,
  pnCode: 150,
  matNo: 160,
  macDocType: 120,
  macDocNo: 120,
  macDocItem: 120,
  macDocYear: 120,
  moveType: 180,
  poNo: 120,
  poItemNo: 120,
  conditionPrice: 120,
  conditionUnit: 120,
  curr: 120,
  cnt: 120,
  amt: 150,
  rmbAmt: 130,
  unit: 120,
  subject: 120,
  baselineMonth: 120,
  baselineInCnt: 120,
  baselineInRmbAmt: 150,
  baselineInUnitCost: 150,
  inCnt: 150,
  inRmbAmt: 150,
  inUnitCost: 150,
  reduceRmbAmt: 150,
}

export const amountCols = [
  'conditionPrice',
  'amt',
  'rmbAmt',
  'baselineInRmbAmt',
  'baselineInUnitCost',
  'inRmbAmt',
  'inUnitCost',
  'reduceRmbAmt',
]
