import intl from 'react-intl-universal'
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useRef, useState } from 'react'
import { useFullscreen, useMount } from 'ahooks'
import SearchPannel from '@mi/sc-ui-search-panel'
import { FormDatePicker, FormSelect } from '@mi/sc-ui-pro-form'
import { Card } from '@hi-ui/card'
import { Chart } from '@antv/g2'
import Button from '@hi-ui/button'
import {
  DownloadOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  SettingOutlined,
  FreezeOutlined,
} from '@hi-ui/icons'
import Table, { TableColumnItem, SettingDrawer } from '@hi-ui/table'
import Dropdown from '@hi-ui/dropdown'
import { data } from './mockData'
import { FrozenColumns, Columns } from './constants'
import { ConditionsType, TableData } from './types'
import * as API from '@/api/cost/mpnid-cost'
import dayjs from 'dayjs'

import './index.scss'
import { ellipses } from '@/components/ellipsis-tool'
import { ProLocaleProvider } from '@mi/sc-ui-i18n'

const ExtraNode = () => {
  return <div>{intl.get('更新日期：实时')}</div>
}

const MpnidCost: React.FC = () => {
  const chartContainer = useRef<HTMLDivElement | null>(null)
  const [columnsConfigVisible, setColumnsConfigVisible] = useState<boolean>(false)
  const tableContainer = useRef<HTMLDivElement | null>(null)
  const [isFullscreen, { enterFullscreen, exitFullscreen }] = useFullscreen(tableContainer)
  const [hiddenColKeys, setHiddenColKeys] = React.useState<string[]>([])
  const [sortedColKeys, setSortColKeys] = React.useState<string[]>([])
  const [columns, setColumns] = useState<TableColumnItem[]>(Columns)
  const [frozenKey, setFrozenKey] = useState<string>('')
  const [conditions, setConditions] = useState<ConditionsType>({
    mpnIds: [],
    costMatCatLvl2Codes: [],
    cvpnCodes: [],
    matCatLvl1Codes: [],
    matCatLvl2Codes: [],
    skus: [],
    projectNames: [],
    saleVersions: [],
    projectSeries: [],
  })

  const [tableData, setTableData] = useState<TableData>({
    tableList: [],
    pageSize: 0,
    pageNum: 0,
    total: 0,
  })

  // 表格列排序，隐藏等
  const handleColumnsChange = (sortedColKeys: string[], hiddenColKeys: string[], newColumns) => {
    setColumns(newColumns.concat(Columns.filter((item) => item.children)))
    setSortColKeys(sortedColKeys)
    setHiddenColKeys(hiddenColKeys)
  }

  // 设置列
  const handleColumnsCancel = () => {
    setColumnsConfigVisible(false)
  }

  const getConditions = () => {
    const _conditions: ConditionsType = conditions
    API.getConditions().then((res: any) => {
      Object.keys(res.data).forEach((key) => {
        _conditions[key] = res.data[key].map((item) => {
          return { id: item, title: item }
        })
      })
      setConditions(_conditions)
    })
  }

  useMount(() => {
    getConditions()
    const chart = new Chart({
      container: chartContainer.current as HTMLDivElement,
      autoFit: true,
      height: 260,
    })

    // 设置数据
    chart.data(data)
    // 设置度量
    chart.scale({
      month: {
        range: [0, 1],
      },
      temperature: {
        nice: true,
      },
    })

    // 提示信息
    chart.tooltip({
      showCrosshairs: true,
      shared: true,
    })

    // 图例
    chart.legend({
      position: 'top',
      marker: {
        symbol: 'circle',
      },
    })

    // 绘制线
    chart.line().position('month*temperature').color('city').shape('smooth')

    // 绘制点
    chart.point().position('month*temperature').color('city').shape('circle')

    // 绘制单位
    chart.guide().text({
      top: true,
      position: ['start', 'end'],
      content: intl.get('单位：元'),
      offsetX: -20,
      offsetY: -30,
    })

    // 绘制辅助线
    chart.render()
  })

  const handleSearch = (formData: any) => {
    return API.getList(formData).then((res: any) => {
      setTableData(res.data)
    })
  }

  return (
    <div className="mpnid-cost">
      <Card className="top-card">
        <ProLocaleProvider locale={intl.getInitOptions().currentLocale as any}>
          <SearchPannel
            initialValues={{
              timeRange: [
                dayjs().format('YYYY-MM'),
                dayjs(dayjs().year() + 1 + '-' + dayjs().month()).format('YYYY-MM'),
              ],
              projectNames: '',
              projectSeries: '',
              saleVersions: '',
              mpnIds: '',
              cvpnCodes: '',
              matCatLvl1Codes: '',
              matCatLvl2Codes: '',
              type: '',
              skus: '',
            }}
            labelPlacement="right"
            labelWidth={110}
            onSubmit={handleSearch}
          >
            <FormDatePicker
              label={ellipses(intl.get('成本预估区间'))}
              field="timeRange"
              required
              valueType="array"
              rules={[{ required: true, message: intl.get('请选择成本预估区间') }]}
              fieldProps={{ format: 'YYYY-MM', type: 'monthrange' }}
            />
            <FormSelect
              label={ellipses(intl.get('项目'))}
              field="projectNames"
              fieldProps={{ data: conditions.projectNames }}
            />
            <FormSelect
              label={ellipses(intl.get('系列'))}
              field="projectSeries"
              fieldProps={{ data: conditions.projectSeries }}
            />
            <FormSelect
              label={ellipses(intl.get('区域'))}
              field="saleVersions"
              fieldProps={{ data: conditions.saleVersions }}
            />
            <FormSelect label="MPN" field="mpnIds" fieldProps={{ data: conditions.mpnIds }} />
            <FormSelect
              label="CVPN"
              field="cvpnCodes"
              fieldProps={{ data: conditions.cvpnCodes }}
            />
            <FormSelect
              label={ellipses(intl.get('中类'))}
              field="matCatLvl2Codes"
              fieldProps={{ data: conditions.matCatLvl2Codes }}
            />
            <FormSelect
              label={ellipses(intl.get('大类'))}
              field="matCatLvl1Codes"
              fieldProps={{ data: conditions.matCatLvl1Codes }}
            />
            <FormSelect
              label={ellipses(intl.get('成本组中类'))}
              field="costMatCatLvl2Codes"
              fieldProps={{ data: conditions.costMatCatLvl2Codes }}
            />
            <FormSelect label="SKU" field="skus" fieldProps={{ data: conditions.skus }} />
          </SearchPannel>
        </ProLocaleProvider>
      </Card>
      <div style={{ height: 12 }}></div>
      <Card title={intl.get('报表')} extra={<ExtraNode />}>
        <div id="container" ref={chartContainer}></div>
      </Card>
      <div style={{ height: 12 }}></div>
      <Card title={intl.get('明细')}>
        <div className="table-operator">
          <Button
            icon={<SettingOutlined />}
            type="default"
            appearance="unset"
            onClick={() => {
              setColumnsConfigVisible(true)
            }}
          />
          <Button icon={<DownloadOutlined />} type="default" appearance="unset" />
          <Button
            icon={<FullscreenOutlined />}
            type="default"
            appearance="unset"
            onClick={enterFullscreen}
          />
          <Dropdown
            data={FrozenColumns}
            title={intl.get('冻结列')}
            trigger="click"
            overlay={{ placement: 'bottom-start' }}
            onClick={(key) => {
              setFrozenKey(key as string)
            }}
          >
            <Button
              icon={<FreezeOutlined />}
              type="default"
              appearance="unset"
              style={{ marginLeft: 12, color: '#ffffff' }}
            />
          </Dropdown>
        </div>
        <div ref={tableContainer}>
          {isFullscreen && (
            <div className="fullscreen-operator">
              <Button
                icon={<FullscreenExitOutlined />}
                type="default"
                appearance="unset"
                onClick={exitFullscreen}
              />
            </div>
          )}

          <Table
            fixedToColumn={{ left: frozenKey }}
            columns={columns}
            data={tableData.tableList || []}
            size="sm"
          />
        </div>
      </Card>
      <SettingDrawer
        visible={columnsConfigVisible}
        onClose={handleColumnsCancel}
        drawerProps={{ width: 400, title: intl.get('字段设置') }}
        columns={Columns}
        onSetColKeysChange={handleColumnsChange}
        hiddenColKeys={hiddenColKeys}
        sortedColKeys={sortedColKeys}
      />
    </div>
  )
}

export default MpnidCost
