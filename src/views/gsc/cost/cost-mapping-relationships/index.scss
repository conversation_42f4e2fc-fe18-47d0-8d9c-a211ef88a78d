.mapping-container {
  height: 100%;

  .query-wrapper {
    background-color: #fff;
    border-radius: 6px 6px 0 0;
    padding: 12px 16px;
  }

  .cost-table-wrapper {
    background-color: #fff;
    border-radius: 0 0 6px 6px;
    padding: 8px 16px 12px;

    .hi-v4-table {
      overflow: visible;

      .hi-v4-table-cell {
        padding: 9px 16px;
      }

      &::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 1px;
        top: -15px;
        left: 0;
        background-color: rgb(242 244 247);
      }
    }
  }
}

.cost-table-wrapper.cost-fullscreen-wrapper {
  .hi-v4-table-body {
    height: calc(100vh - 152px);
  }
}

.cost-table-wrapper.expand {
  height: calc(100vh - 186px);

  .hi-v4-table-body {
    height: calc(100vh - 252px);
  }
}

.cost-table-wrapper.no-expand {
  height: calc(100vh - 274px);

  .hi-v4-table-body {
    height: calc(100vh - 342px);
  }
}
