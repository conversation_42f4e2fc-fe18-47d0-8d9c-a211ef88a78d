.mapping-table-container {
  min-width: 660px;
  margin-top: 12px;

  .table-container {
    min-width: 660px;
    margin-top: 12px;

    .table-wrapper {
      .hi-v4-table {
        height: 100%;

        .hi-v4-table__wrapper {
          height: 100%;

          .hi-v4-table-body {
            overflow: overlay;
            height: calc(100vh - 295px);

            .active-column-text {
              color: #237ffa;

              &:hover {
                cursor: pointer;
                filter: brightness(1.5);
              }
            }
          }
        }
      }
    }
  }

  .footer-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    padding-left: 12px;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 56px;
    background-color: #fff;
    box-shadow: 0 -4px 4px rgb(0 0 0 / 5%);

    .selected_span {
      margin-left: 8px;
      margin-right: 18px;
      position: relative;
      top: 2px;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #1f2733;
    }
  }

  .table-tools {
    display: flex;
    align-items: center;
    height: 32px;
    background-color: #fff;
    position: relative;
    margin-top: 6px;

    .no-dropdown-buttons {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      i {
        width: 32px;
        height: 32px;
        cursor: pointer;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        color: #5f6a7a;

        &:hover {
          background: #f2f4f7;
        }
      }
    }
  }
}
