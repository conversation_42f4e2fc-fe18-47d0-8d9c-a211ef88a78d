import { CSSProperties, Dispatch, SetStateAction } from 'react'
export interface SearchType {
  matCatLvl1Code?: string
  matCatLvl2Code?: string
  matCatLvl3Code?: string
  costMatCatLvl1CodeId?: string
  costMatCatLvl2CodeId?: string
  costMatCatLvl3CodeId?: string
  status?: string
}
export interface cateType {
  costCategories: object
  matCategories: object
}
export const MappingStatusMap = {
  0: '新增',
  1: '待提交',
  2: '审批中',
  3: '已审批',
}
export const INIT_QUERY_DATA = {
  matCatLvl1Code: '',
  matCatLvl2Code: '',
  matCatLvl3Code: '',
  costMatCatLvl1CodeId: '',
  costMatCatLvl2CodeId: '',
  costMatCatLvl3CodeId: '',
  status: '',
}
export type SearchSetType<T> = Dispatch<SetStateAction<T>>
export type CommonType = Record<string, boolean | CSSProperties | undefined>
export const PRICE_ICON_COLOR = '#5F6A7A'
export const MAT_TABLE_UPLOAD = 'mat-upload-type'
