import intl from 'react-intl-universal'
import React, { memo, useCallback } from 'react'
import Portal from '@/components/portal'
import But<PERSON> from '@hi-ui/button'
import { CloudUploadOutlined } from '@hi-ui/icons'
import { HEADER_TOOLS_PORTAL, MESSAGE_DURATION } from '@/constants'
import './dataTransformButton.scss'
import { MAT_TABLE_UPLOAD, SearchSetType, SearchType } from './config'
import message from '@hi-ui/message'
import { useRequest } from 'ahooks'
import {
  exportMatList,
  generatePreUploadUrl,
  uploadMatList,
} from '@/api/cost/cost-mapping-relationships'
import { ResponseType } from '@/types/type'
import Upload from '@hi-ui/upload'

const DataTransformButton = memo<{
  onSearch: SearchSetType<AnyType>
  filter: SearchType
}>(({ filter, onSearch }) => {
  const portalRoot = document.getElementById(HEADER_TOOLS_PORTAL) || document.body
  const { run: runExport } = useRequest((data) => exportMatList(data), {
    manual: true,
    onBefore: () => {
      message.open({ title: intl.get('导出中...'), type: 'info', autoClose: false })
    },
    onSuccess: (res) => {
      message.closeAll()
      const url = res?.data || ''
      if (url) {
        window.open(url)
        message.open({ title: intl.get('导出成功'), type: 'success', duration: MESSAGE_DURATION })
      } else {
        message.open({
          type: 'error',
          title: intl.get('导出链接为空，请联系管理员'),
          duration: MESSAGE_DURATION,
        })
      }
    },
    onError: (err) => {
      message.closeAll()
      message.open({
        title: err?.message || intl.get('导出失败'),
        type: 'error',
        duration: MESSAGE_DURATION,
      })
    },
  })

  const { run: conductUploadData } = useRequest((data) => uploadMatList({ objectName: data }), {
    manual: true,
    onBefore: () => {
      message.open({ title: intl.get('上传中...'), type: 'info' })
    },
    onSuccess: () => {
      message.closeAll()
      onSearch((pre) => ({ ...pre }))
      message.open({ title: intl.get('上传成功'), type: 'success', duration: MESSAGE_DURATION })
    },
    onError: (err) => {
      message.closeAll()
      message.open({
        title: (err as ResponseType)?.message || (err as ResponseType)?.msg || intl.get('上传失败'),
        type: 'error',
        duration: MESSAGE_DURATION,
      })
    },
  })

  const handleUpload = useCallback(
    (files) => {
      const data = files?.[0]
      const name = data?.name
      generatePreUploadUrl({ category: MAT_TABLE_UPLOAD, fileName: name }).then((res) => {
        const { presignedUri = '' } = res?.data || {}
        fetch(presignedUri, {
          method: 'PUT',
          headers: {
            'Content-Type': '',
          },
          body: data,
        })
          .then((res) => {
            return res.json()
          })
          .then((res) => {
            // 更新文件列表
            const { objectName = '' } = res || {}
            conductUploadData(objectName)
          })
      })
    },
    [conductUploadData]
  )

  const handleExport = useCallback(() => {
    runExport(filter)
  }, [filter, runExport])
  return (
    <div>
      <Portal container={portalRoot}>
        <div className="flex justify-between items-center">
          <Button type="secondary" onClick={handleExport} style={{ marginRight: 12 }}>
            {intl.get('导出')}
          </Button>
          <Upload
            type="default"
            accept="application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            headers={{ 'Content-Type': '' }}
            disabled={false}
            customUpload={handleUpload}
            style={{ marginRight: 12 }}
          >
            <Button type="secondary" icon={<CloudUploadOutlined />}>
              {intl.get('批量导入')}
            </Button>
          </Upload>
          {/* <Button type="primary">{intl.get('提交')}</Button>
          <Button type="primary">{intl.get('批量提交')}</Button> */}
        </div>
      </Portal>
    </div>
  )
})

DataTransformButton.displayName = 'DataTransformButton'
export default DataTransformButton
