import React, { FC, useRef } from 'react'
import { useFullscreen, useRequest } from 'ahooks'
import { ErrorBoundary } from '@sentry/react'
import cx from 'classnames'
import DataTransformButton from './dataTransformButton'
import './index.scss'
import { getCostHistoryConditions } from '@/api/cost/cost-mapping-relationships'
import { useCostBusinessMapping } from '@/hooks/useCostBusinessMapping'
import QueryForm from './queryForm'
import TableList from './table'

const PageView: FC = () => {
  const fullscreenDomRef = useRef<HTMLDivElement>(null)
  const [isFullscreen, { toggleFullscreen }] = useFullscreen(fullscreenDomRef)
  const { filter, setFilter, expanded, setCategories, setSelectData, setEditData } =
    useCostBusinessMapping()

  useRequest(getCostHistoryConditions, {
    onSuccess: (res) => {
      const data = res?.data || {}
      setCategories({ costCategories: data.costCategories, matCategories: data.matCategories })
      const processCostList = (costCategories) => {
        return costCategories.map((item) => item)
      }
      const processMatList = (matCategories) => {
        return matCategories.map((item) => item)
      }
      const finalSelectValues = Object.keys(data)?.reduce((a, b) => {
        if (b === 'costCategories') {
          a[b] = processCostList(data[b]).map((i) => ({ id: i.code, title: i.name }))
        } else if (b === 'matCategories') {
          a[b] = processMatList(data[b]).map((i) => ({ id: i.code, title: i.name }))
        } else {
          a[b] = data[b].map((i) => ({ id: i.value, title: i.name }))
        }
        return a
      }, {})
      setSelectData({ ...finalSelectValues })
      setEditData({ ...finalSelectValues })
    },
  })

  return (
    <div className="mapping-container">
      <ErrorBoundary>
        <DataTransformButton filter={filter} onSearch={setFilter} />
      </ErrorBoundary>
      <ErrorBoundary>
        <div className="query-wrapper">
          <QueryForm />
        </div>
      </ErrorBoundary>
      <ErrorBoundary>
        <div
          className={cx('cost-table-wrapper', {
            'cost-fullscreen-wrapper': isFullscreen,
            'no-expand': !expanded,
            expand: expanded,
          })}
          ref={fullscreenDomRef}
        >
          <TableList toggleFullscreen={toggleFullscreen} isFullscreen={isFullscreen} />
        </div>
      </ErrorBoundary>
    </div>
  )
}
PageView.displayName = 'PageView'
export default PageView
