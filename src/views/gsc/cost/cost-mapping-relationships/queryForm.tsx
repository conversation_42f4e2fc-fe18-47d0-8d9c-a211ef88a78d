import './queryForm.scss'

import { useMount, useSafeState } from 'ahooks'
import React, { memo, useCallback, useRef } from 'react'
import intl from 'react-intl-universal'

import { ellipses } from '@/components/ellipsis-tool'
import { useCostBusinessMapping } from '@/hooks/useCostBusinessMapping'
import { CHECK_SELECT_PROPS, getSelectData } from '@/utils/select-data'
import Button from '@hi-ui/button'
import { Form, FormHelpers, FormItem } from '@hi-ui/form'
import { Col, Row } from '@hi-ui/grid'
import { DownOutlined } from '@hi-ui/icons'
import Select from '@hi-ui/select'

import { INIT_QUERY_DATA, SearchType } from './config'

const QueryForm = memo(() => {
  const { setFilter, categories, expanded, setExpanded, selectData, setSelectData } =
    useCostBusinessMapping()
  const formRef = useRef<FormHelpers>(null)
  const initQueryCondition = useRef<SearchType>(INIT_QUERY_DATA)
  const [costLvl2Disabled, setCostLvl2Disabled] = useSafeState<boolean>(true)
  const [costLvl3Disabled, setCostLvl3Disabled] = useSafeState<boolean>(true)
  const [matLvl2Disabled, setMatLvl2Disabled] = useSafeState<boolean>(true)
  const [matLvl3Disabled, setMatLvl3Disabled] = useSafeState<boolean>(true)

  const handleReset = useCallback(() => {
    formRef.current?.reset()
    setCostLvl2Disabled(true)
    setMatLvl2Disabled(true)
  }, [setCostLvl2Disabled, setMatLvl2Disabled])
  const handleSearch = useCallback(() => {
    formRef.current?.validate()?.then((values) => {
      setFilter({ ...values })
    })
  }, [setFilter])

  // 处理物料
  const handleMatChange = useCallback(
    (matCatLvl1Code, matCatLvl2Code, _changedValues) => {
      const form = formRef.current
      if (matCatLvl1Code) {
        const selectedCategory = Object.values(categories?.matCategories).find(
          (item) => item.code === matCatLvl1Code
        )
        if (selectedCategory && selectedCategory.children) {
          const lvl2Options = selectedCategory.children.map((item) => ({
            id: item.code,
            title: item.name,
          }))
          setSelectData({ ...selectData, matCatLvl2: lvl2Options })
        } else {
          setSelectData({ ...selectData, matCatLvl2: [] })
        }
      }
      setMatLvl2Disabled(!matCatLvl1Code)
      !matCatLvl1Code && form?.setFieldsValue({ matCatLvl2Code: '' })
      if (matCatLvl2Code) {
        const selectedLvl2 = Object.values(categories?.matCategories)
          .find((item) => item.code === matCatLvl1Code)
          .children.find((item) => item.code === matCatLvl2Code)
        if (selectedLvl2 && selectedLvl2.children) {
          const lvl3Options = selectedLvl2.children.map((item) => ({
            id: item.code,
            title: item.name,
          }))
          setSelectData((prevData) => ({ ...prevData, matCatLvl3: lvl3Options }))
        } else {
          setSelectData((prevData) => ({ ...prevData, matCatLvl3: [] }))
        }
      }
      setMatLvl3Disabled(!matCatLvl2Code)
      ;(!matCatLvl2Code || !matCatLvl1Code) && form?.setFieldsValue({ matCatLvl3Code: '' })
      if (_changedValues.matCatLvl1Code) {
        formRef.current?.setFieldsValue({
          matCatLvl2Code: '',
        })
      }
      if (_changedValues.matCatLvl1Code || _changedValues.matCatLvl2Code) {
        formRef.current?.setFieldsValue({
          matCatLvl3Code: '',
        })
        !_changedValues.matCatLvl2Code && setMatLvl3Disabled(true)
      }
    },
    [categories, selectData, setMatLvl2Disabled, setMatLvl3Disabled, setSelectData]
  )
  // 处理成本
  const handleCostChange = useCallback(
    (costMatCatLvl1CodeId, costMatCatLvl2CodeId, _changedValues) => {
      const form = formRef.current
      if (costMatCatLvl1CodeId) {
        const selectedCategory = Object.values(categories?.costCategories).find(
          (item) => item.code === costMatCatLvl1CodeId
        )
        if (selectedCategory && selectedCategory.children) {
          const lvl2Options = selectedCategory.children.map((item) => ({
            id: item.code,
            title: item.name,
          }))
          setSelectData({ ...selectData, costMatCatLvl2Name: lvl2Options })
        } else {
          setSelectData({ ...selectData, costMatCatLvl2Name: [] })
        }
      }
      // 成本大类选择，可选成本中类
      setCostLvl2Disabled(!costMatCatLvl1CodeId)
      !costMatCatLvl1CodeId && form?.setFieldsValue({ costMatCatLvl2CodeId: '' })
      if (costMatCatLvl2CodeId) {
        const selectedLvl2 = Object.values(categories?.costCategories)
          .find((item) => item.code === costMatCatLvl1CodeId)
          .children.find((item) => item.code === costMatCatLvl2CodeId)
        if (selectedLvl2 && selectedLvl2.children) {
          const lvl3Options = selectedLvl2.children.map((item) => ({
            id: item.code,
            title: item.name,
          }))
          setSelectData((prevData) => ({ ...prevData, costMatCatLvl3Name: lvl3Options }))
        } else {
          setSelectData((prevData) => ({ ...prevData, costMatCatLvl3Name: [] }))
        }
      }
      // 成本中类选择，可选成本小类
      setCostLvl3Disabled(!costMatCatLvl2CodeId)
      // 成本大类更改，重置成本中小类
      ;(!costMatCatLvl2CodeId || !costMatCatLvl1CodeId) &&
        form?.setFieldsValue({ costMatCatLvl3CodeId: '' })
      if (_changedValues.costMatCatLvl1CodeId) {
        formRef.current?.setFieldsValue({
          costMatCatLvl2CodeId: '',
        })
      }
      if (_changedValues.costMatCatLvl1CodeId || _changedValues.costMatCatLvl2CodeId) {
        formRef.current?.setFieldsValue({
          costMatCatLvl3CodeId: '',
        })
        !_changedValues.costMatCatLvl2CodeId && setCostLvl3Disabled(true)
      }
    },
    [categories, selectData, setCostLvl2Disabled, setCostLvl3Disabled, setSelectData]
  )

  useMount(() => {
    setFilter(initQueryCondition.current)
  })

  return (
    <div
      className="query-content overflow-hidden transition-[height]"
      style={{
        height: expanded ? 44 : 135,
        display: 'flex',
        justifyContent: 'space-between',
      }}
    >
      <Form
        labelPlacement="right"
        showColon={false}
        initialValues={initQueryCondition.current}
        innerRef={formRef}
        labelWidth={86}
        onValuesChange={(_changedValues, allValues) => {
          const { matCatLvl1Code, matCatLvl2Code, costMatCatLvl1CodeId, costMatCatLvl2CodeId } =
            allValues
          handleMatChange(matCatLvl1Code, matCatLvl2Code, _changedValues)
          handleCostChange(costMatCatLvl1CodeId, costMatCatLvl2CodeId, _changedValues)
        }}
      >
        <Row gutter rowGap={0}>
          <Col span={8} justify="flex-start">
            <FormItem field="matCatLvl1Code" label={ellipses('物料大类')}>
              <Select
                placeholder="请选择"
                data={getSelectData(selectData.matCategories)}
                {...CHECK_SELECT_PROPS}
              />
            </FormItem>
          </Col>
          <Col span={8} justify="flex-start">
            <FormItem field="matCatLvl2Code" label={ellipses('物料中类')}>
              <Select
                placeholder="请选择"
                data={getSelectData(selectData.matCatLvl2)}
                disabled={matLvl2Disabled}
                {...CHECK_SELECT_PROPS}
              />
            </FormItem>
          </Col>
          <Col span={8} justify="flex-start">
            <FormItem field="matCatLvl3Code" label={ellipses('物料小类')}>
              <Select
                placeholder="请选择"
                data={getSelectData(selectData.matCatLvl3)}
                disabled={matLvl2Disabled || matLvl3Disabled}
                {...CHECK_SELECT_PROPS}
              />
            </FormItem>
          </Col>
        </Row>
        <Row gutter rowGap={0}>
          <Col span={8} justify="flex-start">
            <FormItem field="costMatCatLvl1CodeId" label={ellipses('成本大类')}>
              <Select
                placeholder="请选择"
                data={getSelectData(selectData.costCategories)}
                {...CHECK_SELECT_PROPS}
              />
            </FormItem>
          </Col>
          <Col span={8}>
            <FormItem field="costMatCatLvl2CodeId" label={ellipses('成本中类')}>
              <Select
                placeholder="请选择"
                data={getSelectData(selectData.costMatCatLvl2Name)}
                disabled={costLvl2Disabled}
                {...CHECK_SELECT_PROPS}
              />
            </FormItem>
          </Col>
          <Col span={8}>
            <FormItem field="costMatCatLvl3CodeId" label={ellipses('成本小类')}>
              <Select
                placeholder="请选择"
                data={getSelectData(selectData.costMatCatLvl3Name)}
                disabled={costLvl2Disabled || costLvl3Disabled}
                {...CHECK_SELECT_PROPS}
              />
            </FormItem>
          </Col>
        </Row>
        <Row gutter rowGap={0}>
          <Col span={8} justify="flex-start">
            <FormItem field="status" label={ellipses('状态')}>
              <Select
                placeholder="请选择"
                data={getSelectData(selectData.statuses)}
                {...CHECK_SELECT_PROPS}
              />
            </FormItem>
          </Col>
        </Row>
      </Form>
      <div className="ml-3 pl-3">
        <div className="w-max">
          <Button appearance="link" onClick={() => setExpanded((expanded) => !expanded)}>
            {expanded ? intl.get('展开') : intl.get('收起')}
            <DownOutlined
              className="transition-transform ml-2"
              color="#9d9d9f"
              style={{
                transform: expanded ? 'rotateZ(0)' : 'rotateZ(180deg)',
              }}
            />
          </Button>
          <Button type="default" onClick={handleReset}>
            {intl.get('重置')}
          </Button>
          <Button type="secondary" onClick={handleSearch}>
            {intl.get('查询')}
          </Button>
        </div>
      </div>
    </div>
  )
})
QueryForm.displayName = 'QueryForm'
export default QueryForm
