import intl from 'react-intl-universal'
import React, { memo, useCallback, useEffect, useMemo } from 'react'
import cx from 'classnames'
import Table, { TableColumnItem } from '@hi-ui/table'
import { usePagination, useSafeState } from 'ahooks'
import Button from '@hi-ui/button'
import { ColumnHeightOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@hi-ui/icons'
import Pagination from '@hi-ui/pagination'
import { EmptyFunctionType, ResponseType } from '@/types/type'
import { PRICE_ICON_COLOR, MappingStatusMap } from './config'
import './table.scss'
import { ellipses } from '@/components/ellipsis-tool'
import dayjs from 'dayjs'
import Tooltip from '@hi-ui/tooltip'
import { getTextWidth } from '@/utils/get-text-width'
import Dropdown from '@hi-ui/dropdown'
import Drawer from '@hi-ui/drawer'
import { Form, FormHelpers, FormItem } from '@hi-ui/form'
import { getMatList, MatListType, updateMat } from '@/api/cost/cost-mapping-relationships'
import message from '@hi-ui/message'
import Select from '@hi-ui/select'
import { CHECK_SELECT_PROPS, getSelectData } from '@/utils/select-data'
import { customMessage } from '@/utils/custom-message'
import { useCostBusinessMapping } from '@/hooks/useCostBusinessMapping'
import { HiBaseSizeEnum } from '@hi-ui/core'

const TableList = memo<{
  toggleFullscreen: EmptyFunctionType
  isFullscreen: boolean
}>(({ toggleFullscreen, isFullscreen = false }) => {
  const { filter, categories, setEditData, editData } = useCostBusinessMapping()
  const [columns, setColumns] = useSafeState<TableColumnItem[]>([])
  const [tableSize, setTableSize] = useSafeState<HiBaseSizeEnum>('sm')
  const [drawerVisible, setDrawerVisible] = useSafeState<boolean>(false)
  const formRef = React.useRef<FormHelpers>(null)
  const [initialValues, setInitialValues] = useSafeState<object[]>([])
  const [costLvl2Disabled, setCostLvl2Disabled] = useSafeState<boolean>(true)
  const [costLvl3Disabled, setCostLvl3Disabled] = useSafeState<boolean>(true)
  const {
    data: listResult,
    run: getList,
    pagination,
    loading: tableLoading,
  } = usePagination(
    ({ current, pageSize }) => {
      const params = { ...filter }
      Object.keys(params).forEach((key) => {
        if (params[key] === '') delete params[key]
      })
      return getMatList({
        ...params,
        pageNum: current,
        pageSize,
      } as MatListType)
    },
    {
      manual: true,
      refreshDeps: [filter],
      defaultPageSize: 20,
    }
  )

  useEffect(() => {
    Object.keys(filter).length && getList({ pageSize: pagination.pageSize, current: 1 })
  }, [filter, pagination.pageSize, getList])

  const tableColumnRender = (text: string, textWidth: number) => {
    return !text || text === 'null' ? (
      '-'
    ) : getTextWidth(text, 14) > textWidth - 36 ? (
      <Tooltip title={<div style={{ maxWidth: 430, wordWrap: 'break-word' }}>{text}</div>}>
        <span className="truncate block">{text}</span>
      </Tooltip>
    ) : (
      <span className="block">{text}</span>
    )
  }

  // 处理成本
  const handleCostChange = useCallback(
    (costMatCatLvl1CodeId, costMatCatLvl2CodeId) => {
      const form = formRef.current
      if (costMatCatLvl1CodeId) {
        const selectedCategory = Object.values(categories?.costCategories).find(
          (item) => item.code === costMatCatLvl1CodeId
        )
        if (selectedCategory && selectedCategory.children) {
          const lvl2Options = selectedCategory.children.map((item) => ({
            id: item.code,
            title: item.name,
          }))
          setEditData({ ...editData, costMatCatLvl2Name: lvl2Options })
        } else {
          setEditData({ ...editData, costMatCatLvl2Name: [] })
        }
      }
      setCostLvl2Disabled(!costMatCatLvl1CodeId)
      !costMatCatLvl1CodeId && form?.setFieldsValue({ costMatCatLvl2CodeId: '' })
      if (costMatCatLvl2CodeId) {
        const selectedLvl2 = Object.values(categories?.costCategories)
          .find((item) => item.code === costMatCatLvl1CodeId)
          .children.find((item) => item.code === costMatCatLvl2CodeId)
        if (selectedLvl2 && selectedLvl2.children) {
          const lvl3Options = selectedLvl2.children.map((item) => ({
            id: item.code,
            title: item.name,
          }))
          setEditData((prevData) => ({ ...prevData, costMatCatLvl3Name: lvl3Options }))
        } else {
          setEditData((prevData) => ({ ...prevData, costMatCatLvl3Name: [] }))
        }
      }
      setCostLvl3Disabled(!costMatCatLvl2CodeId)
      ;(!costMatCatLvl2CodeId || !costMatCatLvl1CodeId) &&
        form?.setFieldsValue({ costMatCatLvl3CodeId: '' })
    },
    [categories?.costCategories, editData, setCostLvl2Disabled, setCostLvl3Disabled, setEditData]
  )

  const handleOpenDrawer = useCallback(
    (record) => {
      const { costMatCatLvl1CodeId, costMatCatLvl2CodeId, costMatCatLvl3CodeId } = record
      const lvl1 = costMatCatLvl1CodeId === 0 ? '' : String(costMatCatLvl1CodeId)
      const lvl2 = costMatCatLvl2CodeId === 0 ? '' : String(costMatCatLvl2CodeId)
      const lvl3 = costMatCatLvl3CodeId === 0 ? '' : String(costMatCatLvl3CodeId)
      handleCostChange(lvl1, lvl2)
      setInitialValues({
        ...record,
        flag: Number(record.flag),
        costMatCatLvl1CodeId: lvl1,
        costMatCatLvl2CodeId: lvl2,
        costMatCatLvl3CodeId: lvl3,
      })
      setDrawerVisible(true)
    },
    [handleCostChange, setDrawerVisible, setInitialValues]
  )

  const closeDrawer = useCallback(() => {
    setDrawerVisible(false)
    formRef.current?.reset()
    setCostLvl2Disabled(true)
  }, [setCostLvl2Disabled, setDrawerVisible])

  const EditMatItem = () => {
    formRef.current?.validate()?.then((values) => {
      const finalValues = {
        costMatCatLvl1CodeId: values.costMatCatLvl1CodeId,
        costMatCatLvl2CodeId: values.costMatCatLvl2CodeId,
        costMatCatLvl3CodeId: values.costMatCatLvl3CodeId,
        id: values.id,
        flag: values.flag,
      }
      updateMat(finalValues)
        .then((res) => {
          if (res === 'ok') {
            message.open({
              title: '编辑成功！！',
              type: 'success',
            })
            closeDrawer()
            // 更新表格
            getList({ pageSize: pagination.pageSize, current: 1 })
          }
        })
        .catch((err) => {
          customMessage((err as ResponseType)?.msg || intl.get('编辑失败'), 'error')
        })
    })
  }

  const columnsMemo = useMemo<TableColumnItem[]>(() => {
    return [
      {
        title: ellipses('物料大类'),
        dataKey: 'matCatLvl1Code',
        width: 120,
        render: (text) => tableColumnRender(text, 120),
      },
      {
        title: ellipses('物料中类'),
        dataKey: 'matCatLvl2Code',
        width: 100,
        render: (text) => tableColumnRender(text, 100),
      },
      {
        title: ellipses('物料小类'),
        dataKey: 'matCatLvl3Code',
        width: 100,
        render: (text) => tableColumnRender(text, 100),
      },
      {
        title: ellipses('是否需要维护'),
        dataKey: 'flag',
        width: 100,
        render: (text) => {
          return text ? '是' : '否'
        },
      },
      {
        title: ellipses('成本大类'),
        dataKey: 'costMatCatLvl1Name',
        width: 120,
        render: (text) => tableColumnRender(text, 120),
      },
      {
        title: ellipses('成本中类'),
        dataKey: 'costMatCatLvl2Name',
        width: 100,
        render: (text) => tableColumnRender(text, 100),
      },
      {
        title: ellipses('成本小类'),
        dataKey: 'costMatCatLvl3Name',
        width: 100,
        render: (text) => tableColumnRender(text, 100),
      },
      {
        title: ellipses('映射状态'),
        dataKey: 'status',
        width: 100,
        render: (val) => {
          return MappingStatusMap[val]
        },
      },
      {
        title: '更新时间',
        dataKey: 'updateTime',
        width: 180,
        sorter(a, b) {
          return new Date(a.updateTime).getTime() - new Date(b.updateTime).getTime()
        },
        render: (text) => {
          return text ? dayjs(text).format('YYYY/MM/DD HH:mm') : '-'
        },
      },
      {
        title: '更新人',
        dataKey: 'updateName',
        width: 100,
        render: (text) => tableColumnRender(text, 100),
      },
      {
        title: ellipses('操作'),
        dataKey: 'op',
        width: 120,
        render: (_, rowItem) => {
          return (
            <div className="flex items-center">
              <Button
                type="primary"
                appearance="link"
                onClick={() => handleOpenDrawer(rowItem)}
                disabled={Number(rowItem.status) === 2}
              >
                {intl.get('编辑')}
              </Button>
            </div>
          )
        },
      },
    ]
  }, [handleOpenDrawer])

  useEffect(() => {
    setColumns(columnsMemo)
  }, [columnsMemo, setColumns])
  return (
    <div className="cost-table">
      <div className="mapping-table-container">
        <Table
          fieldKey="id"
          columns={columns}
          data={listResult?.list ?? []}
          loading={tableLoading}
          size={tableSize}
          maxHeight="auto"
        />
        <div className="footer-wrapper">
          <div className={cx('table-tools', { 'justify-end': isFullscreen })}>
            {isFullscreen ? (
              <>
                <Button
                  icon={<FullscreenExitOutlined />}
                  appearance="link"
                  onClick={toggleFullscreen}
                ></Button>
              </>
            ) : (
              <>
                <div className="no-dropdown-buttons">
                  <i onClick={toggleFullscreen}>
                    <FullscreenOutlined color={PRICE_ICON_COLOR} />
                  </i>
                  <i>
                    <Dropdown
                      data={[
                        { id: 'sm', title: '默认' },
                        { id: 'lg', title: '宽松' },
                        { id: 'md', title: '中等' },
                      ]}
                      trigger="click"
                      onClick={(id) => {
                        setTableSize(id as HiBaseSizeEnum)
                      }}
                    >
                      <ColumnHeightOutlined color={PRICE_ICON_COLOR} />
                    </Dropdown>
                  </i>
                </div>
              </>
            )}
          </div>
          <div className="flex justify-end w-full pl-13 pr-13">
            <Pagination
              showTotal
              showJumper
              pageSizeOptions={[10, 20, 25, 30]}
              pageSize={pagination?.pageSize || 20}
              total={pagination?.total || 0}
              current={pagination?.current || 0}
              onPageSizeChange={(pageSize) => {
                pagination.changePageSize(pageSize)
              }}
              onChange={(current, _, pageSize) => pagination.onChange(current, pageSize)}
            />
          </div>
        </div>
      </div>
      <Drawer
        title={intl.get('映射关系维护')}
        visible={drawerVisible}
        width={450}
        maskClosable
        unmountOnClose
        closeOnEsc
        onClose={closeDrawer}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button type="default" key={1} onClick={closeDrawer}>
              {intl.get('取消')}
            </Button>
            <Button type="primary" key={0} onClick={() => EditMatItem()}>
              {intl.get('保存')}
            </Button>
          </div>
        }
      >
        <Form
          innerRef={formRef}
          initialValues={initialValues}
          labelWidth="100"
          labelPlacement="top"
          onValuesChange={(_changedValues, allValues) => {
            const { costMatCatLvl1CodeId, costMatCatLvl2CodeId } = allValues
            handleCostChange(costMatCatLvl1CodeId, costMatCatLvl2CodeId)
            if (_changedValues.costMatCatLvl1CodeId) {
              formRef.current?.setFieldsValue({
                costMatCatLvl2CodeId: '',
              })
            }
            if (_changedValues.costMatCatLvl1CodeId || _changedValues.costMatCatLvl2CodeId) {
              formRef.current?.setFieldsValue({
                costMatCatLvl3CodeId: '',
              })
              !_changedValues.costMatCatLvl2CodeId && setCostLvl3Disabled(true)
            }
          }}
        >
          <FormItem
            label={intl.get('是否需要维护')}
            field="flag"
            required={true}
            rules={[{ required: true, message: intl.get('请选择') }]}
          >
            <Select
              clearable
              data={[
                { title: '是', id: 1 },
                { title: '否', id: 0 },
              ]}
            />
          </FormItem>
          <FormItem
            label={intl.get('成本大类')}
            field="costMatCatLvl1CodeId"
            required={true}
            rules={[{ required: true, message: intl.get('请选择') }]}
          >
            <Select data={getSelectData(editData.costCategories)} {...CHECK_SELECT_PROPS} />
          </FormItem>
          <FormItem
            label={intl.get('成本中类')}
            field="costMatCatLvl2CodeId"
            required={true}
            rules={[{ required: true, message: intl.get('请选择') }]}
          >
            <Select
              data={getSelectData(editData.costMatCatLvl2Name)}
              {...CHECK_SELECT_PROPS}
              disabled={costLvl2Disabled}
            />
          </FormItem>
          <FormItem
            label={intl.get('成本小类')}
            field="costMatCatLvl3CodeId"
            required={true}
            rules={[{ required: true, message: intl.get('请选择') }]}
          >
            <Select
              data={getSelectData(editData.costMatCatLvl3Name)}
              {...CHECK_SELECT_PROPS}
              disabled={costLvl2Disabled || costLvl3Disabled}
            />
          </FormItem>
        </Form>
        <br />
      </Drawer>
    </div>
  )
})
TableList.displayName = 'TableList'
export default TableList
