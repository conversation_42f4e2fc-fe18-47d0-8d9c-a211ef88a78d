import intl from 'react-intl-universal'
import React, { FC, useCallback, useRef } from 'react'
import { useSafeState, useFullscreen, useRequest, useUnmount } from 'ahooks'
import { ErrorBoundary } from '@sentry/react'
import message from '@hi-ui/message'
import cx from 'classnames'
import PriceQuery from './PriceQuery'
import PriceTable from './PriceTable'
import { MovePriceType } from './config'
import PortalButton from '@/components/portal-button'
import { exportFull, getFullExportAuth } from '@/api/cost/move-average-price'
import { isTabPage, MESSAGE_DURATION } from '@/constants'
import { gscTrack } from '@/utils/gscTrack-help'
import './index.scss'
import { getStringStorage } from '@/utils/cache'

const MoveAveragePrice: FC = () => {
  const [priceFilter, setPriceFilter] = useSafeState<MovePriceType>({})
  const fullscreenDomRef = useRef<HTMLDivElement>(null)
  const [isFullscreen, { toggleFullscreen }] = useFullscreen(fullscreenDomRef)
  const [expanded, setExpanded] = useSafeState<boolean>(true) // 控制表单折叠与否
  const [isCvpnActive, setIsCvpnActive] = useSafeState<boolean>(false)
  const [isFullExportAuth, setIsFullExportAuth] = useSafeState<boolean>(false)
  const [isFullExportDisable, setIsFullExportDisable] = useSafeState<boolean>(false)

  const { runAsync: exportFullPrice } = useRequest(() => exportFull(), {
    manual: true,
  })

  useRequest(() => getFullExportAuth(), {
    onSuccess: (res) => setIsFullExportAuth(res?.data || false),
  })

  const clickFullExport = useCallback(() => {
    const toastId = message.open({ type: 'info', title: intl.get('导出中...'), autoClose: false })
    setIsFullExportDisable(true)
    exportFullPrice()
      .then((res) => {
        toastId && message.close(toastId)
        const urls = res?.data || []
        for (const url of urls) {
          url && window.open(url)
        }
        message.open({
          type: 'success',
          title: intl.get('导出成功'),
          duration: MESSAGE_DURATION,
        })
      })
      .catch((err) => {
        toastId && message.close(toastId)
        message.open({
          type: 'error',
          title: err?.message || intl.get('导出失败'),
          duration: MESSAGE_DURATION,
        })
      })
      .finally(() => setIsFullExportDisable(false))
    gscTrack.pageElemClick('导出-全量导出', 'export', '01')
  }, [exportFullPrice, setIsFullExportDisable])

  useUnmount(() => message.closeAll())

  return (
    <div className="move-average-price-wrapper">
      <ErrorBoundary>
        {isFullExportAuth ? (
          <PortalButton onClick={clickFullExport} disabled={isFullExportDisable}>
            {intl.get('全量导出')}
          </PortalButton>
        ) : null}
      </ErrorBoundary>
      <ErrorBoundary>
        <div className="price-query-wrapper">
          <PriceQuery
            onSearch={setPriceFilter}
            expanded={expanded}
            setExpanded={setExpanded}
            isCvpnActive={isCvpnActive}
          />
        </div>
      </ErrorBoundary>
      <ErrorBoundary>
        <div
          className={cx('price-table-wrapper', {
            expand: expanded,
            'no-expand': !expanded,
            'move-price-fullscreen-wrapper': isFullscreen,
            tab: getStringStorage(isTabPage) === 'true',
          })}
          ref={fullscreenDomRef}
        >
          <PriceTable
            toggleFullscreen={toggleFullscreen}
            priceFilter={priceFilter}
            isFullscreen={isFullscreen}
            setIsCvpnActive={setIsCvpnActive}
          />
        </div>
      </ErrorBoundary>
    </div>
  )
}

export default MoveAveragePrice
