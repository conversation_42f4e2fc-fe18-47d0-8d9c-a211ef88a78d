import intl from 'react-intl-universal'
import React, { ReactText, memo, useCallback, useEffect, useMemo, useRef } from 'react'
import Table, { SettingDrawer, TableColumnItem } from '@hi-ui/table'
import Button from '@hi-ui/button'
import {
  DownloadOutlined,
  FreezeOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  LeftRightOutlined,
  SettingOutlined,
} from '@hi-ui/icons'
import cx from 'classnames'
import { usePagination, useRequest, useSafeState } from 'ahooks'
import Drawer from '@hi-ui/drawer'
import Tooltip from '@hi-ui/tooltip'
import Dropdown from '@hi-ui/dropdown'
import * as Sentry from '@sentry/react'
import Pagination from '@hi-ui/pagination'
import { message } from '@hi-ui/message'
import { customMessage } from '@/utils/custom-message'
import {
  FetchMoveListType,
  getMoveAvgPriceTrend,
  getSettledPriceList,
  exportSettledPrice,
} from '@/api/cost/move-average-price'
import {
  DataTypeMap,
  MOVE_PRICE_TABLE_FREEZE_KEY,
  MovePriceType,
  calcConditions,
  MOVE_PRICE_COLUMN_ORDER,
  DEFAULT_CONDITIONS,
} from './config'
import MovePriceTrend from '@/components/antv-component/MovePriceTrend'
import { EmptyFunctionType } from '@/types/type'
import { gscTrack } from '@/utils/gscTrack-help'
import './price-table.scss'
import TableBaseTool from '@/components/table-base-tool'
import { ICON_COLOR } from '@/constants'
import { ellipses } from '@/components/ellipsis-tool'
import { getObjectStorage } from '@/utils/cache'

const PriceTable = memo<{
  toggleFullscreen: EmptyFunctionType
  priceFilter: MovePriceType
  isFullscreen: boolean
  setIsCvpnActive: React.Dispatch<React.SetStateAction<boolean>>
}>(({ toggleFullscreen, priceFilter, isFullscreen = false, setIsCvpnActive }) => {
  const ref = useRef(null)
  const [drawerVisible, setDrawerVisible] = useSafeState<boolean>(false)
  const [drawerTitle, setDrawerTitle] = useSafeState<string>('')
  const [columns, setColumns] = useSafeState<TableColumnItem[]>([])
  const [hiddenColKeys, setHiddenColKeys] = useSafeState<string[]>([])
  const [sortedColKeys, setSortColKeys] = useSafeState<string[]>([])
  const [tableSettingVisible, setTableSettingVisible] = useSafeState<boolean>(false)
  const [optionMap, setOptionMap] = useSafeState<Record<string, string>>({ class: 'option1' })
  const [trendData, setTrendData] = useSafeState<Array<Record<string, string | number>>>([])
  const [leftFreezedColumn, setLeftFreezedColumn] = useSafeState<string>('')
  const [settlePercentType, setSettlePercentType] = useSafeState<boolean>(false)
  const [trendPercentType, setTrendPercentType] = useSafeState<boolean>(false)
  const [selectedRowKeys, setSelectedRowKeys] = useSafeState<ReactText[]>([])
  // const [selectedFetchKeys, setSelectedFetchKeys] = useSafeState<string[]>([])
  const clickTab = useRef<boolean>(false)
  const [freezeVisible, setFreezeVisible] = useSafeState<boolean>(false)

  const {
    data: settledListResult,
    run: getSettledList,
    pagination,
    loading: settledTableLoading,
  } = usePagination(
    ({ current, pageSize }) => {
      const { bizNames, categoryList, cvpnCodes, startDate, endDate, dateList, exchangeRateType } =
        priceFilter
      const cvpnFetchObj = {
        bizNames,
        categoryList,
        cvpnCodes,
        startDate,
        endDate,
        dateList,
        exchangeRateType,
      }
      let finalFetch = optionMap.class === 'option1' ? priceFilter : cvpnFetchObj
      if (clickTab.current) {
        finalFetch = { ...DEFAULT_CONDITIONS, ...cvpnFetchObj, dateList: [] }
        clickTab.current = false
      }
      return getSettledPriceList({
        ...finalFetch,
        dataType: DataTypeMap[`${optionMap.class}`],
        amountType: 'YUAN',
        pageNum: current,
        pageSize,
      } as FetchMoveListType)
    },
    {
      manual: true,
      refreshDeps: [priceFilter, optionMap],
      defaultPageSize: 20,
    }
  )

  const { run: getTrend, data: trendResult } = useRequest(
    (params) => getMoveAvgPriceTrend(params),
    {
      manual: true,
    }
  )

  const { run: exportSettled } = useRequest((params) => exportSettledPrice(params), {
    manual: true,
    onSuccess: (res) => {
      message.closeAll()
      if (res?.data) {
        customMessage(intl.get('导出成功'), 'success')
        window.open(res.data)
      } else {
        customMessage(
          intl.get('导出数据较多，下载链接生成中，生成完毕会在飞书通知您，请稍后关注飞书消息'),
          'info'
        )
      }
    },
    onError: () => {
      message.closeAll()
      customMessage(intl.get('导出失败'), 'error')
    },
  })

  const handleExport = useCallback(() => {
    const dataType = optionMap.class === 'option1' ? 'MPN_ID' : 'CVPN'
    const amountType = 'YUAN'
    message.open({ title: intl.get('导出中...'), type: 'info', autoClose: false })
    exportSettled({ ...priceFilter, dataType, amountType })
    gscTrack.pageElemClick(
      optionMap.class === 'option1' ? '导出-MPN-ID' : '导出-CVPN',
      'export',
      '01'
    )
  }, [exportSettled, optionMap.class, priceFilter])

  useEffect(() => {
    if (!Object.keys(priceFilter)?.length || !optionMap.class) return
    // setSelectedFetchKeys([])
    setSelectedRowKeys([])
    const str = calcConditions(priceFilter)
    if (str) {
      customMessage(`${str}所选查询条件数量过多，请减少选择数量后查询`, 'info')
    } else {
      getSettledList({ pageSize: pagination.pageSize, current: 1 })
    }
  }, [
    priceFilter,
    optionMap.class,
    getSettledList,
    pagination.pageSize,
    setSelectedRowKeys,
    // setSelectedFetchKeys,
  ])

  const onSetColKeysChange = useCallback(
    (sortedColKeys: string[], hiddenColKeys: string[], newColumns) => {
      const order = JSON.parse(localStorage.getItem(MOVE_PRICE_COLUMN_ORDER) as string) || {}
      localStorage.setItem(
        MOVE_PRICE_COLUMN_ORDER,
        JSON.stringify({
          ...order,
          [optionMap.class]: newColumns.reduce((a, b, idx) => {
            a[b.dataKey] = idx + 1
            return a
          }, {}),
        })
      )
      setSortColKeys(sortedColKeys)
      setHiddenColKeys(hiddenColKeys)
      setColumns(newColumns)
      gscTrack.pageElemClick(
        optionMap.class === 'option1' ? '设置-MPN-ID' : '设置-CVPN',
        'set',
        '01'
      )
    },
    [optionMap.class, setSortColKeys, setHiddenColKeys, setColumns]
  )

  const handleChangeOption = useCallback(
    (option) => {
      setOptionMap((prev) => ({ ...prev, class: option }))
      gscTrack.pageElemClick(option === 'option1' ? '查看-MPN-ID' : '查看-CVPN', 'view', '01')
    },
    [setOptionMap]
  )

  const clickSettingVisible = useCallback(
    () => setTableSettingVisible(true),
    [setTableSettingVisible]
  )
  const closeSettingVisible = useCallback(
    () => setTableSettingVisible(false),
    [setTableSettingVisible]
  )
  const closeDrawer = useCallback(() => setDrawerVisible(false), [setDrawerVisible])

  const clickFreezeColumn = useCallback(
    (id) => {
      try {
        setLeftFreezedColumn(id)
        const freezeStorage =
          JSON.parse(localStorage.getItem(MOVE_PRICE_TABLE_FREEZE_KEY) as string) || {}
        const key = optionMap.class === 'option1' ? 'mpn' : 'cvpn'
        const newFreezeStorage = { ...freezeStorage, [key]: id }
        localStorage.setItem(MOVE_PRICE_TABLE_FREEZE_KEY, JSON.stringify(newFreezeStorage))
        gscTrack.pageElemClick(
          optionMap.class === 'option1' ? '冻结-MPN-ID' : '冻结-CVPN',
          'freeze',
          '01'
        )
      } catch (error) {
        Sentry.captureException(error)
      }
    },
    [setLeftFreezedColumn, optionMap.class]
  )

  const freezeColumnData = useMemo(
    () =>
      columns?.map((item) => {
        const { title, dataKey } = item
        return {
          id: dataKey,
          title: (
            <span style={{ color: leftFreezedColumn === dataKey ? '#237ffa' : '#1F2733' }}>
              {title}
            </span>
          ),
        }
      }),
    [columns, leftFreezedColumn]
  )
  // 功能暂时删除，下个迭代再放出来，所以代码保留
  // const multiAnalyze = useCallback(() => {
  //   setSettlePercentType(false)
  //   setTrendPercentType(false)
  //   const { startDate, endDate } = priceFilter
  //   let dataType = ''
  //   if (optionMap.class === 'option1') {
  //     dataType = 'MPN_ID'
  //     getTrend({
  //       mpnIds: selectedFetchKeys.join(','),
  //       startDate,
  //       endDate,
  //       dataType,
  //       amountType: 'YUAN',
  //     })
  //   } else {
  //     dataType = 'CVPN'
  //     getTrend({
  //       cvpnCodes: selectedFetchKeys.join(','),
  //       startDate,
  //       endDate,
  //       dataType,
  //       amountType: 'YUAN',
  //     })
  //   }
  //   setDrawerTitle(`${dataType}维度对比分析`)
  //   setDrawerVisible(true)
  //   gscTrack.pageElemClick(
  //     optionMap.class === 'option1' ? '查看-对比分析-MPN-ID' : '查看-对比分析-CVPN',
  //     'view',
  //     '01'
  //   )
  // }, [
  //   setSettlePercentType,
  //   setTrendPercentType,
  //   priceFilter,
  //   optionMap.class,
  //   setDrawerTitle,
  //   setDrawerVisible,
  //   getTrend,
  //   selectedFetchKeys,
  // ])

  const handleOpenDrawer = useCallback(
    (text, rowItem) => {
      const { cvpnCode, matNo, bizName } = rowItem
      const { startDate, endDate } = priceFilter
      let dataType = ''
      if (optionMap.class === 'option1') {
        dataType = 'MPN_ID'
        getTrend({ mpnIds: matNo, startDate, endDate, dataType, amountType: 'YUAN', bizName })
      } else {
        dataType = 'CVPN'
        getTrend({ cvpnCodes: cvpnCode, startDate, endDate, dataType, amountType: 'YUAN' })
      }
      setDrawerTitle(text)
      setDrawerVisible(true)
      gscTrack.pageElemClick(
        optionMap.class === 'option1' ? '查看-MPN-ID详情' : '查看-CVPN详情',
        'view',
        '01'
      )
    },
    [setDrawerTitle, setDrawerVisible, priceFilter, optionMap.class, getTrend]
  )

  useEffect(() => {
    if (!trendResult?.data) return
    setTrendData(trendResult?.data?.fruitfulTrend)
  }, [setTrendData, trendResult])

  const columnsMemo = useMemo(() => {
    const returnedColumns = settledListResult?.columnList || []
    const shouldGray =
      returnedColumns?.filter((i) => i?.type === '1')?.map((item) => item.key) ?? []

    const finalColumns = returnedColumns?.map((item) => {
      const { value, key } = item
      const isFirstClass = optionMap?.class === 'option1'
      if (key === 'matNo' && isFirstClass) {
        return {
          title: value,
          dataKey: key,
          width: 150,
          render: (text, rowItem) => {
            const { maktx } = rowItem
            const finalDrawerTitle = maktx ? `${text}（${maktx}）` : text
            return (
              <span
                className="active-column-text"
                onClick={() => handleOpenDrawer(finalDrawerTitle, rowItem)}
              >
                {text}
              </span>
            )
          },
        }
      }
      if (key === 'cvpnCode' && !isFirstClass) {
        return {
          title: value,
          dataKey: key,
          width: 180,
          render: (text, rowItem) => {
            return (
              <span className="active-column-text" onClick={() => handleOpenDrawer(text, rowItem)}>
                {text}
              </span>
            )
          },
        }
      }
      return {
        title: value,
        dataKey: key,
        width: 180,
        render: (text) => {
          return (
            <div
              style={{
                backgroundColor: shouldGray?.includes(key) ? '#F2F4F7' : '#fff',
                height: 40,
                paddingTop: 2,
                paddingLeft: 14,
                display: 'flex',
                alignItems: 'center',
              }}
            >
              {['maktx', 'spCode'].includes(key) ? ellipses(text, false, 480) : text || '-'}
            </div>
          )
        },
      }
    })

    const allOrders = JSON.parse(localStorage.getItem(MOVE_PRICE_COLUMN_ORDER) as string) || {}
    const order = allOrders?.[optionMap.class] ?? {}
    if (Object.keys(order)?.length !== 0) {
      // 有缓存才用
      finalColumns.sort((a, b) => {
        const indexA = order[a.dataKey]
        const indexB = order[b.dataKey]
        if (indexA < indexB) {
          return -1
        } else if (indexA > indexB) {
          return 1
        } else {
          return 0
        }
      })
    }
    return finalColumns || []
  }, [optionMap.class, settledListResult?.columnList, handleOpenDrawer])

  useEffect(() => {
    const freezeStorage = getObjectStorage(MOVE_PRICE_TABLE_FREEZE_KEY)
    const key = optionMap?.class === 'option1' ? 'mpn' : 'cvpn'
    const initKey = optionMap?.class === 'option1' ? 'matNo' : 'cvpnCode'
    const initFreezedColumn = freezeStorage[`${key}`] || initKey
    setLeftFreezedColumn(initFreezedColumn)
  }, [optionMap?.class, setLeftFreezedColumn])

  const resetClick = useCallback(() => {
    setHiddenColKeys([])
    setDrawerVisible(false)
  }, [setHiddenColKeys, setDrawerVisible])

  const clickMpnid = useCallback(() => {
    clickTab.current = true
    resetClick()
    handleChangeOption('option1')
  }, [handleChangeOption, resetClick])

  const clickCvpn = useCallback(() => {
    clickTab.current = true
    resetClick()
    handleChangeOption('option2')
  }, [handleChangeOption, resetClick])
  const trackToggleFullscreen = useCallback(() => {
    gscTrack.pageElemClick(
      optionMap.class === 'option1' ? '查看-全屏-MPN-ID' : '查看-全屏-CVPN',
      'fullScreen',
      '01'
    )
    toggleFullscreen()
  }, [toggleFullscreen, optionMap.class])

  useEffect(() => {
    setColumns(columnsMemo.filter((col) => !hiddenColKeys.includes(col.dataKey)))
    setSortColKeys(columnsMemo.map((i) => i.dataKey))
  }, [columnsMemo, hiddenColKeys, setColumns, setSortColKeys])

  useEffect(() => {
    setIsCvpnActive(optionMap.class === 'option2')
  }, [optionMap.class, setIsCvpnActive])

  const finalSettledData = useMemo(() => trendData.filter((i) => i?.type === '1'), [trendData])
  const finalTrendData = useMemo(() => trendData.filter((i) => i?.type === '2'), [trendData])
  const lineWrapperStyle = useMemo(
    () =>
      finalSettledData.length > 0 && finalTrendData.length > 0
        ? { height: '100%', width: 'calc((100% - 25px) / 2)' }
        : {
            height: '100%',
            width: '100%',
          },
    [finalSettledData, finalTrendData]
  )

  // const curSelectKey = useMemo(
  //   () => (optionMap.class === 'option1' ? 'matNo' : 'cvpnCode'),
  //   [optionMap.class]
  // )

  return (
    <div className="price-table">
      <div className="table-tools">
        <div className="buttons">
          {isFullscreen ? (
            <span className="unit">
              {intl.get('货币单位：')}
              {priceFilter?.exchangeRateType || 'CNY'}
            </span>
          ) : (
            <>
              <span
                className={cx('option1', { active: optionMap.class === 'option1' })}
                onClick={clickMpnid}
              >
                MPNID
              </span>
              <span
                className={cx('option2', { active: optionMap.class === 'option2' })}
                onClick={clickCvpn}
              >
                CVPN
              </span>
              <Tooltip title={intl.get('列冻结')} trigger="hover" visible={freezeVisible}>
                <Dropdown
                  data={freezeColumnData}
                  trigger="click"
                  overlay={{ placement: 'bottom-start' }}
                  onClick={clickFreezeColumn}
                  width={160}
                  className="price-freeze-dropdown-wrapper"
                  overlayClassName="freeze-dropdown-content"
                >
                  <div>
                    <TableBaseTool
                      icon={<FreezeOutlined color={ICON_COLOR} />}
                      open={() => setFreezeVisible(true)}
                      close={() => setFreezeVisible(false)}
                    />
                  </div>
                </Dropdown>
              </Tooltip>
              <div className="no-dropdown-buttons">
                <Tooltip title={intl.get('列设置')} trigger="hover">
                  <i onClick={clickSettingVisible}>
                    <SettingOutlined color={ICON_COLOR} />
                  </i>
                </Tooltip>
                <Tooltip title={intl.get('下载数据')} trigger="hover">
                  <i onClick={handleExport}>
                    <DownloadOutlined color={ICON_COLOR} />
                  </i>
                </Tooltip>
                <Tooltip title={intl.get('全屏')} trigger="hover">
                  <i onClick={trackToggleFullscreen}>
                    <FullscreenOutlined color={ICON_COLOR} />
                  </i>
                </Tooltip>
              </div>
            </>
          )}
        </div>
        {isFullscreen ? (
          <Button
            icon={<FullscreenExitOutlined />}
            appearance="link"
            onClick={toggleFullscreen}
          ></Button>
        ) : (
          <span className="unit ml-6">
            {intl.get('货币单位：')}
            {priceFilter?.exchangeRateType || 'CNY'}
          </span>
        )}
      </div>
      <div className="table-container">
        {
          <div className="table-wrapper" ref={ref}>
            <Table
              fieldKey="uk"
              maxHeight="auto"
              scrollbar={{ zIndex: 9 }}
              columns={columns}
              data={settledListResult?.list || []}
              loading={settledTableLoading}
              fixedToColumn={{ left: leftFreezedColumn }}
              rowSelection={{
                selectedRowKeys,
                onChange: (keys) => {
                  setSelectedRowKeys(keys)
                  // if (!Array.isArray(targetRow)) {
                  //   setSelectedFetchKeys((prev) => {
                  //     const targetValue = targetRow?.[curSelectKey]
                  //     const newSet = new Set(prev)
                  //     if (newSet.has(targetValue)) {
                  //       newSet.delete(targetValue)
                  //     } else {
                  //       newSet.add(targetValue)
                  //     }
                  //     return [...newSet]
                  //   })
                  // } else {
                  //   // 全选
                  //   setSelectedFetchKeys((prev) => {
                  //     const newSet = new Set(prev)
                  //     targetRow.forEach((row) => {
                  //       const { [curSelectKey]: targetValue } = row || {}
                  //       if (newSet.has(targetValue)) {
                  //         newSet.delete(targetValue)
                  //       } else {
                  //         newSet.add(targetValue)
                  //       }
                  //     })
                  //     return [...newSet]
                  //   })
                  // }
                },
              }}
            />
            <div className="price-footer-wrapper">
              <div className="flex justify-between w-full pl-13 pr-13">
                {/* <Button type="primary" disabled={selectedRowKeys.length < 2} onClick={multiAnalyze}>
                  {intl.get('多行对比分析')}
                </Button> */}
                <div></div>
                <Pagination
                  showTotal
                  showJumper
                  pageSizeOptions={[10, 20, 30, 40, 50]}
                  pageSize={pagination?.pageSize || 10}
                  total={pagination?.total || 0}
                  current={pagination?.current || 0}
                  onPageSizeChange={(pageSize) => {
                    pagination.changePageSize(pageSize)
                    gscTrack.pageElemClick(
                      optionMap.class === 'option1' ? '每页条目-MPN-ID' : '每页条目-CVPN',
                      'entryPerPage',
                      '01'
                    )
                  }}
                  onChange={(current, _, pageSize) => {
                    pagination.onChange(current, pageSize)
                    gscTrack.pageElemClick(
                      optionMap.class === 'option1' ? '翻页-MPN-ID' : '翻页-CVPN',
                      'flip',
                      '01'
                    )
                  }}
                />
              </div>
            </div>
          </div>
        }
        <SettingDrawer
          visible={tableSettingVisible}
          onClose={closeSettingVisible}
          drawerProps={{ width: 400, title: intl.get('字段设置') }}
          columns={columnsMemo}
          checkDisabledColKeys={['name', 'type']}
          hiddenColKeys={hiddenColKeys}
          sortedColKeys={sortedColKeys}
          onSetColKeysChange={onSetColKeysChange}
        />
      </div>
      <Drawer
        title={drawerTitle}
        visible={drawerVisible}
        maskClosable
        unmountOnClose
        closeOnEsc
        onClose={closeDrawer}
        placement="bottom"
      >
        {drawerVisible && (
          <div className="price-table-drawer-wrapper">
            {!!finalSettledData?.length && (
              <div style={{ ...lineWrapperStyle }}>
                <>
                  {Object.keys(finalSettledData?.[0]?.movingAvgPriceNodeDtoMap).length === 1 && (
                    <Button
                      icon={<LeftRightOutlined />}
                      appearance="link"
                      style={{ float: 'right' }}
                      onClick={() => setSettlePercentType((prev) => !prev)}
                    />
                  )}
                  <MovePriceTrend
                    data={finalSettledData || []}
                    xLineName="date"
                    title={intl.get('实结')}
                    isPercent={settlePercentType}
                    allY={Object.keys(finalSettledData?.[0]?.movingAvgPriceNodeDtoMap)}
                  />
                </>
              </div>
            )}
            {!!finalTrendData?.length && (
              <div style={{ ...lineWrapperStyle }}>
                <>
                  {Object.keys(finalTrendData?.[0]?.movingAvgPriceNodeDtoMap).length === 1 && (
                    <Button
                      icon={<LeftRightOutlined />}
                      appearance="link"
                      style={{ float: 'right' }}
                      onClick={() => setTrendPercentType((prev) => !prev)}
                    />
                  )}
                  <MovePriceTrend
                    data={finalTrendData || []}
                    xLineName="date"
                    title={intl.get('预估')}
                    isPercent={trendPercentType}
                    allY={Object.keys(finalTrendData?.[0]?.movingAvgPriceNodeDtoMap)}
                  />
                </>
              </div>
            )}
          </div>
        )}
      </Drawer>
    </div>
  )
})

PriceTable.displayName = 'PriceTable'

export default PriceTable
