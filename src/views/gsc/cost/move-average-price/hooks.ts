/* eslint-disable @typescript-eslint/no-explicit-any */
import { getCategoryConditions, getDateVersion } from '@/api/cost/move-average-price'
import { NOOP_ARR, NOOP_OBJ } from '@/utils/noop'
import { useRequest } from 'ahooks'

export const useGetMovePriceCategory = () => {
  const { data } = useRequest(() => getCategoryConditions())
  const dataArrOrObj = data?.data ?? NOOP_ARR
  // 构造三级选择框的数据，@@隔开不同级别
  return Object.keys(dataArrOrObj)?.reduce((a: any[], b: any) => {
    const temp = { title: b, id: b, children: [] }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _res = Object.keys(dataArrOrObj[b]).reduce((c: any[], d: any) => {
      const temp1 = { title: d, id: `${b}@@${d}`, children: [] as any[] }
      const thirdArr = dataArrOrObj[b][d].map((item) => {
        return { id: `${b}@@${d}@@${item}`, title: item }
      })
      temp1.children = thirdArr
      c.push(temp1)
      return c
    }, temp.children)
    a.push(temp)
    return a
  }, [])
}

export const useGetDateVersion = () => {
  const { data } = useRequest(() => getDateVersion())
  return data?.data ?? NOOP_OBJ
}
