import * as config from '../config'
import { describe, it, expect, vi } from 'vitest'

// mock react-intl-universal
vi.mock('react-intl-universal', () => ({
  default: {
    get: (key: string) => `mock_${key}`,
  },
}))
// mock dayjs
vi.mock('dayjs', async () => {
  const actual = await vi.importActual<AnyType>('dayjs')
  const mockDayjs = () => ({
    format: (fmt: string) => {
      if (fmt === 'YYYY-MM-DD') return '2024-01-01'
      return 'mock-date'
    },
    add: () => mockDayjs(),
    subtract: () => mockDayjs(),
    endOf: () => mockDayjs(),
  })
  mockDayjs.extend = actual.default.extend
  mockDayjs.isDayjs = actual.default.isDayjs
  return { default: mockDayjs }
})

describe('move-average-price/config.ts 导出内容', () => {
  it('MoveSearchType 应为字符串数组', () => {
    expect(Array.isArray(config.MoveSearchType)).toBe(true)
    expect(config.MoveSearchType).toContain('CVPN')
    expect(config.MoveSearchType).toContain('BIZ_NAME')
  })

  it('DataTypeMap 应包含 option1 和 option2', () => {
    expect(config.DataTypeMap.option1).toBe('MPN_ID')
    expect(config.DataTypeMap.option2).toBe('CVPN')
  })

  it('DEFAULT_CONDITIONS 应为对象且字段为数组', () => {
    expect(typeof config.DEFAULT_CONDITIONS).toBe('object')
    Object.values(config.DEFAULT_CONDITIONS).forEach((val) => {
      expect(Array.isArray(val)).toBe(true)
    })
  })

  it('DEFAULT_DATE_RANGE 应为长度为2的数组', () => {
    expect(Array.isArray(config.DEFAULT_DATE_RANGE)).toBe(true)
    expect(config.DEFAULT_DATE_RANGE.length).toBe(2)
    expect(config.DEFAULT_DATE_RANGE[0]).toBe('2024-01-01')
    expect(config.DEFAULT_DATE_RANGE[1]).toBe('2024-01-01')
  })

  it('MOVE_PRICE_TABLE_FREEZE_KEY 应为字符串', () => {
    expect(config.MOVE_PRICE_TABLE_FREEZE_KEY).toBe('move-table-freeze-key')
  })

  it('MAX_QUERY_LENGTH 应为2000', () => {
    expect(config.MAX_QUERY_LENGTH).toBe(2000)
  })

  describe('calcConditions', () => {
    it('CVPN 超限时返回 CVPN', () => {
      const arr = Array(config.MAX_QUERY_LENGTH + 1).fill('a')
      expect(config.calcConditions({ cvpnCodes: arr })).toBe('CVPN')
    })
    it('PN 超限时返回 PN', () => {
      const arr = Array(config.MAX_QUERY_LENGTH + 1).fill('a')
      expect(config.calcConditions({ pnCodes: arr })).toBe('PN')
    })
    it('MPN-ID 超限时返回 MPN-ID', () => {
      const arr = Array(config.MAX_QUERY_LENGTH + 1).fill('a')
      expect(config.calcConditions({ mpnIds: arr })).toBe('MPN-ID')
    })
    it('多个超限时返回空格分隔', () => {
      const arr = Array(config.MAX_QUERY_LENGTH + 1).fill('a')
      expect(config.calcConditions({ cvpnCodes: arr, pnCodes: arr, mpnIds: arr })).toBe(
        'CVPN PN MPN-ID'
      )
    })
    it('都不超限时返回空字符串', () => {
      expect(config.calcConditions({ cvpnCodes: [], pnCodes: [], mpnIds: [] })).toBe('')
    })
    it('参数缺省时返回空字符串', () => {
      expect(config.calcConditions(undefined)).toBe('')
      expect(config.calcConditions(null)).toBe('')
      expect(config.calcConditions({})).toBe('')
    })
  })

  it('SETTLE_COST_TITLE、PERCENT_COST_TITLE、TREND_COST_TITLE', () => {
    expect(config.SETTLE_COST_TITLE).toBe('当日移动平均价')
    expect(config.PERCENT_COST_TITLE).toBe('价格波动')
    expect(config.TREND_COST_TITLE).toBe('预估移动平均价')
  })

  it('MOVE_FORM_NPNID_CACHE 应为字符串', () => {
    expect(config.MOVE_FORM_NPNID_CACHE).toBe('MOVE_FORM_NPNID_CACHE')
  })

  it('PRICE_ICON_COLOR 应为字符串', () => {
    expect(config.PRICE_ICON_COLOR).toBe('#5F6A7A')
  })

  it('MovePriceFetchEnum 枚举应包含 normal/ascend/descend', () => {
    expect(config.MovePriceFetchEnum.normal).toBe(0)
    expect(config.MovePriceFetchEnum.ascend).toBe(1)
    expect(config.MovePriceFetchEnum.descend).toBe(2)
  })

  it('MovePriceFilterNameKey 枚举应包含所有key', () => {
    expect(config.MovePriceFilterNameKey.bizNames).toBe('业务线')
    expect(config.MovePriceFilterNameKey.categoryList).toBe('品类')
    expect(config.MovePriceFilterNameKey.brandNames).toBe('品牌')
    expect(config.MovePriceFilterNameKey.businessDate).toBe('业务日期')
    expect(config.MovePriceFilterNameKey.cvpnCodes).toBe('CVPN')
    expect(config.MovePriceFilterNameKey.pnCodes).toBe('PN')
    expect(config.MovePriceFilterNameKey.mpnIds).toBe('MPN')
    expect(config.MovePriceFilterNameKey.pnCodeDescs).toBe('物料描述')
    expect(config.MovePriceFilterNameKey.sourcingOprNameCns).toBe('资源负责人')
    expect(config.MovePriceFilterNameKey.purchaseUserNameCns).toBe('采购员')
    expect(config.MovePriceFilterNameKey.purchasePatternDescs).toBe('采购渠道')
    expect(config.MovePriceFilterNameKey.projectNames).toBe('初始项目')
    expect(config.MovePriceFilterNameKey.odmMgrDisplayNames).toBe('ODM负责人')
  })

  it('MovePriceMpnIdFilterNameKey 枚举应包含所有key', () => {
    expect(config.MovePriceMpnIdFilterNameKey.categoryList).toBe('品类')
    expect(config.MovePriceMpnIdFilterNameKey.businessDate).toBe('业务日期')
    expect(config.MovePriceMpnIdFilterNameKey.cvpnCodes).toBe('CVPN')
  })

  it('MOVE_PRICE_COLUMN_ORDER 应为字符串', () => {
    expect(config.MOVE_PRICE_COLUMN_ORDER).toBe('MOVE_PRICE_COLUMN_ORDER')
  })

  it('BIZ_NAMES 应为特定对象数组', () => {
    expect(Array.isArray(config.BIZ_NAMES)).toBe(true)
    expect(config.BIZ_NAMES.map((i) => i.value)).toEqual([
      '手机',
      '生态链',
      '笔记本',
      '可穿戴',
      '电视',
    ])
    expect(config.BIZ_NAMES.map((i) => i.name)).toEqual([
      '手机',
      '生态链',
      '笔记本',
      '可穿戴',
      '电视',
    ])
  })

  it('PURCHASE_WAYS 应为特定对象数组', () => {
    expect(Array.isArray(config.PURCHASE_WAYS)).toBe(true)
    expect(config.PURCHASE_WAYS.length).toBe(6)
    expect(config.PURCHASE_WAYS[0].value).toBe('mock_mock_小米直采')
    expect(config.PURCHASE_WAYS[0].name).toBe('小米直采')
  })

  it('CURRENCY_SELECT_DATA 应为币种对象数组', () => {
    expect(Array.isArray(config.CURRENCY_SELECT_DATA)).toBe(true)
    expect(config.CURRENCY_SELECT_DATA.length).toBe(4)
    expect(config.CURRENCY_SELECT_DATA[0].id).toBe('CNY')
    expect(config.CURRENCY_SELECT_DATA[0].title).toBe('mock_人民币')
    expect(config.CURRENCY_SELECT_DATA[1].id).toBe('USD')
    expect(config.CURRENCY_SELECT_DATA[1].title).toBe('mock_美元')
    expect(config.CURRENCY_SELECT_DATA[2].id).toBe('INR')
    expect(config.CURRENCY_SELECT_DATA[2].title).toBe('mock_印度卢比')
    expect(config.CURRENCY_SELECT_DATA[3].id).toBe('IDR')
    expect(config.CURRENCY_SELECT_DATA[3].title).toBe('mock_印尼盾')
  })
})
