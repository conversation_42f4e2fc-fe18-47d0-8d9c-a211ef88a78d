import './price-query.scss'

import { useDebounceFn, useMount, useSafeState } from 'ahooks'
import dayjs from 'dayjs'
import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react'
import intl from 'react-intl-universal'

import { getMoveAvgPriceConditions } from '@/api/cost/move-average-price'
import * as API from '@/api/gsc/form-view'
import { ellipses } from '@/components/ellipsis-tool'
import FormView from '@/components/form-view'
import { FORM_VIEWS_CACHE, MOVING_AVG_PRICE } from '@/constants'
import { customMessage } from '@/utils/custom-message'
import { gscSearchTrack, gscTrack } from '@/utils/gscTrack-help'
import { formatInputString } from '@/utils/input-format'
import { FORM_WIDTH } from '@/utils/select-data'
import Button from '@hi-ui/button'
import CheckSelect from '@hi-ui/check-select'
import CheckTreeSelect from '@hi-ui/check-tree-select'
import DatePicker from '@hi-ui/date-picker'
import { Form, FormHelpers, FormItem } from '@hi-ui/form'
import { Col, Row } from '@hi-ui/grid'
import { DownOutlined } from '@hi-ui/icons'
import message from '@hi-ui/message'
import Select from '@hi-ui/select'

import {
  BIZ_NAMES,
  calcConditions,
  CommonType,
  CURRENCY_SELECT_DATA,
  DEFAULT_DATE_RANGE,
  MovePriceSetType,
  MovePriceType,
  MoveSearchType,
  PURCHASE_WAYS,
} from './config'
import { useGetDateVersion, useGetMovePriceCategory } from './hooks'
import { QueryCodeName } from './track'

const QueryCheckSelect = memo<{
  type?: string
  formDate?: string | string[] | null
  commonSettings?: CommonType
  defaultData?: { id: string; title: string }[]
  // 无法准确知道所有 extra props 类型，暂用单行清零
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [k: string]: any
}>(({ type, formDate, commonSettings, defaultData = [], ...props }) => {
  const { run: debouncedFetch } = useDebounceFn(
    (input, resolve) => {
      const finalQueryString = input ? formatInputString(input) : ''
      const startDate = dayjs(formDate?.[0])
        ?.startOf('day')
        ?.valueOf()
      const endDate = dayjs(formDate?.[1])
        ?.endOf('day')
        ?.valueOf()
      return getMoveAvgPriceConditions({
        startDate,
        endDate,
        type,
        prefix: finalQueryString,
      })
        .then((res) => {
          return resolve(res?.data)
        })
        .catch((err) => {
          message.open({
            title: err?.message || intl.get('搜索条件失败，请联系管理员'),
            type: 'error',
          })
        })
    },
    { wait: 200 }
  )

  return (
    <CheckSelect
      data={defaultData || []}
      height={260}
      fieldNames={{ id: 'value', title: 'name' }}
      dataSource={(keyword) => {
        return new Promise((resolve) => {
          return debouncedFetch(keyword, resolve)
        })
      }}
      {...commonSettings}
      {...props}
    />
  )
})

QueryCheckSelect.displayName = 'QueryCheckSelect'

const PriceQuery = memo<{
  onSearch: MovePriceSetType<MovePriceType>
  expanded: boolean
  setExpanded: MovePriceSetType<boolean>
  isCvpnActive: boolean
}>(({ onSearch, expanded, setExpanded, isCvpnActive }) => {
  const formRef = useRef<FormHelpers>(null)
  const rowGapStyle = { minWidth: 200 } // grid布局内容宽度
  const formEleHeightRef = useRef<number>(33) // 表单默认高度
  const commonSettings = {
    searchable: true,
    showCheckAll: true,
    style: rowGapStyle,
  }
  const initQueryCondition = useRef<Record<string, Array<string> | string>>({
    bizNames: [],
    categoryList: [],
    cvpnCodes: [],
    pnCodes: [],
    businessDate: DEFAULT_DATE_RANGE,
    mpnIds: [],
    pnCodeDescs: [],
    brandNames: [],
    sourcingOprNameCns: [],
    purchaseUserNameCns: [],
    projectNames: [],
    odmMgrDisplayNames: [],
    purchasePatternDescs: [],
    waersLsts: [],
    dateList: [],
    exchangeRateType: 'CNY',
  })
  const [formValue, setFormValue] = useSafeState<Record<string, Array<string> | string>>(
    initQueryCondition.current
  )
  const [dataString, setDateString] = useSafeState<string[] | string | null>(DEFAULT_DATE_RANGE)

  const formatFormValues = useCallback((data) => {
    const { businessDate, dateList, ...left } = data
    const startDate = dayjs(businessDate?.[0])
      ?.startOf('day')
      ?.valueOf()
    const endDate = dayjs(businessDate?.[1])
      ?.endOf('day')
      ?.valueOf()
    const formatDateList =
      typeof dateList === 'number' ? [dateList] : typeof dateList === 'string' ? [] : dateList
    const derivedConditions = {
      ...left,
      startDate,
      endDate,
      dateList: formatDateList,
    }
    return derivedConditions
  }, [])

  const categoryConditions = useGetMovePriceCategory()
  const { dateList: dateVersions = {} } = useGetDateVersion()

  const handleSearch = useCallback(() => {
    formRef.current?.validate()?.then((values) => {
      const str = calcConditions(values)
      if (str) {
        customMessage(`${str}所选查询条件数量过多，请减少选择数量后查询`, 'info')
      } else {
        onSearch(formatFormValues(values))
        gscSearchTrack({
          filters: values,
          queryCodeName: QueryCodeName,
        })
      }
    })
  }, [onSearch, formatFormValues])

  const handleClickChange = useCallback(() => {
    setExpanded((prev) => !prev)
    gscTrack.pageElemClick('展开/收起', 'on/off', '01')
  }, [setExpanded])

  const handleReset = useCallback(() => {
    formRef.current?.reset()
    gscTrack.pageElemClick('重置', 'reset', '01')
  }, [])

  useEffect(() => {
    const values = formRef.current?.getFieldsValue()
    const { bizNames, categoryList, cvpnCodes, businessDate, exchangeRateType } = values
    formRef.current?.setFieldsValue({
      ...initQueryCondition.current,
      bizNames,
      categoryList,
      cvpnCodes,
      businessDate,
      exchangeRateType,
    })
    API.get({ pageType: MOVING_AVG_PRICE })
  }, [isCvpnActive])

  const formOption = useMemo(
    () => ({
      disabled: isCvpnActive,
      placeholder: isCvpnActive ? intl.get('维度暂不可选') : intl.get('请选择'),
    }),
    [isCvpnActive]
  )

  const formatter = useCallback(
    (filter) => {
      const { businessDate = [] } = filter
      formRef.current?.setFieldsValue({ ...filter })
      setFormValue({ ...filter })
      const extra: Record<string, string | number> = {}
      if (businessDate?.length !== 0 && !!businessDate?.[0]) {
        extra.startDate = dayjs(businessDate[0])?.startOf('day')?.valueOf()
        extra.endDate = dayjs(businessDate[1])?.endOf('day')?.valueOf()
      } else {
        extra.startDate = ''
        extra.endDate = ''
      }
      const res = {
        ...filter,
        ...extra,
      }
      delete res.businessDate
      return res
    },
    [setFormValue]
  )

  const dateVersionKey = useMemo(() => (isCvpnActive ? 'cvpnId' : 'mpnId'), [isCvpnActive])

  useMount(() => {
    const cache = localStorage.getItem(FORM_VIEWS_CACHE) || '[]'
    const content =
      JSON.parse(cache)?.find((item) => item?.pageType === MOVING_AVG_PRICE)?.content || ''
    let config
    if (content) {
      const { formConfig } = JSON.parse(content)
      const { businessDate = '' } = formConfig
      // 公共视图不存时间
      config = businessDate ? formConfig : { ...formConfig, businessDate: DEFAULT_DATE_RANGE }
    } else {
      const { formConfig } = { formConfig: initQueryCondition.current }
      config = formConfig
    }
    onSearch(formatter(config))
  })

  return (
    <>
      <div
        className="price-query-content overflow-hidden transition-[height]"
        style={{ height: expanded ? formEleHeightRef.current : 220 }}
      >
        <Form
          style={{ width: FORM_WIDTH }}
          labelPlacement="right"
          showColon={false}
          initialValues={initQueryCondition.current}
          innerRef={formRef}
          labelWidth={94}
          onValuesChange={(_changedValues, allValues) => {
            setFormValue(allValues)
          }}
        >
          <Row rowGap={0} columns={26}>
            <Col span={2}>{ellipses(intl.get('物料基本信息'))}</Col>
            <Col span={8} justify="flex-start">
              <FormItem field="bizNames" label={ellipses(intl.get('业务线'))}>
                <QueryCheckSelect
                  defaultData={BIZ_NAMES}
                  type={MoveSearchType[11]}
                  formDate={dataString}
                  commonSettings={commonSettings}
                />
              </FormItem>
            </Col>
            <Col span={8} justify="center">
              <FormItem field="categoryList" label={ellipses(intl.get('品类'))}>
                <CheckTreeSelect
                  height={260}
                  data={categoryConditions || []}
                  checkedMode="CHILD"
                  clearable
                  searchable
                  searchMode="filter"
                  closeOnEsc
                  style={rowGapStyle}
                ></CheckTreeSelect>
              </FormItem>
            </Col>
            <Col span={8} justify="flex-end">
              <FormItem field="cvpnCodes" label="CVPN">
                <QueryCheckSelect
                  type={MoveSearchType[0]}
                  formDate={dataString}
                  commonSettings={commonSettings}
                />
              </FormItem>
            </Col>
          </Row>
          <Row rowGap={0} columns={26}>
            <Col span={2}></Col>
            <Col span={8} justify="flex-start">
              <FormItem field="pnCodes" label="PN">
                <QueryCheckSelect
                  type={MoveSearchType[1]}
                  formDate={dataString}
                  commonSettings={commonSettings}
                  {...formOption}
                />
              </FormItem>
            </Col>
            <Col span={8} justify="center">
              <FormItem field="mpnIds" label="MPNID">
                <QueryCheckSelect
                  type={MoveSearchType[2]}
                  formDate={dataString}
                  commonSettings={commonSettings}
                  {...formOption}
                />
              </FormItem>
            </Col>
            <Col span={8} justify="flex-end">
              <FormItem field="pnCodeDescs" label={ellipses(intl.get('物料描述'))}>
                <QueryCheckSelect
                  type={MoveSearchType[3]}
                  formDate={dataString}
                  commonSettings={commonSettings}
                  {...formOption}
                />
              </FormItem>
            </Col>
          </Row>
          <Row rowGap={0} columns={26}>
            <Col span={2}></Col>
            <Col span={8} justify="flex-start">
              <FormItem field="projectNames" label={ellipses(intl.get('初始项目'))}>
                <QueryCheckSelect
                  type={MoveSearchType[7]}
                  formDate={dataString}
                  commonSettings={commonSettings}
                  {...formOption}
                />
              </FormItem>
            </Col>
          </Row>
          <Row rowGap={0} columns={26}>
            <Col span={2}>{ellipses(intl.get('项目信息'))}</Col>
            <Col span={8} justify="flex-start">
              <FormItem field="businessDate" label={ellipses(intl.get('业务日期'))}>
                <DatePicker
                  type="daterange"
                  format="YYYY-MM-DD"
                  onChange={(_date, dateStr) => {
                    setDateString(dateStr)
                  }}
                  {...commonSettings}
                />
              </FormItem>
            </Col>
            <Col span={8} justify="center">
              <FormItem field="exchangeRateType" label={ellipses(intl.get('货币单位'))}>
                <Select data={CURRENCY_SELECT_DATA} clearable={false} style={rowGapStyle} />
              </FormItem>
            </Col>
            <Col span={8} justify="flex-end">
              <FormItem field="dateList" label={ellipses(intl.get('数据版本'))}>
                <Select
                  data={dateVersions?.[dateVersionKey] || []}
                  style={rowGapStyle}
                  searchable
                  fieldNames={{ id: 'value', title: 'name' }}
                />
              </FormItem>
            </Col>
          </Row>
          <Row rowGap={0} columns={26}>
            <Col span={2}>{ellipses(intl.get('物料采购信息'))}</Col>
            <Col span={8} justify="flex-start">
              <FormItem field="purchasePatternDescs" label={ellipses(intl.get('采购渠道'))}>
                <QueryCheckSelect
                  type={MoveSearchType[9]}
                  formDate={dataString}
                  commonSettings={commonSettings}
                  defaultData={PURCHASE_WAYS}
                  {...formOption}
                />
              </FormItem>
            </Col>
            <Col span={8} justify="center">
              <FormItem field="brandNames" label={ellipses(intl.get('品牌'))}>
                <QueryCheckSelect
                  type={MoveSearchType[4]}
                  formDate={dataString}
                  commonSettings={commonSettings}
                  {...formOption}
                />
              </FormItem>
            </Col>
            <Col span={8} justify="flex-start">
              <FormItem field="sourcingOprNameCns" label={ellipses(intl.get('资源负责人'))}>
                <QueryCheckSelect
                  type={MoveSearchType[5]}
                  formDate={dataString}
                  commonSettings={commonSettings}
                  {...formOption}
                />
              </FormItem>
            </Col>
          </Row>
          <Row rowGap={0} columns={26}>
            <Col span={2}></Col>
            <Col span={8} justify="flex-start">
              <FormItem field="purchaseUserNameCns" label={ellipses(intl.get('采购员'))}>
                <QueryCheckSelect
                  type={MoveSearchType[6]}
                  formDate={dataString}
                  commonSettings={commonSettings}
                  {...formOption}
                />
              </FormItem>
            </Col>
            <Col span={8} justify="center">
              <FormItem field="odmMgrDisplayNames" label={ellipses(intl.get('ODM负责人'))}>
                <QueryCheckSelect
                  type={MoveSearchType[8]}
                  formDate={dataString}
                  commonSettings={commonSettings}
                  {...formOption}
                />
              </FormItem>
            </Col>
            <Col span={8} justify="flex-end">
              <FormItem field="waersLsts" label={ellipses(intl.get('原币种'))}>
                <QueryCheckSelect
                  type={MoveSearchType[10]}
                  formDate={dataString}
                  commonSettings={commonSettings}
                  {...formOption}
                />
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div className="ml-6 pl-6">
          <div className="w-max">
            <Button appearance="link" onClick={handleClickChange}>
              {expanded ? intl.get('展开') : intl.get('收起')}
              <DownOutlined
                className="transition-transform ml-2"
                color="#9d9d9f"
                style={{
                  transform: expanded ? 'rotateZ(0)' : 'rotateZ(180deg)',
                }}
              />
            </Button>
            <Button type="default" onClick={handleReset}>
              {intl.get('重置')}
            </Button>
            <Button type="secondary" onClick={handleSearch} className="search-button">
              {intl.get('查询')}
            </Button>
            <FormView
              api={API}
              filter={formValue}
              pageType={MOVING_AVG_PRICE}
              onSearch={onSearch}
              clickFormatter={formatter}
              trackCode="保存常用"
            />
          </div>
        </div>
      </div>
    </>
  )
})

PriceQuery.displayName = 'PriceQuery'
export default PriceQuery
