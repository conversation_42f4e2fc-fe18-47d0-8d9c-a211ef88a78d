/* stylelint-disable no-descending-specificity */
@import '@/common/styles/vars';

.price-table {
  height: 100%;

  .table-tools {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 32px;
    background-color: #fff;
    position: relative;
    margin-top: 6px;

    &::before {
      content: '';
      position: absolute;
      height: 1px;
      width: 100%;
      background-color: rgb(235 237 240);
      top: -10px;
      left: 0;
    }

    .buttons {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .freeze-button {
        width: 14px;
        height: 14px;
        background-image: url('#{$img-url}frozen.svg');
        color: #5f6a7a;
        margin-right: 13px;

        &:hover {
          cursor: pointer;
        }
      }

      .no-dropdown-buttons {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-left: 12px;

        i {
          width: 32px;
          height: 32px;
          cursor: pointer;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          color: #5f6a7a;

          &:hover {
            background: #f2f4f7;
          }
        }

        .time-change {
          padding-bottom: 2px;
          color: #929aa6;
          font-size: 14px;

          .sep,
          .time {
            margin-left: 8px;
          }

          .time:hover {
            cursor: pointer;
          }

          .time.active {
            color: #237ffa;
          }
        }
      }

      .option1 {
        width: 72px;
        height: 32px;
        border-radius: 4px 0 0 4px;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f2f4f7;
        color: #1f2733;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;

        &::after {
          content: '';
          position: absolute;
          width: 1px;
          height: 16px;
          background-color: #dfe2e8;
          right: 0;
          top: 8px;
        }

        &:hover {
          cursor: pointer;
        }
      }

      .option1.active {
        color: #237ffa;
        background-color: #e2f3fe;
      }

      .option2 {
        width: 63px;
        height: 32px;
        border-radius: 0 4px 4px 0;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f2f4f7;
        color: #1f2733;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        margin-right: 12px;

        &:hover {
          cursor: pointer;
        }
      }

      .option2.active {
        color: #237ffa;
        background-color: #e2f3fe;
      }
    }

    .unit {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #929aa6;
    }
  }

  .table-container {
    min-width: 660px;
    margin-top: 12px;
    // height: calc(100vh - 362px);

    .table-wrapper {
      .hi-v4-table {
        height: 100%;

        .hi-v4-table__wrapper {
          height: 100%;

          .hi-v4-table-body {
            overflow: overlay;
            height: calc(100vh - 295px);

            tbody tr td:nth-of-type(1) {
              padding: 9px 16px !important;
            }

            .hi-v4-table-row:hover > .hi-v4-table-cell {
              background-color: #fff !important;
            }

            .hi-v4-table-cell {
              padding: 0 !important;
            }

            .active-column-text {
              color: #237ffa;
              padding-left: 14px;

              &:hover {
                cursor: pointer;
                filter: brightness(1.1);
              }
            }
          }
        }
      }
    }

    .price-footer-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 56px;
      background-color: #fff;
      box-shadow: 0 -4px 4px rgb(0 0 0 / 5%);

      .selected_span {
        margin-left: 8px;
        margin-right: 18px;
        position: relative;
        top: 2px;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #1f2733;
      }
    }
  }
}

.price-freeze-dropdown-wrapper {
  width: 32px;
  height: 32px;
  cursor: pointer;
  border-radius: 4px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  color: #5f6a7a;

  &:hover {
    background: #f2f4f7;
  }
}

.freeze-dropdown-content {
  .hi-v4-dropdown-menu {
    height: 300px;
    overflow: scroll;
  }
}

.price-table-drawer-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  height: 100%;
}

.hi-v4-drawer__overlay {
  display: none;
}
