.move-average-price-wrapper {
  height: 100%;
  min-width: 1080px;

  .price-query-wrapper {
    background-color: #fff;
    border-radius: 6px 6px 0 0;
    padding: 12px 16px 4px;
  }

  .price-table-wrapper {
    background-color: #fff;
    border-radius: 0 0 6px 6px;
    padding: 8px 16px 12px;
    height: calc(100vh - 252px);

    .hi-v4-table {
      overflow: visible;

      .hi-v4-table-cell {
        padding: 9px 16px;
      }

      .hi-v4-table-header-cell {
        padding: 9px 16px;
      }
    }
  }
}

.price-table-wrapper.move-price-fullscreen-wrapper {
  .hi-v4-table-body {
    overflow: overlay !important;
    height: calc(100vh - 158px) !important;
  }
}

.price-table-wrapper.expand {
  height: calc(100vh - 170px) !important;

  .hi-v4-table-body {
    overflow: overlay !important;
    height: calc(100vh - 270px) !important;
  }
}

.price-table-wrapper.no-expand {
  height: calc(100vh - 354px);

  .hi-v4-table-body {
    overflow: overlay !important;
    height: calc(100vh - 456px) !important;
  }
}

.price-table-wrapper.expand.tab {
  height: calc(100vh - 170px - 40px) !important;

  .hi-v4-table-body {
    overflow: overlay !important;
    height: calc(100vh - 270px - 40px) !important;
  }
}

.price-table-wrapper.no-expand.tab {
  height: calc(100vh - 354px - 40px);

  .hi-v4-table-body {
    overflow: overlay !important;
    height: calc(100vh - 456px - 40px) !important;
  }
}
