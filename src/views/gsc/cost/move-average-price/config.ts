import intl from 'react-intl-universal'
import { AnyType } from '@/constants'
import dayjs from 'dayjs'
import { CSSProperties, Dispatch, SetStateAction } from 'react'

export const MoveSearchType = [
  'CVPN',
  'PN',
  'MPN_ID',
  'PN_CODE_DESC',
  'BRAND_NAME',
  'SOURCING_OPR_NAME_CN',
  'PURCHASE_USER_NAME_CN',
  'PROJECT_NAME',
  'ODM_MGR_DISPLAY_NAME',
  'PURCHASE_PATTERN_DESC',
  'WAERS_LST',
  'BIZ_NAME',
]

export const DataTypeMap = {
  option1: 'MPN_ID',
  option2: 'CVPN',
}

export const DEFAULT_CONDITIONS = {
  bizNames: [],
  categoryList: [],
  cvpnCodes: [],
  pnCodes: [],
  mpnIds: [],
  pnCodeDescs: [],
  brandNames: [],
  sourcingOprNameCns: [],
  purchaseUserNameCns: [],
  projectNames: [],
  odmMgrDisplayNames: [],
  purchasePatternDescs: [],
  waersLsts: [],
  dateList: [],
}

export const DEFAULT_DATE_RANGE = [
  // dayjs().subtract(1, 'month').format('YYYY-MM-DD'), // 测试用
  dayjs().format('YYYY-MM-DD'),
  dayjs().add(12, 'month').endOf('month').format('YYYY-MM-DD'),
]

export interface MovePriceType {
  bizNames?: string[]
  categoryList?: string[]
  cvpnCodes?: string[]
  pnCodes?: string[]
  startDate?: number
  endDate?: number
  mpnIds?: string[]
  pnCodeDescs?: string[]
  brandNames?: string[]
  sourcingOprNameCns?: string[]
  purchaseUserNameCns?: string[]
  projectNames?: string[]
  odmMgrDisplayNames?: string[]
  dateList?: number[]
  exchangeRateType?: string
}

export type MovePriceSetType<T> = Dispatch<SetStateAction<T>>
export type CommonType = Record<string, boolean | CSSProperties | undefined>

export const MOVE_PRICE_TABLE_FREEZE_KEY = 'move-table-freeze-key'

export const MAX_QUERY_LENGTH = 2000

export const calcConditions = (priceFilter) => {
  const { cvpnCodes = [], pnCodes = [], mpnIds = [] } = priceFilter || {}
  const isNotValid = {
    CVPN: cvpnCodes.length > MAX_QUERY_LENGTH,
    PN: pnCodes.length > MAX_QUERY_LENGTH,
    'MPN-ID': mpnIds.length > MAX_QUERY_LENGTH,
  }
  if (isNotValid.CVPN || isNotValid.PN || isNotValid['MPN-ID']) {
    return ['CVPN', 'PN', 'MPN-ID'].filter((i) => isNotValid[i]).join(' ')
  } else {
    return ''
  }
}

export const SETTLE_COST_TITLE = '当日移动平均价'
export const PERCENT_COST_TITLE = '价格波动'
export const TREND_COST_TITLE = '预估移动平均价'

export const MOVE_FORM_NPNID_CACHE = 'MOVE_FORM_NPNID_CACHE'

export const PRICE_ICON_COLOR = '#5F6A7A'

export enum MovePriceFetchEnum {
  normal,
  ascend,
  descend,
}

export enum MovePriceFilterNameKey {
  bizNames = '业务线',
  categoryList = '品类',
  brandNames = '品牌',
  businessDate = '业务日期',
  cvpnCodes = 'CVPN',
  pnCodes = 'PN',
  mpnIds = 'MPN',
  pnCodeDescs = '物料描述',
  sourcingOprNameCns = '资源负责人',
  purchaseUserNameCns = '采购员',
  purchasePatternDescs = '采购渠道',
  projectNames = '初始项目',
  odmMgrDisplayNames = 'ODM负责人',
}

export enum MovePriceMpnIdFilterNameKey {
  categoryList = '品类',
  businessDate = '业务日期',
  cvpnCodes = 'CVPN',
}

export const MOVE_PRICE_COLUMN_ORDER = 'MOVE_PRICE_COLUMN_ORDER'

export const BIZ_NAMES = [
  { value: '手机', name: '手机' },
  { value: '生态链', name: '生态链' },
  { value: '笔记本', name: '笔记本' },
  { value: '可穿戴', name: '可穿戴' },
  { value: '电视', name: '电视' },
] as AnyType[]

export const PURCHASE_WAYS = [
  { value: intl.get(intl.get('小米直采')), name: '小米直采' },
  { value: intl.get(intl.get('海外小米本采')), name: '海外小米本采' },
  { value: intl.get(intl.get('ODM代采')), name: 'ODM代采' },
  { value: intl.get(intl.get('ODM自采自用')), name: 'ODM自采自用' },
  { value: intl.get(intl.get('EMS本采')), name: 'EMS本采' },
  { value: intl.get(intl.get('不涉及')), name: '不涉及' },
  // 指定 fieldNames={{ id: 'value', title: 'name' }}后，defaultData 为 {id: string, title: string}[]，无法正确推断类型
] as AnyType[]
export const CURRENCY_SELECT_DATA = [
  { id: 'CNY', title: intl.get('人民币') },
  { id: 'USD', title: intl.get('美元') },
  { id: 'INR', title: intl.get('印度卢比') },
  { id: 'IDR', title: intl.get('印尼盾') },
]
