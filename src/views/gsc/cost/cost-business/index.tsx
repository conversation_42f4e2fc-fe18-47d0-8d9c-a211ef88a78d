import React, { FC, useEffect, useRef } from 'react'
import Tree, { TreeDataItem, useTreeSearch } from '@hi-ui/tree'
import { ErrorBoundary } from '@sentry/react'
import './index.scss'
import { useFullscreen, useSafeState } from 'ahooks'
import { CostItemType, SearchType } from './config'
import QueryForm from './queryForm'
import TableList from './table'
import cx from 'classnames'
import { getAll } from '@/api/cost/cost-business'
import DataTransformButton from './dataTransformButton'
import EmptyState, { EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL } from '@hi-ui/empty-state'
import intl from 'react-intl-universal'

const CostBusiness: FC = () => {
  const SearchTree = useTreeSearch(Tree)
  const [treeData, setTreeData] = useSafeState<TreeDataItem[]>([])
  const [filter, setFilter] = useSafeState<SearchType>({})
  const [costFilter, setCostFilter] = useSafeState<CostItemType>({
    id: 0,
    treeId: 0,
    level: 0,
  })
  const [visibility, setVisibility] = useSafeState<boolean>(false) // 是否显示树形控件
  const [isChange, setIsChange] = useSafeState<boolean>(true) // 树形控件内容是否改变
  const fullscreenDomRef = useRef<HTMLDivElement>(null)
  const [isFullscreen, { toggleFullscreen }] = useFullscreen(fullscreenDomRef)

  const filterData = (data) => {
    const filterChildren = (node) => {
      if (node.level === 2) {
        return { ...node, children: [] }
      }
      return {
        ...node,
        children: node.children?.reduce((acc, child) => {
          acc.push(filterChildren(child))
          return acc
        }, []),
      }
    }

    return data.reduce((acc, node) => {
      acc.push(filterChildren(node))
      return acc
    }, [])
  }
  useEffect(() => {
    if (isChange) {
      const fetchData = async () => {
        try {
          const treeRes = await getAll()
          setTreeData(filterData(treeRes?.data))
          setIsChange(false)
        } catch (err) {
          console.error('Failed to fetch data', err)
        }
      }
      fetchData()
    }
  }, [isChange, setIsChange, setTreeData])
  return (
    <div className="business-wrapper">
      <ErrorBoundary>
        <div className={cx('cost-menu-wrapper', { 'show-table': visibility })}>
          <SearchTree
            searchable={true}
            searchPlaceholder={intl.get('搜索')}
            data={treeData}
            fieldNames={{
              title: 'name',
              id: 'id',
            }}
            onSelect={(_, node: AnyType) => {
              if (node) {
                setVisibility(true)
                setCostFilter({
                  id: node?.id,
                  treeId: node?.raw?.parentId,
                  level: node?.raw?.level,
                })
              } else {
                setVisibility(false)
              }
            }}
          />
        </div>
      </ErrorBoundary>
      {visibility ? (
        <div className="cost-table-container">
          <ErrorBoundary>
            <DataTransformButton
              onSearch={setFilter}
              costFilter={costFilter}
              onChange={setIsChange}
            />
          </ErrorBoundary>
          <ErrorBoundary>
            <QueryForm onSearch={setFilter} costFilter={costFilter} />
          </ErrorBoundary>
          <ErrorBoundary>
            <div
              className={cx('cost-table-wrapper', {
                'cost-fullscreen-wrapper': isFullscreen,
              })}
              ref={fullscreenDomRef}
            >
              <TableList
                toggleFullscreen={toggleFullscreen}
                filter={filter}
                costFilter={costFilter}
                isFullscreen={isFullscreen}
                onChange={setIsChange}
              />
            </div>
          </ErrorBoundary>
        </div>
      ) : (
        <EmptyState
          className="flex justify-center items-center"
          style={{ width: '100%', backgroundColor: '#fff' }}
          title={intl.get('表格暂无数据')}
          indicator={EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL}
        />
      )}
    </div>
  )
}
export default CostBusiness
