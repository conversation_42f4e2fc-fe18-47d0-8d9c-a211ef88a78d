import intl from 'react-intl-universal'
import React, { memo, useCallback, useRef } from 'react'
import Portal from '@/components/portal'
import Button from '@hi-ui/button'
import { PlusOutlined } from '@hi-ui/icons'
import { HEADER_TOOLS_PORTAL } from '@/constants'
import './dataTransformButton.scss'
import { useSafeState } from 'ahooks'
import Drawer from '@hi-ui/drawer'
import { Form, FormHelpers, FormItem } from '@hi-ui/form'
import Input from '@hi-ui/input'
import { addCostItem } from '@/api/cost/cost-business'
import message from '@hi-ui/message'
import { CostItemType, getLabelText, SearchSetType, SearchType } from './config'
import { SetType } from '@/context/CostBusinessMappingContext'
import { customMessage } from '@/utils/custom-message'

const DataTransformButton = memo<{
  onSearch: SearchSetType<SearchType>
  costFilter: CostItemType
  onChange: SetType<boolean>
}>(({ onChange, costFilter, onSearch }) => {
  const portalRoot = document.getElementById(HEADER_TOOLS_PORTAL) || document.body
  const [drawerVisible, setDrawerVisible] = useSafeState<boolean>(false)
  const formRef = useRef<FormHelpers>(null)
  const initCondition = useRef<Record<string, string | number>>({
    code: '',
    name: '',
    remark: '',
    sortOrder: '',
  })

  const handleOpenDrawer = useCallback(() => {
    setDrawerVisible(true)
  }, [setDrawerVisible])

  const closeDrawer = useCallback(() => {
    setDrawerVisible(false)
    formRef.current?.reset()
  }, [setDrawerVisible])

  const AddCostItem = () => {
    formRef.current?.validate()?.then((values) => {
      const finalValues = {
        parentId: 0,
        ...values,
        sortOrder: Number(values.sortOrder),
      }

      const { code, name, sortOrder } = values

      // 定义验证规则
      const validations = [
        { value: sortOrder, maxLength: 10, message: intl.get('排序码不能超过10位') },
        { value: code, maxLength: 50, message: intl.get('编码不能超过50位') },
        { value: name, maxLength: 50, message: intl.get('名称不能超过50位') },
      ]

      // 执行验证
      for (const { value, maxLength, message } of validations) {
        if (String(value).length > maxLength) {
          customMessage(message, 'warning')
          return
        }
      }

      finalValues.parentId = costFilter?.id
      addCostItem(finalValues)
        .then((res) => {
          if (res.data === 'ok') {
            message.open({
              title: '新增成功！！',
              type: 'success',
            })
            closeDrawer()
            // 刷新树控件
            onChange(true)
            // 刷新表格
            onSearch({ code: [], name: [], status: [] })
          }
        })
        .catch((err) => {
          console.log(err)
        })
    })
  }
  return (
    <div>
      <Portal container={portalRoot}>
        <div className="button-portal-wrapper">
          <Button icon={<PlusOutlined />} type="primary" onClick={handleOpenDrawer}>
            {intl.get('新增')}
          </Button>
          {/* <Button type="primary">{intl.get('提交')}</Button>
          <Button type="primary">{intl.get('批量提交')}</Button> */}
        </div>
      </Portal>
      <Drawer
        title={intl.get('新增')}
        visible={drawerVisible}
        width={450}
        maskClosable
        unmountOnClose
        closeOnEsc
        onClose={closeDrawer}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button type="default" key={1} onClick={closeDrawer}>
              {intl.get('取消')}
            </Button>
            <Button type="primary" key={0} onClick={AddCostItem}>
              {intl.get('确认')}
            </Button>
          </div>
        }
      >
        <Form
          innerRef={formRef}
          initialValues={initCondition.current}
          labelWidth="100"
          labelPlacement="top"
        >
          <FormItem
            label={`${getLabelText(costFilter?.level)}编码`}
            field="code"
            valueType="string"
            required={true}
            rules={[{ required: true, message: intl.get('请输入') }]}
          >
            <Input placeholder={intl.get('请输入')} />
          </FormItem>
          <FormItem
            label={`${getLabelText(costFilter?.level)}名称`}
            field="name"
            valueType="string"
            required={true}
            rules={[{ required: true, message: intl.get('请输入') }]}
          >
            <Input placeholder={intl.get('请输入')} />
          </FormItem>
          <FormItem
            label={intl.get('排序码')}
            field="sortOrder"
            valueType="string"
            required={true}
            rules={[{ required: true, message: intl.get('请输入') }]}
          >
            <Input placeholder={intl.get('请输入')} />
          </FormItem>
          <FormItem label={intl.get('备注')} field="remark" valueType="string">
            <Input placeholder={intl.get('请输入')} />
          </FormItem>
        </Form>
        <br />
      </Drawer>
    </div>
  )
})
DataTransformButton.displayName = 'DataTransformButton'
export default DataTransformButton
