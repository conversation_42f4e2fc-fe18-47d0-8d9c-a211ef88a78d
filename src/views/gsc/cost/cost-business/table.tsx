import React, { memo, useCallback, useEffect, useMemo } from 'react'
import intl from 'react-intl-universal'
import cx from 'classnames'
import { usePagination, useSafeState } from 'ahooks'
import Button from '@hi-ui/button'
import { ColumnHeightOutlined, FullscreenExitOutlined, FullscreenOutlined } from '@hi-ui/icons'
import Pagination from '@hi-ui/pagination'
import { EmptyFunctionType } from '@/types/type'
import { SearchType, PRICE_ICON_COLOR, CostItemType, statusMap, getLabelText } from './config'
import './table.scss'
import { ellipses } from '@/components/ellipsis-tool'
import Dropdown from '@hi-ui/dropdown'
import dayjs from 'dayjs'
import {
  CostListType,
  deleteCostItem,
  disableCostItem,
  editCostItem,
  enableCostItem,
  getCostList,
} from '@/api/cost/cost-business'
import Drawer from '@hi-ui/drawer'
import { Form, FormHelpers, FormItem } from '@hi-ui/form'
import Input from '@hi-ui/input'
import message from '@hi-ui/message'
import Table, { TableColumnItem } from '@hi-ui/table'
import { SetType } from '@/context/CostBusinessMappingContext'
import { HiBaseSizeEnum } from '@hi-ui/core'

const TableList = memo<{
  toggleFullscreen: EmptyFunctionType
  filter: SearchType
  isFullscreen: boolean
  costFilter: CostItemType
  onChange: SetType<boolean>
}>(({ toggleFullscreen, filter, costFilter, onChange, isFullscreen = false }) => {
  const [columns, setColumns] = useSafeState<TableColumnItem[]>([])
  const formRef = React.useRef<FormHelpers>(null)
  const [selectedRowKeys, setSelectedRowKeys] = useSafeState<AnyType>([])
  const [drawerVisible, setDrawerVisible] = useSafeState<boolean>(false)
  const [tableSize, setTableSize] = useSafeState<HiBaseSizeEnum>('sm')
  const [initialValues, setInitialValues] = useSafeState<object[]>([])

  const {
    data: listResult,
    run: getList,
    pagination,
    loading: tableLoading,
  } = usePagination(
    ({ current, pageSize }) => {
      const params = { ...filter }
      Object.keys(params).forEach((key) => {
        if (!params[key]) delete params[key]
      })
      return getCostList({
        ...params,
        parentId: costFilter?.id,
        pageNum: current,
        pageSize,
      } as CostListType)
    },
    {
      manual: true,
      refreshDeps: [filter, costFilter],
      defaultPageSize: 20,
    }
  )
  useEffect(() => {
    Object.keys(filter).length && getList({ pageSize: pagination.pageSize, current: 1 })
  }, [filter, pagination.pageSize, getList, costFilter])

  const handleOpenDrawer = useCallback(
    (record) => {
      setInitialValues(record)
      setDrawerVisible(true)
    },
    [setDrawerVisible, setInitialValues]
  )

  const closeDrawer = useCallback(() => {
    setDrawerVisible(false)
    formRef.current?.reset()
  }, [setDrawerVisible])

  const EditCostItem = () => {
    formRef.current?.validate()?.then((values) => {
      const { id, sortOrder, code, name } = values || {}
      const newOrder = Number(sortOrder)
      const finalValues = {
        ...values,
        parentId: 0,
        sortOrder: newOrder,
      }

      const list = listResult?.list || []
      const leftArr = list.filter((item) => item.id !== id)
      if (leftArr.some((item) => item.sortOrder === newOrder)) {
        message.open({
          title: intl.get('排序码不能重复'),
          type: 'warning',
        })
        return
      }

      if (String(newOrder).length > 10) {
        message.open({
          title: intl.get('排序码不能超过10位'),
          type: 'warning',
        })
        return
      }

      if (String(code).length > 50) {
        message.open({
          title: intl.get('编码不能超过50位'),
          type: 'warning',
        })
        return
      }

      if (String(name).length > 50) {
        message.open({
          title: intl.get('名称不能超过50位'),
          type: 'warning',
        })
        return
      }

      finalValues.parentId = costFilter?.id
      editCostItem(finalValues)
        .then((res) => {
          if (res.data === 'ok') {
            message.open({
              title: intl.get('编辑成功'),
              type: 'success',
            })
            closeDrawer()
            //  触发树控件
            onChange(true)
            // 更新表格
            getList({ pageSize: pagination.pageSize, current: 1 })
          }
        })
        .catch((err) => {
          console.log(err)
        })
    })
  }
  const handleDelete = useCallback(
    (record) => {
      deleteCostItem({ id: record.id, cascade: 1 }).then((res) => {
        if (res.data === 'ok') {
          message.open({
            title: intl.get('删除成功'),
            type: 'success',
          })
          // 触发树控件
          onChange(true)
          // 更新表格
          getList({ pageSize: pagination.pageSize, current: 1 })
        }
      })
    },
    [getList, onChange, pagination.pageSize]
  )
  const handleDisable = useCallback(
    (record) => {
      disableCostItem({ id: record.id }).then((res) => {
        if (res.data === 'ok') {
          message.open({
            title: intl.get('失效成功'),
            type: 'success',
          })
          // 触发树控件
          onChange(true)
          // 更新表格
          getList({ pageSize: pagination.pageSize, current: 1 })
        }
      })
    },
    [getList, onChange, pagination.pageSize]
  )

  const handleEnable = useCallback(
    (record) => {
      enableCostItem({ id: record.id }).then((res) => {
        if (res.data === 'ok') {
          message.open({
            title: intl.get('启用成功'),
            type: 'success',
          })
          // 触发树控件
          onChange(true)
          // 更新表格
          getList({ pageSize: pagination.pageSize, current: 1 })
        }
      })
    },
    [getList, onChange, pagination.pageSize]
  )
  const columnsMemo = useMemo<TableColumnItem[]>(() => {
    return [
      {
        title: `${getLabelText(costFilter?.level)}编码`,
        width: 120,
        dataKey: 'code',
      },
      {
        title: `${getLabelText(costFilter?.level)}名称`,
        width: 150,
        dataKey: 'name',
      },
      {
        title: ellipses(intl.get('排序码')),
        width: 120,
        dataKey: 'sortOrder',
      },
      {
        title: ellipses('备注'),
        width: 120,
        dataKey: 'remark',
      },
      {
        title: ellipses('状态'),
        width: 120,
        dataKey: 'status',
        render: (val) => {
          return statusMap[val]
        },
      },
      {
        title: intl.get('更新时间'),
        width: 180,
        dataKey: 'updateTime',
        sorter: (a, b) => new Date(a.updateTime).getTime() - new Date(b.updateTime).getTime(),
        render: (text) => {
          return text ? dayjs(text).format('YYYY/MM/DD HH:mm') : '-'
        },
      },
      {
        title: intl.get('更新人'),
        width: 120,
        dataKey: 'updateName',
        sorter: (a, b) => a.updater.localeCompare(b.updater),
      },
      {
        title: ellipses('操作'),
        width: 160,
        dataKey: 'op',
        render: (text, record) => {
          const status = record.status
          return (
            <>
              <Button
                type="primary"
                appearance="link"
                onClick={() => handleOpenDrawer(record)}
                disabled={status === 1 || status === 4}
              >
                {intl.get('编辑')}
              </Button>
              {status === 4 ? (
                <Button type="primary" appearance="link" onClick={() => handleEnable(record)}>
                  {intl.get('启用')}
                </Button>
              ) : status === 3 ? (
                <Button type="primary" appearance="link" onClick={() => handleDelete(record)}>
                  {intl.get('删除')}
                </Button>
              ) : (
                <Button
                  type="primary"
                  appearance="link"
                  disabled={status === 1}
                  onClick={() => handleDisable(record)}
                >
                  {intl.get('失效')}
                </Button>
              )}
            </>
          )
        },
      },
    ]
  }, [costFilter?.level, handleDelete, handleDisable, handleEnable, handleOpenDrawer])

  useEffect(() => {
    setColumns(columnsMemo)
  }, [columnsMemo, setColumns])

  return (
    <div className="business-table-container">
      <div className="table-wrapper">
        <Table
          fieldKey="id"
          striped
          size={tableSize}
          columns={columns}
          data={listResult?.list || []}
          loading={tableLoading}
          rowSelection={{
            selectedRowKeys,
            onChange: (id) => {
              setSelectedRowKeys(id)
            },
          }}
          maxHeight="calc(100vh - 270px)"
        />
      </div>
      <div className="footer-wrapper">
        <div className={cx('table-tools', { 'justify-end': isFullscreen })}>
          {isFullscreen ? (
            <Button
              icon={<FullscreenExitOutlined />}
              appearance="link"
              onClick={toggleFullscreen}
            ></Button>
          ) : (
            <>
              <div className="no-dropdown-buttons">
                <i onClick={toggleFullscreen}>
                  <FullscreenOutlined color={PRICE_ICON_COLOR} />
                </i>
                <i>
                  <Dropdown
                    data={[
                      { id: 'sm', title: '默认' },
                      { id: 'lg', title: '宽松' },
                      { id: 'md', title: '中等' },
                    ]}
                    trigger="click"
                    onClick={(id) => {
                      setTableSize(id as HiBaseSizeEnum)
                    }}
                  >
                    <ColumnHeightOutlined color={PRICE_ICON_COLOR} />
                  </Dropdown>
                </i>
              </div>
            </>
          )}
        </div>
        <div className="flex justify-end w-full pl-13 pr-13">
          <Pagination
            showTotal
            showJumper
            pageSizeOptions={[10, 20, 30, 40, 50]}
            pageSize={pagination?.pageSize || 10}
            total={pagination?.total || 0}
            current={pagination?.current || 0}
            onPageSizeChange={(pageSize) => pagination.changePageSize(pageSize)}
            onChange={(current, _, pageSize) => pagination.onChange(current, pageSize)}
          />
        </div>
      </div>
      <Drawer
        title={intl.get('编辑')}
        visible={drawerVisible}
        width={450}
        maskClosable
        unmountOnClose
        closeOnEsc
        onClose={closeDrawer}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button type="default" key={1} onClick={closeDrawer}>
              {intl.get('取消')}
            </Button>
            <Button type="primary" key={0} onClick={EditCostItem}>
              {intl.get('保存')}
            </Button>
          </div>
        }
      >
        <Form
          innerRef={formRef}
          initialValues={initialValues}
          labelWidth="100"
          labelPlacement="top"
        >
          <FormItem
            label={`${getLabelText(costFilter?.level)}编码`}
            field="code"
            valueType="string"
            required={true}
            rules={[{ required: true, message: intl.get('请输入') }]}
          >
            <Input placeholder={intl.get('请输入')} />
          </FormItem>
          <FormItem
            label={`${getLabelText(costFilter?.level)}名称`}
            field="name"
            valueType="string"
            required={true}
            rules={[{ required: true, message: intl.get('请输入') }]}
          >
            <Input placeholder={intl.get('请输入')} />
          </FormItem>
          <FormItem
            label={intl.get('排序码')}
            field="sortOrder"
            required={true}
            valueType="number"
            rules={[{ required: true, message: intl.get('请输入') }]}
          >
            <Input placeholder={intl.get('请输入')} />
          </FormItem>
          <FormItem label={intl.get('备注')} field="remark" valueType="string">
            <Input placeholder={intl.get('请输入')} />
          </FormItem>
        </Form>
        <br />
      </Drawer>
    </div>
  )
})
TableList.displayName = 'TableList'
export default TableList
