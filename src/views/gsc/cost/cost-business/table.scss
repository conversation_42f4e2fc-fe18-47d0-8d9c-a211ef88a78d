.business-table-container {
  // min-width: 660px;
  margin-top: 12px;

  .table-wrapper {
    padding: 10px 16px 12px;

    .hi-v4-table {
      height: 100%;

      .hi-v4-table__wrapper {
        height: 100%;

        .hi-v4-table-body {
          overflow: overlay !important;
          height: calc(100vh - 270px) !important;

          .hi-v4-table-row:hover > .hi-v4-table-cell {
            background-color: #fff !important;
          }

          .active-column-text {
            color: #237ffa;

            &:hover {
              cursor: pointer;
              filter: brightness(1.1);
            }
          }
        }
      }
    }
  }

  .footer-wrapper {
    display: flex;
    height: 56px;
    background-color: #fff;
    position: absolute;
    width: 100%;
    right: 0;
    bottom: 0;

    .table-tools {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 5px;
      background-color: #fff;

      .no-dropdown-buttons {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        i {
          width: 32px;
          height: 32px;
          cursor: pointer;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          color: #5f6a7a;

          &:hover {
            background: #f2f4f7;
          }
        }
      }
    }
  }
}
