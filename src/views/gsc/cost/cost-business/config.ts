import { CSSProperties, Dispatch, SetStateAction } from 'react'
import intl from 'react-intl-universal'
export interface SearchType {
  code?: string[]
  name?: string[]
  status?: number[]
}

export const INIT_QUERY_DATA = {
  code: [],
  name: [],
  status: [],
}
export interface CostItemType {
  id: number
  treeId: number
  level: number
}
export const statusMap = {
  0: '待审批',
  1: '审批中',
  2: '已审批',
  3: '待提交',
  4: '已失效',
}
export type SearchSetType<T> = Dispatch<SetStateAction<T>>
export type CommonType = Record<string, boolean | CSSProperties | undefined>
export const PRICE_ICON_COLOR = '#5F6A7A'

export const getLabelText = (level: number) => {
  switch (level) {
    case 0:
      return intl.get('成本大类')
    case 1:
      return intl.get('成本中类')
    case 2:
      return intl.get('成本小类')
    default:
      return ''
  }
}
