import intl from 'react-intl-universal'
import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react'
import { useRequest } from 'ahooks'
import { Form, FormHelpers, FormItem } from '@hi-ui/form'
import { Col, Row } from '@hi-ui/grid'
import Button from '@hi-ui/button'
import {
  SearchType,
  SearchSetType,
  CostItemType,
  statusMap,
  getLabelText,
  INIT_QUERY_DATA,
} from './config'
import './queryForm.scss'
import { ellipses } from '@/components/ellipsis-tool'
import { getConditions } from '@/api/cost/cost-business'
import CheckSelect from '@hi-ui/check-select'

const QueryForm = memo<{
  onSearch: SearchSetType<SearchType>
  costFilter: CostItemType
}>(({ onSearch, costFilter }) => {
  const formRef = useRef<FormHelpers>(null)
  const initQueryCondition = useRef<Record<string, string[] | number[]>>(INIT_QUERY_DATA)
  const { run: getSelectConditions, data: selectResult } = useRequest(
    () => getConditions({ parentId: costFilter?.id }),
    {
      manual: true,
    }
  )
  const selectData: AnyType = useMemo(() => {
    return Object.keys(selectResult ?? []).reduce((acc, key) => {
      const items = selectResult[key]
      acc[key] = items.map((item) => ({
        id: item,
        title: key === 'status' ? statusMap[Number(item)] : item,
      }))
      return acc
    }, {})
  }, [selectResult])
  const handleReset = useCallback(() => {
    formRef.current?.reset()
    onSearch(initQueryCondition.current)
  }, [onSearch])
  const handleSearch = useCallback(() => {
    formRef.current?.validate()?.then((values) => {
      onSearch?.({ ...values })
    })
  }, [onSearch])
  useEffect(() => {
    Object.keys(costFilter).length && getSelectConditions()
    formRef.current?.reset()
    onSearch(initQueryCondition.current)
  }, [costFilter, getSelectConditions, onSearch])
  return (
    <div className="query-content">
      <Form
        labelPlacement="right"
        showColon={false}
        initialValues={initQueryCondition.current}
        innerRef={formRef}
        labelWidth={100}
      >
        <Row gutter rowGap={0}>
          <Col span={8} justify="flex-start">
            <FormItem field="code" label={`${getLabelText(costFilter?.level)}编码`}>
              <CheckSelect
                placeholder="请选择"
                searchable
                showCheckAll
                clearable
                data={selectData?.codes}
              />
            </FormItem>
          </Col>
          <Col span={8}>
            <FormItem field="name" label={`${getLabelText(costFilter?.level)}名称`}>
              <CheckSelect
                placeholder="请选择"
                searchable
                showCheckAll
                clearable
                data={selectData?.names}
              />
            </FormItem>
          </Col>
          <Col span={8}>
            <FormItem field="status" label={ellipses('状态')}>
              <CheckSelect
                placeholder="请选择"
                searchable
                showCheckAll
                clearable
                data={selectData?.status}
              />
            </FormItem>
          </Col>
        </Row>
      </Form>
      <div className="ml-6">
        <div className="pr-1 w-89">
          <Button type="default" onClick={handleReset}>
            {intl.get('重置')}
          </Button>
          <Button type="secondary" onClick={handleSearch}>
            {intl.get('查询')}
          </Button>
        </div>
      </div>
    </div>
  )
})
QueryForm.displayName = 'QueryForm'
export default QueryForm
