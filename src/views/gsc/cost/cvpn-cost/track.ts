import intl from 'react-intl-universal'
export const TABS_CODE = {
  10: intl.get('查看-SKU(CVPN维度)'),
  7: intl.get('查看-SOPCODE'),
  8: intl.get('查看-项目+配置'),
  9: intl.get('查看-项目'),
  6: intl.get('查看-SKU'),
  1: intl.get('查看-MPNID'),
  2: intl.get('查看-CVPN'),
  3: intl.get('查看-中类'),
  4: intl.get('查看-大类'),
  5: intl.get('查看-成本组中类'),
}
export enum QueryCodeName {
  timeRange = '成本预估区间',
  projectNames = '项目',
  projectSeries = '系列',
  saleVersions = '销售版本',
  mpnIds = 'MPN',
  cvpnCodes = 'CVPN',
  matCatLvl2Codes = '品类',
  costMatCatLvl2Codes = '成本组中类',
  skus = 'SKU',
  brands = '品牌',
  pns = '物料描述',
  configs = '配置',
  exchangeRateType = '货币单位',
  purchasePatternList = '采购渠道',
  waersList = '原币种',
  sourcingList = '资源负责人',
  bizNames = '业务线',
}
