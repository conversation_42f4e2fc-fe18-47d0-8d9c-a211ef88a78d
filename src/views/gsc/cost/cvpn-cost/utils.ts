import dayjs from 'dayjs'

export const formatParams = (formData) => {
  const {
    mpnIds = [],
    mat95s = [],
    costMatCatLvl2Codes = [],
    cvpnCodes = [],
    matCatLvl2Codes = [],
    skus = [],
    projectNames = [],
    saleVersions = [],
    projectSeries = [],
    brands = [],
    pns = [],
    configs = [],
    exchangeRateType = '',
    purchasePatternList = [],
    waersList = [],
    sourcingList = [],
    date = '',
    purchaseLvlCodes = [],
    bizNames = [],
  } = formData

  const formatTime =
    formData?.timeRange?.map((item) => {
      return dayjs(item).format('YYYYMM')
    }) || []

  return {
    startMonth: formatTime?.[0] || '',
    endMonth: formatTime?.[1] || '',
    mpnIds,
    mat95s,
    costMatCatLvl2Codes,
    cvpnCodes,
    matCatLvl2Codes,
    skus,
    projectNames,
    saleVersions,
    projectSeries,
    brands,
    pns,
    configs,
    exchangeRateType,
    purchasePatternList,
    waersList,
    sourcingList,
    date,
    purchaseLvlCodes,
    bizNames,
  }
}
