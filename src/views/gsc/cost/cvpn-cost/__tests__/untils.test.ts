import { describe, it, expect, vi } from 'vitest'
import { formatParams } from '../utils'

// mock dayjs
vi.mock('dayjs', async () => {
  const actual = await vi.importActual<AnyType>('dayjs')
  const mockDayjs = (date?: AnyType) => ({
    format: (fmt: string) => {
      if (fmt === 'YYYYMM') {
        if (date === '2024-01-01') return '202401'
        if (date === '2024-02-01') return '202402'
        return 'mocked'
      }
      return 'mocked'
    },
  })
  mockDayjs.extend = actual.default.extend
  mockDayjs.isDayjs = actual.default.isDayjs
  return { default: mockDayjs }
})

describe('formatParams 工具函数', () => {
  it('应正确格式化完整参数', () => {
    const input = {
      mpnIds: [1],
      mat95s: [2],
      costMatCatLvl2Codes: [3],
      cvpnCodes: [4],
      matCatLvl2Codes: [5],
      skus: [6],
      projectNames: ['A'],
      saleVersions: ['B'],
      projectSeries: ['C'],
      brands: ['D'],
      pns: ['E'],
      configs: ['F'],
      exchangeRateType: 'CNY',
      purchasePatternList: ['G'],
      waersList: ['H'],
      sourcingList: ['I'],
      date: '2024-01-01',
      purchaseLvlCodes: ['J'],
      bizNames: ['K'],
      timeRange: ['2024-01-01', '2024-02-01'],
    }
    const result = formatParams(input)
    expect(result.startMonth).toBe('202401')
    expect(result.endMonth).toBe('202402')
    expect(result.mpnIds).toEqual([1])
    expect(result.mat95s).toEqual([2])
    expect(result.costMatCatLvl2Codes).toEqual([3])
    expect(result.cvpnCodes).toEqual([4])
    expect(result.matCatLvl2Codes).toEqual([5])
    expect(result.skus).toEqual([6])
    expect(result.projectNames).toEqual(['A'])
    expect(result.saleVersions).toEqual(['B'])
    expect(result.projectSeries).toEqual(['C'])
    expect(result.brands).toEqual(['D'])
    expect(result.pns).toEqual(['E'])
    expect(result.configs).toEqual(['F'])
    expect(result.exchangeRateType).toBe('CNY')
    expect(result.purchasePatternList).toEqual(['G'])
    expect(result.waersList).toEqual(['H'])
    expect(result.sourcingList).toEqual(['I'])
    expect(result.date).toBe('2024-01-01')
    expect(result.purchaseLvlCodes).toEqual(['J'])
    expect(result.bizNames).toEqual(['K'])
  })

  it('应处理 timeRange 缺省和空数组', () => {
    const input = { timeRange: undefined }
    const result = formatParams(input)
    expect(result.startMonth).toBe('')
    expect(result.endMonth).toBe('')
  })

  it('应处理 timeRange 只有一个值', () => {
    const input = { timeRange: ['2024-01-01'] }
    const result = formatParams(input)
    expect(result.startMonth).toBe('202401')
    expect(result.endMonth).toBe('') // 实际返回的是空字符串
  })

  it('应处理所有字段缺省', () => {
    const result = formatParams({})
    expect(result.startMonth).toBe('')
    expect(result.endMonth).toBe('')
    expect(result.mpnIds).toEqual([])
    expect(result.mat95s).toEqual([])
    expect(result.costMatCatLvl2Codes).toEqual([])
    expect(result.cvpnCodes).toEqual([])
    expect(result.matCatLvl2Codes).toEqual([])
    expect(result.skus).toEqual([])
    expect(result.projectNames).toEqual([])
    expect(result.saleVersions).toEqual([])
    expect(result.projectSeries).toEqual([])
    expect(result.brands).toEqual([])
    expect(result.pns).toEqual([])
    expect(result.configs).toEqual([])
    expect(result.exchangeRateType).toBe('')
    expect(result.purchasePatternList).toEqual([])
    expect(result.waersList).toEqual([])
    expect(result.sourcingList).toEqual([])
    expect(result.date).toBe('')
    expect(result.purchaseLvlCodes).toEqual([])
    expect(result.bizNames).toEqual([])
  })
})
