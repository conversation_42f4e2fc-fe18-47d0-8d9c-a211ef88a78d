import * as constants from '../constants'
import { describe, it, expect, vi } from 'vitest'

// mock react-intl-universal
vi.mock('react-intl-universal', () => ({
  default: {
    get: (key: string) => `mock_${key}`,
  },
}))
// mock dayjs
vi.mock('dayjs', async () => {
  const actual = await vi.importActual<AnyType>('dayjs')
  const mockDayjs = () => ({
    format: (fmt: string) => {
      if (fmt === 'YYYYMMDD') return '20240101'
      if (fmt === 'YYYY-MM') return '2024-01'
      return 'mock-date'
    },
    add: () => mockDayjs(),
    subtract: () => mockDayjs(),
  })
  mockDayjs.extend = actual.default.extend
  mockDayjs.isDayjs = actual.default.isDayjs
  return { default: mockDayjs }
})

describe('constants.ts 导出内容', () => {
  it('YESTERDAY 应为数字', () => {
    expect(constants.YESTERDAY).toBeTypeOf('number')
    expect(constants.YESTERDAY).toBe(20240101)
  })

  it('tabConfig 应包含所有分支', () => {
    expect(Array.isArray(constants.tabConfig)).toBe(true)
    expect(constants.tabConfig.length).toBeGreaterThan(0)
    expect(constants.tabConfig[0]).toHaveProperty('id')
    expect(constants.tabConfig[0]).toHaveProperty('title')
    expect(constants.tabConfig.map((i) => i.title)).toContain('mock_SKU(CVPN维度)')
    expect(constants.tabConfig.map((i) => i.title)).toContain('mock_SKU(MPNID维度)')
    expect(constants.tabConfig.map((i) => i.title)).toContain('mock_成本组大类')
    expect(constants.tabConfig.map((i) => i.title)).toContain('mock_大类')
    expect(constants.tabConfig.map((i) => i.title)).toContain('mock_中类')
    expect(constants.tabConfig.map((i) => i.title)).toContain('CVPN')
    expect(constants.tabConfig.map((i) => i.title)).toContain('MPNID')
  })

  it('tabBizConfig 应包含所有分支', () => {
    expect(Array.isArray(constants.tabBizConfig)).toBe(true)
    expect(constants.tabBizConfig.length).toBeGreaterThan(0)
    expect(constants.tabBizConfig[0]).toHaveProperty('id')
    expect(constants.tabBizConfig[0]).toHaveProperty('title')
    expect(constants.tabBizConfig.map((i) => i.title)).toContain('mock_料号(CVPN维度)')
    expect(constants.tabBizConfig.map((i) => i.title)).toContain('mock_料号(MPNID维度)')
    expect(constants.tabBizConfig.map((i) => i.title)).toContain('mock_SKU(MPNID维度)')
    expect(constants.tabBizConfig.map((i) => i.title)).toContain('mock_成本组大类')
    expect(constants.tabBizConfig.map((i) => i.title)).toContain('mock_大类')
    expect(constants.tabBizConfig.map((i) => i.title)).toContain('mock_中类')
    expect(constants.tabBizConfig.map((i) => i.title)).toContain('CVPN')
    expect(constants.tabBizConfig.map((i) => i.title)).toContain('MPNID')
  })

  it('columnWidthObj 应包含所有字段', () => {
    expect(constants.columnWidthObj).toHaveProperty('sku', 140)
    expect(constants.columnWidthObj).toHaveProperty('projectName', 100)
    expect(constants.columnWidthObj).toHaveProperty('matCatLvl2Code', 360)
    expect(constants.columnWidthObj).toHaveProperty('costMatCatLvl2Code', 360)
  })

  it('TIME_RANGE 应为长度为2的数组', () => {
    expect(Array.isArray(constants.TIME_RANGE)).toBe(true)
    expect(constants.TIME_RANGE.length).toBe(2)
    expect(constants.TIME_RANGE[0]).toBe('2024-01')
    expect(constants.TIME_RANGE[1]).toBe('2024-01')
  })

  it('initialValues 应包含所有默认字段', () => {
    expect(constants.initialValues).toHaveProperty('timeRange')
    expect(constants.initialValues).toHaveProperty('projectNames')
    expect(constants.initialValues).toHaveProperty('bizNames')
    expect(constants.initialValues.bizNames).toContain('手机')
    expect(constants.initialValues.exchangeRateType).toBe('CNY')
  })

  it('CvpnMatFilterNameKey 枚举应包含所有key', () => {
    expect(constants.CvpnMatFilterNameKey.timeRange).toBe('成本预估区间')
    expect(constants.CvpnMatFilterNameKey.projectNames).toBe('项目')
    expect(constants.CvpnMatFilterNameKey.projectSeries).toBe('系列')
    expect(constants.CvpnMatFilterNameKey.saleVersions).toBe('销售版本')
    expect(constants.CvpnMatFilterNameKey.mpnIds).toBe('MPN')
    expect(constants.CvpnMatFilterNameKey.cvpnCodes).toBe('CVPN')
    expect(constants.CvpnMatFilterNameKey.costMatCatLvl2Codes).toBe('成本组中类')
    expect(constants.CvpnMatFilterNameKey.skus).toBe('SKU')
    expect(constants.CvpnMatFilterNameKey.brands).toBe('品牌')
    expect(constants.CvpnMatFilterNameKey.pns).toBe('物料描述')
    expect(constants.CvpnMatFilterNameKey.configs).toBe('配置')
    expect(constants.CvpnMatFilterNameKey.exchangeRateType).toBe('货币单位')
    expect(constants.CvpnMatFilterNameKey.purchasePatternList).toBe('采购渠道')
    expect(constants.CvpnMatFilterNameKey.waersList).toBe('原币种')
    expect(constants.CvpnMatFilterNameKey.sourcingList).toBe('资源负责人')
    expect(constants.CvpnMatFilterNameKey.bizNames).toBe('业务线')
  })

  it('CVPN_COST_COLUMN_ORDER、COST_TABLE_FREEZE_KEY 常量', () => {
    expect(constants.CVPN_COST_COLUMN_ORDER).toBe('CVPN_COST_COLUMN_ORDER')
    expect(constants.COST_TABLE_FREEZE_KEY).toBe('cost-table-freeze-key')
  })

  it('CURRENCY_SELECT_DATA 应包含所有币种', () => {
    expect(Array.isArray(constants.CURRENCY_SELECT_DATA)).toBe(true)
    expect(constants.CURRENCY_SELECT_DATA.length).toBe(4)
    expect(constants.CURRENCY_SELECT_DATA[0].id).toBe('CNY')
    expect(constants.CURRENCY_SELECT_DATA[0].title).toBe('mock_人民币')
    expect(constants.CURRENCY_SELECT_DATA[1].id).toBe('USD')
    expect(constants.CURRENCY_SELECT_DATA[1].title).toBe('mock_美元')
    expect(constants.CURRENCY_SELECT_DATA[2].id).toBe('INR')
    expect(constants.CURRENCY_SELECT_DATA[2].title).toBe('mock_印度卢比')
    expect(constants.CURRENCY_SELECT_DATA[3].id).toBe('IDR')
    expect(constants.CURRENCY_SELECT_DATA[3].title).toBe('mock_印尼盾')
  })

  it('FORM_DISABLED_MAP 应包含所有key及对应数组', () => {
    expect(constants.FORM_DISABLED_MAP).toHaveProperty('mpnIds')
    expect(Array.isArray(constants.FORM_DISABLED_MAP.mpnIds)).toBe(true)
    expect(constants.FORM_DISABLED_MAP.mpnIds).toContain('10')
    expect(constants.FORM_DISABLED_MAP.cvpnCodes).toContain('10')
    expect(constants.FORM_DISABLED_MAP.costMatCatLvl2Codes).toContain('10')
    expect(constants.FORM_DISABLED_MAP.matCatLvl2Codes).toContain('10')
    expect(constants.FORM_DISABLED_MAP.skus).toContain('9')
    expect(constants.FORM_DISABLED_MAP.brands).toContain('10')
    expect(constants.FORM_DISABLED_MAP.pns).toContain('10')
    expect(constants.FORM_DISABLED_MAP.purchasePatternList).toContain('10')
    expect(constants.FORM_DISABLED_MAP.waersList).toContain('10')
    expect(constants.FORM_DISABLED_MAP.sourcingList).toContain('10')
    expect(constants.FORM_DISABLED_MAP.saleVersions).toContain('9')
    expect(constants.FORM_DISABLED_MAP.configs).toContain('9')
    expect(constants.FORM_DISABLED_MAP.purchaseLvlCodes).toContain('10')
    expect(constants.FORM_DISABLED_MAP.bizNames).toContain('9')
  })
})
