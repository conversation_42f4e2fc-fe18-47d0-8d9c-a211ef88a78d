import { AnyType } from '@/constants'

export interface ConditionsType {
  mpnIds: AnyType[]
  mat95s: AnyType[]
  costMatCatLvl2Codes: AnyType[]
  cvpnCodes: AnyType[]
  matCatLvl1Codes: AnyType[]
  matCatLvl2Codes: AnyType[]
  skus: AnyType[]
  projectNames: AnyType[]
  saleVersions: AnyType[]
  projectSeries: AnyType[]
  brands: AnyType[]
  pns: AnyType[]
  configs: AnyType[]
  purchasePatternList: AnyType[]
  waersList: AnyType[]
  sourcingList: AnyType[]
  dateList: AnyType[]
  sopcodes: AnyType[]
  bizNames: AnyType[]
  colors: AnyType[]
  purchaseLvlCodes: AnyType[]
  saleSites: AnyType[]
  firstLevelSubjects: AnyType[]
  date: AnyType[]
  milestones: AnyType[]
  dates: AnyType[]
}

export interface TableData {
  tableList: AnyType[]
  total: number
}

export interface PaginationOptions {
  pageSize: number
  pageNum: number
}

export interface TabItem {
  id: number
  title: string
}

export interface CategoryNode {
  id: string
  title: string
  children: CategoryNode[]
}
