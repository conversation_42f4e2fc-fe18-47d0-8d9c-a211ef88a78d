import intl from 'react-intl-universal'
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useCallback, useEffect, useRef, useState, FC, ReactText, useMemo } from 'react'
import * as Sentry from '@sentry/react'
import { useFullscreen, useMount, useRequest, useUnmount } from 'ahooks'
import cx from 'classnames'
import SearchPannel from '@mi/sc-ui-search-panel'
import { FormDatePicker, FormCheckSelect, ProFormField, FormSelect } from '@mi/sc-ui-pro-form'
import { Card } from '@hi-ui/card'
import Button, { ButtonGroup } from '@hi-ui/button'
import { FormHelpers } from '@hi-ui/form'
import Tooltip from '@hi-ui/tooltip'
import Loading from '@hi-ui/loading'
import message from '@hi-ui/message'
import {
  DownloadOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  SettingOutlined,
  FreezeOutlined,
} from '@hi-ui/icons'
import CheckTreeSelect from '@hi-ui/check-tree-select'
import Table, { SettingDrawer } from '@hi-ui/table'
import Dropdown from '@hi-ui/dropdown'
import Pagination from '@hi-ui/pagination'
import * as API from '@/api/cost/estimate-cost'
import { CategoryNode, ConditionsType, PaginationOptions, TableData } from './types'
import {
  COST_TABLE_FREEZE_KEY,
  CURRENCY_SELECT_DATA,
  CVPN_COST_COLUMN_ORDER,
  FORM_DISABLED_MAP,
  TIME_RANGE,
  columnWidthObj,
  initialValues,
  needName,
  tabBizConfig,
  tabConfig,
} from './constants'
import { formatParams } from './utils'
import { getCategoryConditionList } from '@/api/cost/estimate-cost'
import { NOOP_ARR } from '@/utils/noop'
import { configFilterOptionFn } from '@/utils/get-pagination-data'
import { formatInput } from '@/utils/input-format'
import { AnyType, ESTIMATE_COST, FORM_VIEWS_CACHE, isTabPage, MESSAGE_DURATION } from '@/constants'
import { gscTrack, gscSearchTrack } from '@/utils/gscTrack-help'
import { QueryCodeName, TABS_CODE } from './track'
import FormView from '@/components/form-view'
import * as VIEW_API from '@/api/gsc/form-view'
import { ellipses } from '@/components/ellipsis-tool'
import { ProLocaleProvider } from '@mi/sc-ui-i18n'
import './index.scss'
import { getStringStorage } from '@/utils/cache'

const CvpnCost: FC = () => {
  const [columnsConfigVisible, setColumnsConfigVisible] = useState<boolean>(false)
  const tableContainer = useRef<HTMLDivElement | null>(null)
  const [isFullscreen, { enterFullscreen, exitFullscreen }] = useFullscreen(tableContainer)
  const [type, setType] = useState<number>(1)
  const [hiddenColKeys, setHiddenColKeys] = React.useState<string[]>([])
  const [sortedColKeys, setSortColKeys] = React.useState<string[]>([])
  const [frozenKey, setFrozenKey] = useState<string>('config')
  const [settingColumns, setSettingColumns] = useState<any[] | undefined>(undefined)
  const [tableColumns, setTableColumns] = useState<any[] | undefined>(undefined)
  const formRef = useRef<FormHelpers>(null)
  const [loading, setLoading] = useState<boolean>(false)
  const [collapse, setCollapse] = useState<boolean>(true)
  const [categoryCondition, setCategoryCondition] = useState<CategoryNode[]>()
  const [formValues, setFormValues] = useState<Record<string, AnyType>>({})
  const [pagination, setPagination] = useState<PaginationOptions>({
    pageSize: 50,
    pageNum: 1,
  })

  const [tableData, setTableData] = useState<TableData>({
    tableList: [],
    total: 0,
  })
  const [conditions, setConditions] = useState<Partial<ConditionsType>>({
    mpnIds: [],
    costMatCatLvl2Codes: [],
    cvpnCodes: [],
    skus: [],
    projectNames: [],
    saleVersions: [],
    projectSeries: [],
    brands: [],
    pns: [],
    configs: [],
    purchasePatternList: [],
    waersList: [],
    sourcingList: [],
    dates: [],
    purchaseLvlCodes: [],
    bizNames: [],
    mat95s: [],
  })
  const rateType = useRef<ReactText>('CNY')
  const [showRateType, setShowRateType] = useState<ReactText>('CNY')
  const [btnDisabled, setBtnDisabled] = useState<boolean>(false)
  const [formValue, setFormValue] = useState<AnyType>(initialValues)
  const clickTab = useRef<boolean>(false)

  const isShowBizForm = useMemo(() => {
    return formValue.bizNames && formValue.bizNames?.includes(needName)
  }, [formValue.bizNames])

  const handleCollapse = useCallback(
    (clps) => {
      setCollapse(clps)
      gscTrack.pageElemClick('展开/收起-项目成本-原材料', 'on/off', '01')
    },
    [setCollapse]
  )

  // 表格列排序，隐藏等
  const handleColumnsChange = useCallback(
    (sortedColKeys: string[], hiddenColKeys: string[], newColumns) => {
      const order = JSON.parse(localStorage.getItem(CVPN_COST_COLUMN_ORDER) as string) || {}
      localStorage.setItem(
        CVPN_COST_COLUMN_ORDER,
        JSON.stringify({
          ...order,
          [type]: newColumns.reduce((a, b, idx) => {
            a[b.dataKey] = idx + 1
            return a
          }, {}),
        })
      )
      setTableColumns(newColumns)
      setSortColKeys(sortedColKeys)
      setHiddenColKeys(hiddenColKeys)
      gscTrack.pageElemClick('设置-项目成本-原材料', 'set', '01')
    },
    [type]
  )

  // 隐藏抽屉
  const handleColumnsCancel = useCallback(() => {
    setColumnsConfigVisible(false)
  }, [])

  const compareValues = useCallback((a, b) => {
    if (typeof a === 'string') {
      return a.localeCompare(b)
    } else {
      return a - b
    }
  }, [])

  const handleSearch = useCallback(
    (formData, currentPage?: PaginationOptions): Promise<any> => {
      setLoading(true)
      return API.getList({
        type,
        pageSize: currentPage?.pageSize ?? pagination.pageSize,
        pageNum: currentPage?.pageNum ?? pagination.pageNum,
        ...formData,
      }).then((res) => {
        let _columns: AnyType[] = []
        const { data, total } = res.data.rowPage
        _columns = res.data.columnList?.map((item) => {
          const sorter = {
            sorter: (prev, next) => {
              const prevValue = prev?.raw?.[item.key] || 0
              const nextValue = next?.raw?.[item.key] || 0
              return compareValues(prevValue, nextValue)
            },
          }
          if (['pnCodeDesc', 'mpnIdDesc', 'spCode'].includes(item.key)) {
            return {
              dataKey: item.key,
              title: item.value,
              width: 200,
              render: (text) => (
                <Tooltip
                  title={<div style={{ maxWidth: 550, wordWrap: 'break-word' }}>{text}</div>}
                >
                  <span className="truncate block">{text}</span>
                </Tooltip>
              ),
              ...sorter,
            }
          } else {
            let width = 130
            if (Object.keys(columnWidthObj).includes(item.key)) {
              width = columnWidthObj[item.key]
            }
            return {
              dataKey: item.key,
              title: item.value,
              width,
              render: (text) => {
                if (typeof text === 'number') {
                  return text
                } else {
                  return text || '-'
                }
              },
              ...sorter,
            }
          }
        })
        setTableColumns((prev) =>
          prev === undefined ? _columns.filter((col) => !hiddenColKeys.includes(col.dataKey)) : prev
        )
        setTableData({
          tableList: data.map((item, index) => {
            return { ...item, id: index + item.projectName }
          }),
          total,
        })
        const freezeStorage =
          JSON.parse(localStorage.getItem(COST_TABLE_FREEZE_KEY) as string) || {}
        const initKey = _columns?.[2]?.dataKey
        const initFreezedColumn = freezeStorage[type] || initKey || ''
        setFrozenKey(initFreezedColumn)
        const allOrders = JSON.parse(localStorage.getItem(CVPN_COST_COLUMN_ORDER) as string) || {}
        const order = allOrders?.[type] || {}
        if (Object.keys(order)?.length !== 0) {
          // 有缓存才用
          _columns.sort((a, b) => {
            const indexA = order[a.dataKey]
            const indexB = order[b.dataKey]
            if (indexA < indexB) {
              return -1
            } else if (indexA > indexB) {
              return 1
            } else {
              return 0
            }
          })
        }
        setSettingColumns((prev) => {
          return prev === undefined ? _columns : prev
        })
        setLoading(false)
        setShowRateType(rateType.current)
      })
    },
    [type, pagination.pageSize, pagination.pageNum, compareValues, hiddenColKeys]
  )

  // 分类tab切换
  /**
   * 处理标签页切换的函数
   *
   * @param index 标签页的索引
   */
  const handleTabChange = (index: number) => {
    clickTab.current = true
    setSettingColumns(undefined)
    setTableColumns(undefined)
    setSortColKeys([])
    setHiddenColKeys([])
    setPagination({ pageSize: pagination.pageSize, pageNum: 1 })
    setType(index)
    const tabID = TABS_CODE[index]
    gscTrack.pageElemClick(tabID, 'view', '01')
  }
  const formatter = useCallback(
    (filter) => {
      const res = { ...filter }
      formRef.current?.setFieldsValue(res)
      setFormValue(res)
      return formatParams(res)
    },
    [setFormValue]
  )

  const onSearch = useCallback(
    (filter) => {
      setSettingColumns(undefined)
      setTableColumns(undefined)
      const currentPage = { pageSize: pagination.pageSize, pageNum: 1 }
      setPagination(currentPage)
      return handleSearch(filter, currentPage)
    },
    [handleSearch, pagination.pageSize]
  )

  const isChangePage = useRef<boolean>(false)

  useEffect(() => {
    if (isChangePage.current) {
      handleSearch(formatParams(formValue))
      isChangePage.current = false
    }
  }, [formValue, handleSearch, pagination])

  useMount(() => {
    const cache = localStorage.getItem(FORM_VIEWS_CACHE) || '[]'
    const content =
      JSON.parse(cache)?.find((item) => item?.pageType === ESTIMATE_COST)?.content || ''
    let config
    if (content) {
      const { formConfig } = JSON.parse(content)
      const { timeRange = [] } = formConfig
      config = timeRange?.length ? formConfig : { ...formConfig, timeRange: TIME_RANGE }
    } else {
      const { formConfig } = {
        formConfig: initialValues,
      }
      config = formConfig
    }
    onSearch(formatter(config))
  })

  useEffect(() => {
    if (clickTab.current) {
      const { timeRange, exchangeRateType, bizNames } = formRef.current?.getFieldsValue()
      const newFormValues = {
        ...initialValues,
        timeRange,
        exchangeRateType,
        bizNames: bizNames.includes(needName) ? bizNames : ['手机'],
      }
      setFormValue(newFormValues)
      formRef.current?.setFieldsValue(newFormValues)
      handleSearch(formatParams(newFormValues))
      clickTab.current = false
    } // 加入handleSearch方法作为依赖，会产生逻辑问题
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type])

  const handleExport = useCallback(() => {
    const toastId = message.open({ type: 'info', title: intl.get('导出中...'), autoClose: false })
    const formData = formRef.current?.getFieldsValue()
    setBtnDisabled(true)
    API.exportFiles({
      ...formatParams(formData),
      type,
    })
      .then((res) => {
        toastId && message.close(toastId)
        const fileArr = res?.data || []
        if (fileArr.length > 0) {
          for (const url of fileArr) {
            url && window.open(url)
          }
          message.open({ type: 'success', title: intl.get('导出成功'), duration: MESSAGE_DURATION })
        } else {
          message.open({
            type: 'info',
            title: intl.get(
              '导出数据较多，下载链接生成中，生成完毕会在飞书通知您，请稍后关注飞书消息'
            ),
            duration: MESSAGE_DURATION,
          })
        }
      })
      .catch((err) => {
        toastId && message.close(toastId)
        message.open({
          type: 'error',
          title: err?.message || intl.get('导出失败'),
          duration: MESSAGE_DURATION,
        })
      })
      .finally(() => setBtnDisabled(false))
    gscTrack.pageElemClick('导出-项目成本-原材料', 'export', '01')
  }, [type])

  useUnmount(() => message.closeAll())

  const clickFreezeColumn = (id) => {
    try {
      setFrozenKey(id)
      const freezeStorage = JSON.parse(localStorage.getItem(COST_TABLE_FREEZE_KEY) as string) || {}
      const newFreezeStorage = { ...freezeStorage, [type]: id }
      localStorage.setItem(COST_TABLE_FREEZE_KEY, JSON.stringify(newFreezeStorage))
      gscTrack.pageElemClick('冻结-项目成本-原材料', 'freeze', '01')
    } catch (error) {
      Sentry.captureException(error)
    }
  }

  const filterOptionMemo = useCallback((keyword, item) => {
    const arr = keyword ? formatInput(keyword) : []
    return arr.some((i) => item.title.includes(i))
  }, [])

  useRequest(getCategoryConditionList, {
    onSuccess: (res) => {
      const dataArrOrObj = res?.data ?? NOOP_ARR
      const finalRes = Object.keys(dataArrOrObj)?.reduce((a: CategoryNode[], b: string) => {
        const temp = { id: b, title: b, children: [] }
        dataArrOrObj[b].reduce((c, d) => {
          const _res = { id: d, title: d, children: [] }
          c.push(_res)
          return c
        }, temp.children)
        a.push(temp)
        return a
      }, [])
      setCategoryCondition(finalRes)
    },
  })

  useRequest(API.getConditions, {
    onSuccess: (res) => {
      const _conditions: Partial<ConditionsType> = {}
      if (
        res.data &&
        Object.prototype.toString.call(res.data) === '[object Object]' &&
        Object.keys(res.data).length
      ) {
        Object.keys(res.data).forEach((key) => {
          if (['string', 'number'].includes(typeof res.data[key][0])) {
            _conditions[key] = res.data[key].map((item) => {
              return { id: item, title: item, disabled: key === 'bizNames' && item === needName }
            })
          } else {
            _conditions[key] = res.data[key].map((item) => {
              return {
                id: item.name,
                title: item.value,
                disabled: key === 'bizNames' && item === needName,
              }
            })
          }
        })
        setConditions(_conditions)
      }
    },
  })

  useMount(() => VIEW_API.get({ pageType: ESTIMATE_COST }))

  const getFormItemConfig = useCallback((fieldKey, type) => {
    return FORM_DISABLED_MAP[fieldKey]?.includes(type.toString())
      ? { disabled: true, placeholder: intl.get('暂不可选') }
      : {}
  }, [])

  const selectOptions = useCallback(
    (options, type) => {
      let rest
      if (formValue[type]?.[0]) {
        rest =
          formValue[type]?.[0] === 'all'
            ? [{ id: 'all', title: '全部' }, ...options.map((key) => ({ ...key, disabled: true }))]
            : [{ id: 'all', title: '全部', disabled: true }, ...options]
      } else {
        rest = [{ id: 'all', title: '全部' }, ...options]
      }
      if (type === 'bizNames') {
        rest.shift() // 移除第一个元素
      }
      return rest
    },
    [formValue]
  )

  const resetBizData = useCallback(() => {
    setConditions((pre) => {
      return {
        ...pre,
        bizNames: (pre.bizNames || []).map((item) => {
          return {
            title: item.title,
            id: item.id,
            disabled: item.title === needName,
          }
        }),
      }
    })
  }, [])

  useEffect(() => {
    const { bizNames } = formRef.current?.getFieldsValue()
    if (!(bizNames || []).includes(needName)) {
      resetBizData()
    }
  }, [resetBizData, type])

  const showMat95s = useMemo(() => {
    return isShowBizForm && [10, 6].includes(type)
  }, [isShowBizForm, type])

  return (
    <div className="cvpn-cost">
      <Card className="top-card" bordered={false}>
        <ProLocaleProvider locale={intl.getInitOptions().currentLocale as any}>
          <SearchPannel
            initialValues={initialValues}
            labelPlacement="right"
            labelWidth={110}
            onSubmit={(formData) => {
              setFormValues(formatParams(formData))
              if (formData.bizNames && formData.bizNames.length === 0) {
                message.open({ type: 'warning', title: intl.get('请选择业务线') })
                return Promise.resolve()
              }
              if (!formData.bizNames.includes(needName) && type === 11) {
                handleTabChange(6)

                return Promise.resolve()
              }
              setSettingColumns(undefined)
              setTableColumns(undefined)
              const currentPage = { pageSize: pagination.pageSize, pageNum: 1 }
              setPagination(currentPage)
              gscSearchTrack({
                filters: formData,
                queryCodeName: QueryCodeName,
              })
              for (const key in formData) {
                // eslint-disable-next-line no-prototype-builtins
                if (formData.hasOwnProperty(key)) {
                  if (Array.isArray(formData[key])) {
                    if (
                      formData[key][0] === 'all' ||
                      (key === 'cvpnCodes' && formData[key].length === conditions[key]?.length) ||
                      (key === 'mpnIds' && formData[key].length === conditions[key]?.length)
                    )
                      formData[key] = []
                  }
                }
              }

              return handleSearch(formatParams(formData), currentPage)
            }}
            formRef={formRef}
            onCollapse={handleCollapse}
            defaultColHeight={44}
            onValuesChange={(_changedValues, allValues) => {
              setFormValue(allValues)
            }}
            onReset={() => {
              formRef.current?.reset()
              setFormValue(initialValues)
              setConditions((pre) => {
                return {
                  ...pre,
                  bizNames: (pre.bizNames || []).map((item) => {
                    return {
                      title: item.title,
                      id: item.id,
                      disabled: item.title === needName,
                    }
                  }),
                }
              })
              gscTrack.pageElemClick('重置-项目成本-原材料', 'reset', '01')
            }}
            submitter={{
              render: (_props, btn) => {
                return (
                  <>
                    {btn}
                    <FormView
                      api={VIEW_API}
                      filter={formValue}
                      pageType={ESTIMATE_COST}
                      onSearch={onSearch}
                      clickFormatter={formatter}
                      trackCode={intl.get('保存常用-项目成本-原材料')}
                    />
                  </>
                )
              },
            }}
          >
            <div>{ellipses(intl.get('项目信息'))}</div>
            <FormCheckSelect
              label={ellipses(intl.get('业务线'))}
              field="bizNames"
              fieldProps={{
                data: selectOptions(conditions.bizNames || [], 'bizNames'),
                searchable: true,
                showCheckAll: false,
                filterOption: configFilterOptionFn,
                onChange: (value) => {
                  setConditions((pre) => {
                    return {
                      ...pre,
                      bizNames: (pre.bizNames || []).map((item) => {
                        return {
                          title: item.title,
                          id: item.id,
                          disabled: value.includes(needName)
                            ? item.id !== needName
                            : value.length
                              ? item.id === needName
                              : false,
                        }
                      }),
                    }
                  })
                },
                ...getFormItemConfig('bizNames', type),
              }}
            />
            <FormCheckSelect
              label={ellipses(intl.get('项目'))}
              field="projectNames"
              fieldProps={{
                data: selectOptions(conditions.projectNames || [], 'projectNames'),
                searchable: true,
                filterOption: filterOptionMemo,
              }}
            />
            <FormCheckSelect
              label={ellipses(intl.get('系列'))}
              field="projectSeries"
              fieldProps={{
                data: selectOptions(conditions.projectSeries || [], 'projectSeries'),
                searchable: true,
              }}
            />
            <div></div>
            <FormCheckSelect
              label={ellipses(intl.get('销售版本'))}
              field="saleVersions"
              fieldProps={{
                data: conditions.saleVersions,
                searchable: true,
                ...getFormItemConfig('saleVersions', type),
              }}
            />
            <FormCheckSelect
              label={ellipses(intl.get('配置'))}
              field="configs"
              fieldProps={{
                data: conditions.configs,
                searchable: true,
                filterOption: configFilterOptionFn,
                ...getFormItemConfig('configs', type),
              }}
            />
            <FormCheckSelect
              label="SKU"
              field="skus"
              fieldProps={{
                data: selectOptions(conditions.skus || [], 'skus'),
                searchable: true,
                height: 260,
                filterOption: filterOptionMemo,
                ...getFormItemConfig('skus', type),
              }}
            />
            {showMat95s && <div></div>}
            {showMat95s && (
              <FormCheckSelect
                label={ellipses(intl.get('料号'))}
                field="mat95s"
                fieldProps={{
                  data: conditions.mat95s,
                  searchable: true,
                  height: 260,
                  ...getFormItemConfig('mat95s', type),
                }}
              />
            )}
            {showMat95s && <div></div>}
            {showMat95s && <div></div>}
            <div>{ellipses(intl.get('基础选项'))}</div>
            <FormDatePicker
              label={ellipses(intl.get('成本预估区间'), true)}
              field="timeRange"
              valueType="array"
              rules={[{ required: true }]}
              fieldProps={{
                format: 'YYYY-MM',
                type: 'monthrange',
                clearable: false,
              }}
            />
            <FormSelect
              label={ellipses(intl.get('货币单位'), true)}
              field="exchangeRateType"
              rules={[{ required: true }]}
              fieldProps={{
                data: CURRENCY_SELECT_DATA,
                clearable: false,
                defaultValue: 'CNY',
                onChange: (selectedId) => {
                  rateType.current = selectedId
                },
              }}
            />
            <FormSelect
              label={ellipses(intl.get('数据版本'))}
              field="date"
              fieldProps={{
                data: conditions.dates,
                searchable: true,
              }}
            />

            <div>{ellipses(intl.get('物料基本信息'))}</div>
            <FormCheckSelect
              label="CVPN"
              field="cvpnCodes"
              fieldProps={{
                data: conditions.cvpnCodes,
                showCheckAll: true,
                searchable: true,
                height: 260,
                filterOption: filterOptionMemo,
                ...getFormItemConfig('cvpnCodes', type),
              }}
            />
            <FormCheckSelect
              label="MPNID"
              field="mpnIds"
              fieldProps={{
                data: conditions.mpnIds,
                showCheckAll: true,
                searchable: true,
                height: 260,
                filterOption: filterOptionMemo,
                ...getFormItemConfig('mpnIds', type),
              }}
            />
            <FormCheckSelect
              label={ellipses(intl.get('物料描述'))}
              field="pns"
              fieldProps={{
                data: conditions.pns,
                searchable: true,
                height: 260,
                ...getFormItemConfig('pns', type),
              }}
            />
            <div></div>
            <FormCheckSelect
              label={ellipses(intl.get('品牌'))}
              field="brands"
              fieldProps={{
                data: selectOptions(conditions.brands || [], 'brands'),
                searchable: true,
                height: 260,
                filterOption: filterOptionMemo,
                ...getFormItemConfig('brands', type),
              }}
            />
            <ProFormField label={ellipses(intl.get('品类'))} field="matCatLvl2Codes">
              <CheckTreeSelect
                data={categoryCondition || []}
                checkedMode="CHILD"
                {...getFormItemConfig('matCatLvl2Codes', type)}
              />
            </ProFormField>
            <FormCheckSelect
              label={ellipses(intl.get('成本组中类'))}
              field="costMatCatLvl2Codes"
              fieldProps={{
                data: conditions.costMatCatLvl2Codes,
                searchable: true,
                ...getFormItemConfig('costMatCatLvl2Codes', type),
              }}
            />
            <div>{ellipses(intl.get('物料采购信息'))}</div>
            <FormCheckSelect
              label={ellipses(intl.get('资源负责人'))}
              field="sourcingList"
              fieldProps={{
                data: selectOptions(conditions.sourcingList || [], 'sourcingList'),
                searchable: true,
                ...getFormItemConfig('sourcingList', type),
                filterOption: filterOptionMemo,
              }}
            />
            <FormCheckSelect
              label={ellipses(intl.get('采购层级'))}
              field="purchaseLvlCodes"
              fieldProps={{
                data: selectOptions(conditions.purchaseLvlCodes || [], 'purchaseLvlCodes'),
                ...getFormItemConfig('purchaseLvlCodes', type),
              }}
            />
            <FormCheckSelect
              label={ellipses(intl.get('采购渠道'))}
              field="purchasePatternList"
              fieldProps={{
                data: selectOptions(conditions.purchasePatternList || [], 'purchasePatternList'),
                searchable: true,
                filterOption: filterOptionMemo,
                ...getFormItemConfig('purchasePatternList', type),
              }}
            />
            <div></div>
            <FormCheckSelect
              label={ellipses(intl.get('原币种'))}
              field="waersList"
              fieldProps={{
                data: conditions.waersList,
                searchable: true,
                ...getFormItemConfig('waersList', type),
              }}
            />
          </SearchPannel>
        </ProLocaleProvider>
        <div className="flex justify-between items-center cvpn-button-wrapper">
          <div className="table-operator">
            <ButtonGroup>
              {(formValues.bizNames && formValues.bizNames?.includes(needName)
                ? tabBizConfig
                : tabConfig
              ).map((item) => (
                <Button
                  type={type === item.id ? 'secondary' : 'default'}
                  key={item.id}
                  onClick={() => handleTabChange(item.id)}
                >
                  {item.title}
                </Button>
              ))}
            </ButtonGroup>
            <Dropdown
              data={(tableColumns || [])
                .filter((_item, index) => {
                  return index < 6
                })
                .map((item) => {
                  return {
                    id: item.dataKey,
                    title: (
                      <span style={{ color: frozenKey === item.dataKey ? '#237ffa' : '#1F2733' }}>
                        {item.title}
                      </span>
                    ),
                  }
                })}
              title={intl.get('冻结列')}
              trigger="click"
              overlay={{
                placement: 'bottom-start',
                matchWidth: true,
              }}
              onClick={(key) => clickFreezeColumn(key)}
              overlayClassName="cvpn-cost-dropdown"
            >
              <Button
                icon={<FreezeOutlined color="#5F6A7A" />}
                type="default"
                appearance="unset"
                className="bg-white"
                color="#5F6A7A"
              />
            </Dropdown>
            <Button
              icon={<SettingOutlined color="#5F6A7A" />}
              type="default"
              appearance="unset"
              style={{ marginLeft: 12 }}
              onClick={() => {
                setColumnsConfigVisible(true)
              }}
            />
            <Button
              icon={<DownloadOutlined color="#5F6A7A" />}
              type="default"
              appearance="unset"
              onClick={handleExport}
              disabled={btnDisabled}
            />
            <Button
              icon={<FullscreenOutlined color="#5F6A7A" />}
              type="default"
              appearance="unset"
              onClick={() => {
                enterFullscreen()
                gscTrack.pageElemClick('查看-全屏-项目成本-原材料', 'fullScreen', '01')
              }}
            />
          </div>
          <span className="unit" style={{ textAlign: 'right' }}>
            {intl.get('货币单位：')}
            {`${showRateType}`}
          </span>
        </div>
        <div ref={tableContainer}>
          {isFullscreen && (
            <div className="fullscreen-operator">
              <Button
                icon={<FullscreenExitOutlined />}
                type="default"
                appearance="unset"
                onClick={exitFullscreen}
              />
            </div>
          )}
          {settingColumns?.length ? (
            <div
              className={cx(
                'cvpn-cost-table',
                { 'no-expand-height-class': !collapse },
                { 'expand-height-class': collapse },
                { 'no-expand-height-class-mat95': !collapse && showMat95s },
                { tab: getStringStorage(isTabPage) === 'true' }
              )}
            >
              <Table
                loading={loading}
                fixedToColumn={{ left: frozenKey }}
                columns={tableColumns}
                data={tableData.tableList}
                size="sm"
                fieldKey="id"
                maxHeight="auto"
              />
            </div>
          ) : (
            <Loading content="">
              <div
                style={{ height: collapse ? 'calc(100vh - 384px)' : 'calc(100vh - 564px)' }}
              ></div>
            </Loading>
          )}
        </div>
        {settingColumns?.length ? (
          <SettingDrawer
            visible={columnsConfigVisible}
            onClose={handleColumnsCancel}
            drawerProps={{ width: 400, title: intl.get('字段设置') }}
            columns={settingColumns}
            hiddenColKeys={hiddenColKeys}
            sortedColKeys={sortedColKeys}
            onSetColKeysChange={handleColumnsChange}
            className="cvpn-column-settings"
          />
        ) : null}
      </Card>
      <div className="footer">
        <Pagination
          pageSize={Number(pagination.pageSize)}
          current={Number(pagination.pageNum)}
          total={tableData.total}
          showTotal
          pageSizeOptions={[10, 20, 50, 100]}
          showJumper
          onPageSizeChange={(pageSize) => {
            setPagination({ ...pagination, pageSize })
            isChangePage.current = true
            gscTrack.pageElemClick('每页条目-项目成本-原材料', 'entryPerPage', '01')
          }}
          onChange={(pageNum, _prev, pageSize) => {
            setPagination({ pageNum, pageSize })
            isChangePage.current = true
            gscTrack.pageElemClick('翻页-项目成本-原材料', 'flip', '01')
            isChangePage.current = true
          }}
        />
      </div>
    </div>
  )
}

export default CvpnCost
