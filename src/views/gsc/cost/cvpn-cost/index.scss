.cvpn-cost {
  div.sc-ui__search-panel__opts {
    button:nth-child(3) {
      border-radius: 4px 0 0 4px;
    }
  }

  div.sc-ui__search-panel__form {
    .hi-v4-grid-row {
      --hi-v4-grid-row-columns: 26 !important;

      .hi-v4-grid-col:nth-child(4n + 1) {
        font-size: 14px;
        font-weight: bolder;
        color: #000;
        padding-top: 4px;

        --hi-v4-grid-col-span: 2 !important;
      }
    }
  }

  .hi-v4-card {
    height: calc(100vh - 120px);

    .hi-v4-card__body {
      padding-bottom: 0;
      padding-top: 12px !important;

      .cvpn-button-wrapper {
        position: relative;

        &::before {
          content: '';
          position: absolute;
          height: 1px;
          width: 100%;
          background-color: rgb(235 237 240);
          top: 0;
          left: 0;
        }
      }
    }

    .hi-v4-form-label {
      margin-bottom: 12px;
    }

    .hi-v4-button__icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .hi-v4-dropdown {
      margin-left: 12px;
    }

    .fullscreen-operator {
      display: flex;
      justify-content: flex-end;
    }
  }

  .footer {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 56px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 24px;
    box-sizing: border-box;
    box-shadow: 0 -4px 4px rgb(0 0 0 / 5%);
  }
}

.table-operator {
  margin: 12px 0;
}

.cvpn-cost-table.no-expand-height-class {
  height: calc(100vh - 237px);

  .hi-v4-table-body {
    height: calc(100vh - 542px) !important;
    overflow: overlay;
  }
}

.cvpn-cost-table.expand-height-class {
  height: calc(100vh - 232px);

  .hi-v4-table-body {
    overflow: overlay;
    height: calc(100vh - 278px) !important;
  }
}

.cvpn-cost-table.no-expand-height-class-mat95 {
  height: calc(100vh - 237px);

  .hi-v4-table-body {
    overflow: overlay;
    height: calc(100vh - 592px) !important;
  }
}

.cvpn-cost-table.no-expand-height-class.tab {
  height: calc(100vh - 237px - 40px);

  .hi-v4-table-body {
    height: calc(100vh - 542px - 40px) !important;
    overflow: overlay;
  }
}

.cvpn-cost-table.expand-height-class.tab {
  height: calc(100vh - 232px - 40px);

  .hi-v4-table-body {
    overflow: overlay;
    height: calc(100vh - 278px - 40px) !important;
  }
}

.cvpn-cost-table.no-expand-height-class-mat95.tab {
  height: calc(100vh - 237px - 40px);

  .hi-v4-table-body {
    overflow: overlay;
    height: calc(100vh - 592px - 40px) !important;
  }
}

.cvpn-column-settings {
  .hi-v4-icon-filter-outlined {
    display: none;
  }
}

.cvpn-cost-dropdown {
  .hi-v4-icon-filter-outlined {
    display: none;
  }
}
