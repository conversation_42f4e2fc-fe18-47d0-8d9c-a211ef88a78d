import intl from 'react-intl-universal'
import dayjs from 'dayjs'
import { TabItem } from './types'

export const YESTERDAY = Number(dayjs().subtract(1, 'day').format('YYYYMMDD'))
export const needName = '生态链'
export const tabConfig: TabItem[] = [
  { id: 10, title: intl.get('SKU(CVPN维度)') },
  // { id: 9, title: intl.get('项目') },
  // { id: 8, title: intl.get('项目+配置') },
  // { id: 7, title: 'SOPCODE' },  笔电数据不匹配，暂时下线，后续解开
  { id: 6, title: intl.get('SKU(MPNID维度)') },
  { id: 5, title: intl.get('成本组大类') },
  { id: 4, title: intl.get('大类') },
  { id: 3, title: intl.get('中类') },
  { id: 2, title: 'CVPN' },
  { id: 1, title: 'MPNID' },
]

export const tabBizConfig: TabItem[] = [
  { id: 10, title: intl.get('料号(CVPN维度)') },
  // { id: 9, title: intl.get('项目') },
  // { id: 8, title: intl.get('项目+配置') },
  // { id: 7, title: 'SOPCODE' },  笔电数据不匹配，暂时下线，后续解开
  { id: 6, title: intl.get('料号(MPNID维度)') },
  { id: 11, title: intl.get('SKU(MPNID维度)') },
  { id: 5, title: intl.get('成本组大类') },
  { id: 4, title: intl.get('大类') },
  { id: 3, title: intl.get('中类') },
  { id: 2, title: 'CVPN' },
  { id: 1, title: 'MPNID' },
]

export const columnWidthObj = {
  sku: 140,
  projectName: 100,
  projectSeries: 190,
  saleVersion: 140,
  saleSite: 140,
  menge: 140,
  sopcode: 300,
  cvpnCode: 200,
  pnCode: 200,
  mpnId: 200,
  bizNames: 200,
  matCatLvl1Code: 300,
  matCatLvl2Code: 360,
  costMatCatLvl1Code: 240,
  costMatCatLvl2Code: 360,
}

export const TIME_RANGE = [
  dayjs().format('YYYY-MM'),
  dayjs(dayjs().add(13, 'month')).format('YYYY-MM'),
]

export const initialValues = {
  timeRange: TIME_RANGE,
  projectNames: [],
  projectSeries: [],
  saleVersions: [],
  mpnIds: [],
  mat95s: [],
  cvpnCodes: [],
  matCatLvl2Codes: [],
  costMatCatLvl2Codes: [],
  skus: [],
  brands: [],
  pns: [],
  type: [],
  configs: [],
  exchangeRateType: 'CNY',
  purchasePatternList: [],
  waersList: [],
  sourcingList: [],
  date: '',
  purchaseLvlCodes: [],
  bizNames: ['手机'],
}

export enum CvpnMatFilterNameKey {
  timeRange = '成本预估区间',
  projectNames = '项目',
  projectSeries = '系列',
  saleVersions = '销售版本',
  mpnIds = 'MPN',
  cvpnCodes = 'CVPN',
  costMatCatLvl2Codes = '成本组中类',
  skus = 'SKU',
  brands = '品牌',
  pns = '物料描述',
  configs = '配置',
  exchangeRateType = '货币单位',
  purchasePatternList = '采购渠道',
  waersList = '原币种',
  sourcingList = '资源负责人',
  bizNames = '业务线',
}

export const CVPN_COST_COLUMN_ORDER = 'CVPN_COST_COLUMN_ORDER'
export const COST_TABLE_FREEZE_KEY = 'cost-table-freeze-key'

export const CURRENCY_SELECT_DATA = [
  { id: 'CNY', title: intl.get('人民币') },
  { id: 'USD', title: intl.get('美元') },
  { id: 'INR', title: intl.get('印度卢比') },
  { id: 'IDR', title: intl.get('印尼盾') },
]

const COMMON_KEYS = ['10', '9', '8', '7', '6', '5', '4', '3', '2', '11']

export const FORM_DISABLED_MAP = {
  mpnIds: COMMON_KEYS,
  cvpnCodes: ['10', '9', '8', '7', '6', '5', '4', '3', '11'],
  costMatCatLvl2Codes: ['10', '9', '8', '7', '6', '11'],
  matCatLvl2Codes: ['10', '9', '8', '7', '6', '5', '11'],
  skus: ['9', '8', '7'],
  brands: COMMON_KEYS,
  pns: COMMON_KEYS,
  purchasePatternList: COMMON_KEYS,
  waersList: COMMON_KEYS,
  sourcingList: COMMON_KEYS,
  saleVersions: ['9', '8'],
  configs: ['9'],
  purchaseLvlCodes: COMMON_KEYS,
  bizNames: ['9', '8', '7'],
}
