import intl from 'react-intl-universal'
import React, { memo, useEffect, useMemo, useRef } from 'react'
import { useSafeState } from 'ahooks'
import { EllipsisTooltip } from '@hi-ui/ellipsis-tooltip'
import { useAllLifeCost } from '@/hooks/useAllLifeCost'
import { ALL_MILESTONES, COLOR_MAP, TEXT_COLOR_MAP } from './constants'

interface Parts {
  all?: number
  p1?: number
  p2?: number
  p3?: number
  p4?: number
}

const COMMON_STYLE_SETTINGS = {
  height: 24,
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  fontSize: 12,
  fontWeight: 400,
  lineHeight: 20,
}

const PromptBar = memo<{ parts: Parts }>(({ parts }) => {
  const { maxDigit, filter } = useAllLifeCost()
  const ref = useRef(null)
  const [pl, setPl] = useSafeState<number>(0)

  useEffect(() => {
    if (maxDigit) {
      const final = Math.floor((maxDigit - 1) / 3) * 2 + maxDigit * 8 + 4
      setPl(final)
    }
  }, [maxDigit, setPl])

  const shouldHiddenColKey = useMemo(
    () => ALL_MILESTONES.filter((item) => !filter.milestone?.includes(item)),
    [filter.milestone]
  )

  const bars = useMemo(() => {
    const { all, p1, p2, p3, p4 } = parts
    let allValue = all as number
    const arr = [
      { id: 1, text: '立项前', value: p1, key: 'beforeProject' },
      { id: 2, text: '量产前', value: p2, key: 'beforeMass' },
      { id: 3, text: '量产后实结', value: p3, key: 'afterMassActual' },
      { id: 4, text: '量产后预估', value: p4, key: 'afterMassEstimate' },
    ]
    const phases = arr.filter((phase) => !shouldHiddenColKey.includes(phase.key) && phase.value)

    allValue -= arr
      .filter((phase) => shouldHiddenColKey.includes(phase.key) && phase.value)
      .reduce((acc, item) => acc + (item.value as number), 0)

    const generatePhaseObject = (phase) => ({
      background: COLOR_MAP[phase.id],
      color: TEXT_COLOR_MAP[phase.id],
      text: intl.get(phase.text),
      width: `calc(100% / ${allValue} * ${phase.value})`,
      ...COMMON_STYLE_SETTINGS,
    })
    return phases.filter((phase) => phase.value !== 0).map(generatePhaseObject)
  }, [parts, shouldHiddenColKey])

  return (
    <div className="mt-8" style={{ paddingLeft: `calc(1% + ${pl}px)`, paddingRight: 'calc(3%)' }}>
      <div className="w-full flex" ref={ref}>
        {bars.map(({ text, ...left }, index) => {
          return (
            <div key={index} style={{ ...left }}>
              <EllipsisTooltip
                tooltipProps={{
                  placement: 'right',
                  gutterGap: 6,
                }}
              >
                {text}
              </EllipsisTooltip>
            </div>
          )
        })}
      </div>
    </div>
  )
})

PromptBar.displayName = 'PromptBar'

export default PromptBar
