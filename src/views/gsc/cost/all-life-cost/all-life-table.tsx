import intl from 'react-intl-universal'
import React, { FC, Fragment, ReactText, useCallback, useEffect, useMemo, useRef } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useFullscreen, useRequest, useSafeState } from 'ahooks'
import { features, useTablePipeline } from 'ali-react-table'
import * as fusion from '@alifd/next'
import dayjs from 'dayjs'
import EmptyState, { EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL } from '@hi-ui/empty-state'
import { Checkbox, CheckboxGroup } from '@hi-ui/checkbox'
import EllipsisTooltip from '@hi-ui/ellipsis-tooltip'
import { Loading } from '@hi-ui/loading'
import message from '@hi-ui/message'
import Button from '@hi-ui/button'
import Drawer from '@hi-ui/drawer'
import Card from '@hi-ui/card'
import {
  FullscreenExitOutlined,
  FullscreenOutlined,
  DownloadOutlined,
  SettingOutlined,
  CloseSquareOutlined,
} from '@hi-ui/icons'
import {
  ALL_MILESTONES,
  COLUMN_WIDTH_OBJ,
  ICON_COLOR,
  KEY_OBJ,
  PROJECT_COST_PATHNAME,
  NEW_PROJECT_COST_PATHNAME,
  P_MILESTONE_MAP,
  getCeilProps,
} from './constants'
import {
  exportAllCostList,
  getAllCostList,
  getProjectType,
  getPValue,
} from '@/api/cost/all-life-cost'
import { AnyType, MESSAGE_DURATION, TableColumnType } from '@/constants'
import { WebsiteBaseTable } from '@/components/website-base-table'
import { useAllLifeCost } from '@/hooks/useAllLifeCost'
import { customMessage } from '@/utils/custom-message'
import Tooltip from '@hi-ui/tooltip'

let emptyChildrenKey = 0
let tooltipKey = 0

const AllLifeTable: FC = () => {
  const { filter, selectObj, fetchChartSwitch, setSelectObj, setChangeFilter } = useAllLifeCost()
  const location = useLocation()
  const navigate = useNavigate()
  const tableContainer = useRef<HTMLDivElement | null>(null)
  const columnCache = useRef<(string | number)[]>([])
  const [isFullscreen, { enterFullscreen, exitFullscreen }] = useFullscreen(tableContainer)
  const [drawerVisible, setDrawerVisible] = useSafeState<boolean>(false)
  const [list, setList] = useSafeState<Record<string, string | number>[]>([])
  const [selectedColumns, setSelectedColumns] = useSafeState<ReactText[]>([])
  const [loading, setLoading] = useSafeState<boolean>(false)
  const [tableDataState, setTableDataState] = useSafeState<AnyType>(null)
  const [projectType, setProjectType] = useSafeState<Record<string, string>>({})
  const [params, setParams] = useSafeState<AnyType>({})
  const afterMassEstimate = useRef<boolean>(false)

  useEffect(() => {
    setChangeFilter({ chooseDimensions: Object.values(selectObj) })
  }, [selectObj, setChangeFilter])

  const { run: fetchTableData } = useRequest((data) => getAllCostList(data), {
    manual: true,
    onBefore: () => setLoading(true),
    onSuccess: (res) => {
      setTableDataState(res?.data)
    },
    onFinally: () => setLoading(false),
  })

  const { run: getType } = useRequest((data) => getProjectType(data), {
    manual: true,
    onSuccess: (data) => {
      setProjectType(data?.data)
    },
    onError: (err) => {
      message.open({
        type: 'error',
        title: err.message || intl.get('请求错误'),
      })
    },
  })

  const { run: download } = useRequest((data) => exportAllCostList(data), {
    manual: true,
    onBefore: () =>
      message.open({ type: 'info', title: intl.get('正在导出...'), autoClose: false }),
    onSuccess: (err) => {
      message.closeAll()
      const { data: url } = err || {}
      if (url) {
        window.open(url)
        message.open({ type: 'success', title: intl.get('导出成功'), duration: MESSAGE_DURATION })
      } else {
        message.open({
          type: 'error',
          title: intl.get('导出链接为空，请联系管理员'),
          duration: MESSAGE_DURATION,
        })
      }
    },
    onError: (err) => {
      message.closeAll()
      message.open({
        type: 'error',
        title: err?.message || intl.get('导出失败'),
        duration: MESSAGE_DURATION,
      })
    },
  })

  const {
    runAsync: getP,
    loading: getPValueLoading,
    cancel: cancelRequest,
  } = useRequest(getPValue, {
    manual: true,
  })

  const downloadList = useCallback(() => {
    if (Object.keys(filter).length !== 0) {
      download(filter)
    } else {
      message.open({ type: 'info', title: intl.get('请先查询数据'), duration: MESSAGE_DURATION })
    }
  }, [download, filter])

  const reTraceFilter = useCallback((filter) => {
    const { startMonth, endMonth, ...rest } = filter
    const res = {
      ...rest,
      range: [dayjs(startMonth).format('YYYYMM'), dayjs(endMonth).format('YYYYMM')],
    }
    return res
  }, [])

  const getCardSubTitle = useCallback(() => {
    const values = Object.values(selectObj) || []
    const arr = Object.values(values?.[0] || {})
    const str = arr?.join('-')
    let info = ''
    if (arr?.length) {
      info = `维度说明：${str}`
    } else {
      info = '维度说明：-'
    }
    return values.length > 0 ? `${info} 等` : info
  }, [selectObj])

  // 一级页面回显
  useEffect(() => {
    const state = location.state as Record<string, AnyType>
    if (state?.selectedObj) {
      setSelectObj(state.selectedObj)
    }
  }, [location.state, setSelectObj])

  useEffect(() => {
    if (Object.keys(projectType).length !== 0) {
      const { startMonth, endMonth, milestoneType, milestone } = projectType
      let range = [] as string[]
      if (startMonth && endMonth) {
        range = [startMonth as string, endMonth as string]
      }
      if (milestoneType === 'ADCP前') {
        navigate(NEW_PROJECT_COST_PATHNAME, { state: { ...params, range, milestone } })
      } else if (milestoneType === 'ADCP后') {
        navigate(PROJECT_COST_PATHNAME, { state: { ...params } })
      }
    }
  }, [filter, params, projectType, navigate])

  const traverseAndModify = useCallback(
    (data) => {
      if (Array.isArray(data)) {
        return data.map(traverseAndModify)
      } else if (typeof data === 'object') {
        return Object.keys(data).reduce((a: AnyType, key) => {
          a[KEY_OBJ[key] || key] = traverseAndModify(data[key])
          if (data.key === 'projectName') {
            a.render = (text, rowItem) => {
              const { projectName, config, saleSite, firstLevelSubject } = rowItem || {}
              const arr = [projectName, config, saleSite, firstLevelSubject].filter(Boolean)
              const lineKey = arr.join('-')
              const { startMonth, endMonth } = filter
              let range = [] as string[]
              if (startMonth && endMonth) {
                range = [startMonth as string, endMonth as string]
              }
              return (
                <Button
                  type="primary"
                  appearance="link"
                  onClick={() => {
                    getType({ projectName })
                    setParams({
                      projectName,
                      config,
                      saleSite,
                      firstLevelSubject,
                      range,
                      activeLine: lineKey,
                      selectedObj: selectObj,
                      title: getCardSubTitle(),
                      singleFilter: reTraceFilter(filter),
                    })
                  }}
                >
                  {text}
                </Button>
              )
            }
          }
          if (key === 'children' && Array.isArray(a.children) && !a.children?.length) {
            a.children = [
              {
                code: `${emptyChildrenKey++}`,
                width: 120,
                align: 'center',
                name: '',
              },
            ]
          }

          if (data.key === 'afterMassEstimate') {
            afterMassEstimate.current = true
          } else if (!isNaN(data.key) && afterMassEstimate.current) {
            a.render = (value, rowItem) => {
              if (!value) {
                return (
                  <div
                    className="w-full h-full cursor-pointer"
                    onMouseEnter={(e) => {
                      Tooltip.open(e.target as unknown as AnyType, {
                        key: `${tooltipKey++}`,
                        title: '合议P：0',
                        placement: 'top',
                      })
                    }}
                    onMouseLeave={() => Tooltip.close(`${tooltipKey - 1}`)}
                  ></div>
                )
              } else {
                return (
                  <div
                    className="w-full h-full cursor-pointer"
                    onMouseEnter={(e) => {
                      const { projectName, config, saleSite, firstLevelSubject, version } = rowItem
                      const params = {
                        projectName,
                        config,
                        saleSite,
                        firstLevelSubject,
                        startMonth: data.key.trim(),
                        endMonth: data.key.trim(),
                        versions: [version],
                      }
                      getP(params).then((res) => {
                        Tooltip.open(e.target as unknown as AnyType, {
                          key: `${tooltipKey++}`,
                          title: `合议P：${res?.data || 0}`,
                          placement: 'top',
                        })
                      })
                    }}
                    onMouseLeave={() => {
                      if (getPValueLoading) {
                        cancelRequest()
                      } else {
                        Tooltip.close(`${tooltipKey - 1}`)
                      }
                    }}
                  >
                    {value}
                  </div>
                )
              }
            }
          }

          a.width = COLUMN_WIDTH_OBJ[data.key] || 90
          if ((data.key || '').includes('MissingReason')) {
            a.render = (text) => <EllipsisTooltip>{text}</EllipsisTooltip>
            a.width = 150
          }
          if (data.key === 'version') {
            a.width = 120
          }
          a.align = 'center'
          return a
        }, {})
      } else {
        return data
      }
    },
    [
      filter,
      getPValueLoading,
      selectObj,
      getType,
      setParams,
      getCardSubTitle,
      reTraceFilter,
      getP,
      cancelRequest,
    ]
  )

  const radioChange = useCallback(
    (e, rowParams) => {
      fetchChartSwitch && (fetchChartSwitch.current = true)
      const subjectArr = (filter?.firstLevelSubjects || []) as string[]
      const { projectName, config, saleSite } = rowParams
      const commonObj = { projectName, config, saleSite }
      setSelectObj((prev) => {
        if (e.target.checked) {
          // 选择
          if (subjectArr.length) {
            subjectArr.forEach((subject) => {
              prev[`${projectName}-${config}-${saleSite}-${subject}`] = {
                ...commonObj,
                firstLevelSubject: subject,
              }
            })
          } else {
            prev[`${projectName}-${config}-${saleSite}`] = {
              ...commonObj,
            }
          }
        } else {
          // 取消选择
          if (subjectArr.length) {
            subjectArr.forEach((subject) => {
              delete prev[`${projectName}-${config}-${saleSite}-${subject}`]
            })
          } else {
            delete prev[`${projectName}-${config}-${saleSite}`]
          }
        }
        const res = { ...prev }
        if (Object.keys(res).length === 0) {
          customMessage(intl.get('清空后会默认选择第一组数据'), 'info')
        }
        return res
      })
    },
    [fetchChartSwitch, filter?.firstLevelSubjects, setSelectObj]
  )

  const cellCombine = useMemo(() => {
    const result = {}
    list.forEach((item) => {
      const key = `${item.projectName}-${item.config}-${item.saleSite}-${item.firstLevelSubject}`
      if (result[key]) {
        result[key]++
      } else {
        result[key] = 1
      }
    })
    return result
  }, [list])

  const columnsMemo = useMemo(() => {
    afterMassEstimate.current = false
    const dynamicCols = traverseAndModify(tableDataState?.columnList || [])
    const index = dynamicCols.findIndex((item) => item.code === P_MILESTONE_MAP.p4)
    const shouldMiddleCombine = filter.milestone?.includes(P_MILESTONE_MAP.p4) && index !== -1

    if (shouldMiddleCombine) {
      dynamicCols.forEach((item, idx) => {
        if (idx < index) {
          item.getCellProps = getCeilProps(cellCombine)
          if (Array.isArray(item.children) && item.children.length > 0 && idx < index) {
            item.children.forEach((child) => {
              child.getCellProps = getCeilProps(cellCombine)
            })
          }
        }
      })
      const arr = dynamicCols[index].children
      const len = arr.length
      if (arr[len - 1].code === 'afterMassEstimateMissingReason') {
        dynamicCols[index].children[len - 1].getCellProps = getCeilProps(cellCombine)
      }
    }

    const chooseColumns = {
      name: (
        <div className="h-7 flex justify-center items-center">
          选择
          <CloseSquareOutlined
            size={15}
            className="ml-1 cursor-pointer"
            onClick={() => {
              fetchChartSwitch && (fetchChartSwitch.current = true)
              setSelectObj({})
              customMessage(intl.get('清空后会默认选择第一组数据'), 'info')
            }}
          />
        </div>
      ),
      code: 'choose',
      lock: true,
      width: 80,
      align: 'center',
      getCellProps(_value, record) {
        const { projectName, config, saleSite, firstLevelSubject } = record
        const key = `${projectName}-${config}-${saleSite}-${firstLevelSubject}`
        return { colSpan: 1, rowSpan: cellCombine[key] }
      },
      render: (_text, row) => {
        const { projectName, config, saleSite, firstLevelSubject } = row || {}
        const arr = [projectName, config, saleSite, firstLevelSubject].filter(Boolean)
        const lineKey = arr.join('-')
        const rowParams = { projectName, config, saleSite, firstLevelSubject }
        return (
          <Checkbox
            checked={Object.keys(selectObj).includes(lineKey)}
            onChange={(e) => radioChange(e, rowParams)}
          />
        )
      },
    }
    let finalCols: AnyType = []
    if (dynamicCols?.length !== 0) {
      let leftIndex = dynamicCols.findIndex((item) => item.code === 'saleSite')
      if (leftIndex === -1) {
        leftIndex = dynamicCols.findIndex((item) => item.code === 'config')
      }
      if (leftIndex === -1) {
        leftIndex = dynamicCols.findIndex((item) => item.code === 'projectName')
      }
      dynamicCols.forEach((item, index) => {
        if (index <= leftIndex) {
          item.lock = true
        }
      })
      finalCols = [chooseColumns, ...dynamicCols]
    }
    return finalCols
  }, [
    traverseAndModify,
    tableDataState?.columnList,
    cellCombine,
    fetchChartSwitch,
    selectObj,
    filter.milestone,
    setSelectObj,
    radioChange,
  ])

  const hiddenColumn = useCallback(
    (arr) =>
      arr.filter(
        (item) =>
          ![TableColumnType.Hidden, TableColumnType.HiddenMeasure].includes(Number(item.type))
      ),
    []
  )

  const resetColumnSetting = useCallback(() => {
    setSelectedColumns(columnCache.current)
  }, [setSelectedColumns])

  const confirmColumnSetting = useCallback(() => {
    setTableDataState((prev) => ({
      ...prev,
      columnList: columnsMemo.slice(1).map((col) => ({
        ...col,
        type:
          col.type === `${TableColumnType.Fixed}`
            ? `${TableColumnType.Fixed}`
            : !selectedColumns.includes(col.code)
              ? `${TableColumnType.Hidden}`
              : `${TableColumnType.Normal}`,
      })),
    }))
    setDrawerVisible(false)
  }, [setTableDataState, setDrawerVisible, columnsMemo, selectedColumns])

  const allColumnsMemo = useMemo(
    () =>
      columnsMemo.slice(1).map((column) => ({
        ...column,
        id: column.code,
        title: column.name,
        disabled: Number(column.type) === TableColumnType.Fixed,
      })),
    [columnsMemo]
  )

  const shouldHiddenColKey = useMemo(
    () => ALL_MILESTONES.filter((item) => !filter.milestone?.includes(item)),
    [filter.milestone]
  )

  useEffect(() => {
    if (Object.keys(filter).length !== 0) {
      fetchTableData(filter)
    }
  }, [filter, fetchTableData])

  useEffect(() => {
    const { data = [] } = tableDataState?.rowPage || {}
    setList(data)
  }, [setList, tableDataState?.rowPage])

  useEffect(() => {
    const showKeys = hiddenColumn(allColumnsMemo).map((col) => col.code)
    columnCache.current = showKeys
    setSelectedColumns(showKeys)
  }, [allColumnsMemo, hiddenColumn, setSelectedColumns])

  const pipeline = useTablePipeline({ components: fusion }).input({
    dataSource: list,
    columns: hiddenColumn(columnsMemo).filter((col) => !shouldHiddenColKey.includes(col.code)),
  })

  pipeline.use(features.columnResize({ minSize: 80 }))

  return (
    <Card title={intl.get('项目全生命周期成本')} bordered={false} size="md" className="mt-6">
      <Loading visible={loading}>
        {list?.length !== 0 ? (
          <Fragment>
            <div className="pb-6">
              <Button
                icon={<SettingOutlined color={ICON_COLOR} />}
                type="default"
                appearance="unset"
                onClick={() => setDrawerVisible(true)}
              />
              <Button
                icon={<DownloadOutlined color={ICON_COLOR} />}
                type="default"
                appearance="unset"
                onClick={downloadList}
              />
              <Button
                icon={<FullscreenOutlined color={ICON_COLOR} />}
                type="default"
                appearance="unset"
                onClick={enterFullscreen}
              />
            </div>
            <div ref={tableContainer}>
              {isFullscreen && (
                <div className="px-8 py-6 flex justify-end">
                  <Button
                    icon={<FullscreenExitOutlined />}
                    type="default"
                    appearance="unset"
                    onClick={exitFullscreen}
                  />
                </div>
              )}
              <WebsiteBaseTable
                className="bordered"
                style={{
                  width: '100%',
                  height: isFullscreen ? 'calc(100vh - 192px)' : '458px',
                  overflow: 'auto',
                }}
                defaultColumnWidth={100}
                useVirtual={true}
                {...pipeline.getProps()}
              />
            </div>
          </Fragment>
        ) : (
          <EmptyState
            className="h-110 flex justify-center items-center"
            title={intl.get('表格暂无数据')}
            indicator={EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL}
          />
        )}
        <Drawer
          visible={drawerVisible}
          title={intl.get('字段设置')}
          width={400}
          onClose={() => setDrawerVisible(false)}
          footer={
            <div className="text-right">
              <Button type="default" key={1} onClick={resetColumnSetting}>
                {intl.get('重置')}
              </Button>
              <Button type="primary" key={0} onClick={confirmColumnSetting}>
                {intl.get('确认')}
              </Button>
            </div>
          }
        >
          <CheckboxGroup
            data={allColumnsMemo}
            placement="vertical"
            value={selectedColumns || []}
            onChange={(value) => setSelectedColumns(value)}
          />
        </Drawer>
      </Loading>
    </Card>
  )
}

export default AllLifeTable
