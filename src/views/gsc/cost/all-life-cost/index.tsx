import React, { FC, Fragment, useEffect } from 'react'
import { ErrorBoundary } from '@sentry/react'
import { CheckSelectMergedItem } from '@hi-ui/check-select'
import { useRequest, useSafeState } from 'ahooks'
import { AllLifeCostProvider } from '@/context/AllLifeCostContext'
import { getObjectStorage, setStorage } from '@/utils/cache'
import { getAllCostConditions } from '@/api/cost/all-life-cost'
import { NEW_ALL_COST_PROJECT_CACHE } from '@/constants'
import { ONE_HOUR_EXPIRES } from './constants'
import AllLifeForm from './all-life-form'
import AllLifeChart from './all-life-chart'
import AllLifeTable from './all-life-table'

const AllLifeCost: FC = () => {
  const [initConditions, setInitConditions] = useSafeState<Record<string, CheckSelectMergedItem[]>>(
    {}
  )

  const { runAsync: getProjectConditions } = useRequest(getAllCostConditions, {
    manual: true,
  })

  useEffect(() => {
    const { cacheTime, cache } = getObjectStorage(NEW_ALL_COST_PROJECT_CACHE)
    if (
      !cacheTime ||
      new Date().getTime() - cacheTime > ONE_HOUR_EXPIRES ||
      !Object.keys(cache)?.length
    ) {
      getProjectConditions().then((res) => {
        const obj = res?.data || {}
        setInitConditions(obj)
        setStorage(NEW_ALL_COST_PROJECT_CACHE, {
          cacheTime: new Date().getTime(),
          cache: obj,
        })
      })
    } else {
      setInitConditions(cache)
    }
  }, [getProjectConditions, setInitConditions])

  return Object.keys(initConditions)?.length !== 0 ? (
    <Fragment>
      <AllLifeCostProvider>
        <ErrorBoundary>
          <AllLifeForm initConditions={initConditions} />
        </ErrorBoundary>
        <ErrorBoundary>
          <AllLifeChart />
        </ErrorBoundary>
        <ErrorBoundary>
          <AllLifeTable />
        </ErrorBoundary>
      </AllLifeCostProvider>
    </Fragment>
  ) : null
}

export default AllLifeCost
