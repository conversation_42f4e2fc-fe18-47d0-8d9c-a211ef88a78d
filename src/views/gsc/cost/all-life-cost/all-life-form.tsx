import intl from 'react-intl-universal'
/* eslint-disable react/jsx-key */
import React, { FC, ReactNode, ReactText, useCallback, useEffect, useMemo, useRef } from 'react'
import { useLocation } from 'react-router-dom'
import { isEmpty } from 'lodash'
import { Form, FormHelpers, FormItem } from '@hi-ui/form'
import CheckSelect, { CheckSelectMergedItem } from '@hi-ui/check-select'
import DatePicker from '@hi-ui/date-picker'
import { DownOutlined } from '@hi-ui/icons'
import { Col, Row } from '@hi-ui/grid'
import { message } from '@hi-ui/message'
import Button from '@hi-ui/button'
import dayjs from 'dayjs'
import { useSafeState, useMount, useRequest } from 'ahooks'
import {
  ALL_ITEM,
  DEFAULT_MONTH_RANGE,
  FORM_INIT_VALUES,
  FORM_ITEM_CONFIG,
  MILESTONE_SELECT_DATA,
} from './constants'
import { AnyType, FORM_VIEWS_CACHE, PROJECT_LIFE_COST } from '@/constants'
import { useAllLifeCost } from '@/hooks/useAllLifeCost'
import * as FormViewAPI from '@/api/gsc/form-view'
import FormView from '@/components/form-view'
import * as API from '@/api/cost/all-life-cost'
import { ellipses } from '@/components/ellipsis-tool'
import './all-life-form.scss'

interface Props {
  initConditions: Record<string, CheckSelectMergedItem[]>
}

const AllLifeForm: FC<Props> = ({ initConditions }) => {
  const location = useLocation()
  const {
    projectNames = [],
    configs = [],
    recordVersions = [],
    saleSites = [],
    firstLevelSubjects = [],
    bizNames = [],
  } = initConditions
  const { setFilter, fetchChartSwitch } = useAllLifeCost()
  const formRef = useRef<FormHelpers>(null)

  const [conditions, setConditions] = useSafeState({
    projectNames,
    configs,
    recordVersions,
    saleSites,
    firstLevelSubjects,
    bizNames,
  })
  const [formValue, setFormValue] =
    useSafeState<Record<string, string | string[]>>(FORM_INIT_VALUES)
  const [expanded, setExpanded] = useSafeState<boolean>(false)
  const [configLoading, setConfigLoading] = useSafeState<boolean>(false)
  const [oldestDate, setOldestDate] = useSafeState<ReactText>('')

  const { runAsync: update } = useRequest((data) => API.getConfigByProject(data), {
    manual: true,
  })

  const selectOptions = useCallback(
    (options, type) => {
      let arr
      if (formValue[type]?.[0]) {
        arr =
          // 在下拉框最前面加上全部选项
          formValue[type]?.[0] === ALL_ITEM.value
            ? [ALL_ITEM, ...options.map((key) => ({ ...key, disabled: true }))]
            : [{ ...ALL_ITEM, disabled: true }, ...options]
      } else {
        arr = [ALL_ITEM, ...options]
      }
      return arr
    },
    [formValue]
  )

  const formValuesToSearch = useCallback(({ range, recordVersions, milestone, ...rest }) => {
    const startMonth = range[0] ? dayjs(range[0]).format('YYYYMM') : DEFAULT_MONTH_RANGE[0]
    const endMonth = range[1] ? dayjs(range[1]).format('YYYYMM') : DEFAULT_MONTH_RANGE[1]
    const obj = {}
    Object.keys(rest).forEach((key) => {
      const cur = rest[key]
      if (Array.isArray(cur)) {
        obj[key] = isEmpty(cur) ? null : cur?.[0] === 'all' ? [] : cur
      } else {
        obj[key] = cur
      }
    })
    return {
      ...obj,
      startMonth,
      endMonth,
      recordVersions,
      milestone,
    }
  }, [])

  const formatter = useCallback(
    (filter) => {
      const res = { ...filter }
      const { range } = res
      const newRange = range[0]
        ? [dayjs(range[0]).format('YYYYMM'), dayjs(range[1]).format('YYYYMM')]
        : DEFAULT_MONTH_RANGE
      setFormValue(res)
      formRef.current?.setFieldsValue({ ...res, range: newRange })
      ;(fetchChartSwitch as AnyType).current = true
      return formValuesToSearch(res)
    },
    [fetchChartSwitch, formValuesToSearch, setFormValue]
  )

  const clickSearch = useCallback(() => {
    const { projectNames = [], range = [], milestone = [] } = formValue || {}
    if ([projectNames?.length, range?.length, milestone?.length].includes(0)) {
      message.open({ title: intl.get('请填写必填项'), type: 'info' })
    } else {
      formRef.current?.validate()?.then((values) => {
        location.state = null
        setFilter(formatter(values))
      })
    }
  }, [formValue, location, formatter, setFilter])

  const clickReset = useCallback(() => {
    formRef.current?.reset()
    setFormValue(FORM_INIT_VALUES)
  }, [setFormValue])

  const updateConfig = useCallback(() => {
    setConfigLoading(true)
    const { projectNames = [] } = formValue || {}
    const params =
      Array.isArray(projectNames) && !projectNames.length
        ? []
        : projectNames[0] === 'all'
          ? []
          : projectNames

    update({ projectNames: params })
      .then((res) => {
        const { data = [] } = res || {}
        setConditions((prev) => ({
          ...prev,
          configs: data,
        }))
      })
      .catch((err) => {
        message.open({ title: err?.message || intl.get('获取配置失败'), type: 'error' })
      })
      .finally(() => {
        setConfigLoading(false)
      })
  }, [formValue, setConditions, setConfigLoading, update])

  useMount(() => {
    let config
    const state = location.state as Record<string, string | string[]>
    if (!state?.singleFilter) {
      const cache = localStorage.getItem(FORM_VIEWS_CACHE) || '[]'
      const content =
        JSON.parse(cache)?.find((item) => item?.pageType === PROJECT_LIFE_COST)?.content || ''
      if (content) {
        const { formConfig } = JSON.parse(content)
        const { range = '' } = formConfig
        config = range ? formConfig : { ...formConfig, range: DEFAULT_MONTH_RANGE }
      } else {
        const { formConfig } = { formConfig: FORM_INIT_VALUES }
        config = formConfig
      }
    } else {
      const finalConfig = {}
      const { recordVersions, ...rest } = state.singleFilter as AnyType
      Object.keys(rest).forEach((key) => {
        const cur = rest[key]
        if (Array.isArray(cur)) {
          if (cur.length === 0) {
            finalConfig[key] = ['all']
          } else {
            finalConfig[key] = cur
          }
        } else {
          finalConfig[key] = []
        }
      })
      config = { ...finalConfig, recordVersions }
    }
    setFilter(formatter(config))
  })

  const items = useMemo(() => {
    const {
      projectNames = [],
      configs = [],
      recordVersions = [],
      saleSites = [],
      firstLevelSubjects = [],
      bizNames = [],
    } = conditions || {}
    return [
      <FormItem field="projectNames" label={ellipses(intl.get('项目'), true)}>
        <CheckSelect
          data={selectOptions(projectNames, 'projectNames')}
          searchable
          fieldNames={{ id: 'value', title: 'name' }}
          {...FORM_ITEM_CONFIG}
        ></CheckSelect>
      </FormItem>,
      <FormItem field="configs" label={ellipses(intl.get('配置'))}>
        <CheckSelect
          data={selectOptions(configs, 'configs')}
          searchable
          fieldNames={{ id: 'value', title: 'name' }}
          onOpen={updateConfig}
          loading={configLoading}
          {...FORM_ITEM_CONFIG}
        ></CheckSelect>
      </FormItem>,
      <FormItem field="range" label={ellipses(intl.get('成本预估区间'), true)}>
        <DatePicker
          type="monthrange"
          format="YYYY-MM"
          clearable={false}
          {...FORM_ITEM_CONFIG}
        ></DatePicker>
      </FormItem>,
      <FormItem field="recordVersions" label={ellipses(intl.get('对比历史预估'))}>
        <CheckSelect
          data={recordVersions}
          searchable
          showCheckAll
          fieldNames={{ id: 'value', title: 'name' }}
          onChange={(_value, _, checkedItems) => {
            let v = dayjs().format('YYYYMM')
            checkedItems.forEach((i: AnyType) => {
              if (Number(i.value) < Number(v)) {
                v = i.value
              }
            })
            v && setOldestDate(v)
          }}
          {...FORM_ITEM_CONFIG}
        ></CheckSelect>
      </FormItem>,
      <FormItem field="saleSites" label={ellipses(intl.get('区域'))}>
        <CheckSelect
          data={selectOptions(saleSites, 'saleSites')}
          searchable
          fieldNames={{ id: 'value', title: 'name' }}
          {...FORM_ITEM_CONFIG}
        ></CheckSelect>
      </FormItem>,
      <FormItem field="firstLevelSubjects" label={ellipses(intl.get('一级科目'))}>
        <CheckSelect
          data={firstLevelSubjects}
          searchable
          fieldNames={{ id: 'value', title: 'name' }}
          {...FORM_ITEM_CONFIG}
        ></CheckSelect>
      </FormItem>,
      <FormItem field="milestone" label={ellipses(intl.get('项目阶段'), true)}>
        <CheckSelect
          data={MILESTONE_SELECT_DATA as CheckSelectMergedItem[]}
          showCheckAll
          fieldNames={{ id: 'value', title: 'name' }}
          {...FORM_ITEM_CONFIG}
        ></CheckSelect>
      </FormItem>,
      <FormItem field="bizNames" label={ellipses(intl.get('业务线'))}>
        <CheckSelect
          data={bizNames as CheckSelectMergedItem[]}
          showCheckAll
          fieldNames={{ id: 'value', title: 'name' }}
          {...FORM_ITEM_CONFIG}
        ></CheckSelect>
      </FormItem>,
    ]
  }, [conditions, configLoading, selectOptions, setOldestDate, updateConfig])

  useEffect(() => {
    if (oldestDate) {
      const arr = formRef.current?.getFieldValue('range')
      const newRange = [oldestDate, arr[1]]
      formRef.current?.setFieldValue('range', newRange)
      setFormValue((prev) => ({
        ...prev,
        range: newRange,
      }))
    }
  }, [oldestDate, setFormValue])

  return (
    <div
      className="all-life-cost__form transition-[height]"
      style={{ height: expanded ? 56 : 144 }}
    >
      <Form
        innerRef={formRef}
        initialValues={FORM_INIT_VALUES}
        labelWidth={110}
        labelPlacement="right"
        showColon={false}
        onValuesChange={(_changedValues, allValues) => {
          setFormValue(allValues)
        }}
      >
        {items
          .map((item, index) => (
            <Col span={8} key={`${item.props.field}${index}`}>
              {item}
            </Col>
          ))
          .reduce((a: ReactNode[], b, idx, arr) => {
            if (idx % 3 === 0)
              a.push(
                <Row gutter rowGap={0} key={`${b.props.field}${idx}`}>
                  {arr.slice(idx, idx + 3)}
                </Row>
              )
            return a
          }, [])}
      </Form>
      <div className="form-button__container ml-6 pl-6">
        <div className="w-100 pt-0.5 pr-0.5 pb-5 flex">
          <Button appearance="link" onClick={() => setExpanded((expanded) => !expanded)}>
            {expanded ? intl.get('展开') : intl.get('收起')}
            <DownOutlined
              className="transition-transform ml-2"
              color="#9d9d9f"
              style={{
                transform: expanded ? 'rotateZ(0)' : 'rotateZ(180deg)',
              }}
            />
          </Button>
          <Button type="default" onClick={clickReset}>
            {intl.get('重置')}
          </Button>
          <Button type="secondary" onClick={clickSearch} className="search-button">
            {intl.get('查询')}
          </Button>
          <FormView
            api={FormViewAPI}
            filter={formValue}
            pageType={PROJECT_LIFE_COST}
            onSearch={setFilter}
            clickFormatter={formatter}
          />
        </div>
      </div>
    </div>
  )
}
export default AllLifeForm
