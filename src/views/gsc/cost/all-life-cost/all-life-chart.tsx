import intl from 'react-intl-universal'
import React, { FC, useEffect, useMemo, useRef } from 'react'
import EmptyState, { EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL } from '@hi-ui/empty-state'
import { Loading } from '@hi-ui/loading'
import Card from '@hi-ui/card'
import { useRequest, useSafeState } from 'ahooks'
import CostLine from '@/components/antv-component/CostLine'
import { useAllLifeCost } from '@/hooks/useAllLifeCost'
import { getAllCostReport } from '@/api/cost/all-life-cost'
import PromptBar from './prompt-bar'
import { useLocation } from 'react-router-dom'
import {
  ALL_MILESTONES,
  generateDynamicColorMap,
  getLen,
  MILESTONE_MAP,
  sortChartData,
} from './constants'
import './all-life-chart.scss'

const AllLifeChart: FC = () => {
  const { filter, changeFilter, fetchChartSwitch, selectObj, setSelectObj, setMaxDigit } =
    useAllLifeCost()
  const location = useLocation()
  const filterRef = useRef<Record<string, string[] | string>>({})
  const [chartData, setChartData] = useSafeState<Record<string, string | number>[]>([])
  const [loading, setLoading] = useSafeState<boolean>(false)
  const [updateDate, setUpdateDate] = useSafeState<string>('')
  const [parts, setParts] = useSafeState<Record<string, number>>({})
  const [chartColor, setChartColor] = useSafeState<Record<string, string>>({})
  const [maxCost, setMaxCost] = useSafeState<number>(0)
  const [minCost, setMinCost] = useSafeState<number>(0)

  const shouldHiddenColKey = useMemo(
    () => ALL_MILESTONES.filter((item) => !filter.milestone?.includes(item)),
    [filter.milestone]
  )

  const { run: fetchChartData } = useRequest(
    (data) => {
      return getAllCostReport({
        ...data,
      })
    },
    {
      manual: true,
      refreshDeps: [filter.milestone],
      onBefore: () => setLoading(true),
      onFinally: () => setLoading(false),
      onSuccess: (res) => {
        const { date = '', projectLifeCostDtoList: chartList = [], chooseFields } = res?.data || {}
        if (!location.state) {
          const obj = chooseFields.reduce((a, b) => {
            const { projectName, config, saleSite, firstLevelSubject } = b
            const arr = [projectName, config, saleSite, firstLevelSubject].filter(Boolean)
            const key = arr.join('-')
            a[key] = b
            return a
          }, {})
          setSelectObj(obj)
        }
        !!date && setUpdateDate(date)
        let newData = chartList?.reduce((acc, item) => {
          const newItem = item.cost === 0 ? { ...item, cost: null } : item
          acc.push(newItem)
          return acc
        }, [])

        shouldHiddenColKey.forEach((key) => {
          newData = newData.filter(
            (item) => item.projectStepType !== Number(MILESTONE_MAP[key]) - 1
          )
        })
        const filteredCosts = [] as number[]
        newData.forEach((item) => {
          if (item.cost && item.cost > 0) {
            filteredCosts.push(item.cost)
          }
        })
        let maxCost = Math.max(...filteredCosts)
        let minCost = filteredCosts.length > 0 ? Math.min(...filteredCosts) : 0
        let costGrid = maxCost - minCost > 100 ? 50 : 10
        if (filteredCosts.length === 1) {
          minCost = 0
          costGrid = 50
          maxCost = Math.ceil(maxCost / costGrid) * costGrid
        } else if (maxCost === minCost) {
          minCost = 0
        } else {
          maxCost = Math.ceil(maxCost / costGrid) * costGrid * 1.1
          minCost = Math.floor(minCost / costGrid) * costGrid * 0.9
        }
        setMaxCost(maxCost)
        setMinCost(minCost)
        const sArr =
          (filter?.milestone as string[])?.map((item) => Number(MILESTONE_MAP[item]) - 1) || []
        const { newArr, newP3, newP4 } = sortChartData(newData, sArr)

        setChartData(newArr)
        const singleLine = [...newArr]
        const finalParts: Record<string, number> =
          singleLine.length === 0
            ? {}
            : {
                all: new Set(newArr.map((item) => item.fcstMonth)).size - 1,
                p1: getLen(newArr, 0),
                p2: getLen(newArr, 1),
                p3: newP3,
                p4: newP4,
              }
        const showArr = (filter.milestone as string[]).map((i) => `p${MILESTONE_MAP[i]}`)
        let hasP3 = false
        let hasP4 = false
        ;(filter.milestone as string[]).forEach((item) => {
          hasP3 = hasP3 || item === 'afterMassActual'
          hasP4 = hasP4 || item === 'afterMassEstimate'
        })

        const filterParts = Object.keys(finalParts).reduce((a, b) => {
          if (showArr.includes(b) || b === 'all') {
            a[b] = finalParts[b]
          }
          return a
        }, {}) as AnyType

        if (!hasP3) {
          filterParts.p2 = finalParts.p2 + finalParts.p3
        }
        if (filterParts?.all === filterParts?.p4) {
          delete filterParts.p3
        }

        if (filterParts?.all === filterParts?.p3) {
          filterParts.p3 = filterParts.p3 - 1
        }

        if (filterParts?.all < filterParts?.p3 + filterParts?.p4) {
          filterParts.p3 = filterParts.p3 - 1
        }

        setParts(filterParts)

        setChartColor(generateDynamicColorMap(filterParts, filter?.milestone || []))
        const max = (Math.ceil(Math.max(...chartList.map((item) => item.cost))) + 1).toString()
          .length
        setMaxDigit(max)
      },
    }
  )

  useEffect(() => {
    filterRef.current = filter
  }, [filter])

  useEffect(() => {
    if (!location.state) {
      if (Object.keys(filter).length !== 0 && fetchChartSwitch?.current) {
        const finalFilter = { ...filterRef.current, ...changeFilter }
        fetchChartData(finalFilter)
        fetchChartSwitch.current = false
      }
    } else {
      const state = location.state as Record<string, string | string[]>
      if (Object.keys(filter).length !== 0 && state?.selectedObj) {
        const { selectedObj } = state

        const arr = Object.values(selectedObj) || []

        if (arr.length > 0) {
          fetchChartData({ ...filterRef.current, chooseDimensions: Object.values(selectedObj) })
        }
        location.state = null
      }
    }
  }, [changeFilter, fetchChartData, fetchChartSwitch, filter, location])

  const cardSubTitle = useMemo(() => {
    const values = Object.values(selectObj) || []
    const commonKey = Object.keys(selectObj)?.[0]?.split('-').slice(0, 3).join('-')
    const subjects = Object.values(selectObj).map((item) => item.firstLevelSubject)
    const filterKeys = Array.from(new Set(subjects))
    const arr = Object.values(values?.[0] || {}) || []
    let info = ''
    if (arr?.length) {
      info = `维度说明：${commonKey}-${filterKeys.join('、')}`
    } else {
      info = '维度说明：-'
    }
    return info
  }, [selectObj])

  return (
    <Card
      title={intl.get('项目全生命周期成本报表')}
      bordered={false}
      size="md"
      className="all-life__card"
      extra={
        !!updateDate && (
          <span>
            {intl.get('更新日期：')}
            {updateDate}
          </span>
        )
      }
      subtitle={cardSubTitle}
    >
      <Loading visible={loading} delay={300}>
        {chartData?.length !== 0 ? (
          <CostLine
            data={chartData}
            x="fcstMonth"
            y="cost"
            diffKey="recordVersion"
            chartColor={chartColor}
            maxYValue={maxCost}
            minYValue={minCost}
          />
        ) : (
          <EmptyState
            className="empty-state"
            title={intl.get('报表暂无数据')}
            indicator={EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL}
          />
        )}
        {Object.keys(parts).length !== 0 && <PromptBar parts={parts} />}
      </Loading>
    </Card>
  )
}

export default AllLifeChart
