import dayjs from 'dayjs'
import { isChineseEnv } from '@/utils/language'

export const DEFAULT_MONTH_RANGE = [
  dayjs().format('YYYYMM'),
  dayjs().add(2, 'year').format('YYYYMM'),
]

export const FORM_ITEM_CONFIG = {
  style: {
    width: '100%',
    minWidth: 196,
  },
  height: 260,
}

export const FORM_INIT_VALUES = {
  // 表单初始条件
  projectNames: ['all'],
  configs: ['all'],
  range: DEFAULT_MONTH_RANGE,
  recordVersions: [],
  saleSites: ['all'],
  firstLevelSubjects: ['材料费（净值）'],
  milestone: ['beforeProject', 'beforeMass', 'afterMassEstimate'],
  bizNames: ['手机'],
}

export const COLOR_MAP = {
  1: 'rgba(250, 176, 7, 0.1)',
  2: 'rgba(151, 114, 251, 0.1)',
  3: 'rgba(254, 121, 64, 0.1)',
  4: 'rgba(35, 127, 250, 0.1)',
}

export const TEXT_COLOR_MAP = {
  1: 'rgba(250, 176, 7, 1)',
  2: 'rgba(151, 114, 251, 1)',
  3: 'rgba(254, 121, 64, 1)',
  4: 'rgba(35, 127, 250, 1)',
}

export const PROJECT_COST_PATHNAME = '/metearial-cost/project'
export const NEW_PROJECT_COST_PATHNAME = '/metearial-cost/project-data'

export const COLUMN_WIDTH_OBJ = {
  projectName: 80, // 项目
  saleSite: 100, // 区域
  config: 100, // 配置
  milestonename: 90, // 项目阶段
  milestoneType: 110,
  projectSeries: 130, // 系列
  firstLevelSubject: 150, // 一级科目
  concept: 80,
  Spec: 80,
  pile: 80,
  Charter: 80,
  SDCP: 80,
  ADCP: 80,
}

export const MILESTONE_SELECT_DATA = [
  { value: 'beforeProject', name: '立项前' },
  { value: 'beforeMass', name: '量产前' },
  { value: 'afterMassActual', name: '量产后实结' },
  { value: 'afterMassEstimate', name: '量产后预估' },
] as { value: string; name: string }[]

export const ALL_MILESTONES = MILESTONE_SELECT_DATA.map((item) => item.value)

export function isAfterThanCurrent(dateString: string): boolean {
  // 解析日期字符串
  const inputDate = dayjs(dateString, 'YYYYMM')
  // 获取当前年月
  const currentDate = dayjs().startOf('month')
  // 比较日期
  return inputDate.isAfter(currentDate)
}

export const KEY_OBJ = {
  key: 'code',
  value: 'name',
}

export const ALL_ITEM = {
  value: 'all',
  name: isChineseEnv ? '全部' : 'All',
}

// 一小时缓存
export const ONE_HOUR_EXPIRES = 1 * 60 * 60 * 1000

export const ICON_COLOR = '#5F6A7A'

export const getChartRegion = (data, x) => {
  const version = data?.[0]?.recordVersion
  const singleLine = data.filter((item) => item.recordVersion === version) || []
  const addedTypes = new Set()
  return singleLine.reduce((a, b, index, array) => {
    if (b.cost === 0) return a
    if (!addedTypes.has(b.projectStepType)) {
      a.push(b[x])
      addedTypes.add(b.projectStepType)
    }
    if (index === array.length - 1 && b.cost !== 0) {
      a.push(b[x])
    }
    return a
  }, [])
}

export const MILESTONE_MAP = {
  beforeProject: '1',
  beforeMass: '2',
  afterMassActual: '3',
  afterMassEstimate: '4',
}

export const P_MILESTONE_MAP = {
  p1: 'beforeProject',
  p2: 'beforeMass',
  p3: 'afterMassActual',
  p4: 'afterMassEstimate',
}

export const getCeilProps = (cellCombine) => (_value, record) => {
  const { projectName, config, saleSite, firstLevelSubject } = record
  const key = `${projectName}-${config}-${saleSite}-${firstLevelSubject}`
  return { colSpan: 1, rowSpan: cellCombine[key] }
}

export const generateDynamicColorMap = (parts, milestone) => {
  const hasValue = Object.entries(parts).reduce((a: string[], [k, v]) => {
    if ((v as number) > 0 && k.startsWith('p')) {
      a.push(P_MILESTONE_MAP[k])
    }
    return a
  }, [])

  const arr = ['beforeProject', 'beforeMass', 'afterMassActual', 'afterMassEstimate']
    .filter((item) => milestone.includes(item) && hasValue.includes(item))
    .map((item) => COLOR_MAP[MILESTONE_MAP[item]])

  return arr.reduce((a, b, index) => {
    a[index + 1] = b
    return a
  }, {})
}

export const getLen = (arr, type) => {
  const first = arr?.[0]?.projectStepType === type

  const targetArr = [] as string[]
  arr.forEach((item) => {
    if (item.projectStepType === type) {
      targetArr.push(item.fcstMonth as string)
    }
  })

  // 使用 Set 来去重
  const uniqueMonths = new Set(targetArr)

  // 获取并集的长度
  const unionLength = uniqueMonths.size
  const res = unionLength > 0 ? (first ? unionLength - 1 : unionLength) : 0
  return res
}

export const findMonth = (arr, type) => {
  return (arr || []).find((item) => item.projectStepType === type)?.fcstMonth
}

export function filterData(data) {
  // 根据 recordVersion 分组数据
  const groupedData = data.reduce((acc, item) => {
    if (!acc[item.recordVersion]) {
      acc[item.recordVersion] = []
    }
    acc[item.recordVersion].push(item)
    return acc
  }, {})

  // 过滤每组数据
  const filteredData = Object.values(groupedData).map((group: AnyType) => {
    if (group.length <= 2) return group // 如果组长度小于等于2，不需要过滤

    const filteredGroup = [group[0]] // 保留第一个元素

    for (let i = 1; i < group.length - 1; i++) {
      if (group[i].cost !== null && group[i].cost !== undefined) {
        filteredGroup.push(group[i])
      }
    }

    filteredGroup.push(group[group.length - 1]) // 保留最后一个元素

    return filteredGroup
  })

  // 将过滤后的数据合并成一个数组
  return filteredData.flat()
}

export const sortChartData = (arr, milestone) => {
  const allMonthsSet = new Set<string>()
  let newArr = [] as AnyType
  let hasP3 = false
  let hasP4 = false
  const targetRecordVersion = arr?.[0]?.recordVersion
  const targetArr = targetRecordVersion?.split('_') || []

  const [projectName, config, saleSite, firstLevelSubject] = targetArr

  arr.forEach((item) => {
    const x = item.fcstMonth
    // 找到数据中所有的月份横坐标
    if (!allMonthsSet.has(x) && !isNaN(x)) {
      allMonthsSet.add(x)
    }
    // 找到符合项目阶段下拉框的所有数据
    if (milestone.includes(item.projectStepType)) {
      newArr.push(item)
    }
    // 判断是否有项目阶段3和4的数据
    hasP3 = hasP3 || item.projectStepType === 2
    hasP4 = hasP4 || item.projectStepType === 3
  })

  const allMonths = Array.from(allMonthsSet)

  const addPoints = allMonths.map((i) => ({
    firstLevelSubject,
    saleSite,
    config,
    projectName,
    fcstMonth: i,
    recordVersion: targetRecordVersion,
    projectStepType: 2,
  }))
  // newArr = filterData(newArr)
  const newProjectIndex = newArr.findLastIndex((item) => item.recordVersion.includes('新项目'))
  if (newProjectIndex !== -1) {
    newArr.splice(newProjectIndex + 1, 0, ...addPoints)
  } else {
    newArr = [...newArr, ...addPoints]
  }
  const currentMonth = dayjs().format('YYYYMM')
  const len = allMonths.length
  // 当前年月开始处理为预估数据
  const index = allMonths.sort((a, b) => Number(a) - Number(b)).indexOf(currentMonth)
  let newP3, newP4
  if (index !== -1) {
    // 如果有当前年月的数据，将当前年月之前的数据处理为实结数据
    newP3 = index + 1
    newP4 = len - index - 1
  } else {
    newP3 = hasP4 ? 0 : allMonths.length
    newP4 = hasP4 ? allMonths.length : 0
  }
  return { newArr: [...newArr, ...addPoints], newP3, newP4 }
}
