import intl from 'react-intl-universal'
import React, { useEffect } from 'react'
import './style.scss'
import { useSafeState } from 'ahooks'
import { useNavigate } from 'react-router-dom'
import EmptyState, { EMPTY_STATE_IMAGE_NO_NETWORK_COLOURFUL } from '@hi-ui/empty-state'

export const OffLine = () => {
  const [isOnline, setNetwork] = useSafeState(window.navigator.onLine)
  const navigate = useNavigate()

  useEffect(() => {
    const updateNetwork = () => {
      setNetwork(window.navigator.onLine)
    }
    window.addEventListener('online', updateNetwork)
    return () => {
      window.removeEventListener('online', updateNetwork)
    }
  }, [setNetwork])

  useEffect(() => {
    if (isOnline) navigate(`${new URLSearchParams(location.search).get('from')}`)
  }, [isOnline, navigate])

  return (
    <div className="exception_wrapper">
      <EmptyState
        size="xxl"
        title={intl.get('抱歉，网络连接中断，请检查网络后尝试')}
        indicator={EMPTY_STATE_IMAGE_NO_NETWORK_COLOURFUL}
      ></EmptyState>
    </div>
  )
}
