import intl from 'react-intl-universal'
import React from 'react'
import './style.scss'
import Button from '@hi-ui/button'
import EmptyState, { EMPTY_STATE_IMAGE_404_COLOURFUL } from '@hi-ui/empty-state'

export const Exception404 = () => {
  return (
    <div className="exception_wrapper">
      <EmptyState
        size="xxl"
        title={intl.get('抱歉，您请求的资源不存在')}
        indicator={EMPTY_STATE_IMAGE_404_COLOURFUL}
      >
        <Button onClick={() => (window.location.href = '/')} type="primary" key="back">
          {intl.get('返回首页')}
        </Button>
      </EmptyState>
    </div>
  )
}
