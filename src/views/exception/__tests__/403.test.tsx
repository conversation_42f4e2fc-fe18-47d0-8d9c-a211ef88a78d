import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { Exception403 } from '../403'
import { GSC_HOME_URL_ARRAY } from '@/constants'

// Mock react-intl-universal
vi.mock('react-intl-universal', () => ({
  default: {
    get: (key: string) => {
      if (key === '抱歉，您没有当前模块的访问权限') {
        return '抱歉，您没有当前模块的访问权限'
      }
      if (key === '返回首页') {
        return '返回首页'
      }
      return key
    },
  },
}))

describe('Exception403 Component', () => {
  beforeEach(() => {
    // 重置所有 mock，确保测试的独立性
    vi.resetAllMocks()

    // Mock window.location.href
    // Vitest 默认在 Node.js 环境中运行测试，所以 window 对象不存在
    // 我们需要用 vi.stubGlobal 来模拟它
    // @ts-expect-error Vitest environment might not have window.location fully defined
    delete window.location
    window.location = { href: '' } as Location // Assigning to window.location for test purposes
  })

  it('should render correctly with title and button', () => {
    render(<Exception403 />)

    // 检查标题是否存在
    expect(screen.getByText('抱歉，您没有当前模块的访问权限')).toBeInTheDocument()

    // 检查按钮是否存在
    const backButton = screen.getByRole('button', { name: '返回首页' })
    expect(backButton).toBeInTheDocument()
  })

  it('should redirect to the homepage when the button is clicked', () => {
    render(<Exception403 />)

    const backButton = screen.getByRole('button', { name: '返回首页' })
    fireEvent.click(backButton)

    // 检查 window.location.href 是否被正确设置
    expect(window.location.href).toBe(GSC_HOME_URL_ARRAY[0])
  })

  it('should apply the correct className to the wrapper div', () => {
    const { container } = render(<Exception403 />)
    const wrapperDiv = container.firstChild
    expect(wrapperDiv).toHaveClass('exception_wrapper')
  })
})
