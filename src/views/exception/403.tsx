import intl from 'react-intl-universal'
import React from 'react'
import './style.scss'
import Button from '@hi-ui/button'
import { GSC_HOME_URL_ARRAY } from '@/constants'
import EmptyState, { EMPTY_STATE_IMAGE_NO_MESSAGE_COLOURFUL } from '@hi-ui/empty-state'

export const Exception403 = () => {
  return (
    <div className="exception_wrapper">
      <EmptyState
        size="xxl"
        title={intl.get('抱歉，您没有当前模块的访问权限')}
        indicator={EMPTY_STATE_IMAGE_NO_MESSAGE_COLOURFUL}
      >
        <Button
          onClick={() => (window.location.href = GSC_HOME_URL_ARRAY[0])}
          type="primary"
          key="back"
        >
          {intl.get('返回首页')}
        </Button>
      </EmptyState>
    </div>
  )
}
