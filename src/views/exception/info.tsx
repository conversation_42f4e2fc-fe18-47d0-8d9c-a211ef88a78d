import React, { memo } from 'react'
import intl from 'react-intl-universal'
import EmptyState, { EMPTY_STATE_IMAGE_NO_MESSAGE_COLOURFUL } from '@hi-ui/empty-state'
import Button from '@hi-ui/button'
import './style.scss'

interface IProps {
  title: string
  indicator?: AnyType
  method?: AnyType
  buttonName?: string
}

const Info = memo<IProps>((props) => {
  const { title, indicator, method, buttonName } = props

  return (
    <div className="exception_wrapper">
      <EmptyState
        size="xxl"
        title={title}
        indicator={indicator || EMPTY_STATE_IMAGE_NO_MESSAGE_COLOURFUL}
      >
        <Button onClick={() => method?.()} type="primary" key="back">
          {buttonName || intl.get('返回首页')}
        </Button>
      </EmptyState>
    </div>
  )
})
Info.displayName = 'Info'
export default Info
