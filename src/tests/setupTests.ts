/* eslint-disable @typescript-eslint/no-empty-function */
import '@testing-library/jest-dom'
import { afterEach, vi } from 'vitest'
import { cleanup } from '@testing-library/react'

// Mock react-intl-universal globally
const mockReactIntlUniversal = {
  get: (key: string) => key,
  getHTML: (key: string) => key,
  init: () => {},
  load: () => {},
  options: {},
  getInitOptions: () => ({}),
  formatMessage: (key: string) => key,
  formatHTMLMessage: (key: string) => key,
}

// Set up the mock
;(globalThis as AnyType)['react-intl-universal'] = mockReactIntlUniversal

// Mock for vitest
vi.mock('react-intl-universal', () => ({
  default: mockReactIntlUniversal,
}))

// 全局模拟 IntersectionObserver
const mockIntersectionObserver = vi.fn().mockImplementation((callback: AnyType) => ({
  observe: vi.fn((element: AnyType) => {
    // 立即触发回调，模拟元素可见
    setTimeout(() => {
      if (callback) {
        // eslint-disable-next-line n/no-callback-literal
        callback([
          {
            target: element,
            isIntersecting: true,
            intersectionRatio: 1,
          },
        ])
      }
    }, 0)
  }),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// 设置全局模拟
Object.defineProperty(window, 'IntersectionObserver', {
  writable: true,
  configurable: true,
  value: mockIntersectionObserver,
})

Object.defineProperty(global, 'IntersectionObserver', {
  writable: true,
  configurable: true,
  value: mockIntersectionObserver,
})

// 模拟 ResizeObserver
class MockResizeObserver {
  callback: AnyType
  constructor(callback: AnyType) {
    this.callback = callback
  }

  observe() {
    // 模拟 observe 方法
  }

  unobserve() {
    // 模拟 unobserve 方法
  }

  disconnect() {
    // 模拟 disconnect 方法
  }
}

Object.defineProperty(window, 'ResizeObserver', {
  writable: true,
  configurable: true,
  value: MockResizeObserver,
})

Object.defineProperty(global, 'ResizeObserver', {
  writable: true,
  configurable: true,
  value: MockResizeObserver,
})

// 在每个测试用例运行后，清理 @testing-library/react 渲染的组件
afterEach(() => {
  cleanup()
})
