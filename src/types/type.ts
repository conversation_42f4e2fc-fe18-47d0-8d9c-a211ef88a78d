export type EmptyFunctionType = () => void

export type ResponseType = {
  msg?: string
  message?: string
  code?: number
  traceId?: string
  data?: object
}

export type ParamOneFunctionType = (param) => void

export enum WorkbenchType {
  INTERNAL = 1,
  EXTERNAL = 2,
}

export interface CrossTableColumn {
  code: string | number
  name: string
  hidden?: boolean
  width?: number
  align?: string
  expression?: string
  type?: string
  lock?: boolean
  render?: (a?: Record<string, AnyType>) => AnyType
}

export type SetType<T> = React.Dispatch<React.SetStateAction<T>>
