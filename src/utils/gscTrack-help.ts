import { Track, ElemClickExtraParams } from '@mi/scm-track'
import { isEmpty, isString } from 'lodash'
import config from '../config'
import { isProd } from '../constants'
class BusinessTrack extends Track {
  tempPath // 需指定页面url使用

  trackInit() {
    if (!isProd) {
      this.setReportEnv('test')
    }
    this.init({
      appId: config.onetrackAppId,
      projectId: '970',
      scope: 'gsc-web',
      tips: {
        pageView: '970.12.0.1.33562',
        pageElemClick: '970.12.0.1.33563',
      },
      extraInitOpts: {
        transport: 'xhr',
      },
    })
  }

  generateUnifiedPagePath() {
    if (this.tempPath) {
      return this.tempPath
    }
    const { pathname, search } = window.location
    return pathname + search
  }

  setTempPathPageView(params) {
    const { fullPath, pageType } = params
    this.tempPath = fullPath ?? null
    this.pageView(pageType)
  }
}
// 使用全局单例
export const gscTrack = new BusinessTrack()

interface SearchTrackConfig {
  filters: Record<string, number | string[]>
  pageType?: string
  queryCodeName: Record<string, string>
  params?: ElemClickExtraParams
}

export const gscSearchTrack = ({
  filters,
  pageType = '01',
  queryCodeName,
  params,
}: SearchTrackConfig) => {
  const queryCodeNames: string[] = []
  Object.keys(filters).forEach((item: string) => {
    if (
      (Array.isArray(filters[item]) && !isEmpty(filters[item])) ||
      (isString(filters[item]) && filters[item])
    ) {
      queryCodeNames.push(queryCodeName[item])
    }
  })
  gscTrack.listFilter(queryCodeNames, pageType, params)
}
