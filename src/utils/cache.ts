import * as Sentry from '@sentry/react'

export const getObjectStorage = (key: string, defaultParams = '{}') => {
  try {
    return JSON.parse(localStorage.getItem(key) || defaultParams)
  } catch (error) {
    Sentry.captureException(error)
    return {}
  }
}

export const getSessionStorage = (key: string) => {
  try {
    return JSON.parse(sessionStorage.getItem(key) || '{}')
  } catch (error) {
    Sentry.captureException(error)
    return {}
  }
}

export const setStorage = (key: string, value) =>
  localStorage.setItem(key, value instanceof Object ? JSON.stringify(value) : value)

export const getStringStorage = (key: string) => {
  try {
    return localStorage.getItem(key) || ''
  } catch (error) {
    return ''
  }
}

export const getObjectSessionStorage = (key: string) => {
  try {
    return JSON.parse(sessionStorage.getItem(key) ?? '{}')
  } catch (error) {
    return {}
  }
}

export const setObjectSessionStorage = (key: string, value) => {
  sessionStorage.setItem(key, value instanceof Object ? JSON.stringify(value) : value)
}

// 判断两个对象的类型是否相同
export const isEqual = (obj1, obj2, ignoreKeys: string[] = []) => {
  if (obj1 === obj2) {
    return true
  }
  if (typeof obj1 !== typeof obj2) {
    return false
  }
  if (typeof obj1 !== 'object') {
    return obj1 === obj2
  }

  const obj1Keys = obj1 ? Object.keys(obj1) : []
  const obj2Keys = obj2 ? Object.keys(obj2) : []

  if (obj1Keys.length !== obj2Keys.length) {
    return false
  }

  for (const key of obj1Keys) {
    if (!ignoreKeys.includes(key) && !isEqual(obj1[key], obj2[key], ignoreKeys)) {
      return false
    }
  }
  // 如果所有属性和值都相等，则两个对象相等
  return true
}
interface IProps {
  key: string
  api: AnyType
  apiParams: AnyType
  customReload?: AnyType
  ignoreProperties?: string[]
}
export const implAsyncCache = async (params: IProps) => {
  const { key, api, apiParams, customReload, ignoreProperties = [] } = params
  const cacheRes = JSON.parse(localStorage.getItem(key) || '{}')
  const res = cacheRes.data ? cacheRes : await api(apiParams)
  if (!cacheRes?.data) {
    // 如果 localStorage 中没有缓存，就将接口返回的数据存入 localStorage
    localStorage.setItem(key, JSON.stringify({ data: res?.data }))
  } else if (cacheRes?.data) {
    // 如果 localStorage 中有缓存，需要异步地将接口最新数据与 localStorage 中的数据进行比较
    setTimeout(() => {
      api(apiParams).then((newestRes) => {
        const newestCacheRes = JSON.parse(localStorage.getItem(key) || '{}')
        const shouldReload = !isEqual(newestRes?.data, newestCacheRes?.data, ignoreProperties)
        if (shouldReload) {
          // 如果不一致，更新缓存，刷新页面
          localStorage.setItem(key, JSON.stringify({ data: newestRes?.data }))
          if (customReload) {
            customReload(newestRes)
          } else {
            location.reload()
          }
        }
      })
    }, 0)
  }
  return res
}
