/* eslint-disable @typescript-eslint/no-explicit-any */
import dayjs from 'dayjs'

export const formatTimeObj = (newValues: any) => {
  if (!!newValues?.startTime && newValues?.startTime?.length !== 0) {
    newValues.endTime = dayjs(newValues?.startTime?.[1]).format('YYYY-MM-DD')
    newValues.startTime = dayjs(newValues?.startTime?.[0]).format('YYYY-MM-DD')
  } else {
    newValues.endTime = ''
    newValues.startTime = ''
  }
}

export const formatStartObj = (startTime: any, queryObj: any) => {
  if (startTime?.length !== 0) {
    queryObj.endTime = dayjs(startTime?.[1]).format('YYYY-MM-DD')
    queryObj.startTime = dayjs(startTime?.[0]).format('YYYY-MM-DD')
  } else {
    queryObj.endTime = ''
    queryObj.startTime = ''
  }
}
