import { Embed, models } from 'powerbi-client'

export let pbiReport = null as null | Embed

export const pbiEmbed = (dom: HTMLElement, config: models.IReportEmbedConfiguration) => {
  const { accessToken, embedUrl = '' } = config || {}
  if (window.powerbi && dom && accessToken) {
    window.__POWER_BI_CONFIG__ = { accessToken, embedUrl }
    pbiReport = window.powerbi.embed(dom, {
      type: 'report',
      tokenType: models.TokenType.Embed,
      // 解决层级问题
      settings: {
        background: models.BackgroundType.Transparent,
      },
      ...config,
    })
  }
  return pbiReport
}

export const pbiBootstrap = (dom: HTMLElement) => {
  if (window.powerbi && dom && !dom.childNodes.length) {
    pbiReport = window.powerbi.bootstrap(dom, {
      type: 'report',
      hostname: 'https://app.powerbi.cn/',
    })
  }
}

export const pbiReset = (dom: HTMLElement) => {
  if (window.powerbi && dom && dom.childNodes.length) {
    window.powerbi.reset(dom)
  }
}
