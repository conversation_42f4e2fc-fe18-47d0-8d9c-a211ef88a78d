// fork from hiui request
// https://github.com/XiaoMi/hiui/blob/master/packages/utils/request/src/download.ts
import type { AxiosRequestConfig, AxiosResponse } from 'axios'
import { http } from './request'

type HiDownloadOptions = AxiosRequestConfig & {
  filename?: string
  downloadSuccess?: <T>(response: AxiosResponse<T>) => void
  downloadFail?: <T>(response: AxiosResponse<T>) => void
  responseType?: 'arraybuffer' | 'blob'
}

export function download(options: HiDownloadOptions) {
  const { filename, downloadSuccess, downloadFail, responseType = 'blob', ...restOptions } = options

  if (responseType !== 'blob' && responseType !== 'arraybuffer') {
    console.warn('responseType must be either "blob" or "arraybuffer", falling back to "blob"')
    Object.assign(restOptions, { responseType: 'blob' })
  } else {
    Object.assign(restOptions, { responseType })
  }

  return http(restOptions).then(
    (res) => {
      // 先检查返回的数据是否为JSON
      return checkResponseData(res).then((checkResult) => {
        if (checkResult.isError && checkResult.res.code !== 0) {
          return Promise.reject(checkResult.res)
        }

        // 获取下载后文件名
        const downloadFilename = filename || getDownloadFilename(res)

        // 创建下载的链接
        const blob = new window.Blob([res.data])
        const href = window.URL.createObjectURL(blob)

        // 使用 a 标签创建下载
        const downloadElement = document.createElement('a')
        downloadElement.download = downloadFilename
        downloadElement.href = href
        downloadElement.style.display = 'none'

        // 开始链接下载
        document.body.appendChild(downloadElement)
        downloadElement.click()

        // 资源释放：DOM 元素 和 blob 对象
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)

        // 成功下载 hook
        downloadSuccess?.(res)

        return res
      })
    },
    (error) => {
      // 失败下载 hook
      downloadFail?.(error)
      return Promise.reject(error)
    }
  )
}

// 检查响应数据是否为JSON，并验证code字段
async function checkResponseData<T>(
  response: AxiosResponse<T>
): Promise<{ isError: boolean; res: AnyType }> {
  const result = { isError: false, res: {} }

  if (response.data instanceof Blob || response.data instanceof ArrayBuffer) {
    try {
      // 获取文本内容
      let text = ''
      if (response.data instanceof Blob) {
        const firstChunk = await response.data.slice(0, 100).text()
        const trimmed = firstChunk.trim()

        // 只有看起来像JSON才继续处理
        if (trimmed.startsWith('{') || trimmed.startsWith('[')) {
          text = await response.data.text()
        } else {
          // 不像JSON格式，直接返回
          return result
        }
      } else {
        // 对于ArrayBuffer类型，直接解码
        const decoder = new TextDecoder('utf-8')
        text = decoder.decode(response.data)
      }

      try {
        const json = JSON.parse(text)
        if (json.code !== undefined && json.code !== 0) {
          result.isError = true
          result.res = json
        } else {
          result.isError = false
          result.res = json
        }
      } catch (e) {
        // 解析JSON失败，认为是二进制文件，不是错误
      }
    } catch (e) {
      // 读取或转换失败，认为是二进制文件
    }
  }

  return result
}

// 如果没有自定义文件名，则根据响应头部信息生成
function getDownloadFilename<T>(response: AxiosResponse<T>) {
  const contentDisposition = response?.headers?.['content-disposition']
  const serverFilename = decodeURI(
    contentDisposition?.split(';')[1]?.split('filename=')[1] || '未命名'
  )
  return serverFilename
}

export type downloadType = typeof download
export type downloadReturnType = ReturnType<downloadType>
