/**
 * 处理一个 Promise，并返回一个包含结果或错误的对象。
 * @template T - Promise 成功时的返回值类型。
 * @template U - Promise 失败时的错误类型。
 * @param {Promise<T>} promise - 要处理的 Promise。
 * @param {object} [errorExt] - 附加到错误对象的可选扩展属性。
 * @returns {Promise<[U, undefined] | [null, T]>} - 返回一个 Promise，解析为一个包含两个元素的数组：
 *  - 第一个元素是错误对象（如果存在）或 `null`。
 *  - 第二个元素是成功的数据或 `undefined`。
 */
export function to<T, U extends Error>(
  promise: Promise<T>,
  errorExt?: object
): Promise<[U, undefined] | [null, T]> {
  return promise
    .then<[null, T]>((data: T) => [null, data])
    .catch<[U, undefined]>((err: U) => {
      if (errorExt) {
        const parsedError = Object.assign({}, errorExt, err)

        return [parsedError, undefined]
      }

      return [err, undefined]
    })
}
