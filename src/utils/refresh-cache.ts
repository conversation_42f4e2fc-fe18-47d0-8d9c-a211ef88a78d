import { getDefaultViews } from '@/api/gsc/form-view'
import { FORM_VIEWS_CACHE } from '@/constants'
import message from '@hi-ui/message'
import intl from 'react-intl-universal'

export const refreshViewCache = () => {
  const getDefault = () => {
    getDefaultViews()
      .then((res) => {
        localStorage.setItem(FORM_VIEWS_CACHE, JSON.stringify(res?.data || []))
      })
      .catch((err) => {
        message.open({ title: err.message || intl.get('获取视图信息失败'), type: 'error' })
      })
  }
  getDefault()
}
