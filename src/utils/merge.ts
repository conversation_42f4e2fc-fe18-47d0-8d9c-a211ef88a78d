import { mergeWith } from 'lodash'

// export function mergeProps<T extends AnyObject>(prev: T, next?: T) {
//   return mergeWith({}, prev, next, (a, b) => {
//     return Array.isArray(b) ? b : undefined
//   }) as T
// }

export function mergeProps<T extends AnyObject>(...props: (T | undefined)[]) {
  return mergeWith({}, ...props, (a: AnyType, b: AnyType) => {
    return Array.isArray(b) ? b : undefined
  }) as T
}
