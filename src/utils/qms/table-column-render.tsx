import { getTextWidth } from '@/utils/get-text-width'
import React from 'react'
import Tooltip from '@hi-ui/tooltip'

export const tableColumnRender = (text: string, textWidth: number) => {
  return text === '0' || !text || text === 'null' ? (
    '-'
  ) : getTextWidth(text, 14) > textWidth - 36 ? (
    <Tooltip
      title={
        <div style={{ maxWidth: 550, wordWrap: 'break-word', wordBreak: 'break-all' }}>{text}</div>
      }
    >
      <span className="truncate block">{text}</span>
    </Tooltip>
  ) : (
    <span className="block">{text}</span>
  )
}
