/**
 * 数据按照值倒序排序
 */
// import { tableHtml } from './tableHtml'
export const compareDesc = (property) => {
  return function (a, b) {
    const value1 = a[property]
    const value2 = b[property]
    return value2 - value1
  }
}

/**
 * 数据按照值顺序排序
 */
export const compareAsc = (property) => {
  return function (a, b) {
    const value1 = a[property]
    const value2 = b[property]
    return value1 - value2
  }
}

/**
 * 从数据中唯一并集数组
 * @param data
 * @param dataDescribe
 * @param isSort
 * @param isMain
 * @param isString
 */
export function getUniqueIndex(
  data,
  dataDescribe = 'index',
  isSort = true,
  isMain = false,
  isString = false
) {
  // 初始化一个set用来去重
  let formatIndex = []
  if (isMain) {
    data.sort(function (a, b) {
      return b.list.length - a.list.length
    })
  }
  // 从列表数据中获取并集index
  data.forEach((item) => {
    const itemIndexArr = item.list.map((faultItem) => {
      return faultItem[dataDescribe]
    })
    // 获取每个item列表中和最终的index之间的并集
    formatIndex = formatIndex.concat(itemIndexArr.filter((v: never) => !formatIndex.includes(v)))
  })
  if (isSort) {
    if (isString) {
      return formatIndex.sort()
    } else {
      return formatIndex.sort(function (a, b) {
        return a - b
      })
    }
  } else {
    return formatIndex
  }
}

/**
 * 获取折线图数据
 */
export function getIndexArray(item, authType: number, xAxis, dataDesc = 'index') {
  // 读取数据，获取其在index列表中的位置
  const res = []
  item.list.forEach((elem) => {
    const resIndex: string = xAxis.indexOf(elem[dataDesc])
    switch (authType) {
      case 1:
        // 能看所有
        res[resIndex] = { value: parseFloat(elem.rate).toFixed(4), id: item.id }
        break
      case 2:
        // 能看分子
        res[resIndex] = { value: elem.fz_num, id: item.id }
        break
      case 3:
        // 能看故障率
        res[resIndex] = { value: elem.rate.toFixed(4), id: item.id }
        break
    }
  })

  return res
}

/**
 * 格式化折线图鼠标放上去显示数据
 */
export const formatLineHtml = (params, authType, originData, order = 1) => {
  const paramsList = params
  let html = '' + params[0].name + '<br/>'
  if (order === 1) {
    paramsList.sort(compareDesc('value')) // 根据值倒序排序数组
  } else {
    paramsList.sort(compareAsc('value')) // 根据值顺序排序数组
  }

  paramsList.forEach((paramsItem) => {
    // 若item的数据为空，表示该列没有数据，直接返回
    if (!paramsItem.data) {
      return
    }
    // //使用当前数据的index日期过滤原数组，获取数据信息
    const itemData = originData[paramsItem.seriesIndex].list.filter((item) => {
      return item.index === paramsItem.axisValue
    })[0]
    switch (authType) {
      case 1:
        // 能看所有
        html +=
          paramsItem.marker +
          paramsItem.seriesName +
          '：' +
          itemData.fz_num +
          '/' +
          itemData.fm_num +
          '<br/>'
        break
      case 2:
        // 只能看分子
        html += paramsItem.marker + paramsItem.seriesName + '：' + itemData.fz_num + '<br/>'
        break
      case 3:
        // 能看故障率
        html +=
          paramsItem.marker + paramsItem.seriesName + '：' + itemData.rate.toFixed(4) + '<br/>'
        break
    }
  })
  return html
}

/**
 * 格式化
 */
export function formatCateInfo(value) {
  return {
    // 分别将我们查询出来的值做出改变他的key
    label: value.name,
    value: value.id,
    isLeaf: value.leaf,
    id: value.id,
    parentId: value.parentId,
    data: value.data,
  }
}

/**
 * 获取该机型下对应的列表,cateIdList为原列表，ids为上一级列表
 */
export function getCateListById(cateIdList, ids) {
  if (!ids || !cateIdList) {
    return []
  }
  return cateIdList.filter((value) => {
    return ids.includes(value.parentId)
  })
}

// 根据机型树，分出每一级的机型
export function getCateList(cateTree, level: number, productLine?) {
  // 循环遍历产品线当前层级下的数据
  switch (level) {
    case 0:
      // 0级机型
      return cateTree.map((value) => formatCateInfo(value))
    case 1:
      // 1级机型
      let firstClassList = []
      cateTree.forEach((item) => {
        firstClassList = firstClassList.concat(item.children.map((value) => formatCateInfo(value)))
      })
      if (productLine) {
        return firstClassList.filter((item: { data }) => {
          return item.data.productLines.includes(productLine)
        })
      }
      return firstClassList

    case 2:
      // 2级机型
      let secondClassList = []
      cateTree.forEach((item) => {
        item.forEach((senitem) => {
          secondClassList = secondClassList.concat(
            senitem.children.map((value) => formatCateInfo(value))
          )
        })
      })
      if (productLine) {
        return secondClassList.filter((item: { data }) => {
          return item.data.productLines.includes(productLine)
        })
      }
      return secondClassList

    case 3:
      let thirdClassList = []
      cateTree.forEach((item) => {
        item.forEach((senitem) => {
          senitem.forEach((third) => {
            thirdClassList = thirdClassList.concat(
              third.children.map((value) => formatCateInfo(value))
            )
          })
        })
      })
      if (productLine) {
        return thirdClassList.filter((item: { data }) => {
          return item.data.productLines.includes(productLine)
        })
      }
      return thirdClassList
  }
  return cateTree
}

export const uniqueArray = (arr) => {
  const arr1: AnyType = []
  for (let i = 0, len = arr.length; i < len; i++) {
    if (!arr1.includes(arr[i])) {
      // 检索arr1中是否含有arr中的值
      arr1.push(arr[i])
    }
  }
  return arr1
}
