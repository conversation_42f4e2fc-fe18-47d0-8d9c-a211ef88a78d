import { compareAsc, compareDesc } from './analysis-general'
import { merge } from 'lodash'
const color = [
  '#4A9EFF',
  '#48D4CF',
  '#FFC540',
  '#FF7A75',
  '#38C3FE',
  '#38D677',
  '#6372FF',
  '#FE9561',
  '#BE8CF1',
  '#7298D0',
  '#237FFA',
  '#97DA78',
  '#ca8622',
  '#bda29a',
  '#6e7074',
  '#546570',
  '#c4ccd3',

  '#5b9bd5',
  '#ed7d31',
  '#70ad47',
  '#ffc000',
  '#4472c4',
  '#91d024',
  '#b235e6',
  '#02ae75',

  '#95a2ff',
  '#fa8080',
  '#ffc076',
  '#fae768',
  '#87e885',
  '#3cb9fc',
  '#73abf5',
  '#cb9bff',
  '#434348',
  '#90ed7d',
  '#f7a35c',
  '#8085e9',

  '#05f8d6',
  '#0082fc',
  '#fdd845',
  '#22ed7c',
  '#09b0d3',
  '#1d27c9',
  '#f9e264',
  '#f47a75',
  '#009db2',
  '#024b51',
  '#765005',
]
const tooltipOptions = {
  confine: true,
  enterable: true,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  position: (pt, _params, dom, _rect = {}, size) => {
    const [viewWidth, viewHeight] = size.viewSize
    const [contentWidth, contentHeight] = size.contentSize
    if (contentHeight >= viewHeight) {
      const contentOffsetHeight = dom.children[0].offsetHeight
      const diff = contentOffsetHeight - viewHeight
      const { top: viewTop } = dom.parentElement.getClientRects()[0]
      const top = Math.min(diff, Math.max(viewTop - 80, 0)) // 80是tooltip举例视口顶部的最小距离
      const leftOffet = 10
      if (contentWidth + leftOffet + pt[0] <= viewWidth) {
        return {
          top: top * -1,
          left: pt[0] + leftOffet,
        }
      } else if (pt[0] - leftOffet - contentWidth >= 0) {
        return {
          top: top * -1,
          left: pt[0] - leftOffet - contentWidth,
        }
      }
    }
    return {
      top: 0,
      left: pt[0] + 20,
    }
  },
  appendToBody: true,
  extraCssText: 'max-height:calc(100vh - 160px);overflow:auto;background-color: #FFFFFF',
  hideDelay: 10,
}
/**
 * Echarts通用样式类
 * 主要包含常用echarts的基本样式配置，使用时只需要按照相关要求填入数据即可
 */
export class EchartCommonOption {
  // 多值柱状图
  public static multipleBarOption
  // 折线图
  public static lineChartOption
  public static stackBarChartOption
  public static BarChartOption

  /**
   * 普通柱状图样式
   */
  public static getBarOption = (
    title: string,
    originData,
    xAxis,
    yAxisFormat,
    series,
    authType: number,
    isNormal?: boolean,
    isShowGrid?: boolean
  ) => {
    return (EchartCommonOption.BarChartOption = {
      title: {
        text: title,
        left: 'center',
      },
      color,
      tooltip: {
        ...tooltipOptions,
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: (params) => {
          const html = EchartCommonOption.formatNormalBarHtml(
            params,
            authType,
            originData,
            isNormal
          )
          return `<div>${html}</div>`
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '20%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: xAxis,
        axisLabel: {
          interval: 0,
          rotate: 20,
        },
        splitLine: {
          show: isShowGrid ?? true,
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          show: true,
          formatter: yAxisFormat,
        },
        splitLine: {
          show: isShowGrid ?? true,
        },
      },
      dataZoom: [
        {
          type: 'slider',
          filterMode: 'filter',
          start: 20,
          end: 90,
          bottom: '10%',
        },
      ],
      series,
    })
  }

  /**
   * 格式化普通柱状图鼠标放上去显示的数据
   */
  public static formatNormalBarHtml(params, authType, originData, isNormal?) {
    // 获取柱状图中的name
    const paramsList = params
    let html = '' + params[0].name + '<br/>'

    paramsList.forEach((paramsItem) => {
      let itemData
      if (isNormal) {
        itemData = originData[paramsItem.dataIndex]
      } else {
        const originItem = originData.filter((res) => {
          return res.name === paramsItem.name
        })
        itemData = originItem ? originItem[0].list[0] : null
      }

      html = EchartCommonOption.formatDisplay(authType, paramsItem, itemData, html)
    })
    return html
  }

  /**
   * 格式化堆叠柱状图鼠标放上去显示数据
   */
  public static formatStackBarHtml = (
    params,
    authType,
    originData,
    dataDesc,
    uniqDataDesc,
    order = 1,
    normalization = null,
    series
  ) => {
    let paramsList = params
    paramsList = paramsList.filter((item) => {
      return item.value !== undefined
    })
    let html = '' + params[0].name + '<br/>'
    if (normalization == null) {
      if (order === 1) {
        paramsList.sort(compareDesc('value')) // 根据值倒序排序数组
      } else {
        paramsList.sort(compareAsc('value')) // 根据值顺序排序数组
      }
    } else {
      paramsList.reverse()
    }

    paramsList.forEach((paramsItem) => {
      let originItem
      let itemData
      if (dataDesc !== '') {
        if (uniqDataDesc !== '') {
          // 若存在唯一ID，用名称过滤会导致重复，要使用唯一id来过滤
          originItem = originData[paramsItem.dataIndex].list.filter((item) => {
            return item[uniqDataDesc] === series[paramsItem.seriesIndex].uniqueDataDesc
          })
        } else {
          // 不存在唯一ID，可直接用名称过滤
          originItem = originData[paramsItem.dataIndex].list.filter((item) => {
            return item[dataDesc] === paramsItem.seriesName
          })
        }
        itemData = originItem.length === 0 ? null : originItem[0]
      } else {
        itemData = originData[paramsItem.dataIndex].list[paramsItem.seriesIndex - 1]
      }
      if (itemData == null) {
        html += ''
      } else {
        html += paramsItem.marker + paramsItem.seriesName + '：'
        switch (authType) {
          case 1:
            // 具有所有展示权限
            if (originData[0].authType === 3) {
              // 若数据权限只有敏感无详情，一般情况下是分母是故障总数，此时只能显示比例
              html += Number(itemData.rate.toFixed(4)) + '%<br/>'
            } else {
              html += itemData.fz_num + '/' + itemData.fm_num + '<br/>'
            }
            break
          case 2:
            html += itemData.fz_num + '<br/>'
            break
          case 3:
            // 能看故障率
            html += Number(itemData.rate.toFixed(4)) + '%<br/>'
            break
        }
      }
    })
    return html
  }

  /**
   * 堆叠柱状图样式
   */
  public static getStackBarOption = (
    title,
    originData,
    authType,
    legend,
    xAxis,
    yAxisFormat,
    series,
    DataDesc,
    uniqDataDesc,
    order = 2,
    yAxisName = '',
    max = null,
    normalization = null,
    customChartOption,
    tooltipHtmlParam,
    i18n
  ) => {
    const defaultOption = {
      title: {
        text: title,
        left: 'center',
      },
      color,
      tooltip: {
        ...tooltipOptions,
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: (params) => {
          const html =
            tooltipHtmlParam !== undefined && tooltipHtmlParam !== null
              ? tooltipHtmlParam(params, originData, i18n)
              : EchartCommonOption.formatStackBarHtml(
                  params,
                  authType,
                  originData,
                  DataDesc,
                  uniqDataDesc,
                  order,
                  normalization,
                  series
                )
          return `<div>${html}</div>`
        },
      },
      legend: {
        data: legend,
        width: '90%',
        type: 'scroll',
        bottom: '0',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '20%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: xAxis,
        axisLabel: {
          interval: 0,
          rotate: 20,
        },
      },
      yAxis: {
        name: yAxisName === '' ? '' : yAxisName,
        type: 'value',
        axisLabel: {
          show: true,
          formatter: yAxisFormat,
        },
        max: max != null ? max : null,
      },
      dataZoom: [
        {
          type: 'slider',
          filterMode: 'filter',
          xAxisIndex: [0],
          bottom: '10%',
        },
      ],
      series,
    }
    const optionResult = merge({}, defaultOption, customChartOption)
    return (EchartCommonOption.stackBarChartOption = optionResult)
  }

  /**
   * 格式化折线图鼠标放上去显示数据
   */
  public static formatLineHtml = (
    params,
    authType,
    originData,
    dataDesc,
    needCollect,
    targetLength,
    order = 1
  ) => {
    const paramsList = params
    let html = `<div class="echarts-line__tooltip-title">${params[0].name}</div>`
    if (order === 1) {
      paramsList.sort(compareDesc('value')) // 根据值倒序排序数组
    } else {
      paramsList.sort(compareAsc('value')) // 根据值顺序排序数组
    }
    paramsList.forEach((paramsItem) => {
      // 若item的数据为空，表示该列没有数据，直接返回
      if (!paramsItem.data) {
        return
      }
      const originDataItem = originData[paramsItem.seriesIndex - 1]
      if (originDataItem == null) {
        // 若有多个目标线，需减去
        html += paramsItem.marker + paramsItem.seriesName + '：' + paramsItem.value + '%<br/>'
      } else {
        // 使用当前数据的index日期过滤原数组，获取数据信息
        const itemData = originDataItem.list.filter((item) => {
          return item[dataDesc] === paramsItem.axisValue
        })[0]
        html = EchartCommonOption.formatDisplay(authType, paramsItem, itemData, html)
      }
    })
    return html
  }

  /**
   * 折线图样式
   * @param title
   * @param originData
   * @param authType
   * @param legend
   * @param xAxis
   * @param yAxisFormat
   * @param series
   * @param dataDesc
   * @param needCollect
   * @param targetLength 目标线数组长度
   * @param tooltipHtmlParam
   * @param legendWidth // 需要补充legend配置项
   */
  public static getLineChartOption = (
    title,
    originData,
    authType,
    legend,
    xAxis,
    yAxisFormat,
    series,
    needCollect,
    targetLength,
    tooltipHtmlParam,
    dataDesc = 'index',
    isShowGrid = true,
    legendWidth = ''
  ) => {
    return (EchartCommonOption.lineChartOption = {
      title: {
        text: title,
        left: 'center',
      },
      color,
      tooltip: {
        ...tooltipOptions,
        trigger: 'axis',
        extraCssText:
          'border-radius: 3px; background: rgba(255, 255, 255, 0.95); box-shadow: 0 8px 16px 0 rgba(31, 39, 51, 0.12);',
        formatter: (params) => {
          const html =
            tooltipHtmlParam !== undefined && tooltipHtmlParam !== null
              ? tooltipHtmlParam(params, originData)
              : EchartCommonOption.formatLineHtml(
                  params,
                  authType,
                  originData,
                  dataDesc,
                  needCollect,
                  targetLength
                )
          return `<div class="echarts-line__tooltip">${html}</div>`
        },
      },
      legend: {
        data: legend,
        width: legendWidth ?? '90%',
        type: 'scroll',
        itemGap: 20,
        top: 10,
        selected: legend.reduce((prev, item) => {
          prev[item] = true
          return prev
        }, {}),
        lineStyle: {
          width: 1.5,
        },
        textStyle: {
          color: '#5F6A7A',
        },
        itemHeight: 0,
        itemWidth: 20,
      },
      grid: {
        left: 0,
        right: 10,
        bottom: '10%',
        containLabel: true,
      },

      xAxis: {
        type: 'category',
        boundaryGap: true,
        axisLabel: {
          rotate: 20,
          color: '#5F6A7A',
        },
        data: xAxis,
        axisLine: {
          lineStyle: {
            color: '#EBEDF0',
          },
        },
        splitLine: {
          show: isShowGrid,
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          show: true,
          formatter: yAxisFormat,
          interval: 0,
          color: '#5F6A7A',
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#EBEDF0',
          },
        },
        axisTick: {
          show: false, // 显示刻度线
        },
        splitLine: {
          show: isShowGrid,
          lineStyle: {
            color: '#EBEDF0',
          },
        },
      },
      dataZoom: [
        {
          type: 'slider',
          bottom: '5%',
          filterMode: 'filter',
          xAxisIndex: [0],
          height: 12,
        },
      ],
      series,
    })
  }

  /**
   * 多个柱状图样式
   * @param title
   * @param originData
   * @param authType
   * @param legend
   * @param xAxis
   * @param yAxisFormat
   * @param series
   * @param dataDesc
   * @param _order
   */
  public static getMultipleBarOptionV2 = (
    title,
    originData,
    authType,
    legend,
    xAxis,
    yAxisFormat,
    series,
    dataDesc = 'index',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _order?
  ) => {
    return (EchartCommonOption.multipleBarOption = {
      title: {
        text: title,
        left: 'center',
      },
      color,
      tooltip: {
        ...tooltipOptions,
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
        },
        // 鼠标放在图上显示的格式
        formatter: (params) => {
          const html = EchartCommonOption.formatTop2BarHtml(params, authType, originData, dataDesc)
          return `<div>${html}</div>`
        },
      },
      legend: {
        data: legend,
        width: '90%',
        type: 'scroll',
        // left: 'left',
        bottom: '0',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '20%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: xAxis,
        boundaryGap: ['5%', '5%'],
        axisLabel: {
          interval: 0,
          rotate: 20,
        },
        max: 'dataMax',
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          show: true,
          formatter: yAxisFormat, // Y周坐标显示数据的类型
        },
      },
      dataZoom: [
        {
          type: 'slider',
          filterMode: 'filter',
          bottom: '10%',
        },
      ],
      series,
    })
  }

  // 格式化二级故障柱状图tooltip
  public static formatTop2BarHtml = (params, authType, Data, dataDesc) => {
    const paramsList = params
    let html = '' + params[0].name + '<br/>'
    paramsList.forEach((paramsItem) => {
      const originItem = Data[paramsItem.seriesIndex].list.filter((item) => {
        return item[dataDesc] === paramsItem.name
      })
      const itemData = originItem.length !== 0 ? originItem[0] : null
      html = EchartCommonOption.formatDisplay(authType, paramsItem, itemData, html)
    })
    return html
  }

  // 格式化显示鼠标放上去的分子分母展示
  public static formatDisplay = (authType, paramsItem, itemData, html) => {
    switch (authType) {
      case 1:
        // 能看所有
        if (itemData?.fm_num !== 0 || itemData?.fz_num !== 0) {
          html += `<div class="echarts-line__tooltip-line">
            <span class="echarts-line__tooltip-legend" style="background-color: ${paramsItem.color};"></span>
            <span class="echarts-line__tooltip-name">${paramsItem.seriesName}</span>
            <span class="echarts-line__tooltip-value">${itemData.fz_num}/${itemData.fm_num}</span>
          </div>`
          // html += paramsItem.marker + paramsItem.seriesName + '：'
          // html += itemData.fz_num + '/' + itemData.fm_num + '<br/>'
        }
        break
      case 2:
        // 只能看分子
        if (itemData?.fz_num !== 0) {
          html += `<div class="echarts-line__tooltip-line">
            <span class="echarts-line__tooltip-legend" style="background-color: ${paramsItem.color};"></span>
            <span class="echarts-line__tooltip-name">${paramsItem.seriesName}</span>
            <span class="echarts-line__tooltip-value">${itemData.fz_num}</span>
          </div>`
          // html += paramsItem.marker + paramsItem.seriesName + '：'
          // html += itemData.fz_num + '<br/>'
        }
        break
      case 3:
        // 能看故障率
        if (itemData?.rate !== 0) {
          html += `<div class="echarts-line__tooltip-line">
            <span class="echarts-line__tooltip-legend" style="background-color: ${
              paramsItem.color
            };"></span>
            <span class="echarts-line__tooltip-name">${paramsItem.seriesName}</span>
            <span class="echarts-line__tooltip-value">${itemData.rate.toFixed(4)}%</span>
          </div>`
          // html += paramsItem.marker + paramsItem.seriesName + '：'
          // html += itemData.rate.toFixed(4) + '%<br/>'
        }
        break
    }
    return html
  }
}
