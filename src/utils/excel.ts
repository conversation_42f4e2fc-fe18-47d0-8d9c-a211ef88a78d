import { useState, useCallback, useEffect } from 'react'
import dayjs from 'dayjs'
import * as XLSX from 'xlsx'
import { TableColumnItem } from '@hi-ui/table'
import message from '@hi-ui/message'

export enum ExportStatus {
  INIT = 'init',
  EXPORTING = 'exporting',
  SUCCESS = 'success',
  ERROR = 'error',
}

export type ExportFormatter<T = AnyType> = {
  [K in keyof T]?: (value: T[K]) => string | number
}

interface UseExcelExportReturn {
  exportExcel: (
    cols: TableColumnItem[],
    list: Record<string, AnyType>[],
    fileName?: string,
    formatter?: ExportFormatter
  ) => void
  status: ExportStatus
}

interface ExportConfig {
  fieldNames?: Record<string, string>
  timeFormat?: string
}

export function useExcelExport(config?: ExportConfig): UseExcelExportReturn {
  const [status, setStatus] = useState<ExportStatus>(ExportStatus.INIT)
  const { fieldNames, timeFormat = 'YYYYMMDD' } = config || {}
  const kvMap = { k: 'dataKey', v: 'title' }

  if (fieldNames) {
    kvMap.k = fieldNames.dataKey || 'dataKey'
    kvMap.v = fieldNames.title || 'title'
  }

  const processExcelData = useCallback(
    (cols, list, formatter) => {
      const headers = cols.map((col) => col[kvMap.v] as string)
      const processRow = (item: Record<string, AnyType>) =>
        cols.map((col) => {
          const key = col[kvMap.k] as string
          const value = item[key]
          return formatter?.[key] ? formatter[key](value) : value
        })

      return [headers, ...list.map(processRow)]
    },
    [kvMap.k, kvMap.v]
  )

  const exportExcel = useCallback(
    (cols, list, fileName = 'excel', formatter = null) => {
      setStatus(ExportStatus.EXPORTING)
      try {
        const excelData = processExcelData(cols, list, formatter)

        const workbook = XLSX.utils.book_new()
        const worksheet = XLSX.utils.aoa_to_sheet(excelData)
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

        const formattedFileName = `${fileName}_${dayjs().format(timeFormat)}.xlsx`
        XLSX.writeFile(workbook, formattedFileName)

        setStatus(ExportStatus.SUCCESS)
      } catch (error) {
        console.error('导出失败:', error)
        setStatus(ExportStatus.ERROR)
      }
    },
    [processExcelData, timeFormat]
  )

  useEffect(() => {
    switch (status) {
      case ExportStatus.EXPORTING:
        message.open({ title: '导出中...', type: 'info' })
        break
      case ExportStatus.SUCCESS:
        message.closeAll()
        message.open({ title: '导出成功', type: 'success' })
        break
      case ExportStatus.ERROR:
        message.closeAll()
        message.open({ title: '导出失败', type: 'error' })
        break
      default:
    }
  }, [status])

  return { exportExcel, status }
}
