/* eslint-disable @typescript-eslint/no-explicit-any */
import { NOOP_ARR } from './noop'

export const traverseTree = function <T extends { children?: T[] }>(
  children: T[],
  callback: (child: T, parent: T | null) => void
) {
  const visit = (children = NOOP_ARR as T[], parent: null | T = null, callback) => {
    for (const child of children) {
      callback(child, parent)
      if (child.children && child.children.length) {
        visit(child.children, child, callback)
      }
    }
  }
  visit(children, null, callback)
}
