import intl from 'react-intl-universal'
import { AnyType, MESSAGE_DURATION } from '@/constants'
import message from '@hi-ui/message'
export interface MainMenuItem {
  id: string
  title: string
  appId: string
  parentId: string
  children: MainMenuItem[]
  icon: {
    name: string
    url: string
  }
  isExternalLink: boolean | null
  isFavorite: boolean
  menuRoute: string
  url: string
  parentTitle?: string
}

// 从主应用中传递下来的数据
export interface MicroAppProps {
  container: HTMLElement
  route: string
  workbenchUUID: string // 工作台 UUID
  credential: {
    token: string
  }
  login: () => void // 跳转登录
  appInfo: {
    menuMap: Record<string, MainMenuItem>
  }
  microApps: AnyType // 所有应用的信息
  name: string // 当前appid
  orgTreeCode: string
  withTab: () => boolean
  switchMenu: (menuId: string, appId: string) => void
  getFunctionCodes: () => Promise<string[]>
  getAccessData: () => Promise<AnyType>
}

class MicroApp {
  private props: Partial<MicroAppProps> = {}

  // 是否是在主应用中作为子应用进行加载
  is() {
    return window.__POWERED_BY_QIANKUN__
  }

  publicPath() {
    return window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__
  }

  setProps(props?: MicroAppProps) {
    props && (this.props = props)
  }

  getProps() {
    return this.props
  }
}

export const microApp = new MicroApp()

// 判断是否是当前应用
export const isActiveApp = (appId) => {
  return appId === microApp.getProps()?.name
}

const TODO_DETAIL_INFO = {
  test: { menuId: '10855', appId: '8QExUZUpuiqj' },
  pro: { menuId: '4365', appId: 'hAmwm9rL461R' },
  pre: { menuId: '10226', appId: '8QExUZUpuiqj' },
  local: { menuId: '10226', appId: '8QExUZUpuiqj' },
  dev: { menuId: '', appId: '8QExUZUpuiqj' },
}[process.env.DEPLOY_ENV]

export const goTodoDetail = () => {
  const { switchMenu } = microApp.getProps()
  const { menuId, appId } = TODO_DETAIL_INFO
  if (switchMenu && menuId && appId) {
    switchMenu(menuId, appId)
  } else {
    message.open({
      title: intl.get('menuId或appId缺失，请联系管理员'),
      type: 'error',
      duration: MESSAGE_DURATION,
    })
  }
}
