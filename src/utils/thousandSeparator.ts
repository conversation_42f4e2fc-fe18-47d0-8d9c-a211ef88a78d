export const thousandSeparator = (input: string | number = '') => {
  const num = (input || 0).toString()
  let result = ''
  const decimal = num.split('.')[1] ?? ''
  let integer = num.split('.')[0] ?? ''
  while ((integer.length > 3 && num[0] !== '-') || integer.length > 4) {
    result = ',' + integer.slice(-3) + result
    integer = integer.slice(0, integer.length - 3)
  }
  if (integer) {
    result = integer + result
  }
  return result + (decimal !== '' ? `.${decimal}` : '')
}
