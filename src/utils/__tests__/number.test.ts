import { describe, it, expect } from 'vitest'
import { absOptimized } from '../number'

describe('number utility functions', () => {
  describe('absOptimized', () => {
    it('应该格式化正数，添加千位分隔符', () => {
      expect(absOptimized(1234.5)).toBe('1,234.5')
      expect(absOptimized(1000)).toBe('1,000')
      expect(absOptimized(0.1)).toBe('0.1')
    })

    it('应该格式化带有多位小数的数字，超过digits位时保留digits位数', () => {
      expect(absOptimized(1234.567)).toBe('1,234.57') // 默认保留2位小数
      expect(absOptimized(0.001)).toBe('0') // 四舍五入后为0
    })

    it('应该支持自定义小数位数', () => {
      expect(absOptimized(1234.5678, 3)).toBe('1,234.568')
      expect(absOptimized(1000, 0)).toBe('1,000')
      expect(absOptimized(0.1, 4)).toBe('0.1')
    })

    it('应该正确处理零', () => {
      expect(absOptimized(0)).toBe('0')
      expect(absOptimized(-0)).toBe('0')
    })

    it('应该处理负数', () => {
      expect(absOptimized(-1234.5)).toBe('-1,234.5')
      expect(absOptimized(-0.1)).toBe('-0.1')
    })

    it('应该对非数字输入返回"0.00"', () => {
      // @ts-expect-error 测试不正确的输入类型
      expect(absOptimized(null)).toBe('0.00')
      // @ts-expect-error 测试不正确的输入类型
      expect(absOptimized(undefined)).toBe('0.00')
      // @ts-expect-error 测试不正确的输入类型
      expect(absOptimized('string')).toBe('0.00')
      expect(absOptimized(NaN)).toBe('0.00')
    })

    it('应该处理非常大的数字', () => {
      expect(absOptimized(1000000)).toBe('1,000,000')
      expect(absOptimized(1234567.89)).toBe('1,234,567.89')
    })

    it('应该处理非常小的数字', () => {
      expect(absOptimized(0.0001)).toBe('0') // 四舍五入后为0
      expect(absOptimized(0.009)).toBe('0.01') // 四舍五入后为0.01
    })
  })
})
