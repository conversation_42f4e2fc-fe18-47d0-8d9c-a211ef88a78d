import { getTrackConditions } from '@/api/gsc/track'
import { setStorage } from './cache'
import { GSC_TRACK_SUPPLIER_TYPES_CACHE } from '@/constants'
import dayjs from 'dayjs'

const outerVisTrackParams = {
  type: '1',
  fromSupplier: 1,
  supplierTypes: [],
  suppliers: [],
  systemNames: ['GSC'],
  workbenchNames: ['GSC'],
  menuTitles: [],
  accountNames: [],
  startDate: dayjs().subtract(7, 'day').format('YYYYMMDD'),
  endDate: dayjs().subtract(1, 'day').format('YYYYMMDD'),
}

export const setOuterTrackConditionCache = () => {
  getTrackConditions(outerVisTrackParams as AnyType).then((res) => {
    const supplierTypesObj = {
      defaultSupplierTypes: res?.data?.map((item) => item.code) || [],
      defaultsupplierTypesSelect:
        res?.data?.map((item) => ({
          id: item.code,
          title: item.name,
        })) || [],
    }
    setStorage(GSC_TRACK_SUPPLIER_TYPES_CACHE, supplierTypesObj)
  })
}
