/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable prettier/prettier */
import config from '../config'

export default {
  trackInit() {
    console.info('%c TrackInit', 'color:green')
    if (config.onetrackAppId && (window as any).onetrack) {
      (window as any).onetrack('gsc-web.init', config.onetrackAppId)
    }
  },

  /**
   * 跟踪页面访问
   * @param params
   *  page:   页面路径（path）
   *  title:  页面标题
   *  module: 页面模块
   *
   * example
   * TrackHelp.trackView({
        page: 'pages/work-time/index',
        title: '工时管理-填报 | 工时管理-设置',
        module: '工时管理'
      })
   */
  trackView(params) {
    console.info('%c TrackView', 'color:green', params)
    if (config.onetrackAppId && (window as any).onetrack) {
      (window as any).onetrack('gsc-web.track', 'view', params)
    }
  },

  trackVersion(params) {
    if (config.onetrackAppId && (window as any).onetrack) {
      (window as any).onetrack('gsc-web.track', 'custom_version', params)
    }
  },

  trackEvent(event, params) {
    console.info('%c TrackEvent', 'color:green', params)
    if (config.onetrackAppId && (window as any).onetrack) {
      (window as any).onetrack('gsc-web.track', event, params)
    }
  },

  trackClick(params) {
    console.info('%c TrackClick', 'color:green', params)
    if (config.onetrackAppId && (window as any).onetrack) {
      (window as any).onetrack('gsc-web.track', 'click', params)
    }
  },

  setUser(user) {
    if (user) {
      const { userId, displayName, email, department } = user
      const depts: string[] = department.split('/')
      const userInfo = {
        name: userId,
        displayName,
        email,
        dept1: depts[1] || '',
        dept2: depts[2] || '',
        dept3: depts[3] || '',
        dept4: depts[4] || '',
        dept5: depts[5] || '',
        dept6: depts[6] || '',
        client: 'PC',
        xiaomi_id: userId,
      }
      // 设置onetrack公共用户业务参数
      this.set({ ...userInfo })
    }
  },
  set(params) {
    console.info('%c TrackSet', 'color:green', params)
    if (config.onetrackAppId && (window as any).onetrack) {
      (window as any).onetrack('gsc-web.set', params)
    }
  },
  login(userId, accountType, params?, skipTrackLogin?) {
    if (config.onetrackAppId && (window as any).onetrack) {
      (window as any).onetrack('gsc-web.login', userId, accountType, params, skipTrackLogin)
    }
    console.log('login', userId, accountType, params, skipTrackLogin)
  },
}
