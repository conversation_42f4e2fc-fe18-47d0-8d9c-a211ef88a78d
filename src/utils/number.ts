export const multiply = (a, b) => {
  return Math.round(a * b * 1e10) / 1e10
}

export const isEmptyExceptZero = (value: unknown): boolean =>
  value == null || (typeof value === 'string' && value.trim() === '')

export const formatDisplayNumber = (
  num: number | null | undefined | string,
  decimals: number
): string => {
  if (num === null || num === '' || num === undefined || typeof num !== 'number' || isNaN(num)) {
    return ''
  }
  if (num === 0) return '0'

  const factor = Math.pow(10, decimals)
  let roundedNum = Math.round(num * factor) / factor

  if (Object.is(roundedNum, -0)) {
    roundedNum = 0
  }

  return roundedNum.toFixed(decimals)
}

export const roundToDecimal = (number: number, decimals: number) => {
  const factor = Math.pow(10, decimals)
  return Math.round(number * factor) / factor
}

export const absOptimized = function (cost: number, digits = 2) {
  if (typeof cost !== 'number' || isNaN(cost)) {
    // 处理无效输入，返回 '0.00'
    return '0.00'
  }

  // 特殊处理负零的情况
  if (Object.is(cost, -0)) {
    cost = 0
  }

  // 获取小数位数
  const costStr = Math.abs(cost).toString()
  const decimalParts = costStr.split('.')
  const hasDecimal = decimalParts.length > 1
  const decimalLength = hasDecimal ? decimalParts[1].length : 0

  // 使用 Intl.NumberFormat 进行格式化
  // 如果小数位数超过了 digits，则保留 digits 位数，否则保留原有小数位数
  const formatter = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0, // 不强制要求最小小数位
    maximumFractionDigits: Math.min(decimalLength, digits), // 限制最大小数位
    useGrouping: true, // 确保使用千位分隔符
  })

  return formatter.format(cost)
}

/**
 * 安全地保留指定小数位数，避免JavaScript浮点数精度问题
 * @param value 数值
 * @param decimal 小数位数，默认2位
 * @returns 精确的数值
 */
export const safeToFixed = (value: number | string, decimal = 2): number => {
  const num = typeof value === 'string' ? parseFloat(value) : value

  if (isNaN(num)) {
    return 0
  }

  // 使用乘法和四舍五入避免精度问题
  const multiplier = Math.pow(10, decimal)
  return Math.round(num * multiplier) / multiplier
}

/**
 * 安全的数字格式化，保留指定小数位数
 * @param value 数值
 * @param decimal 小数位数，默认2位
 * @returns 格式化后的数值
 */
export const safeFormatNumber = (value: number | string, decimal = 2): number => {
  return safeToFixed(value, decimal)
}
