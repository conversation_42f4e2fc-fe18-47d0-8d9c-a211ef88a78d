import intl from 'react-intl-universal'
import config from '@/config' // 引入配置
import axios, { AxiosRequestConfig } from 'axios'
import Message from '@hi-ui/message'
import Modal from '@hi-ui/modal'
import * as Sentry from '@sentry/react'
import {
  COST_URLS,
  EXCEPTION_403_URLS,
  FETCH_SUCCESS_CODES,
  GSC_AUTH,
  MESSAGE_DURATION,
  getGSCLang,
} from '@/constants'
import { microApp } from './micro-app'
import { mergeProps } from './merge'
import { download } from './request-download'

export const apiHost = config.API_BASE
const DEFAULT_ERROR_MESSAGE = '接口报错'

interface RequestConfig extends AxiosRequestConfig {
  /**
   * 非2000请求是否被reject
   */
  rejectError?: boolean
  /**
   * 自动处理错误信息
   */
  handleError?: boolean
  /**
   * 自定义请求头
   */
  headers?: Record<string, string>
  /**
   * 接口是否携带国际化参数
   */
  enableLang?: boolean

  /** 跳过拼接apiHost */
  skipApiHost?: boolean
}

export interface BaseResponse<T> {
  /**
   * 业务状态码
   */
  code: number
  /**
   * 提示信息
   */
  msg: string
  /**
   * 业务是否按照预期成功
   */
  message?: string
  success: boolean
  data: T
}
export const http = axios.create({
  withCredentials: true,
})

http.interceptors.request.use((config) => {
  const token = microApp.getProps().credential?.token
  config.headers = {
    ...config.headers,
    Authorization: token
      ? `Bearer ${token}`
      : JSON.parse(localStorage.getItem(GSC_AUTH) || '{}')?.value || '',
  }
  return config
})
http.interceptors.response.use(
  (response) => {
    const config = response.config as RequestConfig
    const { handleError = true, rejectError = true } = config
    if (response.headers.location) {
      window.location.href = response.headers.location
    }

    return new Promise((resolve, reject) => {
      if (
        !(response.data instanceof Blob || response.data instanceof ArrayBuffer) &&
        !FETCH_SUCCESS_CODES.includes(response.data.code)
      ) {
        const { message, msg } = response.data || {}
        const errorMsg = message || msg || DEFAULT_ERROR_MESSAGE

        switch (response.data.code) {
          // 弱校验失败或者系统异常
          case -1000:
          case 6500:
            if (handleError) {
              Message.open({
                type: 'error',
                title: errorMsg,
                duration: MESSAGE_DURATION,
              })
              return reject(response.data)
            } else if (rejectError) {
              return reject(response.data)
            } else {
              return resolve(response)
            }
          case 6002:
            if (handleError) {
              Modal.confirm({
                type: 'error',
                className: 'error-msg-modal',
                title: intl.get('错误'),
                content: errorMsg,
                cancelText: null,
                closeable: false,
              })
              return reject(response.data)
            } else if (rejectError) {
              return reject(response.data)
            } else {
              return resolve(response)
            }
          default:
            if (handleError) {
              Message.open({
                type: 'error',
                title: errorMsg,
                duration: MESSAGE_DURATION,
              })
              return reject(response.data)
            } else {
              return rejectError ? reject(response.data) : response
            }
        }
      } else {
        return resolve(response)
      }
    })
  },
  (error) => {
    const status = (error.response || {}).status
    if (status === 401 && microApp.is()) {
      // 在主应用中作为子应用加载，直接调用主应用的登录方法
      microApp.getProps().login?.()
      return Promise.resolve()
    }
    if (error.message.includes('timeout')) {
      Message.open({
        type: 'error',
        title: intl.get('请求超时了，请重试!'),
        duration: MESSAGE_DURATION,
      })
      return Promise.reject(error)
    }
    if (status === 403 || status === 401) {
      Message.open({
        type: 'error',
        title: intl.get('暂无权限'),
        duration: MESSAGE_DURATION,
      })
      // https://github.com/remix-run/react-router/issues/8264

      const pathname = window.location.pathname
      // 避免二次请求重复跳转
      if (!EXCEPTION_403_URLS.includes(pathname)) {
        const detail = COST_URLS.includes(pathname) ? '/exception/cost-403' : '/exception/403'
        window.dispatchEvent(
          new CustomEvent('route', {
            detail,
          })
        )
      }
    } else if (error.code !== 'ERR_CANCELED') {
      Message.open({
        type: 'error',
        title: intl.get('请求错误'),
        duration: MESSAGE_DURATION,
      })
      Sentry.captureException(error)
    }
    return Promise.resolve()
  }
)

export const genLangHeader = (lang: string | undefined): Record<string, string> =>
  lang ? { 'Accept-Language': lang } : {}

// url的处理逻辑太多啦！都放在这里
function generateUrl(url: string, reqConfig: RequestConfig) {
  const httpReg = /^http(s)?:\/\/.+/
  const qmsReg = /\/api\/qms/

  if (httpReg.test(url)) return url
  if (qmsReg.test(url)) return url

  if (reqConfig?.skipApiHost) return url

  return apiHost + url
}

export const request = function <T = AnyType>(
  _url: string | RequestConfig,
  _reqConfig?: RequestConfig
): Promise<BaseResponse<T>> {
  const config: RequestConfig =
    typeof _url === 'object' ? { ..._url, ..._reqConfig } : { ..._reqConfig, url: _url }
  const { url, ...reqConfig } = config

  const lang = getGSCLang()
  const { params = {}, enableLang = true, ...rest } = reqConfig || {}
  const langParams =
    enableLang && lang
      ? { i18n_locale: lang, i18n_timeZoneOffset: '' + new Date().getTimezoneOffset() }
      : {}

  return http({
    url: generateUrl(url || '', reqConfig),
    ...rest,
    params: { ...params, ...langParams },
  }).then((res) => {
    if (res?.data && res?.data?.data === null) res.data.data = undefined
    return res?.data || {}
  })
}

// 利用闭包，存一些基础的配置项
export function createRequest(baseConfig?: RequestConfig) {
  function requestWrapper<T = AnyType>(config?: RequestConfig) {
    const merged = mergeProps(baseConfig, config)
    return request<T>(merged)
  }

  requestWrapper.get = function <T = AnyType>(url: string, config?: RequestConfig) {
    return requestWrapper<T>({ url, method: 'GET', ...config })
  }
  requestWrapper.post = function <T = AnyType>(url: string, config?: RequestConfig) {
    return requestWrapper<T>({ url, method: 'POST', ...config })
  }
  requestWrapper.put = function <T = AnyType>(url: string, config?: RequestConfig) {
    return requestWrapper<T>({ url, method: 'PUT', ...config })
  }
  requestWrapper.patch = function <T = AnyType>(url: string, config?: RequestConfig) {
    return requestWrapper<T>({ url, method: 'PATCH', ...config })
  }
  requestWrapper.delete = function <T = AnyType>(url: string, config?: RequestConfig) {
    return requestWrapper<T>({ url, method: 'DELETE', ...config })
  }
  requestWrapper.head = function <T = AnyType>(url: string, config?: RequestConfig) {
    return requestWrapper<T>({ url, method: 'HEAD', ...config })
  }
  requestWrapper.options = function <T = AnyType>(url: string, config?: RequestConfig) {
    return requestWrapper<T>({ url, method: 'OPTIONS', ...config })
  }

  requestWrapper.download = function (url: string, opts: Parameters<typeof download>[0]) {
    const merged = mergeProps(baseConfig, opts as RequestConfig)
    const downloadConfig = {
      url,
      ...merged,
      responseType: (merged.responseType as 'arraybuffer' | 'blob') || 'blob',
    }
    return download(downloadConfig)
  }

  return requestWrapper
}
