import { useSearchParams } from 'react-router-dom'
import querystring from 'querystring'

export const formatQueryStr = (state = {}) => {
  return querystring.stringify(state)
}

export const getQueryStr = (search: string) => {
  return querystring.parse(search)
}

export const getQueryObject = (search = '') => {
  const searchObject = {}
  if (!search) {
    return searchObject
  }
  const searchParams = new URLSearchParams(search)
  for (const param in searchParams) {
    searchObject[param[0]] = param[1]
  }
  return searchObject
}

export function useQueryParams<T extends string[]>(params: T) {
  const [searchParams] = useSearchParams()
  return params.reduce(
    (acc, key) => ({ ...acc, [key]: searchParams.get(key) ?? undefined }),
    {} as Record<ArrayToUnion<T>, string | undefined>
  )
}
