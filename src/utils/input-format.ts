export const excelToInput = (excelContent: string) => {
  // 将制表符替换为英文逗号
  let csvContent = excelContent.replace(/\t/g, ',')
  // 删除所有换行符
  csvContent = csvContent.replace(/\r?\n|\r/g, '')
  return csvContent
}

export const formatInput = (input: string) => {
  const queryInput = excelToInput(input)
  const queryString = queryInput?.includes(',')
    ? queryInput // Excel 贴入
    : queryInput?.trim()?.split(' ')?.join(',') // 手输
  return queryString?.split(',') || []
}

export const formatInputString = (input: string) => {
  const queryInput = excelToInput(input)
  const queryString = queryInput?.includes(',')
    ? queryInput // Excel 贴入
    : queryInput?.trim()?.split(' ')?.join(',') // 手输
  return queryString || ''
}
