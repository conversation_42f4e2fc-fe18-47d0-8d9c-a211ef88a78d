export const insertOrRemoveObjectByKey = <T extends Record<string, AnyType>>(
  array: T[],
  key: string,
  value: string,
  newObject: T,
  isRemove?: boolean
) => {
  // 找到插入位置的索引
  const insertIndex = array.findIndex((obj) => obj[key] === value)

  // 找到具有相同键值的对象的索引
  const existingIndex = array.findIndex((obj) => obj[key] === newObject[key])

  if (existingIndex !== -1 && isRemove) {
    // 如果找到了具有相同键值的对象，删除该对象
    array.splice(existingIndex, 1)
  } else if (!isRemove) {
    if (insertIndex !== -1) {
      // 如果没有找到具有相同键值的对象，且找到了插入位置，则在该位置插入新对象
      array.splice(insertIndex, 0, newObject)
    } else {
      // 如果没有找到插入位置，则将新对象添加到数组末尾
      array.push(newObject)
    }
  }

  return array
}

export function uniqueObjectArray(arr, keyFn = (item) => item.value) {
  // 使用 Map 替代普通对象，保持插入顺序
  const seen = new Map()

  return arr.filter((item) => {
    const key = keyFn(item)

    // 如果没见过这个key，就保留该项并标记为已见
    if (!seen.has(key)) {
      seen.set(key, true)
      return true
    }

    // 否则过滤掉该项
    return false
  })
}

/**
 * 通用分桶排序函数
 * @param data 要排序的对象数组
 * @param sortFields 排序字段数组，按优先级排列
 * @returns 分桶排序后的对象数组
 *
 * @example
 * // 示例：按品类和物料号进行分桶排序
 * const sortedData = bucketSort(tabList, ['costMatCatLvl1Code', 'matNo'])
 *
 * // 示例：按品牌名称排序
 * const sortedData = bucketSort(tabList, ['brandName'])
 */
export function bucketSort<T extends Record<string, AnyType>>(
  data: T[],
  sortFields: string[]
): T[] {
  if (!data || data.length === 0 || !sortFields || sortFields.length === 0) {
    return data
  }

  return data.sort((a, b) => {
    // 按照字段优先级依次比较
    for (const field of sortFields) {
      const valueA = a[field]
      const valueB = b[field]

      // 处理 null 或 undefined 值
      if (valueA == null && valueB == null) {
        continue
      }
      if (valueA == null) {
        return -1 // null 值排在后面
      }
      if (valueB == null) {
        return 1 // null 值排在后面
      }

      // 字符串比较使用 localeCompare
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        const comparison = valueB.localeCompare(valueA)
        if (comparison !== 0) {
          return comparison
        }
      } else if (typeof valueA === 'number' && typeof valueB === 'number') {
        // 数字比较
        if (valueA !== valueB) {
          return valueB - valueA
        }
      } else {
        // 其他类型转换为字符串比较
        const strA = String(valueA)
        const strB = String(valueB)
        const comparison = strA.localeCompare(strB)
        if (comparison !== 0) {
          return comparison
        }
      }
    }

    // 所有字段都相等
    return 0
  })
}
