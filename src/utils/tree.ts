import { cloneDeep } from 'lodash'

export const findParentId = (data, targetId) => {
  // 检查根节点
  if (data.node.id === targetId) {
    return null
  }

  // 递归查找父节点
  function search(nodes, parentId = null) {
    for (const node of nodes) {
      if (node.id === targetId) {
        return parentId
      }
      if (node.children && node.children.length > 0) {
        const result = search(node.children, node.id)
        if (result !== null) {
          return result
        }
      }
    }
    return null
  }

  return search(data.positive, data.node.id)
}

export const deepCopy = <T extends AnyType>(data: T) => JSON.parse(JSON.stringify(data)) as T

export function countLeafNodes(treeData) {
  let leafCount = 0

  function traverse(node) {
    if (!node.children || node.children.length === 0) {
      leafCount++
    } else {
      node.children.forEach(traverse)
    }
  }

  treeData.forEach(traverse)
  return leafCount
}

export function findAllLeafNodes(treeData) {
  const leafNodes = [] as AnyType[]
  const seenCodes = new Set<number>()

  function collectLeafNodes(node) {
    if (!node.children || node.children.length === 0) {
      if (!seenCodes.has(node.code)) {
        leafNodes.push(node)
        seenCodes.add(node.code)
      }
    } else {
      node.children.forEach(collectLeafNodes)
    }
  }

  treeData.forEach(collectLeafNodes)
  return leafNodes
}

export const getAllIds = (arr, key = 'id', level = 1) => {
  const ids = [] as string[]

  function traverseMenu(node, currentLevel) {
    if (currentLevel <= level) {
      ids.push(node[key])
    }

    if (node.children && node.children.length > 0 && currentLevel < level) {
      node.children.forEach((child) => {
        traverseMenu(child, currentLevel + 1)
      })
    }
  }

  arr.forEach((menu) => {
    traverseMenu(menu, 1)
  })

  return ids
}

function findCheckedLeafNodes(
  node,
  path = [] as { code: number; value: string }[],
  result = [] as { code: number; value: string }[][]
) {
  // 如果当前节点是叶子节点（没有子节点或 children 为 null）并且 checked 为 true，则将路径添加到结果中
  if (node.checked && (!node.children || node.children.length === 0)) {
    result.push([...path, { code: node.code, value: node.value }])
  }
  // 如果当前节点有子节点，则递归遍历子节点
  if (node.children) {
    for (const child of node.children) {
      findCheckedLeafNodes(child, [...path, { code: node.code, value: node.value }], result)
    }
  }
  return result
}

export function getCheckedPaths(tree) {
  const result = []
  for (const node of tree) {
    findCheckedLeafNodes(node, [], result)
  }
  return result
}

export const genTableRow = (arr) =>
  arr.map((path: { code: number; value: string }[]) => {
    const obj = path.reduce((a, { code, value }, idx) => {
      a[`level${idx}`] = { code, value }
      return a
    }, {})
    return { ...obj, code: path[path.length - 1]?.code }
  })

export const genTableFromTree = (tree) => genTableRow(getCheckedPaths(tree))

// isAlone 当前分级是否需要独立统计(剔除故障列表需要按一级分类维度独立统计)
export function countLeafNodesAtEachLevel(tree, level = 0, result = {}, isAlone = false) {
  for (const item of tree) {
    if (isAlone) {
      result[item.code] = {}
    } else if (result[level]) {
      result[level]++
    } else {
      result[level] = 1
    }

    // 如果当前节点是叶子节点，增加对应层级的计数
    if (item?.children?.length) {
      // 如果不是叶子节点，递归遍历子节点
      countLeafNodesAtEachLevel(item?.children, level + 1, isAlone ? result[item.code] : result)
    }
  }

  return result
}

function findCheckedLeafNodesById(
  node,
  id,
  path = [] as { code: number; value: string }[],
  result = [] as { code: number; value: string }[][]
) {
  // 如果当前节点是叶子节点（没有子节点或 children 为 null）并且 checked 为 true，则将路径添加到结果中
  if (node.code === id && (!node.children || node.children.length === 0)) {
    result.push([...path, { code: node.code, value: node.value }])
  }
  // 如果当前节点有子节点，则递归遍历子节点
  if (node.children) {
    for (const child of node.children) {
      findCheckedLeafNodesById(child, id, [...path, { code: node.code, value: node.value }], result)
    }
  }
  return result
}

export function getCheckedPathsById(tree, id) {
  const result = []
  for (const node of tree) {
    findCheckedLeafNodesById(node, id, [], result)
  }
  return result
}

// 按照level0的维度构建统计数组
export function markLevel0Array(list) {
  const idxMap = {}
  const result: AnyType[] = []

  let idxCount = 0

  for (const item of list) {
    const firstLevelCode = item?.level0?.code

    if (firstLevelCode) {
      if (idxMap[firstLevelCode] === undefined) {
        idxMap[firstLevelCode] = idxCount++

        result[idxMap[firstLevelCode]] = []
      }

      result[idxMap[firstLevelCode]].push(item)
    }
  }

  return result
}

// 统计剔除故障每一个维度下的数据总和
export function sumErrorTotal(list: AnyType[][] = [], cateTotal) {
  return list.map((item) => {
    const level0 = item[0]?.level0?.value
    const level0Code = item[0]?.level0?.code
    const totalInfo = cateTotal[level0Code]
    const level1List = (item || []).map((errorItem) => errorItem?.level1?.code)
    const level2List = (item || []).map((errorItem) => errorItem?.level2?.code)
    const level3List = (item || []).map((errorItem) => errorItem?.level3?.code)
    const level1Count = new Set(level1List).size
    const level2Count = new Set(level2List).size
    const level3Count = new Set(level3List).size

    return {
      level0,
      level0Code,
      level1Count,
      level1Total: totalInfo['1'],
      level2Count,
      level2Total: totalInfo['2'],
      level3Count,
      level3Total: totalInfo['3'],
    }
  })
}

// 统计剔除特批原因的数据总和
export function sumReasonTotal(list: AnyType[] = [], totalInfo) {
  const level0List = list.map((item) => item?.level0?.code)
  const level1List = list.map((item) => item?.level1?.code)
  const level0Count = new Set(level0List).size
  const level1Count = new Set(level1List).size

  // 空数据额外处理
  if (!level0Count) {
    return []
  }

  return [
    {
      level0Count,
      level0Total: totalInfo['0'],
      level1Count,
      level1Total: totalInfo['1'],
    },
  ]
}

export const getAllParentIds = (treeData: AnyType[]): (string | number)[] => {
  const parentIds: (string | number)[] = []

  const traverse = (nodes: AnyType[]) => {
    nodes.forEach((node) => {
      // 如果节点有子节点，说明是父节点
      if (node.children && node.children.length > 0) {
        parentIds.push(node.id)
        traverse(node.children)
      }
    })
  }

  traverse(treeData || [])
  return parentIds
}

/**
 * @param arr 待过滤的菜单树形结构
 * @param keyword 过滤关键字
 * @returns 过滤后的菜单树形结构
 * @description 过滤规则：
 *  1. 父菜单的 title 包含关键字，则保留该父菜单以及子菜单，并追溯保留所有父菜单；
 *  2. 子菜单的 title 包含关键字，则保留该子菜单以及所有父菜单，并删除同级子菜单中不包含关键字的菜单。
 */
export const filterNodes = (arr, keyword: string, keyValue = 'title') => {
  const newArr = cloneDeep(arr)
  const upperKeyword = keyword.toUpperCase()
  return newArr.filter((node) => {
    if (node[keyValue]?.toUpperCase().includes(upperKeyword)) {
      return true
    } else if (node.children.length > 0) {
      const filteredChildren = filterNodes(node.children, keyword, keyValue)
      if (filteredChildren.length > 0) {
        node.children = filteredChildren
        return true
      }
    }
    return false
  })
}
