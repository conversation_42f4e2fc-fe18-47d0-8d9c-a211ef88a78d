import { CheckSelectDataItem } from '@hi-ui/check-select'
import { NOOP_ARR } from './noop'

type FetchedDataType = AnyType

export const getPaginationData = (data: FetchedDataType = {}) => {
  const { list = NOOP_ARR, total = 0 } = data || {}
  return {
    list,
    total,
  }
}

export const getPaginationDataWithColumns = (res: FetchedDataType = {}) => {
  const { rowPage, columnList = NOOP_ARR } = res || {}
  const { data: list = [], total = 0 } = rowPage || {}
  return {
    list,
    total,
    columnList,
  }
}

export const getPaginationWithColumns = (res: FetchedDataType = {}) => {
  const { dataList: list = [], titleList = NOOP_ARR, total = 0 } = res || {}
  return {
    list,
    total,
    columnList: titleList,
  }
}

export const getPaginationDataNewProjectCost = (fetchedData: FetchedDataType = {}) => {
  const { page } = fetchedData || {}
  const { total, data: list } = page || {}
  return {
    list,
    total,
  }
}

export const configFilterOptionFn = (keyword: string, item: CheckSelectDataItem) => {
  const newKeyword = keyword.trim()
  if (!newKeyword) {
    return true
  } else {
    if (newKeyword.includes('G')) {
      return (item?.title as string).includes(newKeyword)
    } else {
      let str = ''
      if (newKeyword.includes('+')) {
        const idx = newKeyword.indexOf('+')
        str = newKeyword.slice(0, idx) + 'G' + newKeyword.slice(idx)
      } else {
        str = newKeyword
      }
      return (item?.title as string).includes(str)
    }
  }
}
