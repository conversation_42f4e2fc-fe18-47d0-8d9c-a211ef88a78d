export const initSDK = async (str = '') => {
  const sdkService = window?.shared_sdk_one_service
  console.log({ sdkService, sdk: window?.shared_sdk_one_service, str })
  try {
    if (!sdkService) {
      return null
    }
    if (sdkService.sdk) {
      return sdkService.sdk
    }
    sdkService.init()
    const { whalesdk: whaleSDK } = (await sdkService.get('./index'))()
    window.shared_sdk_one_service.sdk = whaleSDK
    return whaleSDK
  } catch (error) {
    console.error(error)
  }
  return null
}

// 开启定时器轮询
export const loop = (condition, time = 0) => {
  return new Promise((resolve) => {
    const waitFunc = () => {
      const timer = setTimeout(() => {
        const shouldStop = typeof condition === 'function' ? condition() : condition
        clearTimeout(timer)
        shouldStop ? resolve(true) : waitFunc()
      }, time)
    }
    waitFunc()
  })
}
