/* eslint-disable @typescript-eslint/no-explicit-any */
import config from '@/config'

export const loadScript = (url, callback) => {
  if (document.querySelector(`[src="${url}"]`)) {
    return
  }
  const script = document.createElement('script') as any
  script.setAttribute('type', 'text/javascript')
  script.setAttribute('src', url)
  const head = document.getElementsByTagName('head')
  if (head.length) {
    head[0].appendChild(script)
  } else {
    document.documentElement.appendChild(script)
  }
  if (script.readyState) {
    // IE
    script.onreadystatechange = function () {
      if (script.readyState === 'loaded' || script.readyState === 'complete') {
        script.onreadystatechange = null
        callback?.()
      }
    }
  } else {
    // 其他浏览器
    script.onload = function () {
      console.timeEnd('whale sdk')
      if (url === config.WHALE_SDK_URL) {
        window.whale_sdk_loaded = true
      }
      callback?.()
    }
  }
}
