/**
 * 将数字格式化为百分比形式
 * @param num 要格式化的数字
 * @param precision 可选参数，表示需要精确到的小数位数，默认为2
 * @returns 返回格式化后的百分比字符串
 */
export function formatPercentage(num: number, precision = 2) {
  // 检查传入的参数是否为数字
  if (typeof num !== 'number') {
    return num // 如果不是数字，则直接返回原值
  }

  // 将数字乘以100后进行格式化，保留指定的小数位数
  const decimal = formatNumber(num * 100, precision)

  // 返回格式化后的数字加上百分号
  return `${decimal}%`
}

/**
 * 将数字四舍五入并格式化为最多保留N位小数，如果没有则不保留。
 * @param num 要格式化的数字
 * @param precision 可选参数，表示需要精确到的小位数值，默认为3
 * @returns 格式化后的字符串
 */
export function formatNumber(num: number, precision = 3): string {
  if (typeof num !== 'number') {
    return num
  }

  // 根据参数保留小数
  return num.toFixed(precision)
}
