import React from 'react'
import { RouteObject } from 'react-router-dom'
import { RequireAuth } from '@/components/require-auth'
import { componentAsyncLazyLoad as lazyLoad } from './lazyComp'

const PurchaseOrg = lazyLoad(() => import('@/views/gsc/pages/purchase-org'))
const StockOrg = lazyLoad(() => import('@/views/gsc/pages/stock-org'))
const BusinessPurchase = lazyLoad(() => import('@/views/gsc/pages/business-purchase'))
const BusinessStock = lazyLoad(() => import('@/views/gsc/pages/business-stock'))
const PurchaseStock = lazyLoad(() => import('@/views/gsc/pages/purchase-stock'))
const MpnPurchaseStock = lazyLoad(() => import('@/views/gsc/pages/mpn-purchase-stock'))
const ProjectStock = lazyLoad(() => import('@/views/gsc/pages/project-stock'))
const FactoryStock = lazyLoad(() => import('@/views/gsc/pages/factory-stock'))
const WarehouseStock = lazyLoad(() => import('@/views/gsc/pages/warehouse-stock'))
const ComponentStock = lazyLoad(() => import('@/views/gsc/pages/component-stock'))
const ManualTodoHandle = lazyLoad(() => import('@/views/gsc/pages/manual-todo-handle'))
const ManualTodoManage = lazyLoad(() => import('@/views/gsc/pages/manual-todo-manage'))
const ManualTodoEdit = lazyLoad(() => import('@/views/gsc/pages/manual-todo-edit'))
const ManualTodoHistory = lazyLoad(() => import('@/views/gsc/pages/manual-todo-history'))
const MaterialVis = lazyLoad(() => import('@/views/gsc/pages/mat-logistics'))
const BIForm = lazyLoad(() => import('@/views/gsc/pages/bi-management/BIForm'))
const BIList = lazyLoad(() => import('@/views/gsc/pages/bi-management/BIList'))
const ProQuickCheck = lazyLoad(() => import('@/views/gsc/pages/pro-quick-check'))
const TodoList = lazyLoad(() => import('@/views/gsc/pages/todo-management/TodoList'))
const TodoConfigForm = lazyLoad(() => import('@/views/gsc/pages/todo-management/TodoConfigForm'))
const NotifyList = lazyLoad(() => import('@/views/gsc/pages/todo-management/NotifyList'))
const NotifyForm = lazyLoad(() => import('@/views/gsc/pages/todo-management/NotifyForm'))
const TodoDetail = lazyLoad(() => import('@/components/todo-detail/index'))
const OuterTodoList = lazyLoad(() => import('@/views/gsc/outer/outer-todo-management/TodoList'))
const EmployeeList = lazyLoad(() => import('@/views/gsc/outer/outer-todo-management/EmployeeList'))

const OuterTodoConfigForm = lazyLoad(
  () => import('@/views/gsc/outer/outer-todo-management/TodoConfigForm')
)
const EmployeeForm = lazyLoad(() => import('@/views/gsc/outer/outer-todo-management/EmployeeForm'))
const GeneralAnnounce = lazyLoad(() => import('@/components/general-announce'))
const AnnounceForm = lazyLoad(() => import('@/components/general-announce/AnnounceForm'))
const VisitorTrack = lazyLoad(() => import('@/views/gsc/pages/visitor-track'))
const VisitorTrackOuter = lazyLoad(() => import('@/views/gsc/pages/visitor-track-outer'))
const FunctionTrack = lazyLoad(() => import('@/views/gsc/pages/function-count-track'))
const FrequencyTrack = lazyLoad(() => import('@/views/gsc/pages/frequency-track'))
const SkuPurChaseStock = lazyLoad(() => import('@/views/gsc/pages/sku-purchase-stock'))
const TodoSearch = lazyLoad(() => import('@/views/gsc/pages/todo-search'))
const TodoSearchDetail = lazyLoad(() => import('@/views/gsc/pages/todo-search/detail'))
const MaterialBill = lazyLoad(() => import('@/views/gsc/pages/material-bill'))
const UserClosure = lazyLoad(() => import('@/views/gsc/pages/user-closure'))
const UserClosureDetail = lazyLoad(() => import('@/views/gsc/pages/user-closure-detail'))
const TranslateLibrary = lazyLoad(() => import('@/views/gsc/pages/translate-library'))
const OpManual = lazyLoad(() => import('@/views/gsc/pages/operation-manual'))
const TrackBrief = lazyLoad(() => import('@/views/gsc/pages/track-brief'))
const Feedback = lazyLoad(() => import('@/views/gsc/pages/feedback'))
const TrackAnalysis = lazyLoad(() => import('@/views/gsc/pages/track-analysis'))
const MenuNoticeConfig = lazyLoad(() => import('@/views/gsc/pages/menu-notice-config'))
const TodoExecuteConfig = lazyLoad(() => import('@/views/gsc/pages/todo-execute-config'))

export const gscRoutes: RouteObject[] = [
  {
    path: 'todo-execute-config',
    element: (
      <RequireAuth>
        <TodoExecuteConfig />
      </RequireAuth>
    ),
  },
  {
    path: 'user-closure',
    element: (
      <RequireAuth>
        <UserClosure />
      </RequireAuth>
    ),
  },
  {
    path: 'user-closure-detail',
    element: (
      <RequireAuth>
        <UserClosureDetail />
      </RequireAuth>
    ),
  },
  {
    path: 'announce-list',
    element: (
      <RequireAuth>
        <GeneralAnnounce />
      </RequireAuth>
    ),
  },
  {
    path: 'create-announce',
    element: <AnnounceForm />,
  },
  {
    path: 'update-announce/:id',
    element: <AnnounceForm />,
  },
  {
    path: '/manual-todo',
    children: [
      {
        path: 'handle/:sourceId',
        element: (
          <RequireAuth>
            <ManualTodoHandle />
          </RequireAuth>
        ),
      },
      {
        path: 'manage',
        element: (
          <RequireAuth>
            <ManualTodoManage />
          </RequireAuth>
        ),
      },
      {
        path: 'edit/:category/:type',
        element: (
          <RequireAuth>
            <ManualTodoEdit />
          </RequireAuth>
        ),
      },
      {
        path: 'history/:taskId/:deptLevel234Name/:fullName/:startTime',
        element: (
          <RequireAuth>
            <ManualTodoHistory />
          </RequireAuth>
        ),
      },
    ],
  },
  {
    path: '/todo-search',
    children: [
      {
        path: 'list',
        element: <TodoSearch />,
      },
      {
        path: 'detail/:id',
        element: <TodoSearchDetail />,
      },
    ],
  },
  {
    path: '/todo/detail',
    element: (
      <RequireAuth>
        <TodoDetail />
      </RequireAuth>
    ),
  },
  {
    path: 'linkvis',
    children: [
      {
        path: 'matlogistics',
        element: (
          <RequireAuth>
            <MaterialVis />
          </RequireAuth>
        ),
      },
      {
        path: 'bom-properties',
        element: (
          <RequireAuth>
            <ProQuickCheck />
          </RequireAuth>
        ),
      },
    ],
  },
  {
    path: '/bi-manage',
    children: [
      {
        path: 'list',
        element: <BIList />,
      },
      {
        path: 'create',
        element: <BIForm />,
      },
      {
        path: 'update/:id',
        element: <BIForm />,
      },
    ],
  },
  {
    path: '/outer-todo-config',
    children: [
      {
        path: 'list',
        element: <OuterTodoList />,
      },
      {
        path: 'employee',
        element: <EmployeeList />,
      },
      {
        path: 'create',
        element: <OuterTodoConfigForm />,
      },
      {
        path: 'update',
        element: <OuterTodoConfigForm />,
      },
      {
        path: 'update-employee/:id',
        element: <EmployeeForm />,
      },
    ],
  },
  {
    path: '/todo-config',
    children: [
      {
        path: 'list',
        element: <TodoList />,
      },
      {
        path: 'create',
        element: <TodoConfigForm />,
      },
      {
        path: 'update/:id',
        element: <TodoConfigForm />,
      },
      {
        path: 'notify-list',
        element: <NotifyList />,
      },
      {
        path: 'create-notify',
        element: <NotifyForm />,
      },
      {
        path: 'update-notify/:id',
        element: <NotifyForm />,
      },
    ],
  },
  {
    path: 'material-bill',
    element: <MaterialBill />,
  },
  {
    path: 'multi-org',
    children: [
      {
        path: 'purchase-org',
        element: <PurchaseOrg />,
      },
      {
        path: 'stock-org',
        element: <StockOrg />,
      },
      {
        path: 'business-purchase',
        element: <BusinessPurchase />,
      },
      {
        path: 'business-stock',
        element: <BusinessStock />,
      },
      {
        path: 'purchase-stock',
        element: <PurchaseStock />,
      },
      {
        path: 'mpn-purchase-stock',
        element: <MpnPurchaseStock />,
      },
      {
        path: 'project-stock',
        element: <ProjectStock />,
      },
      {
        path: 'factory-stock',
        element: <FactoryStock />,
      },
      {
        path: 'warehouse-stock',
        element: <WarehouseStock />,
      },
      {
        path: 'component-stock',
        element: <ComponentStock />,
      },
      {
        path: 'sku-purchase-stock',
        element: <SkuPurChaseStock />,
      },
    ],
  },
  {
    path: 'track',
    children: [
      {
        path: 'function',
        element: <FunctionTrack />,
      },
      {
        path: 'visitor',
        element: <VisitorTrack />,
      },
      {
        path: 'visitor-outer',
        element: <VisitorTrackOuter />,
      },
      {
        path: 'frequency',
        element: <FrequencyTrack />,
      },
    ],
  },
  {
    path: 'track-brief',
    element: <TrackBrief />,
  },
  {
    path: 'track-analysis',
    element: <TrackAnalysis />,
  },
  {
    path: 'feedback',
    element: <Feedback />,
  },
  {
    path: 'operation-manual',
    element: <OpManual />,
  },
  {
    path: 'translate-library',
    element: <TranslateLibrary />,
  },
  {
    path: '/menu-notice-config',
    element: <MenuNoticeConfig />,
  },
]
