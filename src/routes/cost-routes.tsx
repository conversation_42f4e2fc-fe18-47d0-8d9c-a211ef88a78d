import React from 'react'
import { RouteObject } from 'react-router-dom'

import { RequireAuth } from '@/components/require-auth'
import { componentAsyncLazyLoad as lazyLoad } from './lazyComp'

const MoveAveragePrice = lazyLoad(() => import('@/views/gsc/cost/move-average-price'))
const NewProjectCost = lazyLoad(() => import('@/views/gsc/cost/new-project-cost'))
const MpnidCost = lazyLoad(() => import('@/views/gsc/cost/mpnid-cost'))
const CvpnCost = lazyLoad(() => import('@/views/gsc/cost/cvpn-cost'))
const ProjectCost = lazyLoad(() => import('@/views/gsc/cost/project-cost'))
const ProjectCostData = lazyLoad(() => import('@/views/gsc/cost/project-cost-data'))
const PredictExchangeRate = lazyLoad(() => import('@/views/gsc/cost/predict-exchange-rate'))
const PredictUpload = lazyLoad(() => import('@/views/gsc/cost/predict-upload'))
const AllLifeCost = lazyLoad(() => import('@/views/gsc/cost/all-life-cost'))
const NewCostReductionV2 = lazyLoad(() => import('@/views/gsc/cost/new-cost-reduction-v2'))
const PredActualDelta = lazyLoad(() => import('@/views/gsc/cost/pred-actual-delta'))
const ProjectList = lazyLoad(() => import('@/views/gsc/cost/project-list'))
const PriceMaintenance = lazyLoad(() => import('@/views/gsc/cost/price-maintenance'))
const NewProjectConfig = lazyLoad(() => import('@/views/gsc/cost/new-project-config'))
const CostBusiness = lazyLoad(() => import('@/views/gsc/cost/cost-business'))
const CostMappingRelationships = lazyLoad(
  () => import('@/views/gsc/cost/cost-mapping-relationships')
)
const TariffOnline = lazyLoad(() => import('@/views/gsc/cost/tariff-online'))
const NewProjectList = lazyLoad(() => import('@/views/gsc/cost/new-project-list'))
const NewProjectConfigForm = lazyLoad(() => import('@/views/gsc/cost/new-project-maintenance'))
const MaterialPsi = lazyLoad(() => import('@/views/gsc/cost/material-psi'))
const SalePricing = lazyLoad(() => import('@/views/gsc/cost/sale-pricing'))
const SPDetail = lazyLoad(() => import('@/views/gsc/cost/sale-pricing/sp-detail'))
const RadioMaintenance = lazyLoad(() => import('@/views/gsc/cost/radio-maintenance'))
const SubjectUpload = lazyLoad(() => import('@/views/gsc/cost/subject-upload'))
const PriceHistory = lazyLoad(() => import('@/views/gsc/cost/price-history'))
const PurchaseChannel = lazyLoad(() => import('@/views/gsc/pages/purchase-channel'))
const ProfitLoss = lazyLoad(() => import('@/views/gsc/cost/profit-loss'))
const ProjectCostJustify = lazyLoad(() => import('@/views/gsc/cost/project-cost-justify'))
const CostManagement = lazyLoad(() => import('@/views/gsc/cost/convolution/cost-management'))
const VolumeCostQuery = lazyLoad(() => import('@/views/gsc/cost/convolution/volume-cost-query'))
const CommodityCost = lazyLoad(() => import('@/views/gsc/cost/convolution/commodity-cost'))
const GenerationalCost = lazyLoad(() => import('@/views/gsc/cost/generational-cost'))
const Rebate = lazyLoad(() => import('@/views/gsc/cost/rebate'))

export const costRoutes: RouteObject[] = [
  {
    path: 'generational-cost',
    element: (
      <RequireAuth>
        <GenerationalCost />
      </RequireAuth>
    ),
  },
  {
    path: 'rebate',
    element: (
      <RequireAuth>
        <Rebate />
      </RequireAuth>
    ),
  },
  {
    path: 'convolution',
    children: [
      {
        path: 'cost-management',
        element: (
          <RequireAuth>
            <CostManagement />
          </RequireAuth>
        ),
      },
      {
        path: 'volume-cost-query',
        element: (
          <RequireAuth>
            <VolumeCostQuery />
          </RequireAuth>
        ),
      },
      {
        path: 'commodity-cost',
        element: (
          <RequireAuth>
            <CommodityCost />
          </RequireAuth>
        ),
      },
    ],
  },
  {
    path: 'profit-loss',
    element: (
      <RequireAuth>
        <ProfitLoss />
      </RequireAuth>
    ),
  },
  {
    path: 'project-cost-justify',
    element: (
      <RequireAuth>
        <ProjectCostJustify />
      </RequireAuth>
    ),
  },
  {
    path: 'purchase-channel',
    element: (
      <RequireAuth>
        <PurchaseChannel />
      </RequireAuth>
    ),
  },
  {
    path: 'new-project-cost',
    element: (
      <RequireAuth>
        <NewProjectCost />
      </RequireAuth>
    ),
  },
  {
    path: 'new-project-config',
    element: (
      <RequireAuth>
        <NewProjectConfig />
      </RequireAuth>
    ),
  },
  {
    path: 'move-average-price',
    element: (
      <RequireAuth>
        <MoveAveragePrice />
      </RequireAuth>
    ),
  },
  {
    path: 'cost-business',
    element: (
      <RequireAuth>
        <CostBusiness />
      </RequireAuth>
    ),
  },
  {
    path: 'cost-mapping-relationships',
    element: (
      <RequireAuth>
        <CostMappingRelationships />
      </RequireAuth>
    ),
  },
  {
    path: 'new-cost-reduction',
    element: <NewCostReductionV2 />,
  },
  {
    path: 'pred-actual-delta',
    element: <PredActualDelta />,
  },
  {
    path: 'predict-upload',
    element: <PredictUpload />,
  },
  {
    path: 'predict-exchange-rate',
    element: (
      <RequireAuth>
        <PredictExchangeRate />
      </RequireAuth>
    ),
  },
  {
    path: 'metearial-cost',
    children: [
      {
        path: 'mpnid',
        element: <MpnidCost />,
      },
      {
        path: 'cvpn',
        element: <CvpnCost />,
      },
      {
        path: 'project',
        element: <ProjectCost />,
      },
      {
        path: 'project-data',
        element: <ProjectCostData />,
      },
    ],
  },
  {
    path: 'all-life-cost',
    element: <AllLifeCost />,
  },
  {
    path: 'cost-submit',
    children: [
      {
        path: 'project-list',
        element: <ProjectList />,
      },
      {
        path: 'new-project-list',
        element: <NewProjectList />,
      },
      {
        path: 'price-maintenance',
        element: <PriceMaintenance />,
      },
      {
        path: 'create',
        element: <NewProjectConfigForm />,
      },
      {
        path: 'update/:id',
        element: <NewProjectConfigForm />,
      },
    ],
  },
  {
    path: 'tariff-online',
    element: <TariffOnline />,
  },
  {
    path: 'material-psi',
    element: <MaterialPsi />,
  },
  {
    path: 'sale-pricing',
    children: [
      {
        path: '',
        element: <SalePricing />,
      },
      {
        path: 'add',
        element: <SPDetail />,
      },
      {
        path: 'detail/:id',
        element: <SPDetail />,
      },
    ],
  },
  {
    path: 'radio-maintenance',
    element: <RadioMaintenance />,
  },
  {
    path: 'price-history',
    element: (
      <RequireAuth>
        <PriceHistory />
      </RequireAuth>
    ),
  },
  {
    path: 'subject-upload',
    element: (
      <RequireAuth>
        <SubjectUpload />
      </RequireAuth>
    ),
  },
]
