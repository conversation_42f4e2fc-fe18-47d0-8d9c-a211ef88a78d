import React from 'react'
import { RouteObject } from 'react-router-dom'
import { componentAsyncLazyLoad as lazyLoad } from './lazyComp'

// 全场景追溯
import * as FullSceneTrace from '@/views/qms/full-scene-trace'

const ReportAnalysisOverview = lazyLoad(
  () => import('@/views/qms/quality-analysis-report/overview')
)
const ReportAnalysisFault = lazyLoad(() => import('@/views/qms/quality-analysis-report/fault'))
const ReportAnalysisSpecial = lazyLoad(() => import('@/views/qms/quality-analysis-report/special'))
const ReportAnalysisMaterial = lazyLoad(
  () => import('@/views/qms/quality-analysis-report/material')
)
const Billboard = lazyLoad(() => import('@/views/qms/billboard'))
const NpsBoard = lazyLoad(() => import('@/views/qms/npsboard'))
const ServiceConfig = lazyLoad(() => import('@/views/qms/service-config'))
const FamilyStatus = lazyLoad(() => import('@/views/qms/family-status'))
const UFIBoard = lazyLoad(() => import('@/views/qms/ufi/UFIBoard'))
const PQMBoard = lazyLoad(() => import('@/views/qms/pqmboard'))
const PRDBoard = lazyLoad(() => import('@/views/qms/prdboard'))

export const qmsRoutes: RouteObject[] = [
  {
    path: '/family-status',
    element: <FamilyStatus />,
  },
  {
    path: '/report/analysis',
    children: [
      {
        path: 'general',
        element: <ReportAnalysisOverview />,
      },
      {
        path: 'fault',
        element: <ReportAnalysisFault />,
      },
      {
        path: 'special',
        element: <ReportAnalysisSpecial />,
      },
      {
        path: 'material',
        element: <ReportAnalysisMaterial />,
      },
    ],
  },
  {
    path: '/service-config',
    element: <ServiceConfig />,
  },
  {
    path: '/full-scene-trace',
    children: [
      {
        // 单个IMEI/SN追溯
        path: 'single',
        element: <FullSceneTrace.Single.Entry />,
        children: [
          {
            /** 整机工厂 */ path: 'machine-factory',
            element: <FullSceneTrace.Single.WholeMachineFactoryDrawer />,
          },
          {
            /** 制造过站 */ path: 'machine-factory-mfg-process',
            element: <FullSceneTrace.Single.MfgProcessDrawer />,
          },
          {
            /** 销售信息 */ path: 'sale-detail',
            element: <FullSceneTrace.Single.SaleDetailDrawer />,
          },
          {
            /** 用户信息 */ path: 'user-detail',
            element: <FullSceneTrace.Single.UserDetailDrawer />,
          },
          {
            /** 修整工厂维修单 */ path: 'user-repair-order',
            element: <FullSceneTrace.Single.UserRepairOrderDrawer />,
          },
          {
            /** 服务与反馈 */ path: 'user-service-and-feedback',
            element: <FullSceneTrace.Single.UserServiceAndFeedbackDrawer />,
          },
          {
            // 顺序不可调换
            /** 售后服务单-自动化检测详情 */ path: 'user-after-sale-service-order/test-result',
            element: <FullSceneTrace.Single.AutoDetectionDetailDrawer />,
          },
          {
            /** 售后服务单 */ path: 'user-after-sale-service-order',
            element: <FullSceneTrace.Single.UserServiceOrderDrawer />,
          },
        ],
      },
      {
        // 物料追溯
        path: 'material',
        element: <FullSceneTrace.Material />,
      },
    ],
  },
  {
    path: '/qms-quality',
    children: [
      {
        path: 'hardware',
        element: <Billboard />,
      },
      {
        path: 'nps',
        element: <NpsBoard />,
      },
      {
        path: 'ufi',
        element: <UFIBoard />,
        children: [
          {
            path: 'ufi-phone-whale',
            element: <UFIBoard />,
          },
          {
            path: 'ufi-os-whale',
            element: <UFIBoard />,
          },
        ],
      },
      {
        path: 'pqm',
        element: <PQMBoard />,
      },
      {
        path: 'prd',
        element: <PRDBoard />,
      },
    ],
  },
]
