import React from 'react'
import { RouteObject } from 'react-router-dom'
import { RequireAuth } from '@/components/require-auth'
import { componentAsyncLazyLoad as lazyLoad } from './lazyComp'

const EcoBomCostList = lazyLoad(() => import('@/views/gsc/eco/bom-cost/list'))
const EcoBomCostDetail = lazyLoad(() => import('@/views/gsc/eco/bom-cost/detail'))
const ManualUpload = lazyLoad(() => import('@/views/gsc/eco/manual-upload'))

export const ecoRoutes: RouteObject[] = [
  {
    path: 'eco',
    children: [
      {
        path: 'bom-cost',
        children: [
          {
            path: 'list',
            element: (
              <RequireAuth>
                <EcoBomCostList />
              </RequireAuth>
            ),
          },
          {
            path: 'detail',
            element: (
              <RequireAuth>
                <EcoBomCostDetail />
              </RequireAuth>
            ),
          },
        ],
      },
      {
        path: 'manual-upload',
        element: (
          <RequireAuth>
            <ManualUpload />
          </RequireAuth>
        ),
      },
    ],
  },
]
