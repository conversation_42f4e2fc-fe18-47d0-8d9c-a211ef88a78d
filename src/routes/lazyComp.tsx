import React from 'react'
import { withAsyncLazyLoad, withAsyncLoad } from '@mi/update-notifier-react'

export const componentAsyncLoad = (com) => {
  const LoadComponent = withAsyncLoad(com)
  const Component = (props) => (LoadComponent ? <LoadComponent {...props} /> : null)
  return Component
}

export const componentAsyncLazyLoad = (impFun) => {
  const LazyLoadComponent = withAsyncLazyLoad(impFun)
  const Component = (props) => (LazyLoadComponent ? <LazyLoadComponent {...props} /> : null)
  return Component
}
