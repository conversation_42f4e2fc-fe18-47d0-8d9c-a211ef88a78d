import React from 'react'
import { RouteObject } from 'react-router-dom'
import { Layout } from '@/views/layout'
import { gscRoutes } from './gsc-routes'
import { costRoutes } from './cost-routes'
import { qmsRoutes } from './qms-routes'
import { ecoRoutes } from './eco-routes'
import { Exception404 } from '@/views/exception/404'
import { OffLine } from '@/views/exception/OffLine'
import { DashboardBlank } from '@/views/exception/DashboardBlank'
import NoAuth from '@/views/no-auth'
import CostNoAuth from '@/views/gsc/cost/cost-no-auth'
import { componentAsyncLoad, componentAsyncLazyLoad as lazyLoad } from './lazyComp'

const Exception404Component = componentAsyncLoad(Exception404)
const NoAuthComponent = componentAsyncLoad(NoAuth)
const CostNoAuthComponent = componentAsyncLoad(CostNoAuth)
const OffLineComponent = componentAsyncLoad(OffLine)
const DashboardBlankComponent = componentAsyncLoad(DashboardBlank)
const OuterTodo = lazyLoad(() => import('@/views/gsc/outer/outer-todo'))

export const routes: RouteObject[] = [
  {
    path: '/',
    element: <Layout />,
    children: [
      ...gscRoutes,
      ...costRoutes,
      ...ecoRoutes,
      ...qmsRoutes,
      {
        path: 'hidden',
        element: <></>,
      },
      {
        path: '/exception',
        children: [
          {
            path: '403',
            element: <NoAuthComponent />,
          },
          {
            path: 'cost-403',
            element: <CostNoAuthComponent />,
          },
          {
            path: '404',
            element: <Exception404Component />,
          },
          {
            path: 'offline',
            element: <OffLineComponent />,
          },
          {
            path: 'dashboard-blank',
            element: <DashboardBlankComponent />,
          },
        ],
      },
      {
        path: '/outer-todo',
        element: <OuterTodo />,
      },
    ],
  },
]
