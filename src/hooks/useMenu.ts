import { useMenuContext } from '@/context/MenuContext'
import { dealMenuData } from '@/views/home'
import { MainMenuItem, microApp } from '@/utils/micro-app'
import { useEffect } from 'react'
import { useSafeState } from 'ahooks'
import { traverseTree } from '@/utils/traverseTree'
import { gscMenuPrefix } from '@/constants'

// 需要精确匹配的路由数组
const exactMatchPaths = [
  gscMenuPrefix + '/dashboard/view',
  gscMenuPrefix + '/dashboard/views',
  gscMenuPrefix + '/todo/dashboard',
]

export const useCurrentMenu = () => {
  const { pathname, search } = location
  const { allMenus, setAllMenus } = useMenuContext()
  const [currentMenu, setCurrentMenu] = useSafeState<MainMenuItem | undefined>()

  useEffect(() => {
    const menuList = allMenus.length
      ? allMenus
      : dealMenuData(microApp.getProps()?.appInfo?.menuMap || {})
    const suffix = exactMatchPaths.includes(pathname) ? pathname + search : pathname
    const menu = menuList.find(
      (item) => encodeURI(item.menuRoute.replace(/\/+$/g, '')) === suffix.replace(/\/+$/g, '')
    )
    setCurrentMenu(menu)
    !allMenus.length && setAllMenus(menuList)
  }, [allMenus, pathname, search, setAllMenus, setCurrentMenu])

  return { currentMenu, menuId: currentMenu?.id }
}

// 获取最外层路由
export const useMainMenu = () => {
  const { allMenus } = useMenuContext()
  const parentMenu = allMenus.filter((menu) => !menu.parentId) || []
  traverseTree(parentMenu, (child, parent) => {
    child.parent = parent
  })
  return { parentMenu }
}
