/* eslint-disable @typescript-eslint/no-explicit-any */
import { NOOP_ARR } from '@/utils/noop'
import { useRequest } from 'ahooks'
import { BaseResponse } from '@/utils/request'
import { AnyType } from '@/constants'
// 成本降幅多级选择框
export const useGetCategory = (fun: () => Promise<BaseResponse<AnyType>>) => {
  const { data } = useRequest(() => fun())
  const dataArrOrObj = data?.data ?? NOOP_ARR
  // 构造三级选择框的数据，@@隔开不同级别
  return Object.keys(dataArrOrObj)?.reduce((a: AnyType[], b: AnyType) => {
    const temp = { title: b, id: b, children: [] }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _res = Object.keys(dataArrOrObj[b]).reduce((c: AnyType[], d: AnyType) => {
      const temp1 = { title: d, id: `${b}@@${d}`, children: [] as AnyType[] }
      const thirdArr = dataArrOrObj[b][d].map((item) => {
        return { id: `${b}@@${d}@@${item}`, title: item }
      })
      temp1.children = thirdArr
      c.push(temp1)
      return c
    }, temp.children)
    a.push(temp)
    return a
  }, [])
}
