import { useWindowResize } from './useWindowResize'

// 侧边栏响应式计算
export const useWidthResponsive = (width: number) => {
  if (width <= 1280) return 900
  if (width > 1280 && width <= 1700)
    return 900 + Number(parseInt(String(((1136 - 900) / (1700 - 1280)) * (width - 1280))))
  if (width > 1700 && width <= 1920)
    return 1136 + Number(parseInt(String(((1280 - 1136) / (1920 - 1700)) * (width - 1700))))
  // if (width > 1920) return 1280
  return 1280 // 到最后肯定大于1920
}

export const useDrawerResponsive = () => {
  const { width } = useWindowResize()
  const responsiveWidth = useWidthResponsive(width)
  return responsiveWidth
}
