import { renderHook } from '@testing-library/react-hooks'
import { describe, it, expect, vi } from 'vitest'
import { useRebateLedger } from '../useRebateLedger'

// Mock the context
const mockContextValue = {
  filter: { brandName: ['test'] },
  setFilter: vi.fn(),
  expanded: true,
  setExpanded: vi.fn(),
  topTab: 'ledger',
  setTopTab: vi.fn(),
  exChangeRateType: 'CNY',
  setExChangeRateType: vi.fn(),
  unionFilterInfo: { module1: 'info' },
  setUnionFilterInfo: vi.fn(),
  clickedModule: 'testModule',
  setClickedModule: vi.fn(),
}

vi.mock('@/context/RebateLedgerContext', () => ({
  RebateLedgerContext: {
    Provider: ({ children }: AnyType) => children,
  },
}))

vi.mock('react', async () => {
  const actual = await vi.importActual<AnyType>('react')
  return {
    ...actual,
    useContext: vi.fn(() => mockContextValue),
  }
})

describe('useRebateLedger', () => {
  it('should return context values', () => {
    const { result } = renderHook(() => useRebateLedger())

    expect(result.current).toEqual(mockContextValue)
  })

  it('should return filter value', () => {
    const { result } = renderHook(() => useRebateLedger())

    expect(result.current.filter).toEqual({ brandName: ['test'] })
  })

  it('should return expanded value', () => {
    const { result } = renderHook(() => useRebateLedger())

    expect(result.current.expanded).toBe(true)
  })

  it('should return topTab value', () => {
    const { result } = renderHook(() => useRebateLedger())

    expect(result.current.topTab).toBe('ledger')
  })

  it('should return exChangeRateType value', () => {
    const { result } = renderHook(() => useRebateLedger())

    expect(result.current.exChangeRateType).toBe('CNY')
  })

  it('should return unionFilterInfo value', () => {
    const { result } = renderHook(() => useRebateLedger())

    expect(result.current.unionFilterInfo).toEqual({ module1: 'info' })
  })

  it('should return clickedModule value', () => {
    const { result } = renderHook(() => useRebateLedger())

    expect(result.current.clickedModule).toBe('testModule')
  })

  it('should return all setter functions', () => {
    const { result } = renderHook(() => useRebateLedger())

    expect(typeof result.current.setFilter).toBe('function')
    expect(typeof result.current.setExpanded).toBe('function')
    expect(typeof result.current.setTopTab).toBe('function')
    expect(typeof result.current.setExChangeRateType).toBe('function')
    expect(typeof result.current.setUnionFilterInfo).toBe('function')
    expect(typeof result.current.setClickedModule).toBe('function')
  })

  it('should return the exact context value without modification', () => {
    const { result } = renderHook(() => useRebateLedger())

    expect(result.current).toBe(mockContextValue)
  })
})
