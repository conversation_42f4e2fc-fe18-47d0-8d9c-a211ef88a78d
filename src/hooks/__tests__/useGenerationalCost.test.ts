import { renderHook } from '@testing-library/react-hooks'
import { describe, it, expect, vi } from 'vitest'
import { useGenerationalCost } from '../useGenerationalCost'
import { GenerationalCostContext } from '@/context/GenerationalCost'
import React from 'react'

// 模拟Context值
const mockContextValue = {
  filter: { pageType: 'project', projectNames: ['test'] },
  setFilter: vi.fn(),
  expanded: true,
  setExpanded: vi.fn(),
  searched: false,
  setSearched: vi.fn(),
  baseline: 'test-baseline',
  setBaseline: vi.fn(),
}

// 创建测试用的Provider包装器
const createWrapper = (contextValue: AnyType = mockContextValue) => {
  const Wrapper = ({ children }: { children: React.ReactNode }) =>
    React.createElement(GenerationalCostContext.Provider, { value: contextValue }, children)
  return Wrapper
}

describe('useGenerationalCost Hook', () => {
  describe('基础功能测试', () => {
    it('应该返回Context中的所有值', () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useGenerationalCost(), { wrapper })

      expect(result.current.filter).toEqual({ pageType: 'project', projectNames: ['test'] })
      expect(result.current.expanded).toBe(true)
      expect(result.current.searched).toBe(false)
      expect(result.current.baseline).toBe('test-baseline')
    })

    it('应该返回Context中的所有setter函数', () => {
      const wrapper = createWrapper()
      const { result } = renderHook(() => useGenerationalCost(), { wrapper })

      expect(typeof result.current.setFilter).toBe('function')
      expect(typeof result.current.setExpanded).toBe('function')
      expect(typeof result.current.setSearched).toBe('function')
      expect(typeof result.current.setBaseline).toBe('function')
    })
  })

  describe('不同Context值测试', () => {
    it('应该正确返回空的初始状态', () => {
      const emptyContextValue = {
        filter: {},
        setFilter: vi.fn(),
        expanded: false,
        setExpanded: vi.fn(),
        searched: false,
        setSearched: vi.fn(),
        baseline: '',
        setBaseline: vi.fn(),
      }

      const wrapper = createWrapper(emptyContextValue)
      const { result } = renderHook(() => useGenerationalCost(), { wrapper })

      expect(result.current.filter).toEqual({})
      expect(result.current.expanded).toBe(false)
      expect(result.current.searched).toBe(false)
      expect(result.current.baseline).toBe('')
    })

    it('应该正确返回复杂的filter状态', () => {
      const complexContextValue = {
        filter: {
          pageType: 'category',
          projectNames: ['project1', 'project2'],
          saleSites: ['site1', 'site2'],
          configs: ['config1'],
          costMatCatLvl1Codes: ['cat1', 'cat2'],
          date: '2023-12-01',
          exchangeRateType: 'USD',
          tableType: 'MPN',
        },
        setFilter: vi.fn(),
        expanded: true,
        setExpanded: vi.fn(),
        searched: true,
        setSearched: vi.fn(),
        baseline: 'complex-baseline',
        setBaseline: vi.fn(),
      }

      const wrapper = createWrapper(complexContextValue)
      const { result } = renderHook(() => useGenerationalCost(), { wrapper })

      expect(result.current.filter).toEqual({
        pageType: 'category',
        projectNames: ['project1', 'project2'],
        saleSites: ['site1', 'site2'],
        configs: ['config1'],
        costMatCatLvl1Codes: ['cat1', 'cat2'],
        date: '2023-12-01',
        exchangeRateType: 'USD',
        tableType: 'MPN',
      })
      expect(result.current.searched).toBe(true)
      expect(result.current.baseline).toBe('complex-baseline')
    })
  })

  describe('函数引用稳定性测试', () => {
    it('setter函数应该保持引用稳定', () => {
      const wrapper = createWrapper()
      const { result, rerender } = renderHook(() => useGenerationalCost(), { wrapper })

      const initialSetters = {
        setFilter: result.current.setFilter,
        setExpanded: result.current.setExpanded,
        setSearched: result.current.setSearched,
        setBaseline: result.current.setBaseline,
      }

      // 重新渲染hook
      rerender()

      // 验证setter函数引用保持不变
      expect(result.current.setFilter).toBe(initialSetters.setFilter)
      expect(result.current.setExpanded).toBe(initialSetters.setExpanded)
      expect(result.current.setSearched).toBe(initialSetters.setSearched)
      expect(result.current.setBaseline).toBe(initialSetters.setBaseline)
    })
  })

  describe('类型安全性测试', () => {
    it('应该正确处理字符串类型的filter字段', () => {
      const stringFieldsContext = {
        filter: {
          pageType: 'project',
          exchangeRateType: 'CNY',
          tableType: 'CVPN',
          date: '2023-12-01',
          costMatCatLvl1Codes: 'single-category',
          costMatCatLvl2Codes: 'single-category-2',
          costMatCatLvl3Codes: 'single-category-3',
        },
        setFilter: vi.fn(),
        expanded: true,
        setExpanded: vi.fn(),
        searched: false,
        setSearched: vi.fn(),
        baseline: '',
        setBaseline: vi.fn(),
      }

      const wrapper = createWrapper(stringFieldsContext)
      const { result } = renderHook(() => useGenerationalCost(), { wrapper })

      expect(result.current.filter.pageType).toBe('project')
      expect(result.current.filter.exchangeRateType).toBe('CNY')
      expect(result.current.filter.tableType).toBe('CVPN')
      expect(result.current.filter.date).toBe('2023-12-01')
    })

    it('应该正确处理数组类型的filter字段', () => {
      const arrayFieldsContext = {
        filter: {
          projectNames: ['project1', 'project2'],
          saleSites: ['site1', 'site2', 'site3'],
          configs: ['config1'],
          phaseStages: ['stage1', 'stage2'],
          costMatCatLvl1Codes: ['cat1', 'cat2'],
          costMatCatLvl2Codes: ['subcat1'],
          costMatCatLvl3Codes: [],
        },
        setFilter: vi.fn(),
        expanded: false,
        setExpanded: vi.fn(),
        searched: true,
        setSearched: vi.fn(),
        baseline: 'array-test',
        setBaseline: vi.fn(),
      }

      const wrapper = createWrapper(arrayFieldsContext)
      const { result } = renderHook(() => useGenerationalCost(), { wrapper })

      expect(Array.isArray(result.current.filter.projectNames)).toBe(true)
      expect(result.current.filter.projectNames).toHaveLength(2)
      expect(result.current.filter.saleSites).toHaveLength(3)
      expect(result.current.filter.configs).toHaveLength(1)
      expect(result.current.filter.costMatCatLvl3Codes).toHaveLength(0)
    })
  })

  describe('边界情况测试', () => {
    it('应该处理null和undefined的filter值', () => {
      const nullContextValue = {
        filter: null as AnyType,
        setFilter: vi.fn(),
        expanded: false,
        setExpanded: vi.fn(),
        searched: false,
        setSearched: vi.fn(),
        baseline: null as AnyType,
        setBaseline: vi.fn(),
      }

      const wrapper = createWrapper(nullContextValue)
      const { result } = renderHook(() => useGenerationalCost(), { wrapper })

      expect(result.current.filter).toBe(null)
      expect(result.current.baseline).toBe(null)
    })

    it('应该处理undefined的Context值', () => {
      const undefinedContextValue = {
        filter: undefined as AnyType,
        setFilter: vi.fn(),
        expanded: undefined as AnyType,
        setExpanded: vi.fn(),
        searched: undefined as AnyType,
        setSearched: vi.fn(),
        baseline: undefined as AnyType,
        setBaseline: vi.fn(),
      }

      const wrapper = createWrapper(undefinedContextValue)
      const { result } = renderHook(() => useGenerationalCost(), { wrapper })

      expect(result.current.filter).toBe(undefined)
      expect(result.current.expanded).toBe(undefined)
      expect(result.current.searched).toBe(undefined)
      expect(result.current.baseline).toBe(undefined)
    })
  })

  describe('性能测试', () => {
    it('应该不会导致不必要的重新渲染', () => {
      const wrapper = createWrapper()
      let renderCount = 0

      const { result, rerender } = renderHook(
        () => {
          renderCount++
          return useGenerationalCost()
        },
        { wrapper }
      )

      expect(renderCount).toBe(1)

      // 多次重新渲染应该不会增加renderCount（因为Context值没有变化）
      rerender()
      rerender()
      rerender()

      // renderCount应该只增加重新渲染的次数，但Context值相同时hook本身不应该有副作用
      expect(renderCount).toBe(4) // 初始渲染 + 3次rerender
      expect(result.current.filter).toEqual({ pageType: 'project', projectNames: ['test'] })
    })
  })

  describe('实际使用场景测试', () => {
    it('应该支持典型的项目对比场景', () => {
      const projectCompareContext = {
        filter: {
          pageType: 'projectCompare',
          projectNames: ['ProjectA', 'ProjectB', 'ProjectC'],
          saleSites: ['China', 'India'],
          configs: ['Config1', 'Config2'],
          phaseStages: ['Design'],
          exchangeRateType: 'CNY',
          tableType: 'CVPN',
          date: '2023-12',
        },
        setFilter: vi.fn(),
        expanded: true,
        setExpanded: vi.fn(),
        searched: true,
        setSearched: vi.fn(),
        baseline: 'ProjectA',
        setBaseline: vi.fn(),
      }

      const wrapper = createWrapper(projectCompareContext)
      const { result } = renderHook(() => useGenerationalCost(), { wrapper })

      expect(result.current.filter.pageType).toBe('projectCompare')
      expect(result.current.filter.projectNames).toContain('ProjectA')
      expect(result.current.filter.projectNames).toContain('ProjectB')
      expect(result.current.filter.projectNames).toContain('ProjectC')
      expect(result.current.searched).toBe(true)
      expect(result.current.baseline).toBe('ProjectA')
    })

    it('应该支持品类对比场景', () => {
      const categoryCompareContext = {
        filter: {
          pageType: 'categoryCompare',
          costMatCatLvl1Codes: ['Electronics', 'Mechanical'],
          costMatCatLvl2Codes: ['IC', 'Connector'],
          costMatCatLvl3Codes: ['Memory', 'Processor'],
          projectNames: ['MainProject'],
          saleSites: ['Global'],
          configs: ['Standard'],
          phaseStages: ['Production'],
          exchangeRateType: 'USD',
          tableType: 'MPN',
          date: '2023-Q4',
        },
        setFilter: vi.fn(),
        expanded: false,
        setExpanded: vi.fn(),
        searched: true,
        setSearched: vi.fn(),
        baseline: 'Electronics',
        setBaseline: vi.fn(),
      }

      const wrapper = createWrapper(categoryCompareContext)
      const { result } = renderHook(() => useGenerationalCost(), { wrapper })

      expect(result.current.filter.pageType).toBe('categoryCompare')
      expect(result.current.filter.costMatCatLvl1Codes).toContain('Electronics')
      expect(result.current.filter.costMatCatLvl2Codes).toContain('IC')
      expect(result.current.filter.costMatCatLvl3Codes).toContain('Memory')
      expect(result.current.baseline).toBe('Electronics')
    })
  })
})
