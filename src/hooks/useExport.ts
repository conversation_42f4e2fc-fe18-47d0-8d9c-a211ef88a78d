import { customMessage } from '@/utils/custom-message'
import { useRequest } from 'ahooks'
import intl from 'react-intl-universal'

export type ExportFn<T> = (params: T) => Promise<AnyType>

export const useExport = <T extends Record<string, AnyType>>(
  filter?: Partial<T>,
  api?: ExportFn<T>,
  customExportConfig?: ExportFn<T>
) => {
  const params = filter || ({} as Partial<T>)

  const { run: apiExport, loading: apiExportLoading } = useRequest(
    () => (api as AnyType)?.({ ...params } as T),
    {
      manual: true,
      onSuccess: (res: AnyType) => {
        if (res?.data) {
          window.open(res?.data)
          customMessage(intl.get('导出成功'), 'success')
        } else {
          customMessage(intl.get('导出失败'), 'error')
        }
      },
    }
  )

  const { run: customExport, loading: customExportLoading } = useRequest(
    () => (customExportConfig as AnyType)?.({ ...params } as T),
    {
      manual: true,
    }
  )

  return {
    handleTableExport: customExportConfig ? customExport : apiExport,
    loading: customExportConfig ? customExportLoading : apiExportLoading,
  }
}
