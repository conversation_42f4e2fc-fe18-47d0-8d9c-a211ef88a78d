import intl from 'react-intl-universal'
import { message } from '@hi-ui/message'
import { useRequest } from 'ahooks'
import { MESSAGE_DURATION, AnyType, EmptyFunctionType } from '@/constants'
import { ResponseType } from '@/types/type'
import { BaseResponse } from '@/utils/request'
type FileOperationType = (params?: Record<string, AnyType>) => Promise<BaseResponse<string>>
export const useDownload = (fun: FileOperationType) => {
  const { run, loading } = useRequest((params?: Record<string, AnyType>) => fun(params), {
    manual: true,
    onBefore: () => {
      message.open({ title: intl.get('下载中...'), duration: MESSAGE_DURATION })
    },
    onSuccess: (res) => {
      message.closeAll()
      if (res?.data) {
        window.open(res?.data)
        message.open({ title: intl.get('下载成功'), type: 'success', duration: MESSAGE_DURATION })
      } else {
        message.open({ title: intl.get('下载链接有误'), type: 'error', duration: MESSAGE_DURATION })
      }
    },
    onError: (err) => {
      message.closeAll()
      message.open({
        title: (err as ResponseType)?.message || (err as ResponseType)?.msg || intl.get('下载失败'),
        type: 'error',
        duration: MESSAGE_DURATION,
      })
    },
  })
  return { run, loading }
}
export const useUpload = (fun: FileOperationType, update?: EmptyFunctionType) => {
  const { run, loading } = useRequest((params?: Record<string, AnyType>) => fun(params), {
    manual: true,
    onBefore: () => {
      message.open({ title: intl.get('上传中...'), duration: MESSAGE_DURATION })
    },
    onSuccess: () => {
      message.closeAll()
      message.open({ title: intl.get('上传成功'), type: 'success', duration: MESSAGE_DURATION })
      if (update) {
        update()
      }
    },
    onError: (err) => {
      message.closeAll()
      message.open({
        title: (err as ResponseType)?.message || (err as ResponseType)?.msg || intl.get('上传失败'),
        type: 'error',
        duration: MESSAGE_DURATION,
      })
    },
  })
  return { run, loading }
}
