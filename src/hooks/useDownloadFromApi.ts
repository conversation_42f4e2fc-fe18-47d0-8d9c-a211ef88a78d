import { MESSAGE_DURATION } from '@/constants'
import message from '@hi-ui/message'
import { useRequest } from 'ahooks'
import intl from 'react-intl-universal'

interface DownloadResponse {
  data?: string
  success?: boolean
  message?: string
}

interface DownloadError {
  message?: string
  code?: string
}

type DownloadAPI<T> = (params: T) => Promise<DownloadResponse>

/**
 * 通用下载hook
 * @template T - API参数类型
 * @param api - 下载API函数
 * @returns {{ download: (params: T) => void, loading: boolean }}
 */
const useDownloadFromApi = <T extends Record<string, unknown>>(api: DownloadAPI<T>) => {
  const { run: download, loading } = useRequest(api, {
    manual: true,
    onSuccess: (res: DownloadResponse) => {
      message.closeAll()
      if (res?.data) {
        window.open(res.data)
        message.open({
          title: intl.get('下载成功'),
          type: 'success',
          duration: MESSAGE_DURATION,
        })
      } else {
        message.open({
          title: intl.get('下载链接有误'),
          type: 'error',
          duration: MESSAGE_DURATION,
        })
      }
    },
    onError: (err: DownloadError) => {
      message.closeAll()
      message.open({
        title: err?.message || intl.get('下载失败'),
        type: 'error',
        duration: MESSAGE_DURATION,
      })
    },
  })

  return {
    download,
    loading,
  } as const
}

export default useDownloadFromApi
