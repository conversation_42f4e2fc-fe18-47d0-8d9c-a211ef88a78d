/* eslint-disable @typescript-eslint/no-explicit-any */
import { NOOP_OBJ } from '@/utils/noop'
import PerfectScrollbar from 'perfect-scrollbar'
import 'perfect-scrollbar/css/perfect-scrollbar.css'
import { MutableRefObject, useEffect, useRef } from 'react'
export const usePerfectScrollbar = (
  ref: MutableRefObject<any>,
  options: PerfectScrollbar.Options = NOOP_OBJ
) => {
  const psRef = useRef<InstanceType<typeof PerfectScrollbar> | null>(null)
  useEffect(() => {
    if (ref.current) {
      psRef.current = new PerfectScrollbar(ref.current, {
        maxScrollbarLength: 240,
        ...options,
      })
    }
    return () => {
      psRef.current?.destroy()
      psRef.current = null
    }
  }, [ref, options])
  return psRef.current
}
