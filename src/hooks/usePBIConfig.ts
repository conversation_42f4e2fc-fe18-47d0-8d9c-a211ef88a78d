import { useEffect, useCallback, useState, useRef } from 'react'
import { PBI_TOKENS } from '@/constants'
import dayjs from 'dayjs'
import { getPBIToken } from '@/api/gsc/bi'
import { customMessage } from '@/utils/custom-message'
import * as Sentry from '@sentry/react'
import { PBIConfig } from '@/context/AuthContext'
import { Timeout } from 'ahooks/lib/useRequest/src/types'

interface TokenConfig {
  match: PBIConfig | null
  cache: PBIConfig[]
}

export const usePBIConfig = (id) => {
  const [PBIConfig, setPBIConfig] = useState({
    embedUrl: '',
    accessToken: '',
  })
  const tokenConfig = useRef({} as TokenConfig)
  const timerRef = useRef<Timeout>()

  const setUpTimer = useCallback((func) => {
    const { expire, effectiveTime = 0 } = tokenConfig.current.match || {}
    const nowTime = dayjs().valueOf()
    const gap = effectiveTime - nowTime || dayjs().add(Number(expire), 'second').valueOf() - nowTime
    gap > 0 && (timerRef.current = setTimeout(func, gap))
  }, [])

  const getNewAccessToken = useCallback(async () => {
    try {
      const { code = 0, data } = await getPBIToken(id)
      const { embedUrl = '', accessToken = '', expire = 0 } = data || {}
      embedUrl && setPBIConfig({ embedUrl, accessToken })
      if (![0, 2000].includes(code)) {
        customMessage('数据异常，请联系管理员', 'info')
      }
      const newToken = {
        ...data,
        kanbanId: id,
        effectiveTime: dayjs().add(Number(expire), 'second').valueOf(),
      }
      const index = tokenConfig.current.cache?.findIndex((i) => i?.kanbanId === id)
      if (index !== -1) {
        tokenConfig.current.cache.splice(index, 1, newToken)
        localStorage.setItem(PBI_TOKENS, JSON.stringify(tokenConfig.current.cache))
      }
      tokenConfig.current.match = newToken
      setUpTimer(getNewAccessToken)
    } catch (err) {
      Sentry.captureException(err)
    }
  }, [id, setUpTimer])

  useEffect(() => {
    const now = dayjs().valueOf()
    const local = localStorage.getItem(PBI_TOKENS)
    const localTokens = !local || local === 'undefined' ? [] : JSON.parse(local)
    tokenConfig.current = {
      cache: localTokens,
      match: localTokens?.find((token) => token?.kanbanId === id && now <= token?.effectiveTime),
    }
    return () => {
      timerRef.current && clearTimeout(timerRef.current)
    }
  }, [id])

  useEffect(() => {
    if (!Object.keys(tokenConfig.current).length) {
      return
    }
    if (tokenConfig.current.match) {
      const { embedUrl, accessToken } = tokenConfig.current.match
      setPBIConfig({ embedUrl, accessToken })
      setUpTimer(getNewAccessToken)
    } else {
      getNewAccessToken()
    }
  }, [getNewAccessToken, setUpTimer])

  return { PBIConfig }
}
