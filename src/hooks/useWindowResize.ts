import { throttle } from 'lodash'
import { useEffect, useState } from 'react'

let listening = false
type Subscriber = () => void

const subscribers = new Set<Subscriber>()
let info

function calculate() {
  const width = (window.top || window).innerWidth
  const height = (window.top || window).innerHeight

  info = {
    width,
    height,
  }
}

function handleResize() {
  calculate()
  for (const subscriber of subscribers) {
    subscriber()
  }
}

const throttled = throttle(handleResize, 300)
export const useWindowResize = () => {
  if (!listening) {
    info = {}
    calculate()
    window.addEventListener('resize', throttled)
    listening = true
  }
  const [size, setSize] = useState<{
    width: number
    height: number
  }>(info)

  useEffect(() => {
    const subscriber = () => {
      setSize(info)
    }
    subscribers.add(subscriber)
    return () => {
      subscribers.delete(subscriber)
      if (subscribers.size === 0) {
        window.removeEventListener('resize', throttled)
        listening = false
      }
    }
  }, [])
  return size
}
