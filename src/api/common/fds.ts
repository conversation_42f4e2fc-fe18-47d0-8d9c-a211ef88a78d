import { request } from '@/utils/request'
import type { BaseResponse } from '@/utils/request'

// 文件上传URI响应接口
export interface FileUploadUriResp {
  /** 文件下载/上传的预签名URI */
  presignedUri: string
  /** 文件对象名称 */
  objectName: string
}

// 获取文件下载URI的入参
export interface GetDownloadFileUriParams {
  /** 文件对象名称(文件地址) */
  objectName: string
  /** 长期保存类型 */
  longTimeSaveType?: number
}

/**
 * 根据object name获取文件下载uri
 * @param params 包含objectName和longTimeSaveType的参数对象
 * @returns Promise<BaseResponse<FileUploadUriResp>>
 */
export const getDownLoadFileUriByObjectName = async (
  params: GetDownloadFileUriParams
): Promise<BaseResponse<FileUploadUriResp>> => {
  return await request('/common/downloadFileUri', {
    method: 'GET',
    params,
  })
}
