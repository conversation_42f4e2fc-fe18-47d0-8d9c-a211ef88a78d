import { getPaginationDataWithColumns } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'

const prefix = '/iot-bom-cost'
const detailPrefix = '/iot-mat-bom-cost'

interface IEcoBomCost {
  matNo95List?: string[]
  l2ProjList?: string[]
  skuList?: string[]
  pageNum?: number
  pageSize?: number
}

interface IEcoBomCostDetail {
  matNo95: string
  sku: string
  pageNum: number
  pageSize: number
}

type IDetailProps = Pick<IEcoBomCostDetail, 'matNo95' | 'sku'>

/**
 * 列表查询
 */
export const getEcoBomCostList = async (data: IEcoBomCost) => {
  const res = await request(`${prefix}/bom-cost`, {
    method: 'POST',
    data,
  })
  return getPaginationDataWithColumns(res.data)
}

/**
 * 列表导出
 */
export const exportEcoBomCostList = async (data: IEcoBomCost) =>
  request(`${prefix}/bom-cost/export`, {
    method: 'POST',
    data,
  })

/**
 * 获取查询条件
 */
export const getEcoBomCostConditions = async () => {
  return await request(`${prefix}/condition`, {
    method: 'POST',
  })
}

/**
 * 新增时筛选条件
 */
export const getAddEcoConditions = async (data: { type: 'SKU' | 'MAT95'; content: string }) =>
  request(`${prefix}/add-condition`, {
    method: 'POST',
    data,
  })

/**
 * 新增 95 料号
 */
export const addMatNo95Item = async (data: IDetailProps) =>
  request(`${prefix}/bom-cost/add`, {
    method: 'POST',
    data,
    handleError: false,
  })

/**
 * 详情页折线图查询
 */
export const getEcoBomCostDetailChart = async (data: IDetailProps) =>
  request(`${detailPrefix}/his-bom-cost`, {
    method: 'POST',
    data,
  })

/**
 * 详情页折线图导出
 */
export const exportEcoBomCostDetailChart = async (data: IDetailProps) =>
  request(`${detailPrefix}/his-bom-cost/export`, {
    method: 'POST',
    data,
  })

/**
 * 详情页列表查询
 */
export const getEcoBomCostDetailList = async (data: IEcoBomCostDetail) => {
  const res = await request(`${detailPrefix}/detail`, {
    method: 'POST',
    data,
  })
  return getPaginationDataWithColumns(res.data)
}

/**
 * 详情页列表导出
 */
export const exportEcoBomCostDetailList = async (data: IEcoBomCostDetail) =>
  request(`${detailPrefix}/detail/export`, {
    method: 'POST',
    data,
  })

/**
 * 刷新详情页表格
 */
export const refreshDetailList = async (data: IDetailProps) =>
  request(`${detailPrefix}/refresh`, {
    method: 'POST',
    data,
  })

/**
 * 获取版本信息
 */
export const getVersionInfo = async (data: IDetailProps) =>
  request(`${detailPrefix}/version`, { method: 'POST', data })
