import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

interface PaginationProps {
  purchaseOrgList?: string[]
  businessLineList?: string[]
  statusList?: boolean[]
  createNames?: string[]
  startDate?: number
  endDate?: number
  pageNum?: number
  pageSize?: number
}

interface CommonProps {
  id?: number
  businessLineId?: string
  purchaseOrg?: string
  status?: boolean
}

/**
 * 分页查询业务线采购组织
 */
export const getBizPurchaseList = async (params: PaginationProps) => {
  const res = await request('/businessLine-purchase-org', { params })
  return getPaginationData(res.data)
}

/**
 * 新增业务线采购组织
 */
export const addBizPurchase = async (data: CommonProps) =>
  await request('/businessLine-purchase-org', {
    method: 'POST',
    data,
    handleError: false,
  })

/**
 * 更新业务线采购组织
 */
export const updateBizPurchase = async (data: {
  id: number
  status?: boolean
  isOriginal?: boolean
}) => {
  const { id, ...left } = data
  return await request(`/businessLine-purchase-org/${id}`, {
    method: 'PUT',
    data: left,
    handleError: false,
  })
}

/**
 * 获取业务线采购组织默认查询条件
 */
export const getBizPurchaseConditions = async () =>
  await request('/businessLine-purchase-org/conditions')

/**
 * 获取新增业务线采购组织默认条件
 */
export const getAddBizPurchaseConditions = async () =>
  await request('/businessLine-purchase-org/add-conditions')
