import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

interface PaginationProps {
  purchaseOrgList?: string
  stockOrgList?: string
  projectNameList?: string
  skuList?: string
  statusList?: string
  pageNum?: number
  pageSize?: number
}

/**
 * 分页查询采购库存组织
 */
export const getSkuPurchaseStockList = async (params: PaginationProps) => {
  const res = await request('/sku-purchase-stock-org', { params })
  return getPaginationData(res.data)
}

/**
 * 获取采购库存组织默认条件
 */
export const getSkuPurchaseStockConditions = async () =>
  await request('/sku-purchase-stock-org/conditions')
