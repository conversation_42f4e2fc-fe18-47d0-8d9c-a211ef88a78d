import { request } from '@/utils/request'

export interface briefType {
  name?: string
  configRule?: number
  configRange?: string[]
  menuRule?: number
  menuRange?: string[]
  penetrationTarget?: number
}

/**
 * 配置列表
 */
export const getConfigList = async (data) => {
  const res = await request('/event-track-report/config-list', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res?.data
}

/**
 * 保存配置
 */
export const SavaConfig = async (data) => {
  const res = await request('/event-track-report/config-save', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}
/**
 * 删除配置
 */
export const deleteConfig = async (data) => {
  const res = await request('/event-track-report/config-delete', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 检查人员范围配置
 */
export const checkConfigRange = async (data) => {
  const res = await request('/event-track-report/config-range/check', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 检查菜单配置
 */
export const checkMenuRange = async (data) => {
  const res = await request('/event-track-report/menu-range/check', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 简报达标率列表
 */
export const getReportList = async (data) => {
  const res = await request('/event-track-report/report-list', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res?.data
}

/**
 * 简报达标率列表下载
 */
export const getReportDownload = async (data) => {
  const res = await request('/event-track-report/report-download', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 简报达标率列表相关用户信息下载
 */
export const getReportExport = async (data) => {
  const res = await request('/event-track-report/user-download', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

// 埋点简报配置修改
export const updateBrief = async (data: briefType) => {
  const res = await request('/event-track-report/config-update', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

// 抽屉页面，工作台的下拉框的获取
export const listWorkbench = async (data: briefType) => {
  const res = await request('/event-track-report/list-workbench', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

// 埋点简报看板配置
export const getPermissionCode = async (data: Record<string, number | string | string[]>) => {
  const res = await request('/event-track-report/get-account-by-permission-code', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}
