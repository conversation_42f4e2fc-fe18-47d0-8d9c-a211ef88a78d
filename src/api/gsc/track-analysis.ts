import { getPaginationDataWithColumns } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'

/**
 * 趋势图列表
 */
export const getPermeateList = async (data) => {
  const res = await request('/event-track-report/permeate-list', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 趋势图列表下载
 */
export const getPermeateDownload = async (data) => {
  const res = await request('/event-track-report/permeate/download', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}
/**
 * UV列表
 */
export const getUVList = async (data) => {
  const res = await request('/event-track-report/uv-list', {
    method: 'POST',
    handleError: false,
    data,
  })
  return getPaginationDataWithColumns(res?.data)
}

/**
 * UV列表下载
 */
export const getUVDownload = async (data) => {
  const res = await request('/event-track-report/uv-list/download', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}
