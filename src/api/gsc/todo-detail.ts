import { isGSCWorkbench } from '@/constants'
import { getPaginationData } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'

type DetailSearchType = {
  startTime?: string
  endTime?: string
  dept3NameList?: string[]
  dept4NameList?: string[]
  categoryNameList?: string[]
  concatNameList?: string[]
  statusList?: string[]
  showTaskId?: boolean
}

/**
 * 待办场景统计
 */
export const getTodoScenes = async (searchData: DetailSearchType) => {
  const res = await request('/todo-detail/category/statistics', {
    method: 'POST',
    data: searchData,
    handleError: false,
  })
  return res
}

/**
 * 待办明细分页查询
 */
export const getTodoDetailList = async (data) => {
  const res = await request('/todo-detail', {
    method: 'POST',
    data,
  })
  return getPaginationData(res?.data)
}

/**
 * 待办明细导出
 */
export const exportTodoDetail = async (searchData: DetailSearchType) => {
  const res = await request('/todo-detail/export', {
    method: 'POST',
    data: searchData,
    handleError: false,
  })
  return res
}

/**
 * 待办条件查询
 */
export const getTodoCondition = async (searchData) => {
  const res = await request('/todo-detail/conditions', {
    method: 'POST',
    data: searchData,
    handleError: false,
  })
  return res
}

// 运维待办-分页查询
export const getTodoSearchlList = async (data) => {
  const res = await request('/todo/info/list', {
    method: 'POST',
    data,
  })
  return getPaginationData(res?.data)
}
// 运维待办-条件查询
export const getTodoSearchCondition = async (searchData) => {
  const res = await request('/todo/info/conditions', {
    method: 'POST',
    data: { ...searchData, workbenchTypes: isGSCWorkbench ? [1] : [2] },
    handleError: false,
  })
  return res
}
// 运维待办-场景统计
export const getTodoSearchScenes = async (searchData: DetailSearchType) => {
  const res = await request('/todo/info/category/statistics', {
    method: 'POST',
    data: { ...searchData, workbenchTypes: isGSCWorkbench ? [1] : [2] },
    handleError: false,
  })
  return res
}
// 运维待办-获取任务所有执行人
export const getTodoExecutorAll = async (taskId) => {
  const res = await request('/todo/info/executor/all', {
    method: 'POST',
    data: { taskId },
    handleError: false,
  })
  return res
}

// 运维待办-更新执行人
export const todoExecutor = async (searchData) => {
  const res = await request('/todo/info/executor/all', {
    method: 'PUT',
    data: searchData,
    handleError: false,
  })
  return res
}

// 待办运维-关闭任务
export const todoDelete = async (searchData) => {
  const res = await request('/todo/info/batch/del', {
    method: 'POST',
    data: searchData,
    handleError: false,
  })
  return res
}
// 待办运维-获取所有执行人列表
export const getEmployeeCondition = async (searchData) => {
  const res = await request('/todo/info/employee', {
    method: 'POST',
    data: { ...searchData, workbenchTypes: isGSCWorkbench ? [1] : [2] },
    handleError: false,
  })
  return res
}

// 获取oa_person表的账号和姓名，不包括uid
export const getEmployeeNewCondition = async (searchData) =>
  await request('/todo/info/list-person-account', {
    method: 'POST',
    data: { ...searchData, workbenchTypes: isGSCWorkbench ? [1] : [2] },
    handleError: false,
  })

// 待办运维-详情
export const getTodoSearchDetail = async (searchData) => {
  const res = await request('/todo/info', {
    method: 'POST',
    data: searchData,
    handleError: false,
  })
  return res
}

// 对外待办-首页获取执行人当前分类tab列表
export const getSupplierStatistics = async (searchData) => {
  const res = await request('/todo/task/supplierStatistics', {
    method: 'POST',
    data: searchData,
    // handleError: false,
  })
  return res
}
/**
 * 获取待办列表
 */
export const getTableData = async (searchData) => {
  const res = await request('/todo/task', {
    params: { isSupplier: 1, ...searchData },
  })
  return getPaginationData(res?.data)
}
