import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

const API_PREFIX = '/first-material'

export interface IConditionProps {
  orgTreeCodes: string[]
  cvpnCodes: string[]
  pnCodes: string[]
  mpnIds: string[]
  executeAccounts: string[]
  sendStates: string[]
  ownerAccounts: string[]
  matCatLvl1Codes: string[]
  matCatLvl2Codes: string[]
  matCatLvl3Codes: string[]
}

// 获取待办执行配置条件
export const getTodoExecuteConfigConditions = async (data: IConditionProps & { type: string }) =>
  request(`${API_PREFIX}/condition`, { method: 'POST', data })

// 导出待办执行配置
export const exportTodoExecuteConfig = async (data: IConditionProps) =>
  request(`${API_PREFIX}/export`, { method: 'POST', data })

// 更新是否下发状态
export const updateSendState = async (data: {
  ids: number[]
  sendState: number
  startSendDay: string | undefined
  materialCodes: string
  researchStaff: string
}) => request(`${API_PREFIX}/batch-update`, { method: 'POST', data, handleError: false })

// 查询一级物料分类列表
export const getTodoExecuteConfigList = async (
  data: IConditionProps & { pageNum: number; pageSize: number }
) => {
  const res = await request(`${API_PREFIX}/list`, { method: 'POST', data })
  return getPaginationData(res.data)
}

// 批量设置获取分类配置
export const getCategoryConfigList = async (params) =>
  request(`${API_PREFIX}/material-codes`, { method: 'GET', params })

// 模糊查询研发人员
export const getResearcherList = async (params) =>
  request(`${API_PREFIX}/fuzzy-search`, { method: 'GET', params })

// 获取详情
export const getDetailList = async (params) =>
  request(`${API_PREFIX}/get`, { method: 'GET', params })
