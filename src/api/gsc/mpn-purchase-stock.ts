import { request } from '@/utils/request'

export interface PaginationProps {
  businessLineList?: string[]
  mpnIdList?: string[]
  purchaseOrgList?: string[]
  stockOrgList?: string[]
  statusList?: boolean[]
  centPurchFlagList?: string[]
  centStockFlagList?: string[]
  pageNum?: number
  pageSize?: number
}

/**
 * 分页查询物料采购库存组织
 */
export const getMpnPurchaseStockList = async (data) => {
  const res = await request('/mpn-purchase-stock-org', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res?.data
}

/**
 * 更新物料采购库存组织状态
 */
export const updateMpnPurchaseStockStatus = async (data: { status: boolean; id: number }) =>
  await request(`/mpn-purchase-stock-org/update-status/${data.id}`, {
    method: 'PUT',
    data: { status: data.status },
    handleError: false,
  })

/**
 * 获取物料采购库存组织默认条件
 */
export const getMpnPurchaseStockConditions = async () =>
  await request('/mpn-purchase-stock-org/conditions')

/**
 * 物料采购库存组织导出
 */
export const exportMpnPurchaseStock = async (data: PaginationProps) =>
  await request('/mpn-purchase-stock-org/export', { method: 'POST', data, handleError: false })

/**
 * 条件查询
 */
export const getExtraConditions = async (params: { pnIdList: string; mpnIdList: string }) =>
  await request('/mpn-purchase-stock-org/findPnconditions', { params, handleError: false })

/**
 * 更新物料库存组织编码
 */
export const updateMpnPurchaseStockOrg = async (data: { stockOrgList: string[]; id: number }) =>
  await request(`/mpn-purchase-stock-org/modify/${data.id}`, {
    method: 'PUT',
    data: { stockOrgList: data.stockOrgList },
    handleError: false,
  })
