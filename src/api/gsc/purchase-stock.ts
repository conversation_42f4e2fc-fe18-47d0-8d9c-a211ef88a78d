import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

interface PaginationProps {
  purchaseOrgList?: string[]
  stockOrgList?: string[]
  businessLineList?: string[]
  statusList?: boolean[]
  pageNum?: number
  pageSize?: number
}

/**
 * 分页查询采购库存组织
 */
export const getPurchaseStockList = async (params: PaginationProps) => {
  const res = await request('/purchase-stock-org', {
    params,
    headers: {
      'Accept-Language': 'en-US',
    },
  })
  return getPaginationData(res.data)
}

/**
 * 更新采购库存组织状态
 */
export const updatePurchaseStockStatus = async (data: {
  id: number
  status?: boolean
  isOriginal?: boolean
}) => {
  const { id, ...left } = data
  return await request(`/purchase-stock-org/update-status/${id}`, {
    method: 'PUT',
    data: left,
    handleError: false,
  })
}

/**
 * 获取采购库存组织默认条件
 */
export const getPurchaseStockConditions = async () =>
  await request('/purchase-stock-org/conditions')
