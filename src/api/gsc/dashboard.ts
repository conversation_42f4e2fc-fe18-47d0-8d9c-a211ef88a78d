import { request } from '@/utils/request'

// 目标成本达成率
export const getTargetCostUrl = () =>
  request('/dashboard/target-cost-achieve-rates', {
    handleError: false,
  })

// 库存周转率
export const getInventoryTurnover = () => {
  return request('/dashboard/inventory-turnover', {
    handleError: false,
  })
}

export const getDtToken = () => {
  return request(`${location.origin}/_aegis/cas/token`, {
    headers: {
      'X-Midun-Service': '5563e070',
    },
  })
}

export const getDashboardTips = (params: { category: string; businessLine: string }) => {
  return request('/dashboard/inventory-tips', {
    params,
    handleError: false,
  })
}
