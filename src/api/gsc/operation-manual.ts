import { request } from '@/utils/request'

export interface manualType {
  id?: number
  menuTitle?: string
  operationName?: number
  operationManual?: string
  sort?: number
}

/**
 * 获取操作手册列表
 */
export const getOpManual = async (params: {
  id?: number
  menuTitle?: string
  operationName?: string
}) => {
  const res = await request('/operation/mappings/page', {
    method: 'GET',
    params,
  })
  return res
}

/**
 * 添加操作手册
 */
export const addOpManual = async (data: manualType) => {
  const res = await request('/operation/mappings', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 修改操作手册
 */
export const updateOpManual = async (data: manualType) => {
  const res = await request('/operation/mappings', {
    method: 'PUT',
    handleError: false,
    data,
  })
  return res
}

/**
 * 删除操作手册
 */
export const deleteOpManual = (id: number) => {
  return request(`/operation/mappings/${id}`, {
    method: 'DELETE',
    handleError: false,
  })
}
