/* eslint-disable @typescript-eslint/no-explicit-any */
import { NOOP_ARR } from '@/utils/noop'
import { request } from '@/utils/request'
import { rmStrZero } from '@/utils/rmStrZero'
import { uniq } from 'lodash'

export interface PaginationData<T = any> extends PaginationQuery {
  totalPages: number
  total: number
  lines: T[]
  eventTime: string
}

export interface PaginationQuery {
  pageNo: number
  pageSize: number
}
export interface IResgetMaterialCount {
  forCnMoveNum: string
  forCnShpNum: string
  forDestAirportNum: string
  forExAirportNum: string
  forHkMoveNum: string
  forIndonesiaNum: string
  forPdsNum: string
  forSfNum: string
  forSfPdsNum: string
  forShpNum: string
  forStockNum: string
  forXspNum: string
  lifnrMatnrL: string
  maktx: string
  matnr: string
}
export enum MatNodeName {
  forCargoNum = '待分货',
  forPickNum = '待拣货',
  forPdsNum = '待PDS',
  forShpNum = '待提货',
  forHkMoveNum = '香港运输中',
  forCnMoveNum = '大陆运输中',
  forSfNum = '顺丰仓待入库',
  forSfPdsNum = '待PDS（顺丰）',
  forCnShpNum = '陆运待提货',
  forExAirportNum = '待入出口机场',
  forDestAirportNum = '待送达目的地机场',
  forXspNum = '新加坡机场待送达',
  forStockNum = '待入库',
  forwarderNum = '小米货代待入库',
}
export enum BusinessType {
  '进口' = 1,
  '出口' = 2,
  '进口转出口' = 3,
  '国内直发' = 4,
}
export const MatNodeNameByBusinessType = {
  [BusinessType.进口]: [
    'forwarderNum',
    'forCargoNum',
    'forPickNum',
    'forPdsNum',
    'forShpNum',
    'forHkMoveNum',
    'forCnMoveNum',
    'forStockNum',
  ],
  [BusinessType.进口转出口]: [
    'forwarderNum',
    'forCargoNum',
    'forPickNum',
    'forPdsNum',
    'forShpNum',
    'forSfNum',
    'forSfPdsNum',
    'forCnShpNum',
    'forExAirportNum',
    'forDestAirportNum',
    'forXspNum',
    'forStockNum',
  ],
  [BusinessType.出口]: [
    'forwarderNum',
    'forPdsNum',
    'forShpNum',
    'forExAirportNum',
    'forDestAirportNum',
    'forXspNum',
    'forStockNum',
  ],
  [BusinessType.国内直发]: ['forCnMoveNum', 'forStockNum'],
}

export enum MatCategoryName {
  poNum = '已下单',
  asnNum = '已发货',
  stockNum = '工厂已入库',
  notStockNum = '已发货未入库',
}

export type MatFilterType = {
  businessType: BusinessType
  matnr?: string
  lifnrMatnrL?: string
  cateFullId?: string
  asnNo?: string
  fhd?: string
  createDate?: string
  lifnrDg?: string
  lifnr?: string
  appModels?: string
  year?: number
}

/**
 * 全量物料在途数量
 */
export const getMaterialCount = async (params: MatFilterType & PaginationQuery) => {
  const res = await request<PaginationData<IResgetMaterialCount[]>>(
    '/dashboard/material/in-transit-quantity',
    {
      params,
    }
  )
  return {
    list: res.data?.lines ?? NOOP_ARR,
    total: res.data?.total ?? 0,
    eventTime: res.data?.eventTime ?? '',
  }
}
/**
 * 导出物料数量
 */
export const exportMatCount = (params: MatFilterType) => {
  return request('/dashboard/material/in-transit-quantity/export', {
    params,
  })
}

export enum SearchType {
  /**
   * 物料编码
   */
  matnr = 1,
  /**
   * 供应商料号
   */
  lifnrMatnrL = 2,
  /**
   * 品类
   */
  cateFullId = 3,
  /**
   * 供应商
   */
  lifnr = 4,
  /**
   * 代工厂
   */
  lifnrDg = 5,
  /**
   * 项目
   */
  appModels = 6,
}

/**
 * 获取筛选条件
 * @param searchType 查询类型，枚举值：1-物料编码；2-供应商料号；3-品类；4-供应商；5-代工厂；6-项目
 * @param input 模糊搜索输入内容
 */
export const getFilterValue = async (searchType: SearchType, input?: string) => {
  const res = await request('/dashboard/material/query-conditions', {
    params: {
      searchType,
      inputData: input,
    },
  })
  if (searchType === 1) {
    return uniq<any>(res.data).map((i, index) => {
      const matnr = i.split('@@')[0]
      const maktx = i.split('@@')[1]
      return {
        title: `${rmStrZero(matnr)} - ${maktx}`,
        id: matnr,
        key: index,
      }
    })
  }
  return uniq<any>(res.data).map((i) => ({
    id: i,
    title: i,
  }))
}
/**
 * 查询物料平均交付天数
 */
export const getMaterialAvgDays = async (params: MatFilterType & PaginationQuery) => {
  const res = await request('/dashboard/material/avg-delivery-days', {
    params,
  })
  return {
    list: res.data?.lines ?? NOOP_ARR,
    total: res.data?.total ?? 0,
    eventTime: res.data?.eventTime ?? '',
  }
}
/**
 * 导出物料平均交付天数
 */
export const exportMatAvgDays = (params: MatFilterType) => {
  return request('/dashboard/material/avg-delivery-days/export', {
    params,
  })
}
/**
 * 查询年度品类概览
 */
export const getMatCategoryOverview = async (
  params: Pick<MatFilterType, 'businessType' | 'cateFullId'> & {
    year?: number
  } & PaginationQuery
) => {
  const res = await request<PaginationData>('/dashboard/material/annual-category-overview', {
    params,
  })
  return {
    list: res.data.lines,
    total: res.data.total,
    eventTime: res.data.eventTime,
  }
}
/**
 * 导出物料年度品类概览
 */
export const exportMatCat = (params: MatFilterType) => {
  return request('/dashboard/material/annual-category-overview/export', {
    params,
  })
}
/**
 * 查询物料在途数量概览
 */
export const getMaterialCountOverview = (matnr?: Pick<MatFilterType, 'matnr'>) => {
  return request('/dashboard/material/in-transit-quantity/overview', {
    params: {
      matnr,
    },
  })
}
