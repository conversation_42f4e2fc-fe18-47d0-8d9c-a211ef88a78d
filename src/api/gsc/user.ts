/* eslint-disable @typescript-eslint/no-explicit-any */
import { MainMenuItem } from '@/utils/micro-app'
import { request } from '@/utils/request'

export const auth = () => request('/api/v1/auth/aegis-login')

export interface IMenuItem extends MainMenuItem {
  // id: number;
  createUser: string
  createUserName?: any
  createTime: string
  updateUser: string
  updateUserName?: any
  updateTime: string
  applicationId: number
  // parentId: number;
  sort: number
  menuTitle: string
  menuRoute: string
  iconUrl: string
  description: string
  children: IMenuItem[]
  parent: null | IMenuItem
  parentTitle?: string
}
export interface IResGetUser {
  userName: string
  account?: string
  mobile?: any
  email?: string
  personalEmail: string
  headUrl?: any
  thumbnailUrl?: any
  menuTree: IMenuItem[]
  deptDescr?: string
  dataCodes?: string[]
  functionCodes?: string[]
  fullDeptDescr?: string
}

export const getUser = () => request<IResGetUser>('/userinfo/detail')

export const getUserTagetTree = () => request('/menu')

export const getDataCodes = () => request('/userinfo/data-codes')

export const DATA_CODES_VALUE = 'DATA_CODES_VALUE'
