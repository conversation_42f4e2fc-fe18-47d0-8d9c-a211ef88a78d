/* eslint-disable @typescript-eslint/no-explicit-any */
import { NOOP_ARR } from '@/utils/noop'
import { request } from '@/utils/request'
import { MatFilterTypeStr, SearchType } from '@/views/gsc/pages/mat-logistics/type'
export interface PaginationData<T = any> extends PaginationQuery {
  totalPages: number
  total: number
  lines: T[]
  eventTime: string
}

export interface PaginationQuery {
  pageNo: number
  pageSize: number
}
/**
 * 获取筛选条件
 * @param searchType 查询类型，枚举值：1-物料编码；2-供应商料号；3-品类；4-供应商；5-代工厂；6-项目
 * @param input 模糊搜索输入内容
 */
export const getFilterValue = (searchType: SearchType, input?: string) => {
  return request<string[]>('/dashboard/material/query-conditions', {
    params: {
      searchType,
      inputData: input,
    },
  })
}
/**
 * 获取默认搜索条件
 */
export const getDefaultFiltervalue = () => {
  return request<Record<any, any[]>>('/dashboard/material/default-conditions')
}

/**
 * 获取缓存的默认搜索条件
 */
export const getCacheDefaultFilterValue = () => {
  return request<Record<any, any[]>>('/dashboard/material/cache-conditions')
}

/**
 * 新增变式，变式内容使用JSON字符串存储
 */
export const createVariant = (data: {
  title: string
  content: string | Record<string, Record<string, string> | Record<string, string | boolean>[]>
  pageType: string
  isDefault?: number
}) => {
  return request('/variant', {
    method: 'POST',
    data,
  })
}

/**
 * 修改变式名称
 */
export const updateVariant = (data: { title: string; id: number; pageType?: string }) => {
  return request('/variant', {
    method: 'PUT',
    data,
  })
}
/**
 * 查询变式列表
 */
export const getVariants = (params: { pageType: string }) => {
  return request<{ title: string; content: string }[]>('/variant', { params })
}
/**
 * 删除变式
 */
export const delVariant = (id: number) => {
  return request(`/variant/${id}`, {
    method: 'DELETE',
  })
}
/**
 * 查询物料在途数量概览
 */
export const getMaterialCountOverview = (filter: MatFilterTypeStr) => {
  return request('/dashboard/material/in-transit-quantity/overview', {
    params: {
      ...filter,
    },
  })
}
/**
 * 全量物料在途数量
 */
export const getMaterialCount = async (params: any) => {
  const res = await request<PaginationData<any[]>>('/dashboard/material/in-transit-quantity', {
    params,
  })
  return {
    list: res.data?.lines ?? NOOP_ARR,
    total: res.data?.total ?? 0,
    eventTime: res.data?.eventTime ?? '',
  }
}
/**
 * 查询年度品类概览
 */
export const getMatCategoryOverview = async (params: any) => {
  const res = await request<PaginationData>('/dashboard/material/annual-category-overview', {
    params,
  })
  return {
    list: res.data.lines,
    total: res.data.total,
    eventTime: res.data.eventTime,
  }
}
/**
 * 查询物料平均交付天数
 */
export const getMaterialAvgDays = async (params: any) => {
  const res = await request('/dashboard/material/avg-delivery-days', {
    params,
  })
  return {
    list: res.data?.lines ?? NOOP_ARR,
    total: res.data?.total ?? 0,
    eventTime: res.data?.eventTime ?? '',
  }
}
/**
 * 导出物料年度品类概览
 */
export const exportMatCat = (params: any) => {
  return request('/dashboard/material/annual-category-overview/export', {
    params,
  })
}
/**
 * 导出物料平均交付天数
 */
export const exportMatAvgDays = (params: any) => {
  return request('/dashboard/material/avg-delivery-days/export', {
    params,
  })
}
/**
 * 导出物料数量
 */
export const exportMatCount = (params: any) => {
  return request('/dashboard/material/in-transit-quantity/export', {
    params,
  })
}

/**
 * 物流可视获取物料节点单号信息
 * @param businessType  业务类型，枚举值：1：进口；2：出口；3：进口转出口；4.国内直发；5.国内调拨；6.国内调入国外；7.国外调入国内
 * @param matnr  料号
 * @param field  选中的某一阶段; 如: 待分货
 */
export const getMatNodeInfo = async (
  businessType: string,
  matnr: string,
  field: string,
  filter: any
) => {
  const res = await request('/dashboard/material/in-transit-quantity/stage-info', {
    params: {
      ...filter,
      businessType,
      matnr,
      field,
    },
  })
  return res
}
