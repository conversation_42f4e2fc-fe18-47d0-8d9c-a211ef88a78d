import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

export type AddPropsType = {
  bizCode: string
  bizName: string
  category: string
  categoryName: string
  sendSystem: string
  acceptSystem: string
  type: number
  description: string
  overTime: number
  remark: string
  dueType: number
  scheduleTime: string
  sourceType: number
  url: string
}

export type UpdateTodoType = {
  id: number
  description: string
  categoryName: string
  overTime: number
  remark: string
  dueType: number
  scheduleTime: string
  url: string
  workbenchType: number
}

/**
 * 获取待办筛选条件
 */
export const getTodoCondition = async (params?: { sourceType?: number; workbenchType?: number }) =>
  request('/categoryScene/default-conditions', {
    params,
  })

/**
 * 查询待办配置
 */
export const getTodoConfigList = async (data: Record<string, string[] | number[] | number>) => {
  const res = await request('/categoryScene/select', {
    method: 'POST',
    data,
  })
  return getPaginationData(res?.data)
}

/**
 * 查询全量待办配置
 */
export const getAllTodoConfigList = async (data) => {
  const res = await request('/categoryScene/allCategory', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 删除待办配置
 */
export const deleteConfig = async (data) => {
  await request('/categoryScene/delete', {
    method: 'POST',
    data,
  })
}

/**
 * 更新待办配置
 */
export const updateTodoConfig = async (data: UpdateTodoType) => {
  await request('/categoryScene/update', {
    method: 'POST',
    data,
  })
}

/**
 * 增加待办配置
 */
export const createTodoConfig = async (data: AddPropsType) => {
  await request('/categoryScene/create', {
    method: 'PUT',
    data,
  })
}

/**
 * 获取通知筛选条件
 */
export const getNotifyCondition = async (params?: { workbenchType?: number }) =>
  request('/categoryInform/default-conditions', { params })

/**
 * 获取通知列表
 */

export const getNotifyConfigList = async (data) => {
  const res = await request('/categoryInform/select', {
    method: 'POST',
    data,
  })
  return getPaginationData(res?.data)
}

/**
 * 更新通知配置
 */
export const updateNotifyConfig = async (data: {
  id: number
  content: string
  state: number
  isReaded: number
  isBatchProcess: number
  isProcess: number
  acrossPurchaseOrg: number
  acrossStockOrg: number
  isShift: number
  isBatchShift: number
  isClose: number
  isBatchClose: number
  showApprove: number
  showReadBtn: number
  dialogType?: number
}) => {
  await request('/categoryInform/update', {
    method: 'POST',
    data,
  })
}

/**
 * 删除通知配置
 */
export const deleteNotifyConfig = async (data) => {
  await request('/categoryInform/delete', {
    method: 'POST',
    data,
  })
}

/**
 * 查询全量通知配置
 */
export const getAllNotifyConfigList = async () => {
  const res = await request('/categoryInform/getAllCategoryInform')
  return res
}

/**
 * 增加通知配置
 */
export const createNotifyConfig = async (data: {
  category: string
  type: number
  frequency: number
  content: string
  state: number
  isReaded: number
  isBatchProcess: number
  isProcess: number
  isShift: number
  isBatchShift: number
  showApprove: number
  dialogType?: number // 默认为 0，1 为弹窗
}) => {
  await request('/categoryInform/create', {
    method: 'PUT',
    data,
    handleError: false,
  })
}
// 对外待办-获取查询条件
export const getSelectCondition = async (searchData) => {
  const res = await request('/categoryClass/getSelectCondition', {
    method: 'POST',
    data: searchData,
    handleError: false,
  })
  return res
}
// 查询所有待办分类
export const getCategoryClassSelect = async (searchData) => {
  const res = await request('/categoryClass/select', {
    method: 'POST',
    data: searchData,
    handleError: false,
  })
  return getPaginationData(res?.data)
}

/**
 * 新增待办分类
 */
export const categoryClassCreate = async (data) =>
  await request('/categoryClass/create', {
    method: 'PUT',
    data,
    // handleError: false,
  })

/**
 * 删除待办配置
 */
export const deleteCategoryClass = async (data) => {
  await request('/categoryClass/deleteCategoryClass', {
    method: 'POST',
    data,
  })
}
/**
 * 查询所有待办场景
 */
export const getAllCategory = async () => {
  const res = await request(`/categoryClass/allCategory`, {
    method: 'GET',
  })
  return res
}
/**
 * 查询指定分类下待办场景
 */
export const getCategoryClass = async (params: { categoryId: string | number | undefined }) => {
  const res = await request('/categoryClass/getCategoryClass', {
    method: 'GET',
    params,
  })
  return res
}
/**
 * 添加分类和待办场景关系
 */
export const addClassScene = async (data) => {
  await request('/categoryClass/addClassScene', {
    method: 'POST',
    data,
  })
}
/**
 * 查询场景下人员配置
 */
export const allCategoryExecutor = async (params: { categoryId: string | number | undefined }) => {
  const res = await request('/categoryScene/allCategoryExecutor', {
    method: 'GET',
    params,
  })
  return res
}
/**
 * 导出场景下人员配置
 */
export const categoryExecutorExport = async (data: { categoryId: string }) =>
  await request(`/categoryScene/categoryExecutorExport?categoryId=${data.categoryId}`, {
    method: 'GET',
    handleError: false,
  })
/**
 * 上传场景下的人员配置表
 */
export const saveSceneExecutor = async (data) => {
  await request('/categoryScene/saveSceneExecutor', {
    method: 'POST',
    data,
  })
}
/**
 * 删除待办执行人配置
 */
export const deleteCategoryExecutor = async (data) => {
  await request('/categoryScene/deleteCategoryExecutor', {
    method: 'POST',
    data,
  })
}
