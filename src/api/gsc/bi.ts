/* eslint-disable @typescript-eslint/no-explicit-any */
import { request } from '@/utils/request'
import { traverseTree } from '@/utils/traverseTree'
import { PaginationData, PaginationQuery } from './material'
import { IMenuItem } from './user'

export interface IBIConfigEnum {
  pages: IMenuItem[]
  businessCodes: { code: string; name: string }[]
  names: string[]
  statusList: { code: string; name: string }[]
  isOuter?: boolean
}
export interface IBIConfigForm {
  name: string
  shortName?: string
  description?: string
  url: string
  type: number
  height?: string
  businessCode: string
  pages: string
  isFavorite?: boolean
  showTab?: boolean
  enable?: boolean
  customKey?: string
  dashboardId?: string
  functionCode?: string
  sort?: number
  roles?: string
  secretKey?: string
  isOuter?: boolean
  owner?: string
}

export interface DashboardConfig {
  customKey: string
  type: number
  url: string
  name: string
  height: string | number
  id: number
  dashboardId: string
  embedTimeStamp: string
  embedToken: string
  embedUserName: string
  embedVersion: string
  showTab: boolean
}

export enum BIType {
  数鲸看板 = 1,
  PBI看板 = 2,
  自定义 = 3,
}

/**
 * 获取业务线
 */
export const getBuLine = () => request('/kanban-config/businessLine')

/**
 * 获取经营指标树数据
 */
export const getBusinessTree = () => request('/kanban-config/businessTree')
/**
 * 获取用户可见的看板
 */
export const getUserBIList = () =>
  request<
    {
      pageId: string
      mainPageIds: number[]
      kanbanUrls: DashboardConfig[]
    }[]
  >('/kanban-config/page/config')

/**
 * 获取BI配置列表
 */
export const getBIList = (data: Partial<IBIConfigEnum> & PaginationQuery) =>
  request<PaginationData>('/kanban-config/page', {
    params: data,
  }).then((res) => {
    return Promise.resolve({
      list: res.data?.lines ?? [],
      total: res.data?.total ?? 0,
    })
  })

/**
 * 获取
 */
export const getBIEnumValues = async (isOuter: boolean) => {
  const response = await request(`/kanban-config/condition?isOuter=${!isOuter}`)
  const { businessLineInfos, menuInfos, names, statusInfos } = response.data
  traverseTree(menuInfos, (child: any, parent) => {
    child.parent = parent
    child.title = child.menuTitle
    if (!child.referenceTarget) {
      child.checkable = false
    } else {
      child.id = child.referenceTarget.id
    }
  })
  return {
    businessCodes: businessLineInfos,
    pages: menuInfos,
    names,
    statusList: statusInfos,
  }
}

/**
 * 新建、更新配置
 */
export const createBIConfig = (data: IBIConfigForm, method: 'POST' | 'PUT' = 'POST') =>
  request('/kanban-config', {
    method,
    data,
  })

/**
 * 删除 查看配置详情
 */
export const crudBIConfig = (method: 'DELETE' | 'GET', id: string, data?: any) => {
  return request<IBIConfigForm>(`/kanban-config/${id}`, {
    method,
    data,
  })
}

/**
 * 统一获取PBtoken
 */
export const getPBToken = (url: string) =>
  request(`/dashboard/power-bi?url=${url}`, {
    method: 'GET',
    handleError: false,
  })

/**
 * 统一获取PBI token
 */
export const getPBIToken = (id: number) =>
  request(`/dashboard/power-bi/token?id=${id}`, {
    method: 'GET',
  })

/**
 * 批量获取PBI token
 */
export const batchGetPBIToken = () => request('/dashboard/power-bi/user/tokens')
