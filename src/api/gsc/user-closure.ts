import { request } from '@/utils/request'

interface ExportUserParams {
  isBatch?: string[]
  entranceSystemList?: string[]
  accountNameList?: string[]
  deptLevel4NameList?: string[]
  supplierList?: string[]
}

export type UserListQueryParams = ExportUserParams & { pageNum?: number; pageSize?: number }

/**
 * 导出列表
 */
export const exportUserList = async (data: ExportUserParams) => {
  const res = await request('/gsc_person_entrance/exportDetail', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}

/**
 * 查询条件
 */
export const getUserQueryConditions = async () => {
  const res = await request('/gsc_person_entrance/condition', {
    method: 'POST',
  })
  return res
}

/**
 * 模糊查询条件(供应商/账号)
 */
export const getDynamicConditions = async (data) => {
  const res = await request('/gsc_person_entrance/fuzzySearch', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 模糊查询条件（人事部门）
 */
export const getDeptDynamicConditions = async (data) => {
  const res = await request('/gsc_person_entrance/getDept', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 列表页查询
 */
export const userListQuery = async (data: UserListQueryParams) => {
  const res = await request('/gsc_person_entrance/listRule', {
    method: 'POST',
    data,
  })
  return res?.data
}

/**
 * 新增人员
 */
export const addUserList = async (data) => {
  const res = await request('/gsc_person_entrance/addRule', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 用户收口详情
 */
export const userDetailList = async (data: UserListQueryParams) => {
  const res = await request('/gsc_person_entrance/listDetail', {
    method: 'POST',
    data,
  })
  return res?.data
}

/**
 * 删除收口配置
 */
export const deleteConfig = async (data) => {
  await request('/gsc_person_entrance/rule/delete', {
    method: 'POST',
    data,
  })
}

/**
 * 更新人维度配置
 */
export const updataDetailRule = async (data) => {
  const res = await request('/gsc_person_entrance/updateDetail', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 批量更新人维度配置
 */
export const batchUpdataDetailRule = async (data) => {
  const res = await request('/gsc_person_entrance/rule-detail/batch-update', {
    method: 'POST',
    data,
  })
  return res
}
