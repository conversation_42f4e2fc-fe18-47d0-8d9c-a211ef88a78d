import { request } from '@/utils/request'

export interface feedbackType {
  suppliers?: string[]
  createNames?: number[] | string[]
  pageNum?: number
  pageSize?: number
}

/**
 * 反馈列表
 */
export const getFeedbackList = async (data: feedbackType) => {
  const res = await request('/outer-feedback/list', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res?.data
}

/**
 * 查询条件
 */
export const getConditions = () => {
  return request('/outer-feedback/list/condition', {
    method: 'GET',
  })
}

/**
 * 反馈列表导出
 */
export const getReportDownload = async (data: feedbackType) => {
  const res = await request('/outer-feedback/list/export', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}
