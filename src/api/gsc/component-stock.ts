import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

interface PaginationProps {
  pnTypeList?: string[]
  skuCodeList?: string[]
  pnCodeList?: string[]
  stockOrgList?: string[]
  businessLineList?: string[]
  statusList?: boolean[]
  pageNum?: number
  pageSize?: number
}

/**
 * 分页查询采购库存组织
 */
export const getComponentStockList = async (params: PaginationProps) => {
  const res = await request('/component-stock-org', { params, handleError: false })
  return getPaginationData(res.data)
}

/**
 * 获取采购库存组织默认条件
 */
export const getComponentStockConditions = async () =>
  await request('/component-stock-org/conditions')

/**
 * 导出
 */
export const exportComponentStock = async (params: PaginationProps) =>
  await request('/component-stock-org/export', { params, handleError: false })

/**
 * 动态获取PN编码查询条件
 */
export const getComponentStockPnConditions = async (data) =>
  await request('/component-stock-org/pnCodeSearch', {
    data,
    method: 'POST',
    handleError: false,
  })
