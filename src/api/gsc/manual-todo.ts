import { getPaginationData } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'

export interface QueryWithoutDept {
  type?: number
  workBenchCode?: number
  appCode?: number
  categoryList?: string[]
  sourceIdList?: number[]
  executor?: number
  executors?: string[]
  stages?: number[]
  status?: number[]
  summarys?: number
  submitTotals?: number[]
  dataUrls?: number
  startTimeBegin?: number
  startTimeEnd?: number
  endTimeBegin?: number
  endTimeEnd?: number
  submitTimeBegin?: number
  submitTimeEnd?: number
  finished?: number
  deleted?: number
  includeEmptyLink?: number
  offset?: number
  pageSize?: number
}

export type QueryWithDept = QueryWithoutDept & {
  deptLevel3Names?: string[]
  deptLevel4Names?: string[]
}

export interface SubmitToApprovalType {
  remark: string | null
  dataUrl: string | null
}

/**
 * 下载默认模版
 **/
export const downloadDefaultTemplate = async () => {
  const res = await request('/categoryScene/default-summary-url', {
    method: 'GET',
  })
  return res
}

/**
 * 生成预上传地址
 **/
export const generatePreUploadUrl = async (params: { category: string; fileName: string }) => {
  const res = await request('/common/categoryFileUploadUri', {
    method: 'GET',
    params,
  })
  return res
}

/**
 * 生成预下载文件地址
 **/
export const generatePreDownloadUrl = async (params: {
  objectName: string
  longTimeSaveType?: number
}) => {
  const res = await request('/common/downloadFileUri', {
    method: 'GET',
    params,
  })
  return res
}

/**
 * 手工待办查询接口（带三级部门）
 **/
export const queryWithDept = async (data: QueryWithDept) => {
  const res = await request('/manual/todo/task/listFilterHiTaskLinkPage', {
    method: 'POST',
    data,
  })
  return getPaginationData(res?.data)
}

/**
 * 手工待办查询接口（非三级部门接口筛选）
 **/
export const queryWithoutDept = async (data: QueryWithoutDept) => {
  const res = await request('/manual/todo/task/listHiTaskLinkPage', {
    method: 'POST',
    data,
  })
  return res
}

export const queryWithoutDeptQ = async (data: QueryWithoutDept) => {
  const res = await request('/manual/todo/task/listHiTaskLinkPage', {
    method: 'POST',
    data,
  })
  return getPaginationData(res?.data)
}

/**
 * 获取待办查询条件
 **/
export const getTodoQueryConditions = async (params: { category: string }) => {
  const res = await request(`/manual/todo/task/default-conditions/${params.category}`, {
    method: 'GET',
    params,
  })
  return res
}

/**
 * 手工待办查询接口（非三级部门接口筛选）
 **/
export const submitToApproval = async (data: SubmitToApprovalType, sourceId?: string) => {
  const res = await request(`/todo/task/submit-approval/${sourceId}`, {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 获取手工待办表头
 **/
export const getManualTodoTableHeader = async (params: { category: string }) => {
  const res = await request(`/manual/todo/task/getTableHeader/${params.category}`, {
    method: 'GET',
    params,
  })
  return res
}

/**
 * 批量关闭待办
 **/
export const batchCloseTodo = async (data: { taskIds: string[]; action: string }) => {
  const res = await request('/manual/todo/task/batch-action', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 批量删除待办
 **/
export const batchDeleteTodo = async (data: {
  sourceIds: string[]
  deleteReason: string
  finished: boolean
}) => {
  const res = await request('/manual/todo/task/batch-remove', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 批量驳回待办
 **/
export const batchRejectTodo = async (data: {
  sourceIds: string[]
  taskIds: number[]
  rejectReason: string
}) => {
  const res = await request('/manual/todo/task/batch-reject', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 待办下发人-下载
 **/
export const downloadEditTable = async (data: {
  categoryList: string[]
  purchaseOrgList: string[]
  stockOrgList: string[]
}) => {
  const res = await request('/manual/todo/task/export', {
    method: 'POST',
    data,
  })
  return res
}

// 待办详情
export const queryHistoryList = async (params: { taskId: string }) => {
  const res = await request(`/manual/todo/task/getStageLog/${params.taskId}`, {
    method: 'GET',
    params,
  })
  return res
}

/**
 * 待办下发人-批量更新
 **/
export const batchUpdate = async (data) => {
  const res = await request('/manual/todo/task/batch-update', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 待办下发人-查询执行人
 **/
export const findAllExecutor = async (data: {
  executorName?: string
  executorNameList?: string[]
}) => {
  const res = await request('/manual/todo/task/findAllExecutor', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 待办下发人-上传
 **/
export const uploadEditTable = async (data: { objectName: string; category: string }) => {
  const res = await request('/manual/todo/task/upload', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}

/**
 * 部门联动
 **/
export const getThirdDept = async (data: { deptLevel3Names: string[] }) => {
  const res = await request('/manual/todo/task/getDept4ByDept3', {
    method: 'POST',
    data,
  })
  return res
}
