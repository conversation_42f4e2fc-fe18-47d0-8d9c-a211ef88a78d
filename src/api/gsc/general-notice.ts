import { EXTERNAL, INTERNAL, isGSCWorkbench } from '@/constants'
import { request } from '@/utils/request'

/**
 * 首页公告栏信息
 */
export const getGeneralNotice = async () => {
  const res = await request('/notify-items')
  return res
}

/**
 * 已阅公告栏不再展示
 */
export const getNoticeNotShow = async (data) => {
  const res = await request('/notify-items/history', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 公告栏默认查询条件
 */
export const getNoticeDefaultCondition = async () => {
  const res = await request('/notify-items/conditions', {
    method: 'GET',
    params: {
      portal: isGSCWorkbench ? INTERNAL : EXTERNAL,
    },
  })
  return res
}

/**
 * 公告栏新增时职位查询
 */
export const getJobs = async () => {
  const res = await request('/notify-items/queryJobs')
  return res
}

/**
 * 公告栏新增
 */
export const executeNoticeCreate = async (data) => {
  const res = await request('/notify-items/addNotify', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 公告栏删除
 */
export const executeNoticeDelete = async (data) => {
  const res = await request('/notify-items/deleteNotify', {
    method: 'POST',
    params: data,
  })
  return res
}

/**
 * 公告栏编辑
 */
export const executeNoticeUpdate = async (data) => {
  const res = await request('/notify-items/updateNotify', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 公告栏查询
 */
export const executeNoticeSearch = async (data) => {
  const res = await request('/notify-items/select', {
    method: 'POST',
    data,
  })
  return {
    total: res?.data?.total ?? 0,
    list: res?.data?.lines ?? [],
  }
}

/**
 * 获取所有业务线
 */
export const getAllBusinessLines = async () => await request('/notify-items/queryBusinessCodes')

/**
 * 获取所有供应商
 */
export const getAllSupplier = async (params: { supplierName: string }) =>
  await request('/notify-items/querySuppliers', {
    params,
  })
