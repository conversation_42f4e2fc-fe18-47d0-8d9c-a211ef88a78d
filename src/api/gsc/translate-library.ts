import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

interface ListParams {
  entryKey?: string
  regionLang?: number
  entryValue?: number
  pageNum?: number
  pageSize?: number
  startDate?: number
  endDate?: number
  startDateTime?: number
  endDateTime?: number
}

interface TransType {
  id?: number
  entryKey?: string
  regionLang?: number
  entryValue?: string
}

const apiPrefix = '/translation'

/**
 * 上传模板数据
 */
export const uploadTransTemp = async (data) =>
  await request(`${apiPrefix}/upload`, { method: 'POST', data })

/**
 * 删除
 */
export const deleteTrans = async (data) =>
  await request(`${apiPrefix}/deleteByIds`, { method: 'POST', data })

/**
 * 刷新数据生效
 */
export const refreshTrans = async () => await request(`${apiPrefix}/refresh`)

/**
 * 新增单个翻译键值对
 */
export const addTrans = async (data: TransType) =>
  await request(`${apiPrefix}/add`, { method: 'POST', data })

/**
 * 更新单个翻译文案
 */
export const updateTrans = async (data: TransType) =>
  await request(`${apiPrefix}/update`, { method: 'PUT', data })

/**
 * 模糊查询
 */
export const transFuzzySearch = async (data: { searchType: number; content: string[] }) =>
  await request(`${apiPrefix}/fuzzySearch`, { method: 'POST', data })

/**
 * 翻译查询页面
 */
export const getTransList = async (data: ListParams) => {
  const res = await request(`${apiPrefix}/list`, { method: 'POST', data })
  return getPaginationData(res.data)
}

/**
 * 获取批量上传模板
 */
export const getTransTemp = async () => await request(`${apiPrefix}/getExcelTemplate`)
