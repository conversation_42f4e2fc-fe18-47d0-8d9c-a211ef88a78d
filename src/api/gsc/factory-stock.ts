import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

interface PaginationProps {
  factoryCodeList?: string[]
  stockOrgList?: string[]
  businessLineList?: string[]
  statusList?: boolean[]
  pageNum?: number
  pageSize?: number
}

/**
 * 分页查询采购库存组织
 */
export const getFactoryStockList = async (params: PaginationProps) => {
  const res = await request('/factory-stock-org', { params, handleError: false })
  return getPaginationData(res.data)
}

/**
 * 获取采购库存组织默认条件
 */
export const getFactoryStockConditions = async () => await request('/factory-stock-org/conditions')

/**
 * 工厂库存组织导出
 */
export const exportFactoryStock = async (params: PaginationProps) =>
  await request('/factory-stock-org/export', { params, handleError: false })
