import { getPaginationDataWithColumns } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'
const prefix = 'npc/manual-bom'
export const getManualBomDetailConditions = async (data) => {
  const res = await request(`/${prefix}/condition`, {
    method: 'post',
    data,
  })
  return res
}

export const getManualBomDetailList = async (data) => {
  const res = await request(`/${prefix}/list`, {
    method: 'POST',
    data,
  })
  return getPaginationDataWithColumns(res?.data)
}

export const getManualUploadTemplate = async () =>
  await request(`/${prefix}/template`, {
    handleError: false,
  })

export const manualBomUpload = async (data) => {
  const res = await request(`/${prefix}/upload`, {
    method: 'post',
    data,
  })
  return res
}

export const exportManualList = (data) =>
  request(`/${prefix}/download`, {
    method: 'POST',
    data,
  })
