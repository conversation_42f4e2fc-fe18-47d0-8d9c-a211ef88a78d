import { getPaginationData } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'
interface BillListParams {
  pageNum?: number
  pageSize?: number
  projectNames?: string[]
  saleSites?: string[]
  configs?: string[]
  versions?: string[]
  states?: string[]
}
interface ConfirmBill {
  id?: number
  cvpnCode?: string
  matCatLvl1Code?: string
  matCatLvl2Code?: string
  mpnId?: string
  menge?: number
}
const apiPrefix = '/costed-bom'
// 根据 mpnId 获取 mpn 信息
export const getMetaByMpnId = async (params: { mpnId: string }) =>
  await request(`${apiPrefix}/mpn-meta`, {
    params,
    handleError: false,
  })
// 上传 excel
export const uploadBillExcel = async (data: { objectName: string }) =>
  await request(`${apiPrefix}/upload`, {
    method: 'POST',
    data,
    handleError: false,
  })
// 下载模板
export const downloadBillTpl = async () =>
  await request(`${apiPrefix}/template`, {
    handleError: false,
  })
// 获取清单列表
export const getBillList = async (data: BillListParams) => {
  const res = await request(`${apiPrefix}/list`, {
    method: 'POST',
    data,
  })
  return getPaginationData(res?.data)
}
// 根据 id 删除清单
export const deleteBillById = async (data: { id: number }) =>
  await request(`${apiPrefix}/delete`, {
    method: 'POST',
    data,
    handleError: false,
  })
// 根据 id 复制清单
export const copyBillById = async (data: { id: number }) =>
  await request(`${apiPrefix}/copy`, {
    method: 'POST',
    data,
    handleError: false,
  })

// 导出清单
export const exportBill = async (data: Partial<BillListParams>) =>
  await request(`${apiPrefix}/export`, {
    method: 'POST',
    data,
    handleError: false,
  })
// 确认清单
export const confirmBill = async (data: ConfirmBill) =>
  await request(`${apiPrefix}/confirm`, {
    method: 'POST',
    data,
    handleError: false,
  })
// 获取表单条件
export const getBillFormConditions = async () => await request(`${apiPrefix}/condition`)
// 确认
export const batchConfirmBills = async (ids: number[]) =>
  await request(`${apiPrefix}/bulk-confirm`, {
    method: 'POST',
    handleError: false,
    data: { ids },
  })
// 确认无复用
export const batchConfirmReuseBills = async (ids: number[]) =>
  await request(`${apiPrefix}/bulk-no-use`, {
    method: 'POST',
    handleError: false,
    data: { ids },
  })
