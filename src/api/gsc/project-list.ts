import { getPaginationDataWithColumns } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'

const prefix1 = '/npc/cost'
const prefix2 = '/npc/detail'
const prefix3 = '/npc/bom'
const prefix4 = '/npc/uploader'
const prefix5 = '/npc/purchase-channel'

interface PurchaseDetailListParams {
  projectNames?: string[]
  isCollects?: string[]
  bizNames?: string[]
  purchaseChannels?: string[]
  states?: string[]
  matCatLvl2Codes?: string[]
  matCatLvl3Codes?: string[]
  pageNum?: number
  pageSize?: number
}

interface DownloadProps {
  projectNames?: string[]
  milestones?: string[]
  startDate?: number
  endDate?: number
  states?: string[]
  saleSites?: string[]
  configs?: string[]
  colors?: string[]
  exchangeRateType?: string
  type?: number
  updateUsers?: string[]
  subjects?: string[]
  categorys?: string[]
  costMatCatLvl2Codes?: string[]
  costMatCatLvl3Codes?: string[]
  isFinance?: boolean
}

export type ListProps =
  | DownloadProps
  | {
      pageNum?: number
      pageSize?: number
    }

interface BomDetailListParams {
  projectNames?: string[]
  bizNames?: string[]
  searchName?: string
  saleSites?: string[]
  configs?: string[]
  colors?: string[]
  categorys?: string[]
  milestones?: string[]
  states?: string[]
  dataTypes?: string[]
  dataVersions?: string[]
  costMatCatLvl3Codes?: string[]
  bomCategcoorys?: string[]
  dataDimensions?: string[]
  pageNum?: number
  pageSize?: number
}

interface DetailListProps {
  projectNames?: string[]
  bizNames?: string[]
  saleSites?: string[]
  configs?: string[]
  colors?: string[]
  categorys?: string[]
  milestones?: string[]
  states?: string[]
  pageNum?: number
  pageSize?: number
}

export const downloadProList = (data: DownloadProps) =>
  request(`${prefix1}/download`, {
    method: 'POST',
    data,
  })

export const getProList = async (data: ListProps) => {
  const res = await request(`${prefix1}/list`, {
    method: 'POST',
    data,
  })
  return getPaginationDataWithColumns(res?.data)
}

export const getProConditions = () => request(`${prefix1}/condition`)

export const execConfirm = (data) =>
  request(`${prefix1}/confirm`, {
    method: 'POST',
    data,
  })

export const execUnlock = (data) =>
  request(`${prefix1}/unlock`, {
    method: 'POST',
    data,
  })

export const uploadProDetail = (data: {
  objectName?: string
  skipWaersCheck?: boolean
  skipMaterialCostCheck?: boolean
}) =>
  request(`${prefix2}/upload`, {
    method: 'POST',
    data,
    handleError: false,
  })

export const downloadDetail = (data: DetailListProps) =>
  request(`${prefix2}/download`, {
    method: 'POST',
    data,
  })

export const getDetailList = async (data: DetailListProps) => {
  const res = await request(`${prefix2}/list`, {
    method: 'POST',
    data,
  })
  return getPaginationDataWithColumns(res?.data)
}

export const downloadSimulationCalc = (data: DetailListProps) => {
  const res = request('/npc/detail/simu-calc/download', {
    method: 'POST',
    data,
  })
  return res
}

export const downloadBomtemplateCalc = async (data: DetailListProps) => {
  const res = request('/npc/bom/template/download', {
    method: 'POST',
    data,
  })
  return res
}

export const getDetailConditions = async (data) => {
  const res = await request(`${prefix2}/condition`, {
    method: 'POST',
    data,
  })
  return res
}

export const getBomDetailConditions = async (data) => {
  const res = await request(`${prefix3}/condition`, {
    method: 'POST',
    data,
  })
  return res
}

export const getBomList = async (data) => {
  const res = await request(`${prefix3}/list`, {
    method: 'POST',
    data,
  })
  return getPaginationDataWithColumns(res?.data)
}

export const downloadBomList = (data: BomDetailListParams) =>
  request(`${prefix3}/download`, {
    method: 'POST',
    data,
  })

export const uploadBomList = (data: BomDetailListParams) =>
  request(`${prefix3}/upload`, {
    method: 'POST',
    data,
  })

export const getConfigConditions = (data: BomDetailListParams) =>
  request(`${prefix4}/condition`, {
    method: 'POST',
    data,
  })

export const getConfigDetailConditions = (data) =>
  request(`${prefix4}/upsert/condition`, {
    method: 'POST',
    data,
  })

export const getConfigDetailData = (params) =>
  request(`${prefix4}/upsert/get`, {
    method: 'GET',
    params,
  })

export const getConfigDetailList = async (data) => {
  const res = await request(`${prefix4}/list`, {
    method: 'POST',
    data,
  })
  return getPaginationDataWithColumns(res?.data)
}

interface ExportConfigParams {
  subject?: string[]
  category?: string[]
  matCatLvl1Code?: string[]
  matCatLvl2Code?: string[]
  matCatLvl3Code?: string[]
  maintainer?: string[]
  maintainFlag?: string[]
  type?: string[]
  updateTime?: string[]
  updateUser?: string[]
}

export type ConfigListQueryParams = ExportConfigParams & { pageNum?: number; pageSize?: number }

export const exportConfigList = (data: ExportConfigParams) =>
  request(`${prefix4}/download`, {
    method: 'POST',
    data,
  })

export const upsertConfigData = (data) =>
  request(`${prefix4}/upsert`, {
    method: 'POST',
    data,
  })

export const updateConfigData = (data) =>
  request(`${prefix4}/update`, {
    method: 'POST',
    data,
  })

export const addConfigData = (data) =>
  request(`${prefix4}/add`, {
    method: 'POST',
    data,
  })

/**
 * BOM数据类型配置
 */
export const getBomDataType = async (pageType?: string) => {
  const res = await request('/display-field/list/default', {
    params: {
      pageType,
    },
  })
  return res
}

/**
 * 保存BOM数据类型配置
 */
export const saveBomDataType = async (data: {
  pageType: string
  content: string
  id?: string | number
}) => {
  const res = await request<AnyType>('/display-field/upsert', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}

export const getPurchaseDetailConditions = (data) =>
  request(`${prefix5}/condition`, {
    method: 'POST',
    data,
  })

export const getPurchaseList = async (data: PurchaseDetailListParams) => {
  // 发送异步请求，获取采购列表数据
  const res = await request(`${prefix5}/list`, {
    method: 'POST',
    data,
  })
  // 对响应数据进行处理，获取分页数据
  return getPaginationDataWithColumns(res?.data)
}

export const downloadPurchaseList = (data: PurchaseDetailListParams) =>
  request(`${prefix5}/download`, {
    method: 'POST',
    data,
  })

export const deletePurchaseData = (data) =>
  request(`${prefix5}/del`, {
    method: 'POST',
    data,
  })

export const updatePurchaseData = (data) =>
  request(`${prefix5}/upsert`, {
    method: 'POST',
    data,
  })

export const batchUpdatePurchaseData = (data) =>
  request(`${prefix5}/bulk-update`, {
    method: 'POST',
    data,
  })

export const batchUpdateContion = (data) =>
  request(`${prefix5}/bulk-update/condition`, {
    method: 'POST',
    data,
  })

export const batcUpsertContion = (data) =>
  request(`${prefix5}/upsert/condition`, {
    method: 'POST',
    data,
  })

export const getConfigChannelDetailData = (data) =>
  request(`${prefix5}/upsert/get`, {
    method: 'POST',
    data,
  })

export type DropDownConfigProps = {
  bizName?: string
  matCatLvl1Code?: string
  matCatLvl2Code?: string
}

export const getDropDownList = async (params: DropDownConfigProps) => {
  const res = await request(`${prefix5}/upsert/dropdown-list`, {
    params,
  })
  return res
}

export const refreshRight = async () =>
  request(`${prefix5}/priv-reload`, {
    handleError: false,
  })

// 填报变更
export const updateFillIn = async (data: { id: number }) =>
  request(`${prefix5}/newest-milestone/change`, {
    method: 'POST',
    data,
    handleError: false,
  })

// 填报删除
export const deleteFillIn = async (data: { id: number }) =>
  request(`${prefix5}/newest-milestone/del`, {
    method: 'POST',
    data,
    handleError: false,
  })

// 填报补充
export const addFillIn = async (data: { id: number }) =>
  request(`${prefix5}/newest-milestone/add`, {
    method: 'POST',
    data,
    handleError: false,
  })

export const refreshMaintainerRight = async () =>
  request(`${prefix4}/priv-reload`, {
    handleError: false,
  })

/**
 * 同步采购渠道 {}
 * 同步数据维护维度 {id: 0, type: 0}
 * 同步生成模式 {id: 0, type: 1}
 */
export const handleSynchronous = async (data: { id?: number; type?: number }) =>
  request(`${prefix4}/sync`, {
    handleError: false,
    method: 'POST',
    data,
  })

export const getProjectListApproveList = (data: { milestoneId: number; uploadId?: number }) =>
  request(`${prefix1}/audit/list`, {
    method: 'POST',
    data,
  })

export const updateProjectListApprove = (data: {
  milestoneId: number
  remark: string
  uploadId?: number
}) =>
  request(`${prefix1}/audit`, {
    method: 'POST',
    data,
    handleError: false,
  })

export const getNewProjectListCondition = (data) =>
  request(`${prefix1}/condition2`, {
    method: 'POST',
    data,
  })

export const getNewProjectListConditionNew = (data) =>
  // 发送请求到指定URL
  request(`${prefix1}/condition`, {
    // 请求方法
    method: 'POST',
    // 请求数据
    data,
  })
