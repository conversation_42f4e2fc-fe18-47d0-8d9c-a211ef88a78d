import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

interface PaginationProps {
  stockOrgList?: string
  descriptionList?: string
  centStockFlagList?: string
  statusList?: string
  createNameList?: string
  startTime?: string
  endTime?: string
  pageNum?: number
  pageSize?: number
}

interface CommonPropsType {
  stockOrg?: number
  description?: string
  centStFlag?: number
  status?: boolean
}
/**
 * 库存组织分页查询
 */
export const getStockOrgList = async (params: PaginationProps) => {
  const res = await request('/stock-org', { params })
  return getPaginationData(res.data)
}

/**
 * 库存组织新增
 */
export const addStockOrg = async (data: CommonPropsType) =>
  await request('/stock-org', {
    method: 'POST',
    data,
    handleError: false,
  })

/**
 * 更新库存名称
 */
export const updateStockName = async (data: { description: string; id: number }) =>
  await request(`/stock-org/${data.id}`, {
    method: 'PUT',
    data: { description: data.description },
    handleError: false,
  })

/**
 * 更新库存组织状态
 */
export const updateStockOrg = async (data: { status: boolean; id: number }) =>
  await request(`/stock-org/update-status/${data.id}`, {
    method: 'PUT',
    data: { status: data.status },
    handleError: false,
  })

/**
 * 库存组织默认查询条件
 */
export const getStockOrgConditions = async () => await request('/stock-org/conditions')
