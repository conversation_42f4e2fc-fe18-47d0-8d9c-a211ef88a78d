import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

interface PaginationProps {
  purchaseOrgList?: string
  descriptionList?: string
  centPurchFlagList?: string
  statusList?: string
  createNameList?: string
  startTime?: string
  endTime?: string
  pageNum?: number
  pageSize?: number
}

interface CommonPropsType {
  purchaseOrg?: number
  description?: string
  centPurchFlag?: number
  status?: boolean
}

/**
 * 采购组织分页查询
 */
export const getPurchaseOrgList = async (params: PaginationProps) => {
  const res = await request('/purchase-org', { params })
  return getPaginationData(res.data)
}

/**
 * 采购组织新增
 */
export const addPurchaseOrg = async (data: CommonPropsType) =>
  await request('/purchase-org', {
    method: 'POST',
    data,
    handleError: false,
  })

/**
 * 更新采购名称
 */
export const updatePurchaseName = async (data: { description: string; id: number }) =>
  await request(`/purchase-org/${data.id}`, {
    method: 'PUT',
    data: { description: data.description },
    handleError: false,
  })

/**
 * 更新采购组织状态
 */
export const updatePurchaseOrg = async (data: { status: boolean; id: number }) =>
  await request(`/purchase-org/update-status/${data.id}`, {
    method: 'PUT',
    data: { status: data.status },
    handleError: false,
  })

/**
 * 采购组织默认查询条件
 */
export const getPurchaseOrgConditions = async () => await request('/purchase-org/conditions')
