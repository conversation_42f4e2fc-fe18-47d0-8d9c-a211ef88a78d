import { request } from '@/utils/request'

/**
 * 获取待办Tabs统计
 */
export const getTodoStatistics = async () => await request('/todo/task/statistics')

/**
 * 获取待办列表
 */
export const getTableData = async (pageNum = 1, pageSize = 10, category: string, type: number) =>
  await request('/todo/task', {
    params: { pageNum, pageSize, category, type },
  })

/**
 * 批量已读，读一个也用该接口
 */
export const batchRead = async (data: { taskIds: string[] }) =>
  await request('/todo/task', {
    method: 'POST',
    data,
  })

/**
 * 导出Excel
 */
export const exportExcel = async (category: string, type: number, categoryName: string) =>
  await request('/todo/task/export', {
    params: { category, type, categoryName },
  })

/**
 * 批量处理待办
 */
export const batchTodo = ({ category = '', sourceIdList = '' }) =>
  request('/todo/task/batch-url', {
    method: 'POST',
    data: { category, sourceIdList },
    handleError: false,
  })

/**
 * 待办转移
 */
export const exectorTransfer = (data) =>
  request('/todo/info/executor/all/batch', {
    method: 'PUT',
    data,
    handleError: false,
  })

/**
 * 待办弹窗全局筛选器查询
 */
export const getTodoAllConditions = (data) =>
  request('/todo/task/filter', {
    method: 'POST',
    data,
    handleError: false,
  })

/**
 * 待办弹窗全局筛选器模糊查询
 */
export const queryTodoConditions = (data: { category: string; pageTotal: number }) =>
  request('/todo/task/filterCondition', {
    method: 'POST',
    data,
    handleError: false,
  })
