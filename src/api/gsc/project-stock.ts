import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

interface PaginationProps {
  projectOrgList?: string[]
  stockOrgList?: string[]
  businessLineList?: string[]
  statusList?: boolean[]
  pageNum?: number
  pageSize?: number
}

/**
 * 分页查询采购库存组织
 */
export const getProjectStockList = async (params: PaginationProps) => {
  const res = await request('/project-stock-org', { params, handleError: false })
  return getPaginationData(res.data)
}

/**
 * 获取采购库存组织默认条件
 */
export const getProjectStockConditions = async () => await request('/project-stock-org/conditions')

/**
 * 项目库存组织导出
 */
export const exportProjectStock = async (params: PaginationProps) =>
  await request('/project-stock-org/export', { params, handleError: false })
