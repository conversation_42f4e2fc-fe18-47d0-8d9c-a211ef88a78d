import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

interface PaginationProps {
  warehouseCodeList?: string[]
  stockOrgList?: string[]
  businessLineList?: string[]
  statusList?: boolean[]
  pageNum?: number
  pageSize?: number
}

/**
 * 分页查询采购库存组织
 */
export const getWarehouseStockList = async (params: PaginationProps) => {
  const res = await request('/warehouse-stock-org', { params, handleError: false })
  return getPaginationData(res.data)
}

/**
 * 获取采购库存组织默认条件
 */
export const getWarehouseStockConditions = async () =>
  await request('/warehouse-stock-org/conditions')

/**
 * 仓库库存组织导出
 */
export const exportWarehouseStock = async (params: PaginationProps) =>
  await request('/warehouse-stock-org/export', { params, handleError: false })
