import { request } from '@/utils/request'

/**
 * 新增视图
 */
export const add = (data: {
  title: string
  content: string | Record<string, Record<string, string> | Record<string, string | boolean>[]>
  pageType: string
  isDefault?: number
}) => {
  return request('/variant', {
    method: 'POST',
    data,
    handleError: false,
  })
}

/**
 * 修改视图名称
 */
export const update = (data: {
  title: string
  id: number
  pageType: string
  isDefault: number
}) => {
  return request('/variant', {
    method: 'PUT',
    data,
    handleError: false,
  })
}

/**
 * 查询视图
 */
export const get = (params: { pageType: string }) => {
  return request<{ title: string; content: string; isDefault: 0 | 1 }[]>('/variant', {
    params,
    handleError: false,
  })
}

/**
 * 删除视图
 */
export const del = (id: number) => {
  return request(`/variant/${id}`, {
    method: 'DELETE',
    handleError: false,
  })
}

/**
 * 获取所有默认视图
 */
export const getDefaultViews = () =>
  request('/variant/list/default', {
    handleError: false,
  })
