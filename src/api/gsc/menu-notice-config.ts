import { request } from '@/utils/request'

const apiPrefix = '/menu-notice'

/**
 * 删除通知
 *
 * @param data 包含通知ID的对象
 * @param data.noticeId 通知ID，数字
 * @returns 返回删除通知的结果
 */
export const delNotice = async (data: { noticeId: number }) =>
  request(`${apiPrefix}/del-notice`, {
    method: 'POST',
    data,
  })

/**
 * 获取菜单列表
 *
 * @param data 包含通知状态和工作台类型的对象
 * @param data.noticeState 通知状态，布尔值
 * @param data.workbenchType 工作台类型，数字
 * @returns 返回获取到的菜单列表数据
 */
export const getMenuList = async (data: { noticeState: boolean; workbenchType: number }) =>
  request(`${apiPrefix}/menu-list`, {
    method: 'POST',
    data,
  })

/**
 * 获取通知列表
 *
 * @param data 包含menuId和workbenchType的对象
 * @param data.menuId 菜单ID，数字
 * @param data.workbenchType 工作台类型，数字
 * @returns 通知列表数据
 */
export const getNoticeList = async (data: { menuId: number; workbenchType: number }) =>
  request(`${apiPrefix}/notice-list`, {
    method: 'POST',
    data,
  })

/**
 * 保存通知
 *
 * @param data 保存通知所需的数据
 * @param data.id 通知的唯一标识符
 * @param data.workbenchType 工作台类型
 * @param data.menuId 菜单ID
 * @param data.menuCode 菜单编码
 * @param data.title 通知标题
 * @param data.content 通知内容
 * @param data.expireDate 通知过期时间戳
 * @returns 保存结果
 */
export const saveNotice = async (data: {
  id: number
  workbenchType: number
  menuId: number
  menuCode: string
  title: string
  content: string
  expireDate: number
}) => request(`${apiPrefix}/save`, { method: 'POST', data })
