import { getPaginationData } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'

interface TrackCondition {
  type: number | string
  startDate?: string
  endDate?: string
  dept3: string[]
  dept4: string[]
  dept5: string[]
  workbenchNames: string[]
  menuTitles: string[]
  kpi?: string
  nonMenuTitles?: string[]
  isRanking?: boolean
}
interface FuncTrackCondition {
  startDate?: number
  endDate?: number
  dept2?: string[]
  dept3?: string[]
  dept4?: string[]
  dept5?: string[]
  workbenchNames?: string[]
  menuTitles?: string[]
  pageNum?: number
  pageSize?: number
  menuCodes?: string[]
  pageCodes?: string[]
  pageNames?: string[]
  functionTypes?: string[]
  functionCodes?: string[]
  uvRanks?: string[]
  orderField?: number
  orderRule?: number
  fromSupplier?: number
}

/**
 * 动态获取下拉框条件（共用）
 */
export const getTrackConditions = async (data: TrackCondition) =>
  await request('/menuTrackUv/findQueryCondition', { method: 'POST', data, handleError: false })

/**
 * 浏览次数 / 获取次数前三汇总
 */
export const getFreTrackTopThree = async (data: TrackCondition) =>
  await request('/menuTrackPv/getPvTotal', { method: 'POST', data, handleError: false })

/**
 * 浏览次数 / 获取浏览10次以内的明细
 */
export const getFreTrackDetail = async (data) => {
  const res = await request('/menuTrackPv/getPvDetail', {
    method: 'POST',
    data,
    handleError: false,
  })
  return getPaginationData(res.data)
}

/**
 * 浏览次数 / 获取统计柱状图数据
 */
export const getFreTrackHistogram = async (data: TrackCondition) =>
  await request('/menuTrackPv/getPvHistogram', { method: 'POST', data, handleError: false })

/**
 * 浏览次数 / 导出使用次数
 */
export const exportFreTrack = async (data) =>
  await request('/menuTrackPv/export', { method: 'POST', data, handleError: false })

/**
 * 浏览次数 / 导出使用最少菜单
 */
export const exportFreDetail = async (data) =>
  await request('/menuTrackPv/exportDetail', { method: 'POST', data, handleError: false })

/**
 * 浏览人数 / 导出使用人数
 */
export const exportVisTrack = async (data) =>
  await request('/menuTrackUv/export', { method: 'POST', data, handleError: false })

/**
 * 浏览人数 / 导出未使用人员清单
 */
export const exportVisDetail = async (data) =>
  await request('/menuTrackUv/exportDetail', { method: 'POST', data, handleError: false })

/**
 * 浏览人数 / 获取总使用未使用人数
 */
export const getVisTrackTotal = async (data: TrackCondition) =>
  await request('/menuTrackUv/getUvTotal', { method: 'POST', data, handleError: false })

/**
 * 浏览人数 / 获取总使用人数
 */
export const getVisTrackAllTotal = async (data: TrackCondition) =>
  await request('/menuTrackUv/getUvAllTotal', { method: 'POST', data, handleError: false })

/**
 * 浏览人数 / 获取总使用未使用人数
 */
export const getVisTrackHistogram = async (data: TrackCondition) =>
  await request('/menuTrackUv/getUvHistogram', { method: 'POST', data, handleError: false })

/**
 * 浏览人数 / 获取未使用人员清单
 */
export const getVisTrackDetail = async (data) => {
  const res = await request('/menuTrackUv/getUvDetail', {
    method: 'POST',
    data,
    handleError: false,
  })
  return getPaginationData(res.data)
}

/**
 * 功能埋点 / 功能使用明细导出
 */
export const exportUsageDetails = async (data: FuncTrackCondition) => {
  const res = await request('/menuClick/exportDetail', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}

/**
 * 功能埋点 / 功能使用详情表格
 */
export const getClickStatistics = async (data: FuncTrackCondition) => {
  const res = await request('/menuClick/getClickStatistics', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res.data
}

/**
 * 功能埋点 / 功能使用详情表格导出
 */
export const exportStatistics = async (data: FuncTrackCondition) => {
  const res = await request('/menuClick/exportStatistics', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}

/**
 * 功能埋点 / 功能使用频率柱状图
 */
export const getFreHistogram = async (data: FuncTrackCondition) => {
  const res = await request('/menuClick/getFreHistogram', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res.data
}

/**
 * 功能埋点 / 功能使用频率饼状图
 */
export const getFrePieChart = async (data: FuncTrackCondition) => {
  const res = await request('/menuClick/getFrePieChart', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res.data
}

/**
 * 功能埋点 / 功能点使用UV PV柱状图
 */
export const getClickHistogram = async (data: FuncTrackCondition) => {
  const res = await request('/menuClick/getClickHistogram', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res.data
}

/**
 * 功能埋点 / 功能点使用详情表格筛选条件
 */
export const getClickStatisticsCondition = async (data: FuncTrackCondition) => {
  const res = await request('/menuClick/getClickStatisticsCondition', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res.data
}

// 菜单已使用人数汇总列表
export const getMenuTrackUv = async (data: TrackCondition) => {
  const res = await request('/menuTrackUv/page-menu-used-summary', {
    method: 'POST',
    data,
    handleError: false,
  })
  return getPaginationData(res.data)
}

// 菜单已使用人数汇总列表导出
export const exportMenuTrackUv = async (data: TrackCondition) =>
  await request('/menuTrackUv/export-menu-used-summary', {
    method: 'POST',
    data,
    handleError: false,
  })

// 外部用户菜单已使用人数汇总列表
export const getMenuTrackUvExternal = async (data: TrackCondition) => {
  const res = await request('/menuTrackUv/page-supplier-used-summary', {
    method: 'POST',
    data,
    handleError: false,
  })
  return getPaginationData(res.data)
}

// 外部用户菜单已使用人数汇总列表导出
export const exportMenuTrackUvExternal = async (data: TrackCondition) =>
  await request('/menuTrackUv/export-supplier-used-summary', {
    method: 'POST',
    data,
    handleError: false,
  })

// 获取菜单使用PV，UV统计
export const getMenuTrackStatistics = async (data: TrackCondition) => {
  const res = await request('/menuTrackPv/page-menu-pv-uv-summary', {
    method: 'POST',
    data,
    handleError: false,
  })
  return getPaginationData(res.data)
}

// 导出菜单使用PV，UV统计
export const exportMenuTrackStatistics = async (data: TrackCondition) =>
  await request('/menuTrackPv/export-menu-pv-uv-summary', {
    method: 'POST',
    data,
    handleError: false,
  })
