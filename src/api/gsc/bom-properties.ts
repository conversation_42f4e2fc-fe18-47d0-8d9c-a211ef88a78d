import { request } from '@/utils/request'

type MatRelCategoryType = {
  startDate: string // pn创建时间：查询开始时间
  endDate: string // pn创建时间：查询结束时间
  categoryList?: string[] // 三级品类列表
  cvpnList?: string[] // cvpn列表
  pnList?: string[] // pn列表
  mpnIdList?: string[] // mpnId列表
  spCodeList?: string[] // 供应商料号列表
  brandList?: string[] // 品牌列表
  lifnrCodeList?: string[] // 供应商列表列表
  sourcingOprList?: string[] // 资源负责人列表
  bizCodeList?: string[] // 业务线列表
  dynamicConditionList?: AnyType[] // 过滤条件对象数组
  purchaseUserList: string[] // 采购员列表
  projectList: string[] // 初始项目列表
  projectListList: string[] // 项目合集列表
  pageNum: number // 页码
  pageSize: number // 分页大小
}

/**
 * 物料属性详情导出
 * @param cvpn cvpn编码
 * @param pnList pn列表【cvpn和pn列表不能同时为空】
 */
export const exportMatAttDetail = async (
  cvpn?: string,
  pnCode?: string,
  mpnId?: string,
  lifnrCode?: string
) => {
  const res = await request('/material/attribute/detail/export', {
    params: { cvpn, pnCode, mpnId, lifnrCode },
  })
  return res
}

/**
 * 物料属性详情
 * @param cvpn cvpn编码
 * @param pnList pn列表 【cvpn和pn列表不能同时为空】
 */
export const getMatAttDetail = async (
  cvpn: string,
  pnCode: string,
  mpnId: string,
  lifnrCode: string
) => {
  const res = await request('/material/attribute/detail', {
    params: { cvpn, pnCode, mpnId, lifnrCode },
  })
  return res
}

export const exportMatRelCategory = async (data: MatRelCategoryType) => {
  const res = await request<AnyType>('/material/attribute/export', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}

/**
 * 物料关系分类分页
 */
export const getMatRelCatPagination = async (data: MatRelCategoryType) => {
  const res = await request<AnyType>('/material/attribute', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}

/**
 * 物料关系分类分页列查询条件
 */
export const getMatRelColumnCondition = async (data: MatRelCategoryType) => {
  const res = await request<AnyType>('/material/attribute/table-condition', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}

/**
 * 查询条件模糊搜索
 * @param searchType 1-物料编码; 2-CVPN; 3-MPNID; 4-供应商料号; 5-品牌; 6-供应商; 7-资源负责人
 * @param content 模糊搜索输入内容
 */
export const getFuzzySearch = async (data: MatRelCategoryType) => {
  const res = await request('/material/attribute/query-conditions', {
    data,
    method: 'POST',
    handleError: false,
  })
  return res
}

/**
 * 默认查询条件
 */
export const getDefaultSearch = async () => {
  const res = await request<AnyType>('/material/attribute/default-conditions', {
    method: 'GET',
  })
  return res
}

/**
 * 物料三级分类列表
 */
export const getMatThirdCategory = async () => {
  const res = await request('/material/attribute/category/list')
  return res
}

/**
 * 查询页面表头配置
 */
export const getColumnConfig = async (pageType?: string) => {
  const res = await request('/display-field/list/default', {
    params: {
      pageType,
    },
  })
  return res
}

/**
 * 保存页面表头配置
 */
export const saveColumnConfig = async (data: {
  pageType: string
  content: string
  id?: string | number
}) => {
  const res = await request<AnyType>('/display-field/upsert', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}
