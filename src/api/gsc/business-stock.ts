import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

interface PaginationProps {
  stockOrgList?: string[]
  businessLineList?: string[]
  statusList?: boolean[]
  createNames?: string[]
  startDate?: number
  endDate?: number
  pageNum?: number
  pageSize?: number
}

interface CommonProps {
  id?: number
  businessLineId?: string
  stockOrg?: string
  status?: boolean
}

/**
 * 分页查询业务线库存组织
 */
export const getBizStockList = async (params: PaginationProps) => {
  const res = await request('/businessLine-stock-org', { params })
  return getPaginationData(res.data)
}

/**
 * 新增业务线库存组织
 */
export const addBizStock = async (data: CommonProps) =>
  await request('/businessLine-stock-org', {
    method: 'POST',
    data,
    handleError: false,
  })

/**
 * 更新业务线库存组织
 */
export const updateBizStock = async (data: {
  id: number
  status?: boolean
  isOriginal?: boolean
}) => {
  const { id, ...left } = data
  return await request(`/businessLine-stock-org/${id}`, {
    method: 'PUT',
    data: left,
    handleError: false,
  })
}

/**
 * 获取业务线库存组织默认查询条件
 */
export const getBizStockConditions = async () => await request('/businessLine-stock-org/conditions')

/**
 * 获取新增业务线库存组织默认查询条件
 */
export const getAddBizStockConditions = async () =>
  await request('/businessLine-stock-org/add-conditions')
