import { request } from '@/utils/request'

const prefix = '/api/qms/api/v2/business_config/formula'
const apiPrefix = `${window.location.origin}${prefix}`

/**
 * 获取服务类型公式筛选规则详细信息
 */
export const getRuleDetail = async (props: { id: string }) =>
  await request(`${apiPrefix}/${props.id}/detail`, {
    enableLang: false,
  })

/**
 * 统计规则初始化数据
 */
export const getInitData = async (params: { formulaType: string; parentFormulaId: string }) =>
  await request(`${apiPrefix}/rule_init_data`, {
    method: 'GET',
    params,
    enableLang: false,
  })

export interface IFormulaRecord {
  pageNum: number
  pageSize: number
  total: number
  list: {
    formulaId: number
    id: number
    opContent: string
    opTime: string
    opUserId: string
  }[]
}

export interface IProductLine {
  id: number
  title: string
}

/**
 * 分页查询服务公式操作记录
 */
export const getFormulaRecord = async (id: string, data: AnyType = {}) =>
  await request<IFormulaRecord>(`${apiPrefix}/${id}/oplog`, {
    enableLang: false,
    params: data,
  })

/**
 * 获取业务产品线列表
 */
export const getProductLineList = async (params) =>
  await request<IProductLine[]>(`${apiPrefix}/productLine`, {
    method: 'GET',
    enableLang: false,
    params,
  })

/**
 * 新增服务类型公式
 */
export const addFormula = async (data: {
  area: string
  description: string
  formulaName: string
  formulaType: string
  parentId: number
  rules: AnyType[]
}) =>
  await request(`${apiPrefix}/add`, {
    method: 'POST',
    data,
    enableLang: false,
  })

/**
 * 删除服务类型公式
 */
export const deleteFormula = async (id: string) =>
  await request(`${apiPrefix}/delete/${id}`, {
    method: 'DELETE',
    enableLang: false,
  })

/**
 * 更新服务类型公式
 */
export const updateFormula = async (id: string, data: AnyType) =>
  await request(`${apiPrefix}/update/${id}`, {
    method: 'PUT',
    data,
    enableLang: false,
  })

/**
 * 服务类型公式树形结构数据
 */
export const getFormulaTree = async (params: Record<string, AnyType>) =>
  await request(`${apiPrefix}/tree`, {
    params,
    enableLang: false,
  })
