import { request } from '@/utils/request'

const prefix = '/api/dashboard/hardware_quality'
const apiPrefix = `${window.location.origin}/api/qms${prefix}`

/**
 * 获取看板列表信息
 * @returns {Promise<Array>} 看板列表数据
 */
export const getBoardList = () => {
  return request({
    url: `${apiPrefix}/init`,
    method: 'GET',
    enableLang: false,
    skipApiHost: true,
  })
}

/**
 * 获取看板数据
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} 看板数据
 */
export const getBoardData = (params) => {
  return request({
    url: `${apiPrefix}/statistics`,
    method: 'POST',
    data: params,
  })
}

/**
 * 获取重点项目数据
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} 重点项目数据
 */
export const getKeyProjectData = (params) => {
  return request({
    url: `${apiPrefix}/key_project/statistics`,
    method: 'POST',
    data: params,
  })
}
