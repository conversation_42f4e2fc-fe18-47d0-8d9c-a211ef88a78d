/* istanbul ignore file -- @preserve */
import { request } from '@/utils/request'
import { CancelToken } from 'axios'
const prefix = '/api/dashboard/nps'
const apiPrefix = `${window.location.origin}/api/qms${prefix}`

/**
 * 获取看板列表信息-国家地区/数据产出时间
 * @returns {Promise<Array>} 看板列表数据
 */
export const getRegionList = () => {
  return request({
    url: `${apiPrefix}/init`,
    method: 'POST',
  })
}

/**
 * 获取看板列表信息-品牌系列
 * @param {Object} params
 * @returns {Promise<Array>} 看板列表数据
 */
export interface BrandSeriesListParams {
  countryName: string
}
export const getBrandSeriesList = (params: BrandSeriesListParams) => {
  return request({
    url: `${apiPrefix}/init2`,
    method: 'POST',
    data: params,
  })
}

/**
 * 获取看板数据
 * @param {Object} params - 请求参数
 * @returns {Promise<Object>} 看板数据
 */
export interface BoardDataParams {
  category: string // 品类
  brand: string // 品牌
  seriesList: string[] // 系列
  statisticalType: string // 1: 月度 2: 周度
  countryName: string // 国家
}
export const getBoardData = (params: BoardDataParams, cancelToken: CancelToken) => {
  return request({
    url: `${apiPrefix}/statistics`,
    method: 'POST',
    data: params,
    cancelToken,
  })
}
export const getRegionBoardData = (params: BoardDataParams, cancelToken: CancelToken) => {
  return request({
    url: `${apiPrefix}/regional/statistics`,
    method: 'POST',
    data: params,
    cancelToken,
  })
}
