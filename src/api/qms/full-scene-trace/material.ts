import { request } from '@/utils/request'
import { download } from '@/utils/request-download'
import { CancelToken } from 'axios'

const apiPrefix = `/api/qms`

// 数据导出权限查询
export const getMaterialExportPermission = () => {
  return request<string[]>(`${apiPrefix}/api/common/v2/userRole`, {
    method: 'GET',
  })
}

// 筛选列数据查询
export const getMaterialDetailsFilter = (data = {}, cancelToken: CancelToken) => {
  return request(`${apiPrefix}/api/tracing/v2/material/filter`, {
    method: 'POST',
    data,
    cancelToken,
    handleError: false,
  })
}

// 数据导出(离线)
export const exportMaterialDetails = (data = {}) => {
  return request(`${apiPrefix}/api/tracing/v2/material/export`, {
    method: 'POST',
    data,
  })
}

// 数据导出(在线)
export const exportMaterialDetailsOnline = (data = {}) => {
  return download({
    url: `${apiPrefix}/api/tracing/v2/material/exportOnline`,
    method: 'POST',
    data,
  })
}

// 物料追溯查询
export const getMaterialDetails = (data = {}) => {
  return request(`${apiPrefix}/api/tracing/v2/material`, {
    method: 'POST',
    data,
  })
}
