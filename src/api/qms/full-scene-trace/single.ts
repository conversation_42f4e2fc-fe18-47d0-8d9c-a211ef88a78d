import { createRequest } from '@/utils/request'

const request = createRequest({
  baseURL: '/api/qms/api/tracing/v2',
  skipApiHost: true,
  enableLang: false,
})

export function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

export const SingleImeiApi = {
  /** 追溯查询 */
  async getTrace(key: string) {
    return request.get('/search', { params: { key } }).then((res) => {
      return transformTraceData({ ...res.data, key })
    })
  },
  /** 制造过站导出 */
  async exportWholeFactoryMfg(key: string) {
    return request.download('/mmc/export', { method: 'GET', params: { key } })
  },
  /** 用户画像查询 */
  async getUserInfo(key: string, mid: string) {
    return request.get('/persona', { params: { mid, key } }).then((res) => res.data)
  },
  /** 获取修整工厂维修单信息 */
  async getRepairOrderInfo(key: string, repairId: string) {
    // await delay(50000)
    return request.get('/repair', { params: { key, repairId } }).then((res) => res.data)
  },
  /** 获取服务与反馈工单信息 */
  async getServiceAndFeedbackOrderInfo(feedbackId: string) {
    return request.get('/feedback', { params: { feedbackId } }).then((res) => res.data)
  },
  /** 售后服务单查询 */
  async getServiceOrder(key: string, serviceId: string) {
    return request.get('/service', { params: { serviceId, key } }).then((res) => res.data)
  },
  /** 售后服务单-获取故障和维修Tab信息 */
  async getFaultAndRepairInfo(serviceId: string, itemId: string) {
    return request
      .get('/service/faultAndMethod', { params: { serviceId, itemId } })
      .then((res) => res.data)
  },
  /** 售后服务单-获取维修换料Tab信息 */
  async getRepairAndReplaceInfo(serviceId: string, itemId: string) {
    return request.get('/service/mac', { params: { serviceId, itemId } }).then((res) => res.data)
  },
  /** 售后服务单-获取附件信息 */
  async getAttachmentInfo(serviceId: string, itemId: string) {
    return request.get('/service/attach', { params: { serviceId, itemId } }).then((res) => res.data)
  },
  /** 售后服务单-获取检测信息Tab信息 */
  async getDetectionInfo(key: string, serviceId: string) {
    return request.get('/service/mitesting', { params: { serviceId, key } }).then((res) => res.data)
  },
  /** 售后服务单-获取自动化检测结果链接 */
  async getAutoDetectionResult(params: string) {
    return request.get(`/service/mitresult?${params}`).then((res) => res.data)
  },
}

// 追溯查询中制造过站的数据有点复杂，转换一下吧
function transformTraceData(data: AnyObject) {
  const { factory, ...rest } = data
  if (!factory) return rest

  const { obaInfo, workStationList } = factory
  const nextFactory = { ...factory }

  if (obaInfo && Array.isArray(obaInfo)) {
    nextFactory.obaInfo = obaInfo.map((item, index) => ({
      ...item,
      blockTitle: 'OBA' + (index ? '复检' : '初检'),
    }))
  }

  if (workStationList && Array.isArray(workStationList)) {
    const processEnum = { SMT: '贴片', SA: '板测', FA: '组装', RMT: '二检', PA: '包装', IS: '入库' }
    const workStationEnum = {
      SMT_SNO_INPUT: '进站',
      SMT_SNO_OUTPUT: '出站',
      SA_SNO_INPUT: '进站',
      SA_SNO_OUTPUT: '出站',
      ASSY_SNO_INPUT: '进站',
      RUUP: '出站',
      RUDW: '进站',
      ASSY_SNO_OUTPUT: '出站',
      PACK_SNO_INPUT: '进站',
      PACK_SNO_OUTPUT: '出站',
      // FGIS 入库
    }

    nextFactory.workStationList = workStationList.map((item) => {
      const processText = processEnum[item.process] || item.process || ''
      const suffix = workStationEnum[item.workStationEnum] || ''
      return {
        ...item,
        blockTitle: processText + suffix,
      }
    })
  }

  return { ...rest, factory: nextFactory }
}
