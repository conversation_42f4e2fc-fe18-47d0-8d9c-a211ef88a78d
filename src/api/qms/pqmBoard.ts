/* istanbul ignore file -- @preserve */
import { request } from '@/utils/request'
import { ProjectType } from '@/views/qms/pqmboard/interface'
const prefix = '/api/qms/api/dashboard/product_quality'
const apiPrefix = `${window.location.origin}${prefix}`

interface SummaryParams {
  version: string
  series: string
}
// 获取高层视角查询项目中查询 系列
export const getSeriesList = (params: Pick<SummaryParams, 'version'>) => {
  return request(`${apiPrefix}/series`, {
    enableLang: false,
    method: 'GET',
    params,
  })
}
// 获取高层视角下项目统计数量
export const getSummaryList = (params: SummaryParams) => {
  return request(`${apiPrefix}/summary`, {
    enableLang: false,
    method: 'GET',
    params,
  })
}
// 获取项目里程数据
export const getGeneralList = (params: {
  queryType: ProjectType
  series: string
  version: string
}) => {
  return request(`${apiPrefix}/general`, {
    enableLang: false,
    method: 'GET',
    params,
  })
}
// 获取项目设计阶段数据
export const getDesignList = (data: { projectCode: string; version: string; trPhase: string }) => {
  return request(`${apiPrefix}/design`, {
    enableLang: false,
    method: 'POST',
    data,
  })
}
// 获取项目验证阶段数据
export const getValidationList = (data: {
  projectCode: string
  version: string
  trPhase: string
}) => {
  return request(`${apiPrefix}/validation`, {
    enableLang: false,
    method: 'POST',
    data,
  })
}
// 获取项目售后阶段FFR
export const getFfrList = (data: { projectCode: string; version: string }) => {
  return request(`${apiPrefix}/ffr`, {
    enableLang: false,
    method: 'POST',
    data,
  })
}
// 获取项目售后阶段NPS
export const getNpsList = (data: { projectCode: string; version: string; duration: string }) => {
  return request(`${apiPrefix}/nps`, {
    enableLang: false,
    method: 'POST',
    data,
  })
}
