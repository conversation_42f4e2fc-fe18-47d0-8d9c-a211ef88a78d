import { request } from '@/utils/request'

const prefix = '/api/qms/api/dashboard/v2/project'
const apiPrefix = `${window.location.origin}${prefix}`

// 获取高层视角查询项目中查询 系列
export const getSeriesList = () => {
  return request(`${apiPrefix}/series`, {
    enableLang: false,
    method: 'GET',
  })
}

// 获取高层视角下项目统计数量
export const getSummaryList = (params) => {
  return request(`${apiPrefix}/summary`, {
    enableLang: false,
    method: 'GET',
    params,
  })
}

// 获取一周内评审GET
export const getWeekList = (params) => {
  return request(`${apiPrefix}/week`, {
    enableLang: false,
    method: 'GET',
    params,
  })
}

// 获取上市十二月指标
export const getSeriesIndexList = (params) => {
  return request(`${apiPrefix}/index`, {
    enableLang: false,
    method: 'GET',
    params,
  })
}

// 获取项目里程数据
export const getGeneralList = (params) => {
  return request(`${apiPrefix}/general`, {
    enableLang: false,
    method: 'GET',
    params,
  })
}
