import { request } from '@/utils/request'

const prefix = '/api/qms/api/dashboard/v2'
const apiPrefix = `${window.location.origin}${prefix}`

// 获取层初始化用户信息
export const getWorkbenchList = () => {
  return request(`${apiPrefix}/workbench`, {
    enableLang: false,
    method: 'GET',
  })
}

// 保存当前自定义工作台中所有卡片
export const postWorkbenchList = (data) => {
  return request(`${apiPrefix}/workbench`, {
    enableLang: false,
    method: 'POST',
    data,
  })
}

// 保存自定义工作台卡片项目
export const postAddDashboard = (data) => {
  return request(`${apiPrefix}/block`, {
    enableLang: false,
    method: 'POST',
    data,
  })
}
// 删除自定义工作台删除接口
export const deleteAddDashboard = (params) => {
  return request(`${apiPrefix}/block`, {
    enableLang: false,
    method: 'DELETE',
    params,
  })
}
