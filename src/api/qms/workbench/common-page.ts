import { request } from '@/utils/request'

const prefix = '/api/qms/api/dashboard/v2/menu'
const apiPrefix = `${window.location.origin}${prefix}`

export const getFavoriteList = () => {
  return request(`${apiPrefix}/favorite`, {
    enableLang: false,
    method: 'GET',
  })
}

export const postFavoriteList = (data) => {
  return request(`${apiPrefix}/favorite`, {
    enableLang: false,
    method: 'POST',
    data,
  })
}
