import { request } from '@/utils/request'
import qs from 'qs'
import {
  ServiceType,
  IMaterialItem,
  IFactory,
  FaultItem,
  ISpecialType,
  IFaultData,
} from './interface'

const apiPrefix = `/api/qms`

// 权限接口
export const getAnalysisBaseAuth = () => {
  return request(`${apiPrefix}/api/common/v2/getFunctionalAuth`, {
    method: 'GET',
  })
}

export const getAnalysisBaseInfo = () => {
  return request(`${apiPrefix}/api/common/v2/getAnalysisBaseInfo`, {
    method: 'GET',
  })
}

/**
 * 获取服务类型配置
 *
 * @param {Object} data - 获取服务类型配置的请求数据对象，默认为空对象
 * @returns {Promise<Object>} 服务类型配置
 */
export const getAnalysisServiceTypeConfig = (data = {}) => {
  return request<ServiceType[]>(`${apiPrefix}/api/common/v2/getFormulaInfo`, {
    method: 'POST',
    data,
  })
}

export const tableFilterQueryApi = (url: string, data: Record<string, string | number>) => {
  return request(`${url}`, {
    method: 'POST',
    data,
  })
}

/**
 * 获取机型类别列表
 *
 * @param {Object} data - 请求数据对象，默认为空对象
 * @returns {Promise<Object>} 机型类别列表(包含一二三级机型)
 */
export const getAnalysisProductInfo = (data = {}) => {
  return request(`${apiPrefix}/api/common/v2/getAnalysisProductInfo`, {
    method: 'POST',
    data,
  })
}

/**
 * 获取项目列表
 *
 * @param {Object} data - 请求数据对象，默认为空对象
 * @returns {Promise<Object>} 项目列表
 */
export const getProjectCodeInfo = (data = {}) => {
  return request(`${apiPrefix}/api/common/v2/getProjectCodeInfo`, {
    method: 'POST',
    data,
  })
}

/**
 * 获取项目系列信息列表
 *
 * @param {Object} data - 请求数据对象，默认为空对象
 * @returns {Promise<Object>} 项目系列信息列表
 */
export const getProjectSeriesInfo = (data = {}) => {
  return request(`${apiPrefix}/api/common/v2/getProjectSeriesInfo`, {
    method: 'POST',
    data,
  })
}

/**
 * 获取工厂列表
 *
 * @param {Object} data - 请求数据对象，默认为空对象
 * @returns {Promise<Array>} 返回包含工厂列表的Promise
 */
export const getFactoryList = (data = {}) => {
  return request<IFactory[]>(`${apiPrefix}/api/common/v2/getFactoryList`, {
    method: 'POST',
    data,
  })
}

/**
 * 获取二级故障列表
 *
 * @param data - 请求参数对象，默认为空对象
 * @returns 返回一个 Promise，解析为 FaultItem 数组
 */
export const getSecondFaultList = (data = {}) => {
  return request<FaultItem[]>(`${apiPrefix}/api/common/getFault2ListV2`, {
    method: 'POST',
    data,
    handleError: false,
    rejectError: false,
  })
}

/**
 * 获取三级故障列表
 *
 * @param data - 请求参数对象，默认为空对象
 * @returns 返回一个 Promise，解析为 FaultItem 数组
 */
export const getThirdFaultList = (data = {}) => {
  return request<FaultItem[]>(`${apiPrefix}/api/common/getFault3ListV2`, {
    method: 'POST',
    data,
    handleError: false,
    rejectError: false,
  })
}

// 获取售后概况图表数据
export const getGeneralData = (data = {}) => {
  return request(`${apiPrefix}/api/report/analysis/general`, {
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  })
}

export const getAnalysisDetails = (data = {}) => {
  return request(`${apiPrefix}/api/report/analysis/v2/analysisDetails`, {
    method: 'POST',
    data,
  })
}

export const getAnalysisDetailsColumn = (data = {}) => {
  return request(`${apiPrefix}/api/report/analysis/v2/analysisDetails/column`, {
    method: 'POST',
    data,
  })
}

export const exportAnalysisDetails = (data = {}) => {
  return request(`${apiPrefix}/api/report/analysis/v2/analysisDetails/export`, {
    method: 'POST',
    data,
  })
}

export const orderExport = (params) => {
  return request(`${apiPrefix}/api/download/mission/listForGsc?${qs.stringify(params)}`, {
    method: 'GET',
  })
}

/**
 * 获取文件下载URL
 *
 * @param {string} params 对象名称
 * @returns {Promise<string>} 返回文件下载URL的Promise
 */
export const getFileDownloadUrl = (params) => {
  return request(
    `${apiPrefix}/api/common/getFileDownloadUri?bucketName=mmc-qms&objectName=${params}`,
    {
      method: 'POST',
    }
  )
}

/**
 * @abstract 售后换料-报表查询接口
 * **/
export const getMaterialBarInfo = (data) => {
  return request<IFaultData[]>(`${apiPrefix}/api/report/analysis/material`, {
    method: 'POST',
    data,
  })
}

/**
 * @abstract 售后换料-售后换料趋势查询接口
 * **/
export const GET_MATERIAL_TRENDS_API = `${apiPrefix}/api/report/analysis/materialTrends`

/**
 * @abstract 售后故障-明细统计接口
 * **/
export const getFaultBarInfo = (data) => {
  return request<IFaultData[]>(`${apiPrefix}/api/report/analysis/fault`, {
    method: 'POST',
    data,
  })
}

export const GET_FAULT_API = `${apiPrefix}/api/report/analysis/faultTrends`

/**
 * 折线图获取数据
 * @param {string} url 请求地址
 * @param {object} data 请求数据
 * @returns {Promise<object>} 请求结果
 */
export const getFaultTrendsInfo = (url: string, data) => {
  return request(url, {
    method: 'POST',
    data,
  })
}

/**
 * @abstract 售后故障-明细统计接口-字段过滤
 * **/
export const API_FAULT_LIST = `${apiPrefix}/api/report/analysis/v2/faultDetails/column`

/**
 * 专项分析-专项配置信息
 */
export const getSpecialTypeConfig = (data) => {
  return request<ISpecialType[]>(`${apiPrefix}/api/report/analysis/special/conf`, {
    method: 'POST',
    data,
    handleError: false,
  })
}

/**
 * @abstract 专项分析-获取图表数据
 * **/
export const getSpecialChart = (data) => {
  return request<AnyType[]>(`${apiPrefix}/api/report/analysis/special`, {
    method: 'POST',
    data,
  })
}

/**
 * 售后换料- 获取物料类别列表
 * @param params 请求参数对象，默认为空对象
 * @returns 返回一个 Promise，解析为 FaultItem 数组
 */
export const getMaterialList = (data = {}) => {
  return request<IMaterialItem[]>(`${apiPrefix}/api/report/analysis/getMaterialList`, {
    method: 'POST',
    data,
  })
}
