export interface ServiceType {
  label: string
  value: number
}

export interface IMaterialItem {
  label: string
  material_num: number
  value: string
}

export interface IFactory {
  /**
   * 工厂ID
   */
  id: string
  /**
   * 工厂名称
   */
  title: string
}

/**
 * 故障选项
 */
export interface FaultItem {
  /**
   * 故障编号
   */
  fault_num: number
  /**
   * 故障名称
   */
  label: string
  value: string
  /**
   * 对应的二级故障id(三级故障才有该字段)
   */
  secondFaultId?: string
}

export interface ISpecialType {
  id: number
  productLine: number
  brandClassId: number
  specialName: string
  specialType: number
  materialId: string
  secondFaultIds: string
  thirdFaultIds: string | null
  status: number
  updateTime: number
  updaterId: number
  countryId: string
}

export interface IFaultData {
  authType: number
  id: number
  name: string
  sql: string
  list: {
    fault_id: number
    fault_name: string // 售后故障
    fm_num: number
    fz_num: number
    id: number
    rate: number
    index: string
  }[]
}

export interface TooltipParam {
  color: string
  axisValue: string
  seriesName: string
  data?: {
    value?: number
    totalValue?: number
    extraData?: string
    type?: boolean
    isAdverse?: boolean
    adverse?: number | string
    fz?: number | string
    isEmpty?: boolean
  }
}
