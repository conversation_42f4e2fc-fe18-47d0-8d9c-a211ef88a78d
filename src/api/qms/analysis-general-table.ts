// 售后系列工单明细表接口请求
import { request } from '@/utils/request'
import { download } from '@/utils/request-download'
import { ZoneType } from '@/views/qms/quality-analysis-report/common/config'
import message from '@hi-ui/message'
import intl from 'react-intl-universal'

const modelMap = new Map()

// 获取国家区域模型
export const getDistributionModel = (
  countryIds: number[],
  handleError = true
): Promise<{ zoneType: ZoneType; contentPath: string }> => {
  const key = JSON.stringify(countryIds)

  if (modelMap.has(key)) {
    return modelMap.get(key)
  }

  const promise$ = request('/api/qms/api/common/v2/distributionModel', {
    method: 'POST',
    data: {
      countryIds,
    },
    handleError,
  }).then(({ data = [] }) => {
    const model = data?.[0]
    modelMap.set(key, model)
    return model
  })

  modelMap.set(key, promise$)
  return promise$
}

// 页面枚举 1->售后概况 2->售后故障 3->专项分析 4->售后换料
const pathMap = {
  overview: {
    detail: '/api/report/analysis/v2/analysisDetails',
    column: '/api/report/analysis/v2/analysisDetails/column',
    export: '/api/report/analysis/v2/analysisDetails/exportOnline',
  },
  fault: {
    detail: '/api/report/analysis/v2/faultDetails',
    column: '/api/report/analysis/v2/faultDetails/column',
    export: '/api/report/analysis/v2/faultDetails/exportOnline',
  },
  special: {
    detail: '/api/report/analysis/v2/specialDetails',
    column: '/api/report/analysis/v2/specialDetails/column',
    export: '/api/report/analysis/v2/specialDetails/exportOnline',
  },
  material: {
    detail: '/api/report/analysis/v2/materialDetails',
    column: '/api/report/analysis/v2/materialDetails/column',
    export: '/api/report/analysis/v2/materialDetails/exportOnline',
  },
} as const
export type PageType = keyof typeof pathMap

// 工单明细表请求
export const tableDetailRequest = async (
  page: PageType,
  type: 'detail' | 'column' | 'export',
  data: AnyType = {},
  handleError = true
) => {
  const path = pathMap[page][type]
  const { contentPath, zoneType } = await getDistributionModel(data.countryIds, handleError)
  const url = `/${contentPath}${path}?region=${zoneType}`

  // 导出改为下载
  if (type === 'export') {
    return download({
      url,
      method: 'POST',
      data,
      responseType: 'arraybuffer',
    })
      .then(() => {
        message.open({
          title: `${intl.get('导出成功')}`,
          type: 'info',
        })
      })
      .catch((res) => {
        message.open({
          title: res.msg,
          type: 'error',
        })
      })
  }

  return request(url, {
    method: 'POST',
    data,
    handleError,
  })
}
