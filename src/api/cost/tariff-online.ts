import { getPaginationWithColumns } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'

interface IOnlineProps {
  mpnIds?: number[]
  country?: string
  projects?: string[]
  factoryCodes?: string[]
  pageNum?: number
  pageSize?: number
}

const prefix = '/material-tariff-rate'

/**
 * 下载导入模板
 */
export const downloadOnlineTpl = async (params: { country: string }) =>
  await request(`${prefix}/download-template`, {
    params,
  })

/**
 * 列表查询
 */
export const getOnlineList = async (data: IOnlineProps) => {
  const res = await request(`${prefix}/list`, {
    method: 'POST',
    data,
  })
  return getPaginationWithColumns(res.data)
}

/**
 * 导入数据
 */
export const uploadOnlineData = async (data: { objectName: string; country: string }) =>
  await request(`${prefix}/upload`, {
    method: 'POST',
    data,
    handleError: false,
  })

/**
 * 导出数据
 */
export const exportOnlineData = async (data: IOnlineProps) =>
  await request(`${prefix}/export`, {
    method: 'POST',
    data,
  })

/**
 * 获取查询条件
 */
export const getOnlineConditions = async (params?: { key: 'hsCode' | 'mpnId'; value: string }) => {
  const { key, value } = params || {}
  const fetchUrl = key && value ? `${prefix}/condition?${key}=${value}` : `${prefix}/condition`
  return await request(`${fetchUrl}`)
}
