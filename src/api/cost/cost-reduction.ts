import { getPaginationDataWithColumns, getPaginationData } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'
interface ConditionPropsType {
  startDate?: number
  endDate?: number
  type?: string
  prefix?: string
}
interface ExportPropsType {
  name?: string
  type?: string
  param?: string[]
}
export interface ExportListPropsType {
  type?: string
  pageNum?: number
  pageSize?: number
}
export interface FetchCostListType {
  categoryList?: string[]
  cvpnCodes?: string[]
  pnCodes?: string[]
  startDate?: number
  endDate?: number
  mpnIds?: string[]
  pnCodeDescs?: string[]
  brandNames?: string[]
  sourcingOprNameCns?: string[]
  purchaseUserNameCns?: string[]
  projectNames?: string[]
  odmMgrDisplayNames?: string[]
  orderNum?: number
  type?: number
  amountType?: string
  pageNum?: number
  pageSize?: number
}

/**
 * 预约下载列表
 */
export const getExportList = async (params: ExportListPropsType) => {
  const res = await request('/back-download/list', {
    params,
  })
  return getPaginationData(res?.data)
}
/**
 * 预约下载
 */
export const exportReserve = async (data: ExportPropsType) => {
  const res = await request('/back-download/create', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}

/**
 * 实结导出
 */
export const exportCostRedution = async (data: FetchCostListType) => {
  const res = await request('/cost-reduction/export', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}
/**
 * 查询条件
 */
export const getConditions = async (params: ConditionPropsType) => {
  const res = await request('/cost-reduction/conditions', {
    params,
    handleError: false,
  })
  return res
}
/**
 * 品类
 */
export const getCategoryConditions = async () => await request('/cost-reduction/category')

/**
 * 列表
 */
export const getCostList = async (data: FetchCostListType) => {
  const res = await request('/cost-reduction/find', {
    method: 'POST',
    data,
  })

  return getPaginationDataWithColumns(res?.data)
}

/**
 * 成本降幅-入库凭证-列表查询接口
 *
 */
export const getWarehouseTable = async (data: FetchCostListType) => {
  const res = await request('/cost-reduction/find/voucher', {
    method: 'POST',
    data,
  })
  const formatData = getPaginationDataWithColumns(res?.data)
  const finalData = {
    ...formatData,
    list: formatData.list.map((item, index) => {
      return {
        ...item,
        key: index,
      }
    }),
  }
  return finalData
}
/**
 * 成本降幅-入库凭证-列表导出接口
 *
 */
export const exportWarehouseTable = async (data: FetchCostListType) => {
  return await request('/cost-reduction/find/voucher/export', {
    method: 'POST',
    data,
    handleError: false,
  })
}

/**
 * 成本降幅-入库凭证-列表查询接口
 *
 */
export const getCostReductionTime = async () => {
  return await request('/cost-reduction/voucher/version', {
    method: 'GET',
  })
}
