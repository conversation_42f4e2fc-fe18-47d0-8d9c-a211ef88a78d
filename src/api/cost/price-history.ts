import { request } from '@/utils/request'

const prefix = '/ipd-cost-history'

interface ParamProps {
  projectName?: string
  region?: string
  color?: string
  config?: string
  lvl1?: string
  lvl2?: string
  lvl3?: string
}

export const getPriceHistoryConditions = () => {
  return request(`${prefix}/condition`, {
    method: 'GET',
  })
}

export const getPriceHistoryList = (params: ParamProps) => {
  return request(`${prefix}/cost-list`, {
    method: 'POST',
    data: params,
  })
}

export const getPriceHistoryChart = (params: ParamProps) => {
  return request(`${prefix}/milestone-cost`, {
    method: 'POST',
    data: params,
  })
}

export const getPriceHistoryInfo = (params: ParamProps) => {
  return request(`${prefix}/history`, {
    method: 'POST',
    data: params,
  })
}
