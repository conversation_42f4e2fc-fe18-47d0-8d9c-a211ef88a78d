import { request } from '@/utils/request'

const apiPrefix = '/npc/cost/ipmt'

interface IProps {
  milestoneId: number // 上一层级的节点 id
  type: number // 1小类，2中类，3ipd维度
  saleSites: string[] // 销售站点
  configs: string[] // 配置
  colors: string[] // 颜色
  costMatCatLvl1Codes: string[] // 大类
  costMatCatLvl2Codes: string[] // 中类
  costMatCatLvl3Codes: string[] // 小类
}

export const getCostJustifyCondition = (data: IProps) =>
  request(`${apiPrefix}/condition`, {
    method: 'POST',
    data,
  })

export const getCostJustifyList = (data: IProps) =>
  request(`${apiPrefix}/list`, {
    method: 'POST',
    data,
  })

export const downloadCostJustifyList = (data: IProps) =>
  request(`${apiPrefix}/download`, {
    method: 'POST',
    data,
  })

export const modifyCostJustifyLvl3 = (data: {
  id: number
  purchaseReduction: string
  researchReduction: string
  m0: number
  m1: number
  m2: number
  m3: number
  m4: number
  m5: number
  m6: number
  m7: number
  m8: number
  m9: number
  m10: number
  m11: number
}) =>
  request(`${apiPrefix}/lvl3/modify`, {
    method: 'POST',
    data,
  })

export const getUserProjectPriv = () => request(`/npc/user-project-priv`)
