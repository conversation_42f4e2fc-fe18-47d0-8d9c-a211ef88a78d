import { request } from '@/utils/request'

const API_PREFIX = '/generation-cost'

export interface IConditionProps {
  costMatCatLvl1Codes: string[]
  projectNames: string[]
  saleSites: string[]
  configs: string[]
  phaseStages: string[]
  exchangeRateType: string
  date: string
  searchName: string
  content?: string
}

/** 下拉框条件 */
export const getGenerationalCostConditions = (data: IConditionProps) => {
  return request(`${API_PREFIX}/condition`, {
    method: 'POST',
    data,
  })
}

/** 节点对比 */
export const getNodeCompareData = (
  data: Exclude<IConditionProps, 'searchName' | 'content'> & {
    type: string
    pageNum: number
    pageSize: number
  }
) => {
  return request(`${API_PREFIX}/compare-stage`, {
    method: 'POST',
    data,
  })
}

/** 项目成本卡片 */
export const getProjectCostCardData = (
  data: Exclude<IConditionProps, 'searchName' | 'content'>
) => {
  return request(`${API_PREFIX}/project-total`, {
    method: 'POST',
    data,
  })
}

/** 项目对比、品类对比 */
export const getProjectCompareData = (data: Exclude<IConditionProps, 'searchName' | 'content'>) => {
  return request(`${API_PREFIX}/compare-project`, {
    method: 'POST',
    data,
  })
}

export enum PageType {
  PROJECT_COMPARE = 'projectCompare',
  STAGE_COMPARE = 'stageCompare',
  CUSTOM_COMPARE = 'customCompare',
  CATEGORY_COMPARE = 'categoryCompare',
}

/** 导出 */
export const exportGenerationalCost = (
  data: Exclude<IConditionProps, 'searchName' | 'content'> | { pageType: PageType }
) => {
  return request(`${API_PREFIX}/compare-cost/export`, {
    method: 'POST',
    data,
  })
}
