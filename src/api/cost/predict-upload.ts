import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

export interface FetchListType {
  costItem?: string[]
  pageNum: number
  pageSize: number
}
/**
 * 获取成本项查询条件
 */
export const getCostItemConditions = async () => {
  const res = await request('/cost-item-predict/cost-items', {
    handleError: false,
  })
  return res?.data
}
/**
 * 获取成本项列表
 */
export const getCostItemList = async (params: FetchListType) => {
  const res = await request('/cost-item-predict/list', {
    params,
    handleError: false,
  })
  return getPaginationData(res?.data)
}
/**
 * 获取成本项填写模版
 */
export const getCostItemTemplate = async () =>
  await request('/cost-item-predict/template', {
    handleError: false,
  })

/**
 * 上传成本项
 */
export const uploadCostItem = async (data) =>
  await request('/cost-item-predict/upload', {
    method: 'POST',
    data,
    handleError: false,
  })
/**
 * 系统试算
 */
export const systemCalculateDownload = (params?: Record<string, number>) =>
  request('/cost-item-predict/trial-calc', {
    params,
    handleError: false,
  })
