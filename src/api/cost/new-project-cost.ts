import { request } from '@/utils/request'
import { getPaginationDataNewProjectCost } from '@/utils/get-pagination-data'

interface CostStateCompareParams {
  zProject: string
  zArea: string
  zConfig: string
  zColor: string
  zVersion: string
  zAccount: string
  zClass1: string
  zClass2: string
}

type PercentParams = Pick<
  CostStateCompareParams,
  'zProject' | 'zArea' | 'zConfig' | 'zColor' | 'zVersion'
> & { projectStage: string; type: number }

interface ExportCostParams {
  zPhases?: string
  zProjects?: string
  zArea?: string
  zConfigs?: string
  zColors?: string
  zVersions?: string
  zPrempdates?: string
  type?: string
}

export type CostListQueryParams = ExportCostParams & { pageNum?: number; pageSize?: number }

/**
 * 成本阶段对比
 */
export const costStageCompare = async (data: CostStateCompareParams) => {
  const res = await request('/new-project-cost/project-stage', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 百分比查询
 */
export const percentQuery = async (data: PercentParams) => {
  const res = await request('/new-project-cost/percent', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 导出列表
 */
export const exportCostList = async (data: ExportCostParams) => {
  const res = await request('/new-project-cost/export', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}

/**
 * 查询条件
 */
export const getCostQueryConditions = async () => {
  const res = await request('/new-project-cost/condition')
  return res
}

/**
 * 动态查询条件
 */
export const getDynamicConditions = async (data) => {
  const res = await request('/new-project-cost/condition', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 下载模板
 */
export const getCostTemplate = async (params: { type: string }) => {
  const res = await request('/new-project-cost/templates', {
    params,
    handleError: false,
  })
  return res
}

/**
 * 列表页查询
 */
export const costListQuery = async (data: CostListQueryParams) => {
  const res = await request('/new-project-cost', {
    method: 'POST',
    data,
  })
  return getPaginationDataNewProjectCost(res?.data)
}

/**
 * 上传数据
 */
export const uploadCostData = async (data: { objectName: string; type: string }) => {
  const res = await request('/new-project-cost/templates', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}

/**
 * 生成预上传地址
 **/
export const generatePreUploadUrl = async (params: { category: string; fileName: string }) => {
  const res = await request('/common/categoryFileUploadUri', {
    method: 'GET',
    params,
  })
  return res
}
