import { getPaginationData } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'

const prefix = '/ipmt-cost'

interface IProps {
  projectNames: string[]
  projectStages: string[]
  regions: string[]
  costConfigs: string[]
  costColors: string[]
  confirmStates: boolean[]
  pageNum: number
  pageSize: number
}

// 损益下拉框条件
export const getProfitLossConditions = () => {
  return request(`${prefix}/condition`, {
    method: 'POST',
    data: {},
  })
}

// 损益成本列表
export const getProfitLossList = async (data: IProps) => {
  const res = await request(`${prefix}/page`, {
    method: 'POST',
    data,
  })
  return getPaginationData(res?.data)
}

// 查询项目对应的颜色列表
export const getProfitLossColorList = (data: { id: string }) => {
  return request(`${prefix}/list-color`, {
    method: 'POST',
    data,
  })
}

// 确认项目成本颜色
export const confirmProjectColor = (data: { id: number; costColor: string }) => {
  return request(`${prefix}/confirm-cost-color`, {
    method: 'POST',
    data,
  })
}

// 确认损益成本，0 成本确认；1 经理确认
export const confirmCost = (data: { confirmType: 0 | 1; itemCostReqs: AnyType[] }) => {
  return request(`${prefix}/update-confirm`, {
    method: 'POST',
    data,
  })
}

// 确认损益成本，0 成本确认；1 经理确认
export const confirmCostNew = (data: { confirmType: 0 | 1; itemCostReqs: AnyType[] }) => {
  return request(`${prefix}/update-new-confirm`, {
    method: 'POST',
    data,
    handleError: false,
  })
}

// 财经经理退回
export const rollbackCost = (data: { id: number }) => {
  return request(`${prefix}/cost-confirm/rollback`, {
    method: 'POST',
    data,
    handleError: false,
  })
}

// 颜色列表
export const getStageColorList = (data: { id: number }) => {
  return request(`${prefix}/list-stage-color`, {
    method: 'POST',
    data,
    handleError: false,
  })
}

// 确认颜色
export const confirmStageColor = (data: { list: { ids: number[]; costColor: string }[] }) => {
  return request(`${prefix}/confirm-cost-color`, {
    method: 'POST',
    data,
    handleError: false,
  })
}

// 获取定价物料列表
export const getPricingMaterialList = (params: { id: number }) => {
  return request(`/npc/ipmt/calc`, {
    params,
  })
}

// 获取用户角色
export const getUserRole = (data: { projectName: string }) => {
  return request(`${prefix}/user-role`, {
    method: 'POST',
    data,
  })
}

export const getProjectConfigList = (data: { projectName: string }) => {
  return request(`${prefix}/list-project-config`, {
    method: 'POST',
    data: { items: [data] },
  })
}

export const adjustCost = (data: { id: number }) => {
  return request(`${prefix}/adjust-cost`, {
    method: 'POST',
    data,
  })
}
// 同步资源
export const synchronizeResources = (data: { id: number }) => {
  return request(`${prefix}/sourcing-ajdust-cost`, {
    method: 'POST',
    data,
  })
}
export const downloadAdjustCostList = (data: { id: number }) =>
  request(`${prefix}/export-adjust-cost`, {
    method: 'POST',
    data,
  })
export const uploadAdjustCost = (data: { objectName: string; id: number }) =>
  request(`${prefix}/upload-adjust-cost`, {
    method: 'POST',
    data,
    handleError: false,
  })
export const submitAdjustCost = (
  data: Array<{
    projectName: string
    projectStage: string
    firstCategory: string
    secondCategory: string
    thirdCategory: string
    yearMonth: string
    cost: number
    usdCost: number
  }>
) => {
  return request(`${prefix}/submit-adjust-cost`, {
    method: 'POST',
    data,
  })
}
