import { request } from '@/utils/request'
import { getPaginationData } from '@/utils/get-pagination-data'

const commonPrefix = '/material-oversea-price'
const prefix = `${commonPrefix}/price-bill`
const prefix2 = `${commonPrefix}/markup`
const prefix3 = `${commonPrefix}/price-bill-item`

interface IProjectInfoProps {
  area?: string
  projects?: string
  configs?: string[]
  mpnIds?: string[]
  processStatus?: number
  pageNum: number
  pageSize: number
}

interface ISubmitProps {
  billCode: string
  changeReason: string
  remark: string
}

interface ISaleListProps {
  pageNum: number
  pageSize: number
  billCode?: string
  billStatus?: string
  createUserName?: string
}

interface IProportionProps {
  id: number
  markupRatio: number
  validFrom: string
  validTo: string
  remark: string
}

interface IBillProps {
  billCode: string
  mpnIds: string[]
}

interface IBillListProps {
  billCode: string
  pageSize: number
  pageNum: number
}

/**
 * 项目信息输入
 */
export const getProjectInfo = async (data: IProjectInfoProps) => {
  const res = await request(`${commonPrefix}/material-list`, {
    method: 'POST',
    data,
  })
  return getPaginationData(res?.data)
}

/**
 * 项目信息输入表单条件
 */
export const getProjectInfoConditions = async () =>
  await request(`${commonPrefix}/material-list/conditions`)

/**
 * 获取定价单查询条件
 */
export const getSaleConditions = async () => {
  return await request(`${prefix}/list-conditions`)
}

/**
 * 定价单详情(不含明细)
 */
export const getSaleDetail = async (params: { billCode: string }) => {
  const res = await request(`${prefix}/detail`, {
    params,
  })
  return res?.data
}

/**
 * 提交定价单
 */
export const submitSaleBill = async (data: ISubmitProps) => {
  return await request(`${prefix}/submit`, {
    method: 'POST',
    data,
    handleError: false,
  })
}

/**
 * 创建定价单
 */
export const createSaleBill = async (data) =>
  request(`${prefix}/create`, {
    method: 'POST',
    data,
  })

/**
 * 获取定价单列表
 */
export const getSaleList = async (data: ISaleListProps) => {
  const res = await request(`${prefix}/list`, {
    method: 'POST',
    data,
  })
  return getPaginationData(res?.data)
}

/**
 * 加成比例列表
 */
export const getProportionList = async (params: { pageNum: number; pageSize: number }) => {
  const res = await request(`${prefix2}/list`, {
    params,
  })
  return getPaginationData(res?.data)
}

/**
 * 编辑加成比例
 */
export const editProportion = async (data: IProportionProps) => {
  return await request(`${prefix2}/update`, {
    method: 'POST',
    data,
    handleError: false,
  })
}

/**
 * 定价单删除明细
 */
export const deleteBillDetail = async (data: { billCode: string; ids: string[] }) => {
  return await request(`${prefix3}/delete`, {
    method: 'POST',
    data,
    handleError: false,
  })
}

/**
 * 定价单增加明细
 */
export const addBillDetail = async (data: IBillProps) => {
  return await request(`${prefix3}/add`, {
    method: 'POST',
    data,
  })
}

/**
 * 定价单明细列表
 */
export const getBillDetailList = async (data: IBillListProps) => {
  const res = await request(`${prefix3}/list`, {
    method: 'POST',
    data,
  })
  return getPaginationData(res?.data)
}

/**
 * 定价单明细列表导出
 */
export const exportBillDetailList = async (data: { billCode: string }) => {
  const res = await request(`${prefix3}/export`, {
    method: 'POST',
    data,
  })
  return res?.data
}
