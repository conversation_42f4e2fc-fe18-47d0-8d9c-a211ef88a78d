import { request } from '@/utils/request'

interface ListProps {
  type?: string
  pageSize?: number
  pageNum?: number
  startMonth?: string
  endMonth?: string
  mpnIds?: string
  costMatCatLvl2Codes?: string
  cvpnCodes?: string
  matCatLvl2Codes?: string
  skus?: string
  projectNames?: string
  saleVersions?: string
  projectSeries?: string
  brands?: string
  pns?: string
  configs?: string
  tableColumns?: Record<string, string | boolean>[]
}

interface CostRowPage {
  data: Record<string, string | number | Record<string, string | number>[]>[]
  pageNum: number
  pageSize: number
  pageTotal: number
  total: number
}

interface Cost {
  fcstMonth: number
  cost: number
}
interface CostLine {
  date?: number
  sku?: string
  bizName?: string
  sopcode?: string
  firstLevelSubject?: string
  costMatCatLvl1Code?: string
  costMatCatLvl2Code?: string
  exchangeRateType?: string
  costs?: Cost[]
}

const prefix = '/project-cost'
const prefix2 = '/project-cost/npc'

export const getNewProjectCostList = (data: ListProps) =>
  request<{
    columnList: { key: string; value: string; type: string }[]
    rowPage: CostRowPage
  }>(`${prefix2}/list`, {
    method: 'POST',
    data,
  })

export const getNewConditions = () => {
  return request(`${prefix2}/condition`, {
    method: 'GET',
  })
}

export const downloadNewProjectList = async (data: ListProps) =>
  await request(`${prefix2}/export`, {
    method: 'POST',
    data,
    handleError: false,
  })

export const getProjectCostList = (data: ListProps) =>
  request<{
    columnList: { key: string; value: string; type: string }[]
    rowPage: CostRowPage
  }>(prefix, {
    method: 'POST',
    data,
  })

export const getConditions = () => {
  return request(`${prefix}/condition`, {
    method: 'GET',
  })
}

export const exportFile = (formData) => {
  return request(`${prefix}/export`, {
    method: 'GET',
    params: formData,
  })
}

export const getCategoryConditionList = async () => await request(`${prefix}/category`)

export const downloadProjectList = async (data: ListProps) =>
  await request(`${prefix}/export`, {
    method: 'POST',
    data,
    handleError: false,
  })

export const getVersions = (firstLevelSubject: string) =>
  request(`${prefix}/data-confirm/version`, {
    method: 'GET',
    params: { firstLevelSubject },
  })

export const updateVData = (data: CostLine) =>
  request(`${prefix}/data-confirm/update`, {
    method: 'POST',
    data,
  })

export const execRelease = (data: { date: string; firstLevelSubject: string }) =>
  request(`${prefix}/data-confirm/release`, {
    method: 'POST',
    data,
  })

// 获取当前用户可以确认的科目列表
export const getCurValidSubjectList = () =>
  request(`${prefix}/data-confirm/user-priv`, {
    method: 'GET',
  })
