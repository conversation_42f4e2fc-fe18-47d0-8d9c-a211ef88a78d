import { getPaginationData } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'

export interface MatType {
  matCatLvl1Code?: string[]
  matCatLvl2Code?: string[]
  matCatLvl3Code?: string[]
  costMatCatLvl1CodeId?: string[]
  costMatCatLvl2CodeId?: string[]
  costMatCatLvl3CodeId?: string[]
  status?: number[]
  flag?: number[]
}
export type MatListType = MatType & {
  pageNum: number
  pageSize: number
}

/**
 * 条件信息
 */

export const getCostHistoryConditions = () => {
  return request('/material-cost-mapping/condition', {
    method: 'GET',
  })
}

/**
 * 查询信息
 */
export const getMatList = async (data) => {
  const res = await request('/material-cost-mapping/list', {
    method: 'POST',
    data,
    handleError: false,
  })
  return getPaginationData(res?.data)
}

/**
 * 导出
 */
export const exportMatList = async (data) => {
  const res = await request('/material-cost-mapping/export', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}
/**
 * 生成预上传地址
 **/
export const generatePreUploadUrl = async (params: { category: string; fileName: string }) =>
  await request('/common/categoryFileUploadUri', { params })

/**
 * 上传数据
 */
export const uploadMatList = async (data: { objectName: string }) => {
  const res = await request('/material-cost-mapping/upload', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}
/**
 * 维护信息
 */
export const updateMat = async (data) => {
  const res = await request('/material-cost-mapping/update', {
    method: 'PUT',
    data,
    handleError: false,
  })
  return res?.data
}
