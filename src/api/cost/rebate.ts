import { request } from '@/utils/request'

const REBATE_API_PREFIX = '/rebate'

// 礼成接口

// 获取返利品牌前十金额
export const getRebateBrandTopTen = (params) => {
  return request(`${REBATE_API_PREFIX}/getRebateBrandTopTen`, {
    method: 'POST',
    data: params,
  })
}

// 获取返利品牌前三趋势
export const getRebateBrandTopThreeTrend = (params) => {
  return request(`${REBATE_API_PREFIX}/getRebateBrandTopThreeTrend`, {
    method: 'POST',
    data: params,
  })
}

// TOP3品牌_返利计提&待回款金额
export const getRebateBrandTopThree = (params) => {
  return request(`${REBATE_API_PREFIX}/getRebateBrandTopThree`, {
    method: 'POST',
    data: params,
  })
}

// TOP5物料_返利单价
export const getPostingMonthTopFive = (params) => {
  return request(`${REBATE_API_PREFIX}/getPostingMonthTopFive`, {
    method: 'POST',
    data: params,
  })
}

// 下拉查询列表
export const getRebateCondition = (params) => {
  return request(`${REBATE_API_PREFIX}/get-rebate-condition`, {
    method: 'POST',
    data: params,
  })
}

// 台账明细 (by料+批次)
export const getRebateDetail = (params) => {
  return request(`${REBATE_API_PREFIX}/get-rebate-detail`, {
    method: 'POST',
    data: params,
  })
}

// 品牌_返利计提汇总
export const getBrandNameDetail = (params) => {
  return request(`${REBATE_API_PREFIX}/getBrandNameDetail`, {
    method: 'POST',
    data: params,
  })
}

// 品牌_返利计提汇总（按成本大类统计）
export const getCostMatCatLvl1CodeDetail = (params) => {
  return request(`${REBATE_API_PREFIX}/getCostMatCatLvl1CodeDetail`, {
    method: 'POST',
    data: params,
  })
}

// 年度返利金额
export const getPostingMonthByYear = (params) => {
  return request(`${REBATE_API_PREFIX}/getPostingMonthByYear`, {
    method: 'POST',
    data: params,
  })
}

// 月度返利金额趋势
export const getPostingMonthByMonth = (params) => {
  return request(`${REBATE_API_PREFIX}/getPostingMonthByMonth`, {
    method: 'POST',
    data: params,
  })
}

// 概览分析（百万元，%）,品牌
export const getOverviewAnalysis = (params) => {
  return request(`${REBATE_API_PREFIX}/getOverviewAnalysis`, {
    method: 'POST',
    data: params,
  })
}

// 概览分析（百万元，%）,品类
export const getOverviewAnalysisMat = (params) => {
  return request(`${REBATE_API_PREFIX}/getOverviewAnalysisMat`, {
    method: 'POST',
    data: params,
  })
}

// 返利by季度
export const getRebateByQuarter = (params) => {
  return request(`${REBATE_API_PREFIX}/getRebateByQuarter`, {
    method: 'POST',
    data: params,
  })
}

// 返利台账总览
export const getDashboardOverview = (params) => {
  return request(`${REBATE_API_PREFIX}/getDashboardOverview`, {
    method: 'POST',
    data: params,
  })
}

// 返利料_采购支出&返利趋势
export const getPayRebateTrend = (params) => {
  return request(`${REBATE_API_PREFIX}/getPayRebateTrend`, {
    method: 'POST',
    data: params,
  })
}

// 返利计提by套 (出货口径)
export const qryRebateAccrualListByType = (params) => {
  return request(`${REBATE_API_PREFIX}/qryRebateAccrualListByType`, {
    method: 'POST',
    data: params,
  })
}

// 返利摊出（整机下线）
export const selectRebateAllocate = (params) => {
  return request(`${REBATE_API_PREFIX}/selectRebateAllocate`, {
    method: 'POST',
    data: params,
  })
}

// 返利摊出（整机下线）明细
export const selectRebateAllocateDetail = (params) => {
  return request(`${REBATE_API_PREFIX}/selectRebateAllocateDetail`, {
    method: 'POST',
    data: params,
  })
}

// 家豪接口

// 返利计提推送
export const pushData = (params) => {
  return request(`${REBATE_API_PREFIX}/pushData`, {
    method: 'POST',
    data: params,
    handleError: false,
  })
}

// 计提明细接口
export const qryRebateDetailList = (params) => {
  return request(`${REBATE_API_PREFIX}/qryRebateDetailList`, {
    method: 'POST',
    data: params,
  })
}

// const MAX_PAGE_SIZE = 5000

// 返利计提ByMDFTab
export const qryRebateAccrualListByMDF = (params) => {
  return request(`${REBATE_API_PREFIX}/qryRebateAccrualListByMDF`, {
    method: 'POST',
    // data: { ...params, qryType: 'mdf', pageSize: MAX_PAGE_SIZE, pageNum: 1 },
    data: { ...params, qryType: 'mdf' },
  })
}

// 返利计提by套 (出货口径) 大 tab 页面，下面一个表格
// qryType: set by 套
export const qryRebateAccrualMoreListByTypeSet = (params) => {
  return request(`${REBATE_API_PREFIX}/qryRebateAccrualMoreListByType`, {
    method: 'POST',
    // data: { ...params, qryType: 'set', pageSize: MAX_PAGE_SIZE, pageNum: 1 },
    data: { ...params, qryType: 'set' },
  })
}

// 返利计提by料
// qryType: mat by 料
export const qryRebateAccrualMoreListByTypeMat = (params) => {
  return request(`${REBATE_API_PREFIX}/qryRebateAccrualMoreListByType`, {
    method: 'POST',
    // data: { ...params, qryType: 'mat', pageSize: MAX_PAGE_SIZE, pageNum: 1 },
    data: { ...params, qryType: 'mat' },
  })
}

// 返利计提明细树状详情数据
export const qryRebateChildrenList = (params) => {
  return request(`${REBATE_API_PREFIX}/qryRebateChildrenList`, {
    method: 'POST',
    data: params,
  })
}

// 妥投 tab 数据
export const qryFuchnRebateList = (params) => {
  return request(`${REBATE_API_PREFIX}/qryFuchnRebateList`, {
    method: 'POST',
    data: params,
  })
}

// 妥投明细
export const qryFuchnRebateDetailList = (params) => {
  return request(`${REBATE_API_PREFIX}/qryFuchnRebateDetailList`, {
    method: 'POST',
    data: params,
  })
}

// 返利摊出推送
export const pushFuchnRebateData = (params) => {
  return request(`${REBATE_API_PREFIX}/pushFuchnRebateData`, {
    method: 'POST',
    data: params,
  })
}

// 查询摊出 gap 列表
export const qryFuchnRebateChildrenList = (params) => {
  return request(`${REBATE_API_PREFIX}/qryFuchnRebateChildrenList`, {
    method: 'POST',
    data: params,
  })
}

// 奕鸿接口

// 返利结算列表详情接口
export const getSettlementListDetail = (params) => {
  return request(`${REBATE_API_PREFIX}/settlement/listDetail`, {
    method: 'POST',
    data: params,
  })
}

// 返利结算创建审批单接口
export const getSettlementCreateSettlementDoc = (params) => {
  return request(`${REBATE_API_PREFIX}/settlement/createSettlementDoc`, {
    method: 'POST',
    data: params,
    handleError: false,
  })
}

// 返利结算汇总接口
export const getSettlementListSummary = (params) => {
  return request(`${REBATE_API_PREFIX}/settlement/listSummary`, {
    method: 'POST',
    data: params,
  })
}

// 返利结算 备注申报单号
export const handleRemarkDeclare = (params) => {
  return request(`${REBATE_API_PREFIX}/settlement/declare`, {
    method: 'POST',
    data: params,
    handleError: false,
  })
}

// 清空申报单号
export const onCancelDeclare = (params) => {
  return request(`${REBATE_API_PREFIX}/settlement/cancelDeclare`, {
    method: 'POST',
    data: params,
    handleError: false,
  })
}

/**
 * 生成预上传地址
 **/
export const generateRebatePreUploadUrl = async (params: {
  categoryDir: string
  fileName: string
}) => await request(`/common/categoryDirFileUploadUri`, { params })
