import { getPaginationDataWithColumns } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'

interface CommonPropsType {
  matCatLvl1Codes: string[]
  matCatLvl2Codes: string[]
  matCatLvl3Codes: string[]
  cvpnCode: string[]
  pnCode: string[]
  matNo: string[]
  pn: string
  dataType: number
  amountType: string
  startDate: number
  endDate: number
}

export interface FetchMoveListType {
  categoryList?: string[]
  cvpnCodes?: string[]
  pnCodes?: string[]
  startDate?: number
  endDate?: number
  mpnIds?: string[]
  pnCodeDescs?: string[]
  brandNames?: string[]
  sourcingOprNameCns?: string[]
  purchaseUserNameCns?: string[]
  projectNames?: string[]
  odmMgrDisplayNames?: string[]
  orderNum?: number
  dataType?: number
  amountType?: string
  pageNum?: number
  pageSize?: number
}

interface ConditionPropsType {
  startDate?: number
  endDate?: number
  type?: string
  prefix?: string
}

type TrendPropsType =
  | Pick<CommonPropsType, 'startDate' | 'endDate' | 'dataType' | 'amountType'>
  | {
      mpnIds: string
      cvpnCode: string
    }

export type ListProps = CommonPropsType & { pageNum: number; pageSize: number }

/**
 * 实结移动平均价导出
 */
export const exportSettledPrice = async (data: CommonPropsType) => {
  const res = await request('/moving-avg-price/fruitful/export', {
    method: 'POST',
    data,
    handleError: false,
  })
  return res
}

/**
 * 查询实结移动平均价列表
 */
export const getSettledPriceList = async (data: FetchMoveListType) => {
  const res = await request('/moving-avg-price/fruitful', {
    method: 'POST',
    data,
  })
  return getPaginationDataWithColumns(res?.data)
}

/**
 * 移动平均价查询条件
 */
export const getMoveAvgPriceConditions = async (params: ConditionPropsType) => {
  const res = await request('/moving-avg-price/conditions', {
    params,
    handleError: false,
  })
  return res
}

/**
 * 移动平均价趋势
 */
export const getMoveAvgPriceTrend = async (params: TrendPropsType) => {
  const res = await request('/moving-avg-price/trends', { params })
  return res
}

/**
 * 大中小类
 */
export const getCategoryConditions = async () => await request('/moving-avg-price/category')

/**
 * 全量导出
 */
export const exportFull = async () =>
  await request('/moving-avg-price/fruitful/exportFiles', {
    handleError: false,
  })

/**
 * 全量导出权限
 */
export const getFullExportAuth = async () =>
  await request('/moving-avg-price/fruitful/getExportAllDataAuth', {
    handleError: false,
  })

/**
 * 获取数据版本
 */
export const getDateVersion = async () =>
  await request('/moving-avg-price/defaultCondition', {
    handleError: false,
  })
