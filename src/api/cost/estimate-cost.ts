/* eslint-disable @typescript-eslint/no-explicit-any */
import { request } from '@/utils/request'

// 目标成本达成率
export const getConditions = () => {
  return request('/estimate-cost/condition', {
    method: 'GET',
  })
}

export const getList = (formData) => {
  return request<any>('/estimate-cost', { method: 'POST', data: formData })
}

export const exportFile = (formData) => {
  return request<any>('/estimate-cost/export', {
    method: 'POST',
    data: formData,
  })
}

export const exportFiles = (formData) => {
  return request<any>('/estimate-cost/exports', {
    method: 'POST',
    data: formData,
  })
}

export const getCategoryConditionList = async () => await request('/estimate-cost/category')
