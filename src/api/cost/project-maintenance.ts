import { request } from '@/utils/request'
import { FilterType } from '@/context/NewProjectListContext'
import { getPaginationDataWithColumns } from '@/utils/get-pagination-data'

export interface NodeType {
  id?: number
  milestone: string
  milestoneDate: string
  state?: number
}
export interface ConfigType {
  id?: number
  saleSite: string
  config: string
  color: string
  configFlag?: number
  mat95?: string
  sku?: string
  releaseTime?: string
  canDelete?: number
}

export interface ConfigInfoType {
  id?: number
  projectName?: string
  bizName?: string

  oprType?: number
  projectSeries?: string
  releaseTime?: string
  projectType?: string
  pValue?: number
  maintainers?: string[]
  project?: ConfigInfoType
  milestones?: NodeType[]
  projectConfigs?: ConfigType[]
}

export type SearchProps = FilterType & {
  pageNum: number
  pageSize: number
}

/**
 * 新列表页查询
 */
export const getNewProList = async (data: SearchProps) => {
  const res = await request('/npc/project-list', {
    method: 'POST',
    data,
  })
  return getPaginationDataWithColumns(res?.data)
}

/**
 * 新列表页条件
 */
export const getNewProCondition = (data: Record<string, AnyType>) =>
  request('/npc/project-list/condition', { method: 'POST', data })

/**
 * 数据下载
 */
export const downloadData = async (data: SearchProps) => {
  const res = await request('/npc/project-list/export', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 收集
 */
export const collectData = async (data) => {
  return request('/npc/milestone/collect', {
    method: 'POST',
    data,
  })
}
/**
 * 获取基础条件
 */
export const getBaseCondition = () => request('/npc/upsert/condition')

/**
 * 基础信息填报
 */
export const upsertBaseInfo = (data) => {
  return request('/npc/project/upsert', {
    method: 'POST',
    data,
  })
}

/**
 * 获取项目配置
 */
export const getProjectConfig = async (data) => {
  const res = await request('/npc/get-project-config', {
    method: 'POST',
    data,
  })
  return res?.data
}
/**
 * 获取项目详情数据
 */
export const getProjectDetail = async (params: Record<string, string>) => {
  const res = await request('/npc/project/upsert/get', {
    method: 'GET',
    params,
  })
  return res?.data
}
/**
 * 获取项目详情配置
 */
export const getProjectCondition = async (data: Record<string, AnyType>) => {
  const res = await request('/npc/project/upsert/condition', {
    method: 'POST',
    data,
  })
  return res?.data
}
/**
 * 模糊查询sku
 */
export const getSkuValue = async (mat95: string) => {
  const res = await request('/npc/project/upsert/sku', {
    method: 'GET',
    params: {
      mat95,
    },
  })
  return res
}

export const stopCollect = async (data: { id: number }) =>
  request('/npc/milestone/collect/stop', {
    method: 'POST',
    data,
  })

export const getUserRole = async () =>
  request<AnyType>('/npc/user-biz-line-priv', {
    method: 'GET',
  })
