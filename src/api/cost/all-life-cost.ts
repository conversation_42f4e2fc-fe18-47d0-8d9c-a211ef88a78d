import { request } from '@/utils/request'

const apiPrefix = '/project-life-cost'

export const getAllCostConditions = async () => await request(`${apiPrefix}/condition`)
export const getAllCostReport = async (data) =>
  await request(`${apiPrefix}/report`, {
    method: 'POST',
    data,
  })
export const getAllCostList = async (data) =>
  await request(`${apiPrefix}`, {
    method: 'POST',
    data,
  })
export const exportAllCostList = async (data) =>
  await request(`${apiPrefix}/export`, {
    method: 'POST',
    data,
  })
export const getConfigByProject = async (data) =>
  await request(`${apiPrefix}/condition/cascade`, {
    method: 'POST',
    data,
  })
export const getProjectType = async (data) => {
  const queryString = new URLSearchParams(data).toString()
  return await request(`${apiPrefix}/project-type?${queryString}`, {
    method: 'GET',
  })
}

interface PParams {
  projectName?: string
  config?: string
  saleSite?: string
  firstLevelSubject?: string
  startMonth: string
  endMonth: string
  versions: string[]
}

export const getPValue = async (data: PParams) => {
  return await request(`${apiPrefix}/list/p-value`, {
    method: 'POST',
    data,
  })
}
