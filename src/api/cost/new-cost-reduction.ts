import { getPaginationDataWithColumns } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'

/**
 * 查询条件
 */
export const getConditions = async (data) => {
  const res = await request('/cost-reduction/conditions', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 版本
 */
export const getVersion = async (dates) => {
  let url = '/cost-reduction/version'
  if (dates) {
    url += `?dates=${dates}`
  }
  const res = await request(url, {
    handleError: false,
    method: 'GET',
  })
  return res
}

export interface exportDetailProps {
  startDate?: number
  endDate?: number
  bizNames?: string[]
  depts?: string[]
  teamOwners?: string[]
  matOprNameCns?: string[]
  cvpnCodes?: string[]
  mpnIds?: string[]
}

export type DetailProps = exportDetailProps & {
  pageNum: number
  pageSize: number
}

/**
 * 查找明细
 */
export const findVoucher = async (data: DetailProps) => {
  const res = await request('/cost-reduction/find/voucher', {
    method: 'POST',
    handleError: false,
    data,
  })
  return getPaginationDataWithColumns(res?.data)
}

/**
 * 导出明细
 */
export const exportVoucher = async (data: exportDetailProps) => {
  const res = await request('/cost-reduction/find/voucher/export', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * MPN降本明细
 */
export const getMPNDetail = async (data: DetailProps) => {
  const res = await request('/cost-reduction/find/mpn-detail', {
    method: 'POST',
    handleError: false,
    data,
  })
  return getPaginationDataWithColumns(res?.data)
}

/**
 * 导出MPN降本明细
 */
export const exportMPNDetail = async (data: exportDetailProps) => {
  const res = await request('/cost-reduction/find/mpn-detail/export', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 指标概览
 */
export const getIndicator = async (data: {
  bizName?: string
  startDate?: number
  endDate?: number
  subject?: string
  teamOwner?: string
  source?: string
  cvpnCodes?: []
  pnCodes?: []
  mpnIds?: []
  matCatLvl1Codes?: []
  matCatLvl2Codes?: []
  matCatLvl3Codes?: []
  dept?: string
  teamOwnerEn?: string
  matOprNameCns?: []
  matCats?: []
  dates?: Date
}) => {
  const res = await request('/cost-reduction/stat', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

export interface AchieveChartProps {
  bizName?: string
  startDate?: number
  endDate?: number
  subject?: string
  teamOwnerEn?: string
}

/**
 * 用户权限
 */
export const getUserType = async () => {
  const res = await request('/cost-reduction/user/type', {
    method: 'GET',
    handleError: false,
  })
  return res
}
/**
 * 各月达成情况
 */
export const getMonthAchieve = async (data: AchieveChartProps, apiUrl: string) => {
  const res = await request(apiUrl, {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}
/**
 * 各组达成情况
 */
export const getTeamAchieve = async (data: AchieveChartProps, apiUrl: string) => {
  const res = await request(apiUrl, {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 指标明细表
 */
export const getDetailTable = async (data: {
  bizName?: string
  startDate?: number
  endDate?: number
  subject?: string
  teamOwner?: string
  findFields?: string[]
}) => {
  const res = await request('/cost-reduction/find', {
    method: 'POST',
    handleError: false,
    data,
  })
  return getPaginationDataWithColumns(res?.data)
}

export const getCostReductionDetailTable = async (data: {
  bizName?: string
  startDate?: number
  endDate?: number
  subject?: string
  teamOwner?: string
  findFields?: string[]
  cvpnCodes?: []
  pnCodes?: []
  mpnIds?: []
  matCatLvl1Codes?: []
  matCatLvl2Codes?: []
  matCatLvl3Codes?: []
  dept?: string
  teamOwnerEn?: string
  matOprNameCns?: []
  matCats?: []
  dates?: Date
  current?: number
  pageSize?: number
}) => {
  return await request('/cost-reduction/find/export', {
    method: 'POST',
    handleError: false,
    data,
  })
}
