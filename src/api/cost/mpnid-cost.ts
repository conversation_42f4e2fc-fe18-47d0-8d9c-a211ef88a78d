import intl from 'react-intl-universal'
import { FETCH_SUCCESS_CODES } from '@/constants'
import { request } from '@/utils/request'

// 目标成本达成率
export const getConditions = () => {
  return new Promise((resolve) => {
    resolve({
      msg: '0',
      traceId: '0',
      code: FETCH_SUCCESS_CODES[0],
      data: {
        mpnIds: ['mpnId1'],
        costMatCatLvl2Codes: [intl.get('新项目中类')],
        cvpnCodes: ['cvpnCode1'],
        matCatLvl1Codes: [intl.get('大类')],
        skus: ['sku1'],
        matCatLvl2Codes: [intl.get('中类')],
        projectNames: ['M11', 'M12'],
        saleVersions: [intl.get('中国'), intl.get('国际')],
        projectSeries: [intl.get('小米'), intl.get('红米')],
      },
    })
  })
}

export const getList = (formData) => {
  console.log(formData)
  return new Promise((resolve) => {
    resolve({
      msg: '0',
      traceId: '0',
      code: FETCH_SUCCESS_CODES[0],
      data: {
        columnList: [
          {
            value: intl.get('区域'),
            key: 'saleVersion',
          },
          {
            value: intl.get('系列'),
            key: 'projectSeries',
          },
          {
            value: intl.get('项目'),
            key: 'projectName',
          },
          {
            value: intl.get('配置'),
            key: 'config',
          },
          {
            value: 'SKU',
            key: 'sku',
          },
          {
            value: 'sop_code',
            key: 'sopcode',
          },
          {
            value: intl.get('成本组大类'),
            key: 'costMatCatLvl1Code',
          },
          {
            value: intl.get('成本组中类'),
            key: 'costMatCatLvl2Code',
          },
          {
            value: intl.get('物料大类'),
            key: 'matCatLvl1Code',
          },
          {
            value: intl.get('物料中类'),
            key: 'matCatLvl2Code',
          },
          {
            value: 'CVPN',
            key: 'cvpnCode',
          },
          {
            value: 'MPNID',
            key: 'mpnId',
          },
          {
            value: '202305',
            key: '202305',
          },
          {
            value: '202306',
            key: '202306',
          },
          {
            value: '202307',
            key: '202307',
          },
          {
            value: '202308',
            key: '202308',
          },
          {
            value: '202309',
            key: '202309',
          },
          {
            value: '202310',
            key: '202310',
          },
        ],
        rowPageDto: {
          total: '6',
          data: [
            {
              '202306': '33',
              '202307': '33',
              '202308': '33',
              '202309': '33',
              saleVersion: intl.get('国际'),
              matCatLvl1Code: intl.get('大类'),
              sopcode: 'sopCode1',
              mat95: '951',
              mpnId: 'mpnId1',
              costMatCatLvl2Code: intl.get('新项目中类'),
              mpnEstimateCost: '33',
              costMatCatLvl1Code: intl.get('新项目大类'),
              matCatLvl2Code: intl.get('中类'),
              projectName: 'M12',
              sku: 'sku1',
              config: '8+256',
              projectSeries: intl.get('红米'),
              cvpnCode: 'cvpnCode1',
            },
          ],
          pageTotal: '1',
          pageSize: '10',
          pageNum: '1',
        },
      },
    })
  })
}

export const exportFile = (formData) => {
  return request('http://mock-server.test.mi.com/mock/estimate-cost/export', {
    method: 'POST',
    params: formData,
  })
}
