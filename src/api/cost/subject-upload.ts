import { getPaginationDataWithColumns } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'

const prefix = '/cost-sub-item'

export interface GetSUListParams {
  type: number
  firstLevelSubjects: string[]
  versions: number[]
  pageNum: number
  pageSize: number
}

export const getSUConditions = async (params: { type: number }) =>
  request(`${prefix}/condition`, {
    params,
  })

export const downloadSUList = async (data: {
  type: number
  versions: number[]
  firstLevelSubjects: string[]
}) =>
  request(`${prefix}/download`, {
    method: 'POST',
    data,
  })

export const downloadSUTemplate = async (params: { type: number }) =>
  request(`${prefix}/template`, { params })

export const getSUList = async (data: GetSUListParams) => {
  const res = await request(`${prefix}/list`, {
    method: 'POST',
    data,
  })
  return getPaginationDataWithColumns(res?.data)
}

export const uploadSUTemplate = async (data: { objectName: string }) =>
  request(`${prefix}/upload`, {
    method: 'POST',
    data,
    handleError: false,
  })
