import { request } from '@/utils/request'

/**
 * 下载汇率填写模板
 **/
export const downloadExchangeRateTemplate = async () =>
  await request('/exchange-rate/download-template')

/**
 * 汇率列表查询
 * @param versions 版本列表，多个用英文逗号隔开
 **/
export const getExchangeRateList = async (params: { versions: string }) =>
  await request('/exchange-rate', { params })

/**
 * 汇率列表下载
 * @param versions 版本列表，多个用英文逗号隔开
 **/
export const downloadExchangeRateList = async (params: { versions?: string }) =>
  await request('/exchange-rate/download', { params })

/**
 * 汇率查询条件
 **/
export const getExchangeRateCondition = async () => await request('/exchange-rate/condition')

/**
 * 上传汇率
 * @param objectName 上传文件名称，前缀为自定义类型
 **/
export const uploadExchangeRate = async (data: { objectName: string }) =>
  await request('/exchange-rate/upload', { method: 'POST', data })

/**
 * 生成预上传地址
 * @param category 上传文件自定义类型
 * @param fileName 上传文件名称
 **/
export const generatePreUploadUrl = async (params: { category: string; fileName: string }) =>
  await request('/common/categoryFileUploadUri', { params })
