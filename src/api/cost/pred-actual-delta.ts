import { ReactText } from 'react'

import { request } from '@/utils/request'

/**
 * 版本
 */
export const getVersion = async () => {
  const res = await request('/predict-actual-compare/version', {
    method: 'GET',
  })
  return res
}

/**
 * 默认分析年月
 */
export const getMaxAccountMonth = async () => {
  const res = await request('/predict-actual-compare/getMaxAccountMonth', {
    method: 'GET',
  })
  return res
}
export interface filter {
  accountMonths?: string[]
  selPeriods?: string[]
  bizLineNames?: string[]
  depts?: string[]
  deptOwners?: string[]
  matOprNameCns?: string[]
  subjects?: string[]
  cvpnCodes?: string[]
  mpnIds?: string[]
  pnCodes?: string[]
  matCatLvl1Codes?: string[]
  matCatLvl2Codes?: string[]
  matCatLvl3Codes?: string[]
  factorType?: number
  content?: string[]
  fcstPeriods?: string[]
  matCats?: string[]
  teamOwners?: string[]
  tabType?: ReactText
}

/**
 * 查询条件
 */
export const getConditions = async (data) => {
  const res = await request('/predict-actual-compare/defaultCondition', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 卡片
 */
export const getIndicatorCard = async (data: filter) => {
  const res = await request('/predict-actual-compare/summarize', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 月预实偏差柱状图
 */
export const getGroupBarChart = async (data: filter) => {
  const res = await request('/predict-actual-compare/listBia', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}
/**
 * 因子柱状图
 */
export const getFactorsBarChart = async (data) => {
  const res = await request('/predict-actual-compare/factorGroup', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 物料表筛选条件
 */
export const getMpnTableCondition = async (data) => {
  const res = await request('/predict-actual-compare/mpnTableCondition', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 物料表
 */
export const getMpnFactorTable = async (data) => {
  const res = await request('/predict-actual-compare/mpnFactor', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res.data
}

/**
 * 导出物料
 */
export const exportMpnFactor = async (data) => {
  const res = await request('/predict-actual-compare/mpnFactor/export', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 明细表筛选条件
 */
export const getDeptTableCondition = async (data) => {
  const res = await request('/predict-actual-compare/deptTableCondition', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 明细表
 */
export const getDetailTable = async (data) => {
  const res = await request('/predict-actual-compare/deptFactor', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res?.data
}

/**
 * 导出明细
 */
export const exportDeptFactor = async (data) => {
  const res = await request('/predict-actual-compare/deptFactor/export', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}

/**
 * 部门和组长柱状图
 */
export const getDeptAndTeamOwnerChart = async (data) => {
  const res = await request('/predict-actual-compare/list-predict-actual-compare-tab', {
    method: 'POST',
    handleError: false,
    data,
  })
  return res
}
