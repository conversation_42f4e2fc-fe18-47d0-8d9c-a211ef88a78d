import { NOOP_ARR } from '@/utils/noop'
import { request } from '@/utils/request'

export interface PsiParams {
  cvpnCodes?: string[]
  matCatLvl1Codes?: string[]
  skus?: string[]
  endDate?: number
  pageSize?: number
  dept?: string
  type?: string
  pageNum?: number
  mpnIds?: string[]
  brandNames?: string[]
  matCatLvl2Codes?: string[]
  bizName?: string
  matCatLvl3Codes?: string[]
  pnCodes?: string[]
  teamOwnerEn?: string[]
  startDate?: string
}

const apiPrefix = '/cost-inventory'

const getData = (data) => {
  const { data: list = NOOP_ARR, total = 0 } = data || {}
  return {
    list,
    total,
  }
}

export const getPsiConditions = async (date?: string) => {
  const url = date ? `${apiPrefix}/conditions?date=${date}` : `${apiPrefix}/conditions`
  const res = await request(url)
  return res
}

export const getPsiList = async (data: PsiParams) => {
  const res = await request(`${apiPrefix}/list`, {
    method: 'POST',
    data,
  })
  return getData(res?.data)
}

export const exportPsiList = async (data: PsiParams) =>
  await request(`${apiPrefix}/export`, {
    method: 'POST',
    data,
  })

export const getBomConditions = async () => await request(`${apiPrefix}/heyp-conditions`)

export const getBomList = async (data: PsiParams) => {
  const res = await request(`${apiPrefix}/heyp-list`, {
    method: 'POST',
    data,
  })
  return getData(res?.data)
}

export const exportBomList = async (data: PsiParams) =>
  await request(`${apiPrefix}/heyp/export`, {
    method: 'POST',
    data,
  })

export const getFuzzyCondition = async (data) =>
  await request(`${apiPrefix}/query-conditions`, {
    method: 'POST',
    data,
    handleError: false,
  })
