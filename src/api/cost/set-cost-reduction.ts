import { request } from '@/utils/request'

const apiPrefix = '/cost-reduction-set'

export const getSetConditions = (params: { source: '实际' | '全年预测' }) => {
  return request(`${apiPrefix}/conditions`, {
    params,
  })
}

interface SetListParams {
  source: '实际' | '全年预测'
  calcYear?: number
  startMonth?: string
  endMonth?: string
  date?: number
  bizName?: string
  dept?: string
  teamOwner?: string
  matOprNameCn?: string
  subject?: string
  projectNames?: string
  setNos?: string
  regions?: string
}

export const getSetList = (data: SetListParams) => {
  return request(`${apiPrefix}/list`, {
    method: 'POST',
    data,
  })
}

interface MatListParams {
  source: string // 预估 / 实际
  startMonth?: string
  endMonth?: string
  date?: number
  setNo?: string
}

export const getMatList = (data: MatListParams) => {
  return request(`${apiPrefix}/mat-list`, {
    method: 'POST',
    data,
  })
}
