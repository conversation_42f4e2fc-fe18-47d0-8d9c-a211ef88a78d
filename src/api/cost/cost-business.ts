import { getPaginationData } from '@/utils/get-pagination-data'
import { request } from '@/utils/request'

export interface CostListType {
  code?: string[]
  name?: string[]
  status?: number[]
  pageNum: number
  pageSize: number
}
/**
 * 获得所有信息
 */
export const getAll = async () => await request('/cost-type')

/**
 * 根据parentid查询获取条件信息
 */
export const getConditions = async (params: { parentId: number }) => {
  const res = await request('/cost-type/condition', { params })
  return res.data
}

/**
 * 获取list
 */
export const getCostList = async (data) => {
  const res = await request('/cost-type/list', {
    method: 'POST',
    data,
    handleError: false,
  })
  return getPaginationData(res?.data)
}

/**
 * 新增成本项
 */
export const addCostItem = async (data) => {
  const res = await request('/cost-type/add', {
    method: 'POST',
    data,
  })
  return res
}

/**
 * 修改成本项
 */
export const editCostItem = async (data) => {
  const res = await request('/cost-type/update', {
    method: 'PUT',
    data,
  })
  return res
}

/**
 * 删除成本项
 */
export const deleteCostItem = async (params: { id: number; cascade: number }) => {
  const res = await request('/cost-type', {
    method: 'DELETE',
    params,
  })
  return res
}
/**
 * 失效
 */
export const disableCostItem = async (data) => {
  const res = await request('/cost-type/disable', {
    method: 'POST',
    data,
  })
  return res
}
/**
 * 启用
 */
export const enableCostItem = async (data) => {
  const res = await request('/cost-type/enable', {
    method: 'POST',
    data,
  })
  return res
}
