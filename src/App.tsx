/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useCallback, useEffect, useState } from 'react'
import { useNavigate, useRoutes } from 'react-router-dom'
import { routes } from './routes'
import * as Sentry from '@sentry/react'
import { useIdleCheckVersion, usePollCheckVersion } from '@mi/update-notifier-react'
import LocaleProvider from '@hi-ui/locale-context'
import { LocaleEnum } from '@hi-ui/locale-context/lib/types/types'
import intl from 'react-intl-universal'
import zhCN from '../language/zh-CN.json'
import enUS from '../language/en-US.json'
import { getObjectStorage, setStorage } from './utils/cache'
import { getGSCLang } from './constants'

const locales = {
  'zh-CN': zhCN,
  'en-US': enUS,
}
console.log('ddd ==> localhost')

export const App = () => {
  const elements = useRoutes(routes)
  const navigate = useNavigate()
  const lang = getGSCLang()
  const [isInitDone, setInitDone] = useState<boolean>(false)

  const loadLocales = useCallback(() => {
    intl
      .init({
        currentLocale: lang,
        locales,
      })
      .then(() => {
        setInitDone(true)
      })
  }, [lang, setInitDone])

  useEffect(() => {
    if (!getObjectStorage('language')) {
      const language = {
        'zh-CN': zhCN,
        'en-US': enUS,
      }
      setStorage('language', JSON.stringify(language))
    }
  }, [])

  useEffect(() => {
    lang && loadLocales()
  }, [loadLocales, lang])

  useIdleCheckVersion()
  usePollCheckVersion()

  useEffect(() => {
    window.addEventListener('route', (e: any) => {
      navigate(e.detail)
    })
  }, [navigate])

  if (!isInitDone) return <></>

  return (
    <LocaleProvider locale={lang as LocaleEnum}>
      <Sentry.ErrorBoundary>{elements}</Sentry.ErrorBoundary>
    </LocaleProvider>
  )
}
