import { createSlice, PayloadAction } from '@reduxjs/toolkit'

const url = new URLSearchParams(window.location.search)
const initialState: {
  separateWindow: boolean
  routeStack: { route: string; title: string }[]
} = {
  separateWindow: url.get('layout') === 'sepa',
  routeStack: [],
}

export const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    syncRouteStack: (state, action: PayloadAction<{ route: string; title: string }[]>) => {
      state.routeStack = action.payload
    },
  },
})

export default uiSlice.reducer
export const { syncRouteStack } = uiSlice.actions
