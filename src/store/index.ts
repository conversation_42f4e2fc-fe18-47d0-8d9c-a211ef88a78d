import { configureStore } from '@reduxjs/toolkit'
import uiReducer from './reducers/ui'

const store = configureStore({
  reducer: {
    ui: uiReducer,
  },
})

export default store

// store types
export type AppDispatch = typeof store.dispatch
export type AppGetState = typeof store.getState
export type RootState = ReturnType<typeof store.getState>
export type AppSelector<T> = (state: RootState) => T
export type DispatchProps = { dispatch: AppDispatch }
