import { SelectMergedItem } from '@hi-ui/select'
import React, { useState, createContext, Dispatch, SetStateAction } from 'react'
type SetType<T> = Dispatch<SetStateAction<T>>
export const OnlineContext = createContext<{
  filter: Record<string, string | string[]>
  setFilter: SetType<Record<string, string | string[]>>
  expanded: boolean
  setExpanded: SetType<boolean>
  allCountry: Record<string, SelectMergedItem>[]
  setAllCountry: SetType<Record<string, SelectMergedItem>[]>
}>({
  filter: {},
  setFilter: () => undefined,
  expanded: false,
  setExpanded: () => undefined,
  allCountry: [],
  setAllCountry: () => undefined,
})
export const OnlineProvider = ({ children }) => {
  const [filter, setFilter] = useState<Record<string, string | string[]>>({})
  const [expanded, setExpanded] = useState<boolean>(false)
  const [allCountry, setAllCountry] = useState<Record<string, SelectMergedItem>[]>([])

  return (
    <OnlineContext.Provider
      value={{
        filter,
        setFilter,
        expanded,
        setExpanded,
        allCountry,
        setAllCountry,
      }}
    >
      {children}
    </OnlineContext.Provider>
  )
}
