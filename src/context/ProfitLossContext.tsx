import React, { useState, createContext, Dispatch, SetStateAction } from 'react'

type SetType<T> = Dispatch<SetStateAction<T>>

interface IProps {
  filter: Record<string, string | string[]>
  setFilter: SetType<Record<string, AnyType>>
  expanded: boolean
  setExpanded: SetType<boolean>
}

export const ProfitLossContext = createContext<IProps>({
  filter: {},
  setFilter: () => undefined,
  expanded: false,
  setExpanded: () => undefined,
})

export const ProfitLossProvider = ({ children }) => {
  const [filter, setFilter] = useState<Record<string, string | string[]>>({})
  const [expanded, setExpanded] = useState<boolean>(false)

  return (
    <ProfitLossContext.Provider
      value={{
        filter,
        setFilter,
        expanded,
        setExpanded,
      }}
    >
      {children}
    </ProfitLossContext.Provider>
  )
}
