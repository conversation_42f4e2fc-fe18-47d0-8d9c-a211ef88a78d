/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { createContext, FC, ReactText, useEffect, useRef, useState } from 'react'
import { useCRUDVariant } from '@/views/gsc/pages/mat-logistics/hooks'
import { PROJECT_COST } from '@/constants'
import { NOOP_FUNC } from '@/utils/noop'

type SetType<T> = React.Dispatch<React.SetStateAction<T>>

export const ProjectCostContext = createContext<{
  views: Record<string, string | number>[]
  activeViewId: number
  setActiveViewId: React.Dispatch<React.SetStateAction<number>>
  get: any
  add: any
  del: any
  update: any
  hoverVisible: boolean
  rateType: ReactText
  setRateType: SetType<ReactText>
  setHoverVisible: SetType<boolean>
  clickVisible: boolean
  setClickVisible: SetType<boolean>
  expanded: boolean
  setExpanded: SetType<boolean>
  showDataVersion: boolean
  setShowDataVersion: SetType<boolean>
}>({
  views: [],
  activeViewId: -1,
  setActiveViewId: NOOP_FUNC,
  get: NOOP_FUNC,
  add: NOOP_FUNC,
  del: NOOP_FUNC,
  update: NOOP_FUNC,
  hoverVisible: false,
  rateType: 'CNY',
  setRateType: NOOP_FUNC,
  setHoverVisible: NOOP_FUNC,
  clickVisible: false,
  setClickVisible: NOOP_FUNC,
  expanded: false,
  setExpanded: NOOP_FUNC,
  showDataVersion: false,
  setShowDataVersion: NOOP_FUNC,
})

export const ProjectCostProvider: FC = ({ children }) => {
  const [views, setViews] = useState<Record<string, string | number>[]>([])
  const [activeViewId, setActiveViewId] = useState<number>(-1)
  const [hoverVisible, setHoverVisible] = useState<boolean>(false)
  const [clickVisible, setClickVisible] = useState<boolean>(false)
  const [expanded, setExpanded] = useState<boolean>(false) // 控制表单折叠与否
  const { get, del, add, update, variantList: projectViewsList } = useCRUDVariant()
  const onceDefault = useRef<boolean>(true)
  const [rateType, setRateType] = useState<ReactText>('CNY')
  const [showDataVersion, setShowDataVersion] = useState<boolean>(false)

  useEffect(() => {
    get({ pageType: PROJECT_COST })
  }, [get])

  useEffect(() => {
    setViews(projectViewsList)
    if (onceDefault.current) {
      const activeDefaultId = projectViewsList.find((i) => i.isDefault === 1)?.id
      setActiveViewId(activeDefaultId)
      onceDefault.current = false
    }
  }, [setViews, setActiveViewId, projectViewsList])

  return (
    <ProjectCostContext.Provider
      value={{
        views,
        activeViewId,
        setActiveViewId,
        get,
        add,
        del,
        update,
        hoverVisible,
        rateType,
        setRateType,
        setHoverVisible,
        clickVisible,
        setClickVisible,
        expanded,
        setExpanded,
        showDataVersion,
        setShowDataVersion,
      }}
    >
      {children}
    </ProjectCostContext.Provider>
  )
}
