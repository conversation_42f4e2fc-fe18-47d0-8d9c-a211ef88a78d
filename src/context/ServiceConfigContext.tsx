import React, { useState, createContext, Dispatch, SetStateAction } from 'react'
import { AnyType } from '@/constants'

type SetType<T> = Dispatch<SetStateAction<T>>

export const ServiceConfigContext = createContext<{
  filter: Record<string, string | number>
  setFilter: SetType<Record<string, string | number>>
  editParams: AnyType
  setEditParams: SetType<AnyType>
  /**
   * 继承规则二次确认弹窗(子节点配置是否跟随更新)
   */
  inheritConfirm: boolean
  setInheritConfirm: SetType<boolean>
}>({
  filter: {},
  setFilter: () => undefined,
  editParams: {},
  setEditParams: () => undefined,
  inheritConfirm: false,
  setInheritConfirm: () => undefined,
})

export const ServiceConfigProvider = ({ children }) => {
  const [filter, setFilter] = useState<Record<string, string | number>>({
    area: 'CHN',
    productLine: 1,
  })
  const [editParams, setEditParams] = useState<AnyType>({})
  const [inheritConfirm, setInheritConfirm] = useState<boolean>(false)

  return (
    <ServiceConfigContext.Provider
      value={{
        filter,
        setFilter,
        editParams,
        setEditParams,
        inheritConfirm,
        setInheritConfirm,
      }}
    >
      {children}
    </ServiceConfigContext.Provider>
  )
}
