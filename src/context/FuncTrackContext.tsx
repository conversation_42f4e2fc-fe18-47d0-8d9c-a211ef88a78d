import React, { FC, createContext, useState } from 'react'
import { NOOP_FUNC } from '@/utils/noop'
import {
  initialChartFilter,
  initialTableSortMap,
} from '@/views/gsc/pages/function-count-track/config'

type FilterType = Record<string, string | string[]>
type tableSortType = Record<string, number>
type SetType<T> = React.Dispatch<React.SetStateAction<T>>

export const FuncTrackContext = createContext<{
  filter: FilterType
  chartFilter: FilterType
  tipCondition: FilterType
  clickChartCode: string
  resetFlag: boolean
  clickTitle: string
  tableSort: tableSortType
  orderField: number
  orderRule: number
  setFilter: SetType<FilterType>
  setClickChartCode: SetType<string>
  setChartFilter: SetType<FilterType>
  setTipCondition: SetType<FilterType>
  setResetFlag: SetType<boolean>
  setClickTitle: SetType<string>
  setTableSort: SetType<tableSortType>
  setOrder<PERSON>ield: SetType<number>
  setOrderRule: SetType<number>
}>({
  tipCondition: {},
  filter: {},
  chartFilter: initialChartFilter,
  clickChartCode: '',
  resetFlag: false,
  clickTitle: 'none',
  tableSort: {},
  orderField: -1,
  orderRule: -1,
  setFilter: NOOP_FUNC,
  setChartFilter: NOOP_FUNC,
  setTipCondition: NOOP_FUNC,
  setClickChartCode: NOOP_FUNC,
  setResetFlag: NOOP_FUNC,
  setClickTitle: NOOP_FUNC,
  setTableSort: NOOP_FUNC,
  setOrderField: NOOP_FUNC,
  setOrderRule: NOOP_FUNC,
})

export const FuncTrackProvider: FC = ({ children }) => {
  const [filter, setFilter] = useState<FilterType>({})
  const [chartFilter, setChartFilter] = useState<FilterType>(initialChartFilter)
  const [tipCondition, setTipCondition] = useState<FilterType>({})
  const [clickChartCode, setClickChartCode] = useState<string>('')
  const [resetFlag, setResetFlag] = useState<boolean>(false)
  const [clickTitle, setClickTitle] = useState<string>('')
  const [tableSort, setTableSort] = useState<Record<string, number>>(initialTableSortMap)
  const [orderField, setOrderField] = useState<number>(-1)
  const [orderRule, setOrderRule] = useState<number>(-1)

  return (
    <FuncTrackContext.Provider
      value={{
        filter,
        chartFilter,
        tipCondition,
        clickChartCode,
        resetFlag,
        clickTitle,
        tableSort,
        orderField,
        orderRule,
        setFilter,
        setChartFilter,
        setTipCondition,
        setClickChartCode,
        setResetFlag,
        setClickTitle,
        setTableSort,
        setOrderField,
        setOrderRule,
      }}
    >
      {children}
    </FuncTrackContext.Provider>
  )
}
