import { getTodoQueryConditions } from '@/api/gsc/manual-todo'
import { NOOP_OBJ } from '@/utils/noop'
import { useRequest } from 'ahooks'
import React, { createContext, FC, useCallback, useEffect, useState } from 'react'
import { useMatch } from 'react-router-dom'

type SelectType = Record<string, { id: string; title: string }[]>

export const TableFilterContext = createContext<{
  formConditions: SelectType
  abstractConditions: SelectType
}>({
  formConditions: {},
  abstractConditions: {},
})

export const TableFilterProvider: FC = ({ children }) => {
  const match = useMatch({
    path: '/manual-todo/edit/:category/:type',
  })
  const category = match?.params?.category || ''
  const [formConditions, setFormConditions] = useState<SelectType>({})
  const [abstractConditions, setAbstractConditions] = useState<SelectType>({})

  const { data: defaultConditions, run: getDefaultConditions } = useRequest(
    () => getTodoQueryConditions({ category }),
    {
      manual: true,
      refreshDeps: [category],
    }
  )

  useEffect(() => {
    if (category) {
      getDefaultConditions()
    }
  }, [category, getDefaultConditions])

  const formatData = useCallback((item: { code: string; name: string }) => {
    const { code, name } = item
    return { id: code, title: name }
  }, [])

  useEffect(() => {
    const dataArrOrObj = defaultConditions?.data ?? NOOP_OBJ
    const {
      categorys,
      executors,
      stageTypes,
      statusTypes,
      type,
      deptLevel3NameList,
      deptLevel4NameList,
      summaryMap,
      purchaseOrgs,
      stockOrgs,
    } = dataArrOrObj || {}
    const mapData = (data) => data?.map(formatData) || []
    const formRes = {
      categorys: [{ id: categorys?.code || '', title: categorys?.name || '' }],
      executors: mapData(executors),
      stageTypes: mapData(stageTypes),
      statusTypes: mapData(statusTypes),
      type: [{ id: type?.code, title: type?.name }],
      deptLevel3NameList: mapData(deptLevel3NameList),
      deptLevel4NameList: mapData(deptLevel4NameList),
      purchaseOrgs: mapData(purchaseOrgs),
      stockOrgs: mapData(stockOrgs),
    }
    const abstractTemp = summaryMap ?? {}
    setFormConditions(formRes)
    setAbstractConditions(abstractTemp)
  }, [defaultConditions, formatData])

  return (
    <TableFilterContext.Provider value={{ formConditions, abstractConditions }}>
      {children}
    </TableFilterContext.Provider>
  )
}
