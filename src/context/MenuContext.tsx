/* eslint-disable @typescript-eslint/no-explicit-any */
import { ReactNode, createContext, useContext, useEffect, useRef, useState } from 'react'
import { IMenuItem } from '@/api/gsc/user'
import { NOOP_ARR, NOOP_FUNC } from '@/utils/noop'

const MIN_WIDTH = 1280
type SetState = (s: any) => void

export const MenuContext = createContext<{
  allMenus: IMenuItem[]
  curMenu: IMenuItem | null
  showSubMenus: boolean
  setCurMenu: SetState
  setAllMenus: SetState
  setShowSubMenus: SetState
  isFold: boolean
  setIsFold: SetState
  flatMenus: IMenuItem[]
  menuTitle: string | ReactNode
  setMenuTitle: SetState
}>({
  allMenus: [],
  curMenu: null,
  showSubMenus: false,
  setCurMenu: NOOP_FUNC,
  setAllMenus: NOOP_FUNC,
  setShowSubMenus: NOOP_FUNC,
  isFold: false,
  setIsFold: NOOP_FUNC,
  flatMenus: NOOP_ARR,
  menuTitle: '',
  setMenuTitle: NOOP_FUNC,
})

export const useMenuContext = () => {
  return useContext(MenuContext)
}

export const useMenuContextValue = () => {
  const [curMenu, setCurMenu] = useState<IMenuItem | null>(null) // 当前选中的菜单
  const [allMenus, setAllMenus] = useState<IMenuItem[]>([])
  const [menuTitle, setMenuTitle] = useState<string | ReactNode>('')
  const flatMenusRef = useRef<IMenuItem[]>([])
  const [isFold, setIsFold] = useState(window.innerWidth < MIN_WIDTH) // 菜单是否收起
  const lastSizeRef = useRef(window.innerWidth)
  const [showSubMenus, setShowSubMenus] = useState(false)
  // 过小时直接收齐
  useEffect(() => {
    let timer: any = 0
    function resize() {
      timer && clearTimeout(timer)
      timer = setTimeout(() => {
        if (lastSizeRef.current !== window.innerWidth) {
          lastSizeRef.current = window.innerWidth
          setIsFold(window.innerWidth <= MIN_WIDTH)
        }
        timer = 0
      }, 100)
    }
    window.addEventListener('resize', resize)

    return () => {
      window.removeEventListener('resize', resize)
    }
  }, [])

  return {
    curMenu,
    allMenus,
    setCurMenu,
    setAllMenus,
    isFold,
    setIsFold,
    showSubMenus,
    setShowSubMenus,
    menuTitle,
    setMenuTitle,
    flatMenus: flatMenusRef.current,
  }
}
