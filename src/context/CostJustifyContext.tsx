import React, { createContext, FC, useCallback, useState } from 'react'
import { getCostJustifyList } from '@/api/cost/cost-justify'
import { NOOP_FUNC } from '@/utils/noop'
import { CrossTableColumn, SetType } from '@/types/type'
import { EDIT_COLUMN_KEYS, JustifyTabs } from '@/views/gsc/cost/project-cost-justify/config'

export type ProjectCostListResType = Pick<Awaited<ReturnType<typeof getCostJustifyList>>, 'data'>
export type TableDataType = ProjectCostListResType['data']['rowPage']['data'][number]

type SetDimensionsFn = (columns: CrossTableColumn[]) => void
type SetIndicatorsFn = (columns: CrossTableColumn[]) => void
type ResultFormatterFn = (result: ProjectCostListResType) => void

const COST_JUSTIFY_COLUMN_WIDTH = {
  id: 110,
  saleSite: 150,
  saleSiteLvl2: 130,
  config: 150,
  color: 130,
  costMatCatLvl1Code: 190,
  costMatCatLvl2Code: 190,
  costMatCatLvl3Code: 190,
  purchaseChannel: 110,
  containLocalPurchase: 170,
  state: 110,
  spec: 110,
  max: 190,
  min: 190,
  avg: 190,
  purchaseReduction: 110,
  researchReduction: 110,
  m0: 190,
  m1: 190,
  m2: 190,
  m3: 190,
  m4: 190,
  m5: 190,
  m6: 190,
  m7: 190,
  m8: 190,
  m9: 190,
  m10: 190,
  m11: 190,
  uploadLockStatus: 110,
  secondCategory: 190,
}

const initFrozenKey = ''

export const CostJustifyContext = createContext<{
  dimensions: CrossTableColumn[]
  indicators: CrossTableColumn[]
  rawList: TableDataType[]
  data: TableDataType[]
  isLoading: boolean
  setState: SetType<{
    isLoading: boolean
    data: Record<string, string | number | Record<string, string | number>[]>[]
  }>
  frozenKey: string
  dataPath: string[]
  type: string
  expanded: boolean
  filter: Record<string, string[] | string>
  editObj: Record<string, TableDataType>
  setDimensions: SetDimensionsFn
  setIndicators: SetIndicatorsFn
  setRawList: SetType<TableDataType[]>
  resultFormatter: ResultFormatterFn
  setFrozenKey: SetType<string>
  setDataPath: SetType<string[]>
  setType: SetType<string>
  setExpanded: SetType<boolean>
  setFilter: SetType<Record<string, string[] | string>>
  setEditObj: SetType<Record<string, TableDataType>>
}>({
  dimensions: [],
  indicators: [],
  rawList: [],
  data: [],
  isLoading: true,
  frozenKey: initFrozenKey,
  dataPath: [],
  type: JustifyTabs.IPD_DIMENSION,
  expanded: false,
  filter: {},
  editObj: {},
  setState: NOOP_FUNC,
  setDimensions: NOOP_FUNC,
  setIndicators: NOOP_FUNC,
  setRawList: NOOP_FUNC,
  resultFormatter: NOOP_FUNC,
  setFrozenKey: NOOP_FUNC,
  setDataPath: NOOP_FUNC,
  setType: NOOP_FUNC,
  setExpanded: NOOP_FUNC,
  setFilter: NOOP_FUNC,
  setEditObj: NOOP_FUNC,
})

export const CostJustifyProvider: FC = ({ children }) => {
  const [dimensions, setDimensions] = useState<CrossTableColumn[]>([])
  const [indicators, setIndicators] = useState<CrossTableColumn[]>([])
  const [rawList, setRawList] = useState<TableDataType[]>([])
  const [{ data, isLoading }, setState] = useState({ isLoading: true, data: [] as TableDataType[] })
  const [frozenKey, setFrozenKey] = useState<string>(initFrozenKey)
  const [dataPath, setDataPath] = useState<string[]>([])
  const [type, setType] = useState<string>(JustifyTabs.IPD_DIMENSION)
  const [expanded, setExpanded] = useState<boolean>(false)
  const [filter, setFilter] = useState<Record<string, string[] | string>>({})
  const [editObj, setEditObj] = useState<Record<string, TableDataType>>({})

  const filterColumnsByType = useCallback(
    (columns, types, render = null) =>
      columns
        ?.filter((item) => types.includes(item?.type))
        ?.map((column) => {
          const { key, value } = column
          const widthObj = { width: COST_JUSTIFY_COLUMN_WIDTH[key] || 190 }
          const renderObj = render && !EDIT_COLUMN_KEYS.includes(key) ? { render } : {}
          return {
            code: key,
            name: value,
            ...widthObj,
            ...renderObj,
          }
        }) || [],
    []
  )

  const resultFormatter = useCallback(
    (result) => {
      const { columnList = [], rowPage } = result?.data || {}
      const newDimensions = filterColumnsByType(columnList, ['1', '3'], (a) =>
        typeof a?.value === 'number' ? a?.value : a?.value || '-'
      )
      const newIndicators = filterColumnsByType(columnList, ['6']).map((indicator, idx) => {
        return {
          ...indicator,
          width: 130,
          hidden: false,
          align: 'right' as const,
          expression: `SUM(a${idx + 1})`,
        }
      })

      setDimensions(newDimensions)
      setIndicators(newIndicators)
      setRawList(rowPage?.data || [])
    },
    [filterColumnsByType, setIndicators, setDimensions, setRawList]
  )

  return (
    <CostJustifyContext.Provider
      value={{
        dimensions,
        indicators,
        rawList,
        data,
        isLoading,
        frozenKey,
        dataPath,
        type,
        expanded,
        filter,
        editObj,
        setState,
        setDimensions,
        setIndicators,
        setRawList,
        resultFormatter,
        setFrozenKey,
        setDataPath,
        setType,
        setExpanded,
        setFilter,
        setEditObj,
      }}
    >
      {children}
    </CostJustifyContext.Provider>
  )
}
