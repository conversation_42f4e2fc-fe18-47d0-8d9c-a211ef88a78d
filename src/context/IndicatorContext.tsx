import React, { createContext, FC, useCallback, ReactNode, useState } from 'react'
import { getProjectCostList } from '@/api/cost/project-cost'
import { NOOP_FUNC } from '@/utils/noop'
import { PROJECT_COLUMN_WIDTH } from '@/views/gsc/cost/project-cost/constants'
import { AnyType } from '@/constants'
export interface CrossTableColumn {
  code: string | number
  name: string
  hidden?: boolean
  width?: number
  align?: string
  expression?: string
  type?: string
  lock?: boolean
  render?: (a?: Record<string, AnyType>) => ReactNode
}
export type ProjectCostListResType = Pick<Awaited<ReturnType<typeof getProjectCostList>>, 'data'>
export type TableDataType = ProjectCostListResType['data']['rowPage']['data'][number]
type SetType<T> = React.Dispatch<React.SetStateAction<T>>

export const columnWidthKeys = Object.keys(PROJECT_COLUMN_WIDTH)
const key = 'saleSite'

export const IndicatorContext = createContext<{
  allColumns: CrossTableColumn[]
  hiddenColumns: CrossTableColumn[]
  mustShowColumns: CrossTableColumn[]
  dimensions: CrossTableColumn[]
  indicators: CrossTableColumn[]
  columnLoading: boolean
  tableList: TableDataType[]
  data: TableDataType[]
  isLoading: boolean
  setState: SetType<{
    isLoading: boolean
    data: Record<string, string | number | Record<string, string | number>[]>[]
  }>
  configFromView: Record<string, string | string[]>
  frozenKey: string
  dataPath: string[]
  setAllColumns: (columns: CrossTableColumn[]) => void
  setHiddenColumns: (columns: CrossTableColumn[]) => void
  setMustShowColumns: (columns: CrossTableColumn[]) => void
  setDimensions: (columns: CrossTableColumn[]) => void
  setIndicators: (columns: CrossTableColumn[]) => void
  setTableList: SetType<TableDataType[]>
  setColumnLoading: SetType<boolean>
  assignFetchedResult: (result: ProjectCostListResType) => void
  setConfigFromView: SetType<Record<string, string | string[]>>
  setFrozenKey: SetType<string>
  setDataPath: SetType<string[]>
}>({
  allColumns: [],
  hiddenColumns: [],
  mustShowColumns: [],
  dimensions: [],
  indicators: [],
  columnLoading: false,
  tableList: [],
  data: [],
  isLoading: true,
  configFromView: {},
  frozenKey: key,
  dataPath: [],
  setState: NOOP_FUNC,
  setAllColumns: NOOP_FUNC,
  setHiddenColumns: NOOP_FUNC,
  setMustShowColumns: NOOP_FUNC,
  setDimensions: NOOP_FUNC,
  setIndicators: NOOP_FUNC,
  setTableList: NOOP_FUNC,
  setColumnLoading: NOOP_FUNC,
  assignFetchedResult: NOOP_FUNC,
  setConfigFromView: NOOP_FUNC,
  setFrozenKey: NOOP_FUNC,
  setDataPath: NOOP_FUNC,
})

export const IndicatorProvider: FC = ({ children }) => {
  const [allColumns, setAllColumns] = useState<CrossTableColumn[]>([])
  const [hiddenColumns, setHiddenColumns] = useState<CrossTableColumn[]>([])
  const [mustShowColumns, setMustShowColumns] = useState<CrossTableColumn[]>([])
  const [dimensions, setDimensions] = useState<CrossTableColumn[]>([])
  const [indicators, setIndicators] = useState<CrossTableColumn[]>([])
  const [columnLoading, setColumnLoading] = useState<boolean>(true)
  const [tableList, setTableList] = useState<TableDataType[]>([])
  const [{ data, isLoading }, setState] = useState({ isLoading: true, data: [] as TableDataType[] })
  const [configFromView, setConfigFromView] = useState<Record<string, string | string[]>>({})
  const [frozenKey, setFrozenKey] = useState<string>(key)
  const [dataPath, setDataPath] = useState<string[]>([])

  const getColumnsByType = useCallback(
    (allColumns, types) =>
      allColumns
        ?.filter((item) => types.includes(item?.type))
        ?.map((column) => {
          const { key, value } = column
          const obj = columnWidthKeys.includes(key) ? { width: PROJECT_COLUMN_WIDTH[key] } : {}
          return { code: key, name: value, ...obj }
        }) || [],
    []
  )

  const assignFetchedResult = useCallback(
    (result) => {
      const { columnList = [], rowPage } = result?.data || {}
      const { data = [] } = rowPage || {}

      // 获取全量列
      const newAllColumns = columnList?.map((column) => {
        const { key, value, type } = column
        return { code: key, name: value, type }
      })
      const newIndicators = getColumnsByType(columnList, ['6']).map((indicator, idx) => {
        return {
          ...indicator,
          width: 120,
          hidden: false,
          align: 'right' as const,
          expression: `SUM(a${idx + 1})`,
        }
      })
      const newMustShowColumns = getColumnsByType(columnList, ['1'])
      const newDimensions = getColumnsByType(columnList, ['1', '3'])
      const newHiddenColumns = getColumnsByType(columnList, ['2'])
      setHiddenColumns(newHiddenColumns)
      setIndicators(newIndicators)
      setAllColumns(newAllColumns)
      setMustShowColumns(newMustShowColumns)
      setDimensions(newDimensions)
      setTableList(data)
    },
    [
      getColumnsByType,
      setHiddenColumns,
      setIndicators,
      setAllColumns,
      setMustShowColumns,
      setDimensions,
      setTableList,
    ]
  )

  return (
    <IndicatorContext.Provider
      value={{
        allColumns,
        hiddenColumns,
        mustShowColumns,
        dimensions,
        indicators,
        tableList,
        columnLoading,
        data,
        isLoading,
        configFromView,
        frozenKey,
        dataPath,
        setState,
        setAllColumns,
        setHiddenColumns,
        setMustShowColumns,
        setDimensions,
        setIndicators,
        setTableList,
        setColumnLoading,
        assignFetchedResult,
        setConfigFromView,
        setFrozenKey,
        setDataPath,
      }}
    >
      {children}
    </IndicatorContext.Provider>
  )
}
