import React, { createContext, FC, useCallback, useEffect } from 'react'
import { useSafeState } from 'ahooks'
import { DATA_CODES_VALUE, getDataCodes } from '@/api/gsc/user'
import { microApp } from '@/utils/micro-app'

export type IUser = {
  userId: string
  uid: string
  email: string
  userType: number
  menuList: object[]
  functions: string[]
  department: string
  displayName: string
  orgTreeCode: string
}

export interface PBIConfig {
  accessToken: string
  embedUrl: string
  expire: number
  kanbanId: number
  effectiveTime?: number
}

type FetchDataCodesFunctionType = () => void

export const AuthContext = createContext<{
  user: IUser | null
  authing: boolean
  dataCodes: string[]
  functionCodes: string[]
  fetchDataCodes: FetchDataCodesFunctionType
}>({
  user: null,
  authing: true,
  dataCodes: [],
  functionCodes: [],
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  fetchDataCodes: () => {},
})
export const AuthProvider: FC = ({ children }) => {
  const [user, setUser] = useSafeState<IUser | null>(null)
  const [authing, setAuthing] = useSafeState(false)
  const [dataCodes, setDataCodes] = useSafeState<string[]>([])
  const [functionCodes, setFunctionCodes] = useSafeState<string[]>([])

  const fetchDataCodes = useCallback(() => {
    getDataCodes().then((res) => {
      sessionStorage.setItem(DATA_CODES_VALUE, res.data.dataCodes || [])
      setDataCodes(res?.data?.dataCodes || [])
    })
  }, [setDataCodes])

  useEffect(() => {
    const { getAccessData } = microApp.getProps() || {}
    setAuthing(true)
    getAccessData?.().then((res: IUser) => {
      setUser(res)
      setFunctionCodes(res?.functions)
      setAuthing(false)
    })
  }, [setUser, setAuthing, setDataCodes, setFunctionCodes])

  return (
    <AuthContext.Provider
      value={{
        user,
        authing,
        dataCodes,
        functionCodes,
        fetchDataCodes,
      }}
    >
      {authing ? null : children}
    </AuthContext.Provider>
  )
}
