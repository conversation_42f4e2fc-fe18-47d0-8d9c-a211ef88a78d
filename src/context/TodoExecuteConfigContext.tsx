import React, { useState, createContext, Dispatch, SetStateAction } from 'react'
type SetType<T> = Dispatch<SetStateAction<T>>

export const TodoExecuteConfigContext = createContext<{
  filter: Record<string, string | string[]>
  setFilter: SetType<Record<string, string | string[]>>
  expanded: boolean
  setExpanded: SetType<boolean>
  selectedRowKeys: React.ReactText[]
  setSelectedRowKeys: SetType<React.ReactText[]>
}>({
  selectedRowKeys: [],
  setSelectedRowKeys: () => undefined,
  filter: {},
  setFilter: () => undefined,
  expanded: false,
  setExpanded: () => undefined,
})

export const TodoExecuteConfigProvider = ({ children }) => {
  const [filter, setFilter] = useState<Record<string, string | string[]>>({})
  const [expanded, setExpanded] = useState<boolean>(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.ReactText[]>([])

  return (
    <TodoExecuteConfigContext.Provider
      value={{
        filter,
        setFilter,
        expanded,
        setExpanded,
        selectedRowKeys,
        setSelectedRowKeys,
      }}
    >
      {children}
    </TodoExecuteConfigContext.Provider>
  )
}
