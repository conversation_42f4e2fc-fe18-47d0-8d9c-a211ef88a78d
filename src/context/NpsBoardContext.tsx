import { createContext, useContext } from 'react'
import { BoardDataParams } from '@/api/qms/qmsNpsBoard'
import { SetState } from '@/views/qms/npsboard/interface'
export const NpsBoardContext = createContext<{
  filter: Pick<BoardDataParams, 'countryName' | 'statisticalType'>
  setFilter: SetState
}>({
  filter: {
    statisticalType: '',
    countryName: '',
  },
  setFilter: () => undefined,
})

export const useNpsBoardContext = () => {
  return useContext(NpsBoardContext)
}
