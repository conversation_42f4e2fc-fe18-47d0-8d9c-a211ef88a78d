import React, { createContext } from 'react'
import { NOOP_FUNC } from '@/utils/noop'
import { useSafeState } from 'ahooks'

export type SetType<T> = React.Dispatch<React.SetStateAction<T>>
export type DataRecord = Record<string, Array<{ id: string; title: string }>>
export interface SearchType {
  matCatLvl1Code?: string
  matCatLvl2Code?: string
  matCatLvl3Code?: string
  costMatCatLvl1CodeId?: string
  costMatCatLvl2CodeId?: string
  costMatCatLvl3CodeId?: string
  status?: string
}
export interface cateType {
  costCategories: object
  matCategories: object
}

export const CostBusinessMappingContext = createContext<{
  filter: SearchType
  setFilter: SetType<SearchType>
  expanded: boolean
  setExpanded: SetType<boolean>
  categories: cateType
  setCategories: SetType<cateType>
  selectData: DataRecord
  setSelectData: SetType<DataRecord>
  editData: DataRecord
  setEditData: SetType<DataRecord>
}>({
  filter: {},
  setFilter: NOOP_FUNC,
  expanded: true,
  setExpanded: NOOP_FUNC,
  categories: {
    costCategories: [],
    matCategories: [],
  },
  setCategories: NOOP_FUNC,
  selectData: {},
  setSelectData: NOOP_FUNC,
  editData: {},
  setEditData: NOOP_FUNC,
})

export const CostBusinessMappingProvider = ({ children }) => {
  const [filter, setFilter] = useSafeState<SearchType>({})
  const [expanded, setExpanded] = useSafeState<boolean>(true) // 控制表单折叠与否
  // 成本物料映射关系列表
  const [categories, setCategories] = useSafeState<cateType>({
    costCategories: [],
    matCategories: [],
  })
  // 查询条件
  const [selectData, setSelectData] = useSafeState<DataRecord>({})
  // 编辑条件
  const [editData, setEditData] = useSafeState<DataRecord>({})

  return (
    <CostBusinessMappingContext.Provider
      value={{
        filter,
        setFilter,
        expanded,
        setExpanded,
        categories,
        setCategories,
        selectData,
        setSelectData,
        editData,
        setEditData,
      }}
    >
      {children}
    </CostBusinessMappingContext.Provider>
  )
}
