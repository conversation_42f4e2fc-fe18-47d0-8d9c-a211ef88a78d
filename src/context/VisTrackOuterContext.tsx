import React, { FC, createContext, useState } from 'react'
import { useMount } from 'ahooks'
import { setOuterTrackConditionCache } from '@/utils/track-cache'
import { NOOP_FUNC } from '@/utils/noop'

type FilterType = Record<string, string | string[]>
type SetType<T> = React.Dispatch<React.SetStateAction<T>>

export const VisTrackOuterContext = createContext<{
  filter: FilterType
  setFilter: SetType<FilterType>
  kpi: number | string
  setKpi: SetType<number | string>
}>({
  filter: {},
  setFilter: NOOP_FUNC,
  kpi: '',
  setKpi: NOOP_FUNC,
})

export const VisTrackOuterProvider: FC = ({ children }) => {
  const [filter, setFilter] = useState<FilterType>({})
  const [kpi, setKpi] = useState<number | string>('')

  useMount(() => {
    setOuterTrackConditionCache()
  })

  return (
    <VisTrackOuterContext.Provider
      value={{
        filter,
        setFilter,
        kpi,
        setKpi,
      }}
    >
      {children}
    </VisTrackOuterContext.Provider>
  )
}
