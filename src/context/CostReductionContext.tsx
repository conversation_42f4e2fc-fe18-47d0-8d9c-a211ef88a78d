import intl from 'react-intl-universal'
import React, { create<PERSON>ontext, FC, useCallback, useState, ReactNode, useEffect } from 'react'
import { isEmpty } from 'lodash'
import { getCostList } from '@/api/cost/cost-reduction'
import { PROJECT_COLUMN_WIDTH } from '@/views/gsc/cost/project-cost/constants'
import { LeftCrossTreeNode, TopCrossTreeNode } from 'ali-react-table/pivot'
import { NOOP_ARR, NOOP_OBJ, NOOP_FUNC } from '@/utils/noop'
import { AnyType } from '@/constants'

export interface CostPriceType {
  startDate?: number
  endDate?: number
  sourcingOprNameCns?: string[]
  projectNames?: string[]
  purchasePatternDescs?: string[]
  depts?: string[]
  deptOwners?: string[]
  teamOwners?: string[]
  subjects?: string[]
  subProjectNames?: string[]
}
export interface CrossTableColumn {
  code: string
  name: string
  title?: ReactNode
  hidden?: boolean
  width?: number
  align?: string
  expression?: string
  type?: string
  lock?: boolean
  render?: (
    a: undefined | Record<string, AnyType>,
    b: undefined | LeftCrossTreeNode,
    c: undefined | TopCrossTreeNode
  ) => ReactNode
}
export type ProjectCostListResType = Pick<Awaited<ReturnType<typeof getCostList>>, 'list'>
export type TableDataType = ProjectCostListResType['list']['rowPage']['total'][number]
export type SetType<T> = React.Dispatch<React.SetStateAction<T>>
export const columnWidthKeys = Object.keys(PROJECT_COLUMN_WIDTH)
type CostFnType = (result: ProjectCostListResType) => void
export const CostReductionContext = createContext<{
  allColumns: CrossTableColumn[]
  costFilter: CostPriceType
  dimensions: CrossTableColumn[]
  indicators: CrossTableColumn[]
  totals: TableDataType
  data: TableDataType[]
  isLoading: boolean
  setTableListState: SetType<{
    isLoading: boolean
    totals: Record<string, string | number | Record<string, string | number>[]>
    data: Record<string, string | number | Record<string, string | number>[]>[]
  }>
  dateType: number
  dimString: string[]
  hiddenColKeys: string[]
  sortedColKeys: string[][]
  disabledColkeys: string[]
  isAscSort: boolean
  queryConditions: Record<string, { id: string; title: string }[]>
  setAllColumns: SetType<CrossTableColumn[]>
  setDimensions: SetType<CrossTableColumn[]>
  setIndicators: SetType<CrossTableColumn[]>
  assignFetchedResult: CostFnType
  setDisabledColKeys: SetType<string[]>
  setCostFilter: SetType<CostPriceType>
  setDateType: SetType<number>
  setHiddenColKeys: SetType<string[]>
  setSortColKeys: SetType<string[][]>
  setIsAscSort: SetType<boolean>
  setQueryConditions: SetType<Record<string, { id: string; title: string }[]>>
}>({
  dimString: NOOP_ARR,
  allColumns: NOOP_ARR,
  dimensions: NOOP_ARR,
  indicators: NOOP_ARR,
  costFilter: NOOP_OBJ,
  data: NOOP_ARR,
  totals: NOOP_OBJ,
  isLoading: true,
  hiddenColKeys: NOOP_ARR,
  sortedColKeys: NOOP_ARR,
  disabledColkeys: NOOP_ARR,
  dateType: 1,
  isAscSort: true,
  queryConditions: NOOP_OBJ,
  setTableListState: NOOP_FUNC,
  setAllColumns: NOOP_FUNC,
  setDimensions: NOOP_FUNC,
  setIndicators: NOOP_FUNC,
  assignFetchedResult: NOOP_FUNC,
  setCostFilter: NOOP_FUNC,
  setHiddenColKeys: NOOP_FUNC,
  setSortColKeys: NOOP_FUNC,
  setDateType: NOOP_FUNC,
  setDisabledColKeys: NOOP_FUNC,
  setIsAscSort: NOOP_FUNC,
  setQueryConditions: NOOP_FUNC,
})

export const initDimensions = {
  dept: { code: 'dept', name: intl.get('部门'), width: 90 },
  deptOwner: { code: 'deptOwner', name: intl.get('部门负责人'), width: 110 },
  teamOwner: { code: 'teamOwner', name: intl.get('小组负责人'), width: 110 },
  sourcingOprNameCn: { code: 'sourcingOprNameCn', name: intl.get('资源负责人'), width: 110 },
  subject: { code: 'subject', name: intl.get('科目'), width: 90 },
  projectName: { code: 'projectName', name: intl.get('项目'), width: 80 },
  purchasePatternDesc: { code: 'purchasePatternDesc', name: intl.get('采购渠道') },
  matCat: { code: 'matCat', name: intl.get('业务大类') },
  matCatLvl1Code: { code: 'matCatLvl1Code', name: intl.get('大类') },
  matCatLvl2Code: { code: 'matCatLvl2Code', name: intl.get('中类') },
  matCatLvl3Code: { code: 'matCatLvl3Code', name: intl.get('小类') },
  subProjectName: { code: 'subProjectName', name: intl.get('二级项目') },
  catLvl3Name: { code: 'catLvl3Name', name: intl.get('系列') },
  catLvl4Name: { code: 'catLvl4Name', name: intl.get('产品品牌') },
  catLvl5Name: { code: 'catLvl5Name', name: intl.get('机型') },
  config: { code: 'config', name: intl.get('配置') },
  cvpnCode: { code: 'cvpnCode', name: 'CVPN', width: 140 },
  pnCode: { code: 'pnCode', name: 'PN', width: 140 },
  mpnId: { code: 'mpnId', name: 'MPN-ID/SKU', width: 145 },
  odmOprNameCn: { code: 'odmOprNameCn', name: intl.get('ODM名称'), width: 140 },
  matOprNameCn: { code: 'matOprNameCn', name: intl.get('物料负责人名称'), width: 110 },
  isAvap: { code: 'isAvap', name: intl.get('是否AVAP料'), width: 100 },
  matType: { code: 'matType', name: intl.get('材料类别'), width: 100 },
}
const dimensionsList = Object.keys(initDimensions).map((key) => initDimensions[key])
const inintHiddenCol = [
  'subject',
  'projectName',
  'purchasePatternDesc',
  'matCat',
  'matCatLvl1Code',
  'matCatLvl2Code',
  'matCatLvl3Code',
  'subProjectName',
  'catLvl3Name',
  'catLvl4Name',
  'catLvl5Name',
  'config',
  'cvpnCode',
  'pnCode',
  'mpnId',
]
export const CostReductionProvider: FC = ({ children }) => {
  const [allColumns, setAllColumns] = useState<CrossTableColumn[]>([])
  const [dateType, setDateType] = useState<number>(4)
  const [costFilter, setCostFilter] = useState<CostPriceType>({})
  const [dimensions, setDimensions] = useState<CrossTableColumn[]>(dimensionsList)
  const [indicators, setIndicators] = useState<CrossTableColumn[]>([])
  const [isAscSort, setIsAscSort] = useState<boolean>(true)
  const [queryConditions, setQueryConditions] = useState<
    Record<string, { id: string; title: string }[]>
  >({})
  const [{ data, totals, isLoading }, setTableListState] = useState({
    isLoading: true,
    totals: {} as TableDataType,
    data: [] as TableDataType[],
  })
  const [hiddenColKeys, setHiddenColKeys] = useState<string[]>(inintHiddenCol)
  const [sortedColKeys, setSortColKeys] = useState<string[][]>([])
  const [disabledColkeys, setDisabledColKeys] = useState<string[]>([])
  const dimString = dimensionsList.map((dim) => dim?.code ?? '')
  const getColumnsByType = useCallback(
    (allColumns, types) =>
      allColumns
        ?.filter((key) => !types.includes(key?.key ?? ''))
        ?.map((column) => {
          const { key, value, type, dataType } = column
          return { code: key, name: value, width: 160, hidden: false, type, dataType }
        }) || [],
    []
  )
  const getNewDimensions = useCallback(
    (allColumns, types) =>
      allColumns
        ?.filter((key) => types.includes(key?.key ?? ''))
        ?.map((column) => {
          const { key, value } = column
          return { ...initDimensions[key], code: key, name: value }
        }) || [],
    []
  )
  const listByComputed = useCallback((list) => {
    return list.map((key) => {
      const newItem = { ...key }
      Object.keys(newItem).forEach((item) => {
        if (newItem[item] === 'N/A') {
          newItem[item] = ''
        }
      })
      if (newItem?.cvpnCode?.startsWith('MZB')) {
        newItem.cvpnCode = ''
      }
      if (newItem?.pnCode?.startsWith('MZB')) {
        newItem.pnCode = ''
      }
      return newItem
    })
  }, [])
  const assignFetchedResult = useCallback(
    (result) => {
      const { columnList = [], list = [] } = result || {}
      const newAllColumns = columnList?.map((column) => {
        const { key, value, type } = column
        return { code: key, name: value, type }
      })
      const newIndicators = getColumnsByType(columnList, dimString)
      const newDimensions = getNewDimensions(columnList, dimString)
      const totals = dateType === 4 ? {} : list.shift()
      const newList = listByComputed(list)
      setDimensions(newDimensions)
      setIndicators(newIndicators)
      setAllColumns(newAllColumns)
      setTableListState({ data: newList, totals, isLoading: false })
    },
    [getColumnsByType, getNewDimensions, listByComputed, dimString, dateType]
  )
  useEffect(() => {
    const newSortedColKeys = [...sortedColKeys]
    if (!isEmpty(newSortedColKeys)) {
      if (newSortedColKeys[0] && isEmpty(newSortedColKeys[0])) {
        newSortedColKeys[0] = (dimensions || []).map(({ code }) => code)
      }
      newSortedColKeys[1] = (indicators || []).map(({ code }) => code)
      setSortColKeys(newSortedColKeys)
      if (!isEmpty(hiddenColKeys)) {
        const newHiddenColKeys = hiddenColKeys.filter((key) => !newSortedColKeys[1].includes(key))
        setHiddenColKeys(newHiddenColKeys)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dimensions, indicators])
  return (
    <CostReductionContext.Provider
      value={{
        allColumns,
        dimensions,
        indicators,
        data,
        totals,
        dimString,
        isLoading,
        costFilter,
        dateType,
        hiddenColKeys,
        sortedColKeys,
        disabledColkeys,
        isAscSort,
        queryConditions,
        setDisabledColKeys,
        setTableListState,
        setAllColumns,
        setDimensions,
        setIndicators,
        assignFetchedResult,
        setCostFilter,
        setDateType,
        setHiddenColKeys,
        setSortColKeys,
        setIsAscSort,
        setQueryConditions,
      }}
    >
      {children}
    </CostReductionContext.Provider>
  )
}
