import React, { FC } from 'react'
import { describe, it, expect, vi } from 'vitest'
import { render, screen, act } from '@testing-library/react'
import { renderHook } from '@testing-library/react-hooks'
import { NpsBoardContext, useNpsBoardContext } from '@/context/NpsBoardContext'
// 模拟子组件，用于验证上下文传递
const TestConsumer: FC = () => {
  const { filter } = useNpsBoardContext()
  return (
    <div>
      <span data-testid="country">{filter.countryName}</span>
      <span data-testid="type">{filter.statisticalType}</span>
    </div>
  )
}
// 测试更新功能的组件
const TestUpdater: FC = () => {
  const { filter, setFilter } = useNpsBoardContext()
  return (
    <div>
      <span data-testid="current-country">{filter.countryName}</span>
      <button
        data-testid="update-button"
        onClick={() => setFilter({ countryName: 'US', statisticalType: 'average' })}
      >
        Update
      </button>
    </div>
  )
}
describe('NpsBoardContext', () => {
  //  测试 Provider 传递值
  it('应正确传递上下文值', () => {
    const mockValue = {
      filter: {
        countryName: 'CN',
        statisticalType: 'total',
      },
      setFilter: vi.fn(),
    }

    render(
      <NpsBoardContext.Provider value={mockValue}>
        <TestConsumer />
      </NpsBoardContext.Provider>
    )

    expect(screen.getByTestId('country')).toHaveTextContent('CN')
    expect(screen.getByTestId('type')).toHaveTextContent('total')
  })

  //  测试 useContext 获取值
  it('应通过 useNpsBoardContext 获取上下文值', () => {
    const mockValue = {
      filter: {
        countryName: 'JP',
        statisticalType: 'median',
      },
      setFilter: vi.fn(),
    }

    const wrapper: FC = ({ children }) => (
      <NpsBoardContext.Provider value={mockValue}>{children}</NpsBoardContext.Provider>
    )

    const { result } = renderHook(() => useNpsBoardContext(), { wrapper })

    expect(result.current.filter.countryName).toBe('JP')
    expect(result.current.filter.statisticalType).toBe('median')
  })

  //  测试上下文更新
  it('应响应上下文值的更新', () => {
    const initialValue = {
      filter: {
        countryName: 'UK',
        statisticalType: 'min',
      },
      setFilter: vi.fn(),
    }

    render(
      <NpsBoardContext.Provider value={initialValue}>
        <TestUpdater />
      </NpsBoardContext.Provider>
    )

    // 验证初始值
    expect(screen.getByTestId('current-country')).toHaveTextContent('UK')

    // 模拟更新操作
    act(() => {
      screen.getByTestId('update-button').click()
    })

    // 验证 setFilter 被正确调用
    expect(initialValue.setFilter).toHaveBeenCalledWith({
      countryName: 'US',
      statisticalType: 'average',
    })
  })
  // 5. 测试多个消费者
  it('应支持多个消费者共享上下文', () => {
    const mockValue = {
      filter: {
        countryName: 'FR',
        statisticalType: 'max',
      },
      setFilter: vi.fn(),
    }

    render(
      <NpsBoardContext.Provider value={mockValue}>
        <TestConsumer />
        <TestConsumer />
      </NpsBoardContext.Provider>
    )

    const countries = screen.getAllByTestId('country')
    expect(countries).toHaveLength(2)
    countries.forEach((element) => {
      expect(element).toHaveTextContent('FR')
    })
  })
})
