import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import {
  RebateLedgerProvider,
  RebateLedgerContext,
  RebateType,
  Currency,
} from '../RebateLedgerContext'

// Mock modules
vi.mock('@/views/gsc/cost/rebate/detail/config', () => ({
  RebateDetailModule: {
    PAY_REBATE_TREND: 'payRebateTrend',
    REBATE_BY_QUARTER: 'rebateByQuarter',
  },
}))

vi.mock('@/views/gsc/cost/rebate/ledger/config', () => ({
  RebateLedgerModule: {
    DASHBOARD_OVERVIEW: 'dashboardOverview',
    POSTING_MONTH_BY_YEAR: 'postingMonthByYear',
  },
}))

// Test component to consume context
const TestConsumer = () => {
  const context = React.useContext(RebateLedgerContext)

  return (
    <div>
      <div data-testid="filter">{JSON.stringify(context.filter)}</div>
      <div data-testid="expanded">{context.expanded ? 'true' : 'false'}</div>
      <div data-testid="topTab">{context.topTab}</div>
      <div data-testid="exChangeRateType">{context.exChangeRateType}</div>
      <div data-testid="unionFilterInfo">{JSON.stringify(context.unionFilterInfo)}</div>
      <div data-testid="clickedModule">{context.clickedModule || 'null'}</div>

      <button data-testid="setFilter" onClick={() => context.setFilter({ brandName: ['test'] })}>
        Set Filter
      </button>
      <button data-testid="setExpanded" onClick={() => context.setExpanded(false)}>
        Set Expanded
      </button>
      <button data-testid="setTopTab" onClick={() => context.setTopTab(RebateType.LEDGER)}>
        Set Top Tab
      </button>
      <button
        data-testid="setExChangeRateType"
        onClick={() => context.setExChangeRateType(Currency.USD)}
      >
        Set Exchange Rate
      </button>
      <button
        data-testid="setUnionFilterInfo"
        onClick={() => context.setUnionFilterInfo({ test: 'info' } as AnyType)}
      >
        Set Union Filter Info
      </button>
      <button
        data-testid="setClickedModule"
        onClick={() => context.setClickedModule('dashboardOverview' as AnyType)}
      >
        Set Clicked Module
      </button>
    </div>
  )
}

describe('RebateLedgerContext', () => {
  describe('enums', () => {
    it('should export RebateType enum', () => {
      expect(RebateType.LEDGER).toBe('ledger')
      expect(RebateType.DETAIL).toBe('detail')
    })

    it('should export Currency enum', () => {
      expect(Currency.CNY).toBe('CNY')
      expect(Currency.USD).toBe('USD')
    })
  })

  describe('RebateLedgerProvider', () => {
    it('should update filter state', () => {
      render(
        <RebateLedgerProvider>
          <TestConsumer />
        </RebateLedgerProvider>
      )

      fireEvent.click(screen.getByTestId('setFilter'))

      expect(screen.getByTestId('filter')).toHaveTextContent(
        JSON.stringify({ brandName: ['test'] })
      )
    })

    it('should update expanded state', () => {
      render(
        <RebateLedgerProvider>
          <TestConsumer />
        </RebateLedgerProvider>
      )

      fireEvent.click(screen.getByTestId('setExpanded'))

      expect(screen.getByTestId('expanded')).toHaveTextContent('false')
    })

    it('should update topTab state', () => {
      render(
        <RebateLedgerProvider>
          <TestConsumer />
        </RebateLedgerProvider>
      )

      fireEvent.click(screen.getByTestId('setTopTab'))

      expect(screen.getByTestId('topTab')).toHaveTextContent('ledger')
    })

    it('should update exChangeRateType state', () => {
      render(
        <RebateLedgerProvider>
          <TestConsumer />
        </RebateLedgerProvider>
      )

      fireEvent.click(screen.getByTestId('setExChangeRateType'))

      expect(screen.getByTestId('exChangeRateType')).toHaveTextContent('USD')
    })

    it('should update unionFilterInfo state', () => {
      render(
        <RebateLedgerProvider>
          <TestConsumer />
        </RebateLedgerProvider>
      )

      fireEvent.click(screen.getByTestId('setUnionFilterInfo'))

      expect(screen.getByTestId('unionFilterInfo')).toHaveTextContent(
        JSON.stringify({ test: 'info' })
      )
    })

    it('should update clickedModule state', () => {
      render(
        <RebateLedgerProvider>
          <TestConsumer />
        </RebateLedgerProvider>
      )

      fireEvent.click(screen.getByTestId('setClickedModule'))

      expect(screen.getByTestId('clickedModule')).toHaveTextContent('dashboardOverview')
    })

    it('should handle multiple state updates', () => {
      render(
        <RebateLedgerProvider>
          <TestConsumer />
        </RebateLedgerProvider>
      )

      fireEvent.click(screen.getByTestId('setFilter'))
      fireEvent.click(screen.getByTestId('setExpanded'))
      fireEvent.click(screen.getByTestId('setTopTab'))

      expect(screen.getByTestId('filter')).toHaveTextContent(
        JSON.stringify({ brandName: ['test'] })
      )
      expect(screen.getByTestId('expanded')).toHaveTextContent('false')
      expect(screen.getByTestId('topTab')).toHaveTextContent('ledger')
    })

    it('should render children correctly', () => {
      render(
        <RebateLedgerProvider>
          <div data-testid="child">Child Component</div>
        </RebateLedgerProvider>
      )

      expect(screen.getByTestId('child')).toHaveTextContent('Child Component')
    })

    it('should handle complex filter objects', () => {
      const ComplexTestConsumer = () => {
        const { setFilter, filter } = React.useContext(RebateLedgerContext)

        return (
          <div>
            <div data-testid="complex-filter">{JSON.stringify(filter)}</div>
            <button
              data-testid="setComplexFilter"
              onClick={() =>
                setFilter({
                  brandName: ['brand1', 'brand2'],
                  region: ['region1'],
                  dateRange: ['2024-01', '2024-12'],
                })
              }
            >
              Set Complex Filter
            </button>
          </div>
        )
      }

      render(
        <RebateLedgerProvider>
          <ComplexTestConsumer />
        </RebateLedgerProvider>
      )

      fireEvent.click(screen.getByTestId('setComplexFilter'))

      expect(screen.getByTestId('complex-filter')).toHaveTextContent(
        JSON.stringify({
          brandName: ['brand1', 'brand2'],
          region: ['region1'],
          dateRange: ['2024-01', '2024-12'],
        })
      )
    })

    it('should handle string and array values in filter', () => {
      const MixedFilterConsumer = () => {
        const { setFilter, filter } = React.useContext(RebateLedgerContext)

        return (
          <div>
            <div data-testid="mixed-filter">{JSON.stringify(filter)}</div>
            <button
              data-testid="setMixedFilter"
              onClick={() =>
                setFilter({
                  stringValue: 'single value',
                  arrayValue: ['array', 'value'],
                })
              }
            >
              Set Mixed Filter
            </button>
          </div>
        )
      }

      render(
        <RebateLedgerProvider>
          <MixedFilterConsumer />
        </RebateLedgerProvider>
      )

      fireEvent.click(screen.getByTestId('setMixedFilter'))

      expect(screen.getByTestId('mixed-filter')).toHaveTextContent(
        JSON.stringify({
          stringValue: 'single value',
          arrayValue: ['array', 'value'],
        })
      )
    })

    it('should handle unionFilterInfo updates correctly', () => {
      const UnionFilterConsumer = () => {
        const { setUnionFilterInfo, unionFilterInfo } = React.useContext(RebateLedgerContext)

        return (
          <div>
            <div data-testid="union-filter-info">{JSON.stringify(unionFilterInfo)}</div>
            <button
              data-testid="setUnionFilter1"
              onClick={() => setUnionFilterInfo({ module1: 'info1' } as AnyType)}
            >
              Set Union Filter 1
            </button>
            <button
              data-testid="setUnionFilter2"
              onClick={() => setUnionFilterInfo((prev) => ({ ...prev, module2: 'info2' }))}
            >
              Set Union Filter 2
            </button>
          </div>
        )
      }

      render(
        <RebateLedgerProvider>
          <UnionFilterConsumer />
        </RebateLedgerProvider>
      )

      fireEvent.click(screen.getByTestId('setUnionFilter1'))
      expect(screen.getByTestId('union-filter-info')).toHaveTextContent(
        JSON.stringify({ module1: 'info1' })
      )

      fireEvent.click(screen.getByTestId('setUnionFilter2'))
      expect(screen.getByTestId('union-filter-info')).toHaveTextContent(
        JSON.stringify({ module1: 'info1', module2: 'info2' })
      )
    })

    it('should provide all setter functions', () => {
      const SetterTestConsumer = () => {
        const context = React.useContext(RebateLedgerContext)

        return (
          <div>
            <div data-testid="setters">
              {typeof context.setFilter}-{typeof context.setExpanded}-{typeof context.setTopTab}-
              {typeof context.setExChangeRateType}-{typeof context.setUnionFilterInfo}-
              {typeof context.setClickedModule}
            </div>
          </div>
        )
      }

      render(
        <RebateLedgerProvider>
          <SetterTestConsumer />
        </RebateLedgerProvider>
      )

      expect(screen.getByTestId('setters')).toHaveTextContent(
        'function-function-function-function-function-function'
      )
    })
  })

  describe('default context (without provider)', () => {
    it('should provide default values when used without provider', () => {
      const DefaultContextConsumer = () => {
        const context = React.useContext(RebateLedgerContext)

        return (
          <div>
            <div data-testid="default-filter">{JSON.stringify(context.filter)}</div>
            <div data-testid="default-expanded">{context.expanded ? 'true' : 'false'}</div>
            <div data-testid="default-topTab">{context.topTab}</div>
            <div data-testid="default-exChangeRateType">{context.exChangeRateType}</div>
            <div data-testid="default-unionFilterInfo">
              {JSON.stringify(context.unionFilterInfo)}
            </div>
            <div data-testid="default-clickedModule">{context.clickedModule || 'null'}</div>
          </div>
        )
      }

      render(<DefaultContextConsumer />)

      expect(screen.getByTestId('default-filter')).toHaveTextContent('{}')
      expect(screen.getByTestId('default-expanded')).toHaveTextContent('true')
      expect(screen.getByTestId('default-topTab')).toHaveTextContent('ledger')
      expect(screen.getByTestId('default-exChangeRateType')).toHaveTextContent('CNY')
      expect(screen.getByTestId('default-unionFilterInfo')).toHaveTextContent('{}')
      expect(screen.getByTestId('default-clickedModule')).toHaveTextContent('null')
    })

    it('should provide no-op functions when used without provider', () => {
      const DefaultSetterConsumer = () => {
        const context = React.useContext(RebateLedgerContext)

        return (
          <div>
            <div data-testid="default-setters">
              {typeof context.setFilter}-{typeof context.setExpanded}-{typeof context.setTopTab}-
              {typeof context.setExChangeRateType}-{typeof context.setUnionFilterInfo}-
              {typeof context.setClickedModule}
            </div>
            <button
              data-testid="default-set-filter"
              onClick={() => context.setFilter({ test: 'value' })}
            >
              Test Default Setter
            </button>
          </div>
        )
      }

      render(<DefaultSetterConsumer />)

      expect(screen.getByTestId('default-setters')).toHaveTextContent(
        'function-function-function-function-function-function'
      )

      // Should not throw error when calling default setters
      expect(() => {
        fireEvent.click(screen.getByTestId('default-set-filter'))
      }).not.toThrow()
    })
  })
})
