import React, { useContext } from 'react'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect } from 'vitest'
import { GenerationalCostContext, GenerationalCostProvider } from '../GenerationalCost'

// 测试组件，用于验证Context功能
const TestConsumer = () => {
  const { filter, setFilter, expanded, setExpanded, searched, setSearched, baseline, setBaseline } =
    useContext(GenerationalCostContext)

  return (
    <div>
      <div data-testid="filter-value">{JSON.stringify(filter)}</div>
      <div data-testid="expanded-value">{expanded.toString()}</div>
      <div data-testid="searched-value">{searched.toString()}</div>
      <div data-testid="baseline-value">{baseline}</div>

      <button
        data-testid="update-filter"
        onClick={() => setFilter({ pageType: 'project', projectNames: ['test'] })}
      >
        Update Filter
      </button>

      <button data-testid="toggle-expanded" onClick={() => setExpanded(!expanded)}>
        Toggle Expanded
      </button>

      <button data-testid="toggle-searched" onClick={() => setSearched(!searched)}>
        Toggle Searched
      </button>

      <button data-testid="update-baseline" onClick={() => setBaseline('new-baseline')}>
        Update Baseline
      </button>
    </div>
  )
}

describe('GenerationalCostContext', () => {
  const renderWithProvider = () => {
    return render(
      <GenerationalCostProvider>
        <TestConsumer />
      </GenerationalCostProvider>
    )
  }

  describe('初始状态测试', () => {
    it('应该提供正确的初始状态值', () => {
      renderWithProvider()

      expect(screen.getByTestId('filter-value')).toHaveTextContent('{}')
      expect(screen.getByTestId('expanded-value')).toHaveTextContent('true')
      expect(screen.getByTestId('searched-value')).toHaveTextContent('false')
      expect(screen.getByTestId('baseline-value')).toHaveTextContent('')
    })
  })

  describe('状态更新测试', () => {
    it('应该能够更新filter状态', async () => {
      const user = userEvent.setup()
      renderWithProvider()

      const updateButton = screen.getByTestId('update-filter')
      await user.click(updateButton)

      expect(screen.getByTestId('filter-value')).toHaveTextContent(
        JSON.stringify({ pageType: 'project', projectNames: ['test'] })
      )
    })

    it('应该能够切换expanded状态', async () => {
      const user = userEvent.setup()
      renderWithProvider()

      // 初始状态为true
      expect(screen.getByTestId('expanded-value')).toHaveTextContent('true')

      const toggleButton = screen.getByTestId('toggle-expanded')
      await user.click(toggleButton)

      expect(screen.getByTestId('expanded-value')).toHaveTextContent('false')

      // 再次点击应该变回true
      await user.click(toggleButton)
      expect(screen.getByTestId('expanded-value')).toHaveTextContent('true')
    })

    it('应该能够切换searched状态', async () => {
      const user = userEvent.setup()
      renderWithProvider()

      // 初始状态为false
      expect(screen.getByTestId('searched-value')).toHaveTextContent('false')

      const toggleButton = screen.getByTestId('toggle-searched')
      await user.click(toggleButton)

      expect(screen.getByTestId('searched-value')).toHaveTextContent('true')

      // 再次点击应该变回false
      await user.click(toggleButton)
      expect(screen.getByTestId('searched-value')).toHaveTextContent('false')
    })

    it('应该能够更新baseline状态', async () => {
      const user = userEvent.setup()
      renderWithProvider()

      const updateButton = screen.getByTestId('update-baseline')
      await user.click(updateButton)

      expect(screen.getByTestId('baseline-value')).toHaveTextContent('new-baseline')
    })
  })

  describe('复杂状态更新测试', () => {
    it('应该能够处理复杂的filter对象', async () => {
      const user = userEvent.setup()

      const ComplexTestConsumer = () => {
        const { filter, setFilter } = useContext(GenerationalCostContext)

        return (
          <div>
            <div data-testid="filter-value">{JSON.stringify(filter)}</div>
            <button
              data-testid="set-complex-filter"
              onClick={() =>
                setFilter({
                  pageType: 'category',
                  projectNames: ['project1', 'project2'],
                  saleSites: ['site1', 'site2'],
                  configs: ['config1'],
                  costMatCatLvl1Codes: ['cat1'],
                  date: '2023-12-01',
                })
              }
            >
              Set Complex Filter
            </button>
          </div>
        )
      }

      render(
        <GenerationalCostProvider>
          <ComplexTestConsumer />
        </GenerationalCostProvider>
      )

      const setButton = screen.getByTestId('set-complex-filter')
      await user.click(setButton)

      const expectedFilter = {
        pageType: 'category',
        projectNames: ['project1', 'project2'],
        saleSites: ['site1', 'site2'],
        configs: ['config1'],
        costMatCatLvl1Codes: ['cat1'],
        date: '2023-12-01',
      }

      expect(screen.getByTestId('filter-value')).toHaveTextContent(JSON.stringify(expectedFilter))
    })

    it('应该能够处理filter的增量更新', async () => {
      const user = userEvent.setup()

      const IncrementalTestConsumer = () => {
        const { filter, setFilter } = useContext(GenerationalCostContext)

        return (
          <div>
            <div data-testid="filter-value">{JSON.stringify(filter)}</div>
            <button
              data-testid="add-page-type"
              onClick={() => setFilter({ ...filter, pageType: 'project' })}
            >
              Add Page Type
            </button>
            <button
              data-testid="add-project-names"
              onClick={() => setFilter({ ...filter, projectNames: ['test-project'] })}
            >
              Add Project Names
            </button>
          </div>
        )
      }

      render(
        <GenerationalCostProvider>
          <IncrementalTestConsumer />
        </GenerationalCostProvider>
      )

      // 先添加pageType
      const addPageTypeButton = screen.getByTestId('add-page-type')
      await user.click(addPageTypeButton)

      expect(screen.getByTestId('filter-value')).toHaveTextContent(
        JSON.stringify({ pageType: 'project' })
      )

      // 再添加projectNames
      const addProjectNamesButton = screen.getByTestId('add-project-names')
      await user.click(addProjectNamesButton)

      expect(screen.getByTestId('filter-value')).toHaveTextContent(
        JSON.stringify({ pageType: 'project', projectNames: ['test-project'] })
      )
    })
  })

  describe('多组件状态共享测试', () => {
    it('应该在多个消费者组件间共享状态', async () => {
      const user = userEvent.setup()

      const Consumer1 = () => {
        const { filter, setFilter } = useContext(GenerationalCostContext)
        return (
          <div>
            <div data-testid="consumer1-filter">{JSON.stringify(filter)}</div>
            <button
              data-testid="consumer1-update"
              onClick={() => setFilter({ pageType: 'from-consumer1' })}
            >
              Update from Consumer 1
            </button>
          </div>
        )
      }

      const Consumer2 = () => {
        const { filter, setFilter } = useContext(GenerationalCostContext)
        return (
          <div>
            <div data-testid="consumer2-filter">{JSON.stringify(filter)}</div>
            <button
              data-testid="consumer2-update"
              onClick={() => setFilter({ pageType: 'from-consumer2' })}
            >
              Update from Consumer 2
            </button>
          </div>
        )
      }

      render(
        <GenerationalCostProvider>
          <Consumer1 />
          <Consumer2 />
        </GenerationalCostProvider>
      )

      // 从Consumer1更新状态
      const consumer1Button = screen.getByTestId('consumer1-update')
      await user.click(consumer1Button)

      // 两个消费者都应该看到更新
      expect(screen.getByTestId('consumer1-filter')).toHaveTextContent(
        JSON.stringify({ pageType: 'from-consumer1' })
      )
      expect(screen.getByTestId('consumer2-filter')).toHaveTextContent(
        JSON.stringify({ pageType: 'from-consumer1' })
      )

      // 从Consumer2更新状态
      const consumer2Button = screen.getByTestId('consumer2-update')
      await user.click(consumer2Button)

      // 两个消费者都应该看到新的更新
      expect(screen.getByTestId('consumer1-filter')).toHaveTextContent(
        JSON.stringify({ pageType: 'from-consumer2' })
      )
      expect(screen.getByTestId('consumer2-filter')).toHaveTextContent(
        JSON.stringify({ pageType: 'from-consumer2' })
      )
    })
  })

  describe('边界情况测试', () => {
    it('应该处理空数组和空对象', async () => {
      const user = userEvent.setup()

      const EmptyValueConsumer = () => {
        const { filter, setFilter } = useContext(GenerationalCostContext)

        return (
          <div>
            <div data-testid="filter-value">{JSON.stringify(filter)}</div>
            <button data-testid="set-empty-object" onClick={() => setFilter({})}>
              Set Empty Object
            </button>
            <button
              data-testid="set-empty-arrays"
              onClick={() =>
                setFilter({
                  projectNames: [],
                  saleSites: [],
                  configs: [],
                })
              }
            >
              Set Empty Arrays
            </button>
          </div>
        )
      }

      render(
        <GenerationalCostProvider>
          <EmptyValueConsumer />
        </GenerationalCostProvider>
      )

      // 设置空对象
      const setEmptyObjectButton = screen.getByTestId('set-empty-object')
      await user.click(setEmptyObjectButton)

      expect(screen.getByTestId('filter-value')).toHaveTextContent('{}')

      // 设置包含空数组的对象
      const setEmptyArraysButton = screen.getByTestId('set-empty-arrays')
      await user.click(setEmptyArraysButton)

      expect(screen.getByTestId('filter-value')).toHaveTextContent(
        JSON.stringify({
          projectNames: [],
          saleSites: [],
          configs: [],
        })
      )
    })
  })

  describe('类型安全性测试', () => {
    it('应该正确处理字符串和数组类型的filter值', async () => {
      const user = userEvent.setup()

      const TypeTestConsumer = () => {
        const { filter, setFilter } = useContext(GenerationalCostContext)

        return (
          <div>
            <div data-testid="filter-value">{JSON.stringify(filter)}</div>
            <button
              data-testid="set-string-values"
              onClick={() =>
                setFilter({
                  pageType: 'project',
                  exchangeRateType: 'CNY',
                  tableType: 'CVPN',
                  date: '2023-12-01',
                })
              }
            >
              Set String Values
            </button>
            <button
              data-testid="set-array-values"
              onClick={() =>
                setFilter({
                  projectNames: ['project1', 'project2'],
                  saleSites: ['site1'],
                  configs: ['config1', 'config2', 'config3'],
                  phaseStages: ['stage1'],
                })
              }
            >
              Set Array Values
            </button>
          </div>
        )
      }

      render(
        <GenerationalCostProvider>
          <TypeTestConsumer />
        </GenerationalCostProvider>
      )

      // 测试字符串类型值
      const setStringButton = screen.getByTestId('set-string-values')
      await user.click(setStringButton)

      expect(screen.getByTestId('filter-value')).toHaveTextContent(
        JSON.stringify({
          pageType: 'project',
          exchangeRateType: 'CNY',
          tableType: 'CVPN',
          date: '2023-12-01',
        })
      )

      // 测试数组类型值
      const setArrayButton = screen.getByTestId('set-array-values')
      await user.click(setArrayButton)

      expect(screen.getByTestId('filter-value')).toHaveTextContent(
        JSON.stringify({
          projectNames: ['project1', 'project2'],
          saleSites: ['site1'],
          configs: ['config1', 'config2', 'config3'],
          phaseStages: ['stage1'],
        })
      )
    })
  })
})
