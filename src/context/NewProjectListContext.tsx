import React, { createContext, FC, useState } from 'react'
import { SetType } from './CostReductionContext'
import { NOOP_FUNC } from '@/utils/noop'
export interface FilterType {
  projectNames?: string[]
  milestones?: string[]
  bizNames?: string
  configFlag?: string
  saleSites?: string[]
  configs?: string[]
  colors?: string[]
  projectSeries?: string[]
  maintainers?: string[]
  startMilestoneDate?: string
  endMilestoneDate?: string
  projectTypes?: string[]
  type?: string
}

export const NewProjectListContext = createContext<{
  costFilter: FilterType
  setCostFilter: SetType<FilterType>
  expanded: boolean
  setExpanded: SetType<boolean>
}>({
  costFilter: {},
  setCostFilter: NOOP_FUNC,
  expanded: false,
  setExpanded: NOOP_FUNC,
})
export const NewProjectListProvider: FC = ({ children }) => {
  const [costFilter, setCostFilter] = useState<FilterType>({})
  const [expanded, setExpanded] = useState<boolean>(true)

  return (
    <NewProjectListContext.Provider
      value={{
        costFilter,
        setCostFilter,
        expanded,
        setExpanded,
      }}
    >
      {children}
    </NewProjectListContext.Provider>
  )
}
