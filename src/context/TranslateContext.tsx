import React, { useState, createContext, Dispatch, SetStateAction } from 'react'
type SetType<T> = Dispatch<SetStateAction<T>>
export const TranslateContext = createContext<{
  filter: Record<string, string | string[]>
  setFilter: SetType<Record<string, string | string[]>>
  expanded: boolean
  setExpanded: SetType<boolean>
}>({
  filter: {},
  setFilter: () => undefined,
  expanded: false,
  setExpanded: () => undefined,
})
export const TranslateProvider = ({ children }) => {
  const [filter, setFilter] = useState<Record<string, string | string[]>>({})
  const [expanded, setExpanded] = useState<boolean>(false)
  return (
    <TranslateContext.Provider
      value={{
        filter,
        setFilter,
        expanded,
        setExpanded,
      }}
    >
      {children}
    </TranslateContext.Provider>
  )
}
