import React, { createContext } from 'react'
import { NOOP_FUNC } from '@/utils/noop'
import { useSafeState } from 'ahooks'
import { FilterType } from '@/views/gsc/pages/feedback/config'

export type SetType<T> = React.Dispatch<React.SetStateAction<T>>

export const FeedbackContext = createContext<{
  filter: FilterType
  setFilter: SetType<FilterType>
  configTableLoading: boolean
  setConfigTableLoading: SetType<boolean>
  isSaving: boolean
  setIsSaving: SetType<boolean>
}>({
  filter: {},
  setFilter: NOOP_FUNC,
  configTableLoading: false,
  setConfigTableLoading: NOOP_FUNC,
  isSaving: false,
  setIsSaving: NOOP_FUNC,
})

export const FeedbackProvider = ({ children }) => {
  const [filter, setFilter] = useSafeState<FilterType>({
    suppliers: [],
    createNames: [],
  })
  const [configTableLoading, setConfigTableLoading] = useSafeState<boolean>(false)
  const [isSaving, setIsSaving] = useSafeState<boolean>(false)

  return (
    <FeedbackContext.Provider
      value={{
        filter,
        setFilter,
        configTableLoading,
        setConfigTableLoading,
        isSaving,
        setIsSaving,
      }}
    >
      {children}
    </FeedbackContext.Provider>
  )
}
