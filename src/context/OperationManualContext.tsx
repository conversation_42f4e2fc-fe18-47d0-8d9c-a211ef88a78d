import React, { createContext } from 'react'
import { NOOP_FUNC } from '@/utils/noop'
import { useSafeState } from 'ahooks'
import { FilterType, initialValues } from '@/views/gsc/pages/operation-manual/constants'

export type SetType<T> = React.Dispatch<React.SetStateAction<T>>

export const OperationManualContext = createContext<{
  filter: FilterType
  setFilter: SetType<FilterType>
  tableLoading: boolean
  setTableLoading: SetType<boolean>
}>({
  filter: {},
  setFilter: NOOP_FUNC,
  tableLoading: false,
  setTableLoading: NOOP_FUNC,
})

export const OperationManualProvider = ({ children }) => {
  const [filter, setFilter] = useSafeState<FilterType>(initialValues)
  const [tableLoading, setTableLoading] = useSafeState<boolean>(false)

  return (
    <OperationManualContext.Provider
      value={{
        filter,
        setFilter,
        tableLoading,
        setTableLoading,
      }}
    >
      {children}
    </OperationManualContext.Provider>
  )
}
