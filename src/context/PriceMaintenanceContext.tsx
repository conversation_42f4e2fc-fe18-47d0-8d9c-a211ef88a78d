import React, { createContext, FC, useState } from 'react'

import { NOOP_FUNC } from '@/utils/noop'

import { SetType } from './CostReductionContext'

interface FilterType {
  projectNames?: string[]
  bizNames?: string
  saleSites?: string[]
  configs?: string[]
  colors?: string[]
  categorys?: string[]
  milestones?: string[]
  costMatCatLvl3Codes?: string[]
  dataTypes?: string[]
  dataVersions?: string[]
  dataDimensions?: string[]
  states?: string[]
  subjects?: string[]
  costMatCatLvl2Codes?: string[]
  purchaseChannels?: string[]
  updateUsers?: string[]
  pageNum?: number
  pageSize?: number
}

export const PriceMaintenanceContext = createContext<{
  costFilter: FilterType
  setCostFilter: SetType<FilterType>
  expanded: boolean
  setExpanded: SetType<boolean>
  isBomActive: boolean
  setIsBomActive: SetType<boolean>
}>({
  costFilter: { bizNames: '手机' },
  setCostFilter: NOOP_FUNC,
  expanded: false,
  setExpanded: NOOP_FUNC,
  isBomActive: false,
  setIsBomActive: NOOP_FUNC,
})
export const PriceMaintenanceProvider: FC = ({ children }) => {
  const [costFilter, setCostFilter] = useState<FilterType>({ bizNames: '手机' })
  const [expanded, setExpanded] = useState<boolean>(false)
  const [isBomActive, setIsBomActive] = useState<boolean>(false)

  return (
    <PriceMaintenanceContext.Provider
      value={{
        costFilter,
        setCostFilter,
        expanded,
        setExpanded,
        isBomActive,
        setIsBomActive,
      }}
    >
      {children}
    </PriceMaintenanceContext.Provider>
  )
}
