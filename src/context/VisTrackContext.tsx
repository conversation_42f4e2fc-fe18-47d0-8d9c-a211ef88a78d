import React, { FC, createContext, useState } from 'react'
import { NOOP_FUNC } from '@/utils/noop'

type FilterType = Record<string, string | string[]>
type SetType<T> = React.Dispatch<React.SetStateAction<T>>

export const VisTrackContext = createContext<{
  filter: FilterType
  setFilter: SetType<FilterType>
  kpi: number | string
  setKpi: SetType<number | string>
}>({
  filter: {},
  setFilter: NOOP_FUNC,
  kpi: '',
  setKpi: NOOP_FUNC,
})

export const VisTrackProvider: FC = ({ children }) => {
  const [filter, setFilter] = useState<FilterType>({})
  const [kpi, setKpi] = useState<number | string>('')

  return (
    <VisTrackContext.Provider
      value={{
        filter,
        setFilter,
        kpi,
        setKpi,
      }}
    >
      {children}
    </VisTrackContext.Provider>
  )
}
