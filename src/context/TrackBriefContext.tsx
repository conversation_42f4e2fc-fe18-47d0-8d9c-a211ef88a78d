import React, { createContext } from 'react'
import { NOOP_FUNC } from '@/utils/noop'
import { useSafeState } from 'ahooks'
import { DEFAULT_DATE_RANGE } from '@/views/gsc/pages/track-brief/constants'

export type SetType<T> = React.Dispatch<React.SetStateAction<T>>

export const TrackBriefContext = createContext<{
  filter: Record<string, string | string[]>
  setFilter: SetType<Record<string, string | string[]>>
  configTableLoading: boolean
  setConfigTableLoading: SetType<boolean>
  isSaving: boolean
  setIsSaving: SetType<boolean>
}>({
  filter: {},
  setFilter: NOOP_FUNC,
  configTableLoading: false,
  setConfigTableLoading: NOOP_FUNC,
  isSaving: false,
  setIsSaving: NOOP_FUNC,
})

export const TrackBriefProvider = ({ children }) => {
  const [filter, setFilter] = useSafeState<Record<string, string | string[]>>({
    startDate: DEFAULT_DATE_RANGE[0],
    endDate: DEFAULT_DATE_RANGE[1],
  })
  const [configTableLoading, setConfigTableLoading] = useSafeState<boolean>(false)
  const [isSaving, setIsSaving] = useSafeState<boolean>(false)

  return (
    <TrackBriefContext.Provider
      value={{
        filter,
        setFilter,
        configTableLoading,
        setConfigTableLoading,
        isSaving,
        setIsSaving,
      }}
    >
      {children}
    </TrackBriefContext.Provider>
  )
}
