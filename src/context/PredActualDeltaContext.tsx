import React, { createContext, FC, ReactText, useMemo, useState } from 'react'

import { NOOP_FUNC } from '@/utils/noop'
import {
  initDetailTableInfo,
  initFcstTableInfo,
} from '@/views/gsc/cost/pred-actual-delta/constants'

export type SetType<T> = React.Dispatch<React.SetStateAction<T>>
export type ColumnType = { title: string; id: string }[]
export const PredActualDeltaContext = createContext<{
  maxAccountMonth: string
  predCardId: number
  groupCardId: number
  userType: string
  searchCondition: Record<string, boolean | number | string | string[]>
  tableCondition: Record<string, boolean | number | string | string[]>
  chartCondition: string[]
  clickTypeList: { factors: string; matCats: string; teamOwners: string; depts: string }
  expanded: boolean
  updateTime: string
  fcstSortField: string
  fcstSortRule: number
  fcstHiddenColKeys: string[]
  fcstSortedColKeys: string[]
  deptSortField: string
  deptSortRule: number
  deptHiddenColKeys: string[]
  deptSortedColKeys: string[]
  skipChart: string
  tabFirst: boolean
  activeTab: ReactText
  newTabType: number
  setMaxAccountMonth: SetType<string>
  setPredCardId: SetType<number>
  setGroupCardId: SetType<number>
  setUserType: SetType<string>
  setSearchCondition: SetType<Record<string, number | string | boolean | string[]>>
  setTableCondition: SetType<Record<string, number | string | boolean | string[]>>
  setChartCondition: SetType<string[]>
  setClickTypeList: SetType<{ factors: string; matCats: string; teamOwners: string; depts: string }>
  setExpanded: SetType<boolean>
  setUpdateTime: SetType<string>
  setFcstSortField: SetType<string>
  setFcstSortRule: SetType<number>
  setFcstHiddenColKeys: SetType<string[]>
  setFcstSortedColKeys: SetType<string[]>
  setDetailSortField: SetType<string>
  setDetailSortRule: SetType<number>
  setDeptHiddenColKeys: SetType<string[]>
  setDeptSortedColKeys: SetType<string[]>
  setSkipChart: SetType<string>
  setTabFirst: SetType<boolean>
  setActiveTab: SetType<ReactText>
}>({
  maxAccountMonth: '',
  predCardId: 1,
  groupCardId: 1,
  userType: '',
  searchCondition: {},
  tableCondition: {},
  chartCondition: [],
  clickTypeList: {
    factors: '',
    matCats: '',
    teamOwners: '',
    depts: '',
  },
  expanded: true,
  updateTime: '',
  fcstSortField: 'null',
  fcstSortRule: 0,
  fcstHiddenColKeys: [],
  fcstSortedColKeys: [],
  deptSortField: 'null',
  deptSortRule: 0,
  deptHiddenColKeys: [],
  deptSortedColKeys: [],
  skipChart: '',
  tabFirst: true,
  activeTab: 1,
  newTabType: 1,
  setMaxAccountMonth: NOOP_FUNC,
  setPredCardId: NOOP_FUNC,
  setGroupCardId: NOOP_FUNC,
  setUserType: NOOP_FUNC,
  setSearchCondition: NOOP_FUNC,
  setTableCondition: NOOP_FUNC,
  setChartCondition: NOOP_FUNC,
  setClickTypeList: NOOP_FUNC,
  setExpanded: NOOP_FUNC,
  setUpdateTime: NOOP_FUNC,
  setFcstSortField: NOOP_FUNC,
  setFcstSortRule: NOOP_FUNC,
  setFcstHiddenColKeys: NOOP_FUNC,
  setFcstSortedColKeys: NOOP_FUNC,
  setDetailSortField: NOOP_FUNC,
  setDetailSortRule: NOOP_FUNC,
  setDeptHiddenColKeys: NOOP_FUNC,
  setDeptSortedColKeys: NOOP_FUNC,
  setSkipChart: NOOP_FUNC,
  setTabFirst: NOOP_FUNC,
  setActiveTab: NOOP_FUNC,
})

export const PredActualDeltaProvider: FC = ({ children }) => {
  const [maxAccountMonth, setMaxAccountMonth] = useState('')
  const [predCardId, setPredCardId] = useState(1)
  const [groupCardId, setGroupCardId] = useState(1)
  const [userType, setUserType] = useState('')
  const [searchCondition, setSearchCondition] = useState({})
  const [tableCondition, setTableCondition] = useState({})
  const [chartCondition, setChartCondition] = useState<string[]>([])
  const [clickTypeList, setClickTypeList] = useState<{
    factors: string
    matCats: string
    teamOwners: string
    depts: string
  }>({
    factors: '',
    matCats: '',
    teamOwners: '',
    depts: '',
  })
  const [expanded, setExpanded] = useState<boolean>(false)
  const [updateTime, setUpdateTime] = useState<string>('')

  const [fcstSortField, setFcstSortField] = useState<string>('null')
  const [fcstSortRule, setFcstSortRule] = useState<number>(0)
  const [fcstHiddenColKeys, setFcstHiddenColKeys] = useState<string[]>(
    initFcstTableInfo.hiddenColKeys
  )
  const [fcstSortedColKeys, setFcstSortedColKeys] = useState<string[]>(
    initFcstTableInfo.sortedColKeys
  )

  const [deptSortField, setDetailSortField] = useState<string>('null')
  const [deptSortRule, setDetailSortRule] = useState<number>(0)
  const [deptHiddenColKeys, setDeptHiddenColKeys] = useState<string[]>(
    initDetailTableInfo.hiddenColKeys
  )
  const [deptSortedColKeys, setDeptSortedColKeys] = useState<string[]>(
    initDetailTableInfo.sortedColKeys
  )
  const [skipChart, setSkipChart] = useState('')
  const [tabFirst, setTabFirst] = useState<boolean>(true)
  const [activeTab, setActiveTab] = useState<ReactText>(1)

  const newTabType = useMemo(() => {
    const { depts = [], teamOwners = [] } = (searchCondition || {}) as Record<string, string[]>
    return !depts?.length && !teamOwners?.length ? 1 : teamOwners?.length ? 3 : 2
  }, [searchCondition])

  return (
    <PredActualDeltaContext.Provider
      value={{
        maxAccountMonth,
        predCardId,
        groupCardId,
        userType,
        searchCondition,
        tableCondition,
        chartCondition,
        clickTypeList,
        expanded,
        updateTime,
        fcstSortField,
        fcstSortRule,
        fcstHiddenColKeys,
        fcstSortedColKeys,
        deptSortField,
        deptSortRule,
        deptHiddenColKeys,
        deptSortedColKeys,
        skipChart,
        tabFirst,
        activeTab,
        newTabType,
        setMaxAccountMonth,
        setPredCardId,
        setGroupCardId,
        setUserType,
        setSearchCondition,
        setTableCondition,
        setChartCondition,
        setClickTypeList,
        setExpanded,
        setUpdateTime,
        setFcstSortField,
        setFcstSortRule,
        setFcstHiddenColKeys,
        setFcstSortedColKeys,
        setDetailSortField,
        setDetailSortRule,
        setDeptHiddenColKeys,
        setDeptSortedColKeys,
        setSkipChart,
        setTabFirst,
        setActiveTab,
      }}
    >
      {children}
    </PredActualDeltaContext.Provider>
  )
}
