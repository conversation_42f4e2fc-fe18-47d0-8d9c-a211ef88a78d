import React, { createContext, FC, useState } from 'react'
import { SetType } from './CostReductionContext'
import { NOOP_FUNC } from '@/utils/noop'
import { ConfigInfoType, NodeType } from '@/api/cost/project-maintenance'
export const PROJECT_INFO_DATA: ConfigInfoType = {
  projectName: '',
  projectSeries: '',
  projectType: '',
  pValue: 0,
  maintainers: [],
  milestones: [],
  projectConfigs: [],
}

export interface ConfigsType {
  saleSites: string
  saleSiteLvl2s: string[]
  configs: string[]
  colors: string[]
  releaseTime: string
}

export const CONFIG_INFO_DATA: ConfigsType = {
  saleSites: '',
  saleSiteLvl2s: [],
  configs: [],
  colors: [],
  releaseTime: new Date().toLocaleString(),
}

export interface ConfigTableType {
  id?: number
  pId?: string
  projectName?: string
  saleSite: string
  config: string
  color: string
  releaseTime: string
  configFlag: number
  mat95: string
  sku: string
  canDelete?: boolean
}

export type ConfigDataType = Record<string, AnyType>

export const NewProjectMaintenanceContext = createContext<{
  formValue: ConfigInfoType
  setFormValue: SetType<ConfigInfoType>
  configForm: ConfigsType
  setConfigForm: SetType<ConfigsType>
  configDatas: ConfigDataType
  setConfigDatas: SetType<ConfigsType>
  detailDatas: ConfigDataType
  setDetailDatas: SetType<ConfigDataType>
  milestoneForm: NodeType[]
  setMilestoneForm: SetType<NodeType[]>
  currentType: string
  setCurrentType: SetType<string>
  tableData: ConfigTableType[]
  setTableData: SetType<ConfigTableType[]>
  editDataLoading: boolean
  setEditDataLoading: SetType<boolean>
}>({
  formValue: PROJECT_INFO_DATA,
  setFormValue: NOOP_FUNC,
  configDatas: {},
  setConfigDatas: NOOP_FUNC,
  detailDatas: {},
  setDetailDatas: NOOP_FUNC,
  configForm: CONFIG_INFO_DATA,
  setConfigForm: NOOP_FUNC,
  milestoneForm: [],
  setMilestoneForm: NOOP_FUNC,
  currentType: 'config',
  setCurrentType: NOOP_FUNC,
  tableData: [],
  setTableData: NOOP_FUNC,
  editDataLoading: true,
  setEditDataLoading: NOOP_FUNC,
})
export const NewProjectMaintenanceProvider: FC = ({ children }) => {
  const [formValue, setFormValue] = useState<ConfigInfoType>(PROJECT_INFO_DATA)
  const [configForm, setConfigForm] = useState<ConfigsType>(CONFIG_INFO_DATA)
  const [milestoneForm, setMilestoneForm] = useState<NodeType[]>([])
  const [currentType, setCurrentType] = useState<string>('config')
  const [tableData, setTableData] = useState<ConfigTableType[]>([])
  const [editDataLoading, setEditDataLoading] = useState(true)
  const [configDatas, setConfigDatas] = useState<ConfigDataType>({})
  const [detailDatas, setDetailDatas] = useState<ConfigDataType>({})

  return (
    <NewProjectMaintenanceContext.Provider
      value={{
        formValue,
        detailDatas,
        setDetailDatas,
        setFormValue,
        configDatas,
        setConfigDatas,
        configForm,
        setConfigForm,
        milestoneForm,
        setMilestoneForm,
        currentType,
        setCurrentType,
        tableData,
        setTableData,
        editDataLoading,
        setEditDataLoading,
      }}
    >
      {children}
    </NewProjectMaintenanceContext.Provider>
  )
}
