import React, { FC, createContext, useState } from 'react'
import { NOOP_OBJ, NOOP_FUNC } from '@/utils/noop'
import { SelectMergedItem } from '@hi-ui/select'
import { useRequest } from 'ahooks'
import { getBomConditions, getPsiConditions } from '@/api/cost/material-psi'

export interface CostPriceType {
  startDate?: number
  endDate?: number
  sourcingOprNameCns?: string[]
  projectNames?: string[]
  purchasePatternDescs?: string[]
  depts?: string[]
  deptOwners?: string[]
  teamOwnerEn?: string[]
  subjects?: string[]
  subProjectNames?: string[]
}

export type SetType<T> = React.Dispatch<React.SetStateAction<T>>
export type ColumnType = { title: string; id: string }[]
export const MaterialPsiContext = createContext<{
  tabId: number
  tableTab: 'cvpn' | 'mpn'
  expanded: boolean
  filter: CostPriceType
  bomFilter: AnyType
  conditionList: Record<string, SelectMergedItem[]>
  bomConditionList: Record<string, SelectMergedItem[]>
  setTabId: SetType<number>
  setTableTab: SetType<'cvpn' | 'mpn'>
  setExpanded: SetType<boolean>
  setFilter: SetType<CostPriceType>
  setBomFilter: SetType<AnyType>
  setConditionList: SetType<Record<string, SelectMergedItem[]>>
}>({
  tabId: 1,
  tableTab: 'mpn',
  expanded: true,
  filter: NOOP_OBJ,
  bomFilter: NOOP_OBJ,
  conditionList: NOOP_OBJ,
  bomConditionList: NOOP_OBJ,
  setTabId: NOOP_FUNC,
  setTableTab: NOOP_FUNC,
  setExpanded: NOOP_FUNC,
  setFilter: NOOP_FUNC,
  setBomFilter: NOOP_FUNC,
  setConditionList: NOOP_FUNC,
})

const onValueMap = (arr) => arr.map((item) => ({ id: item, title: item }))
export const formatCondition = (conditions) => {
  const {
    dates = [], // 数据版本
    bizNames = [], // 业务线
    depts = [], // 部门
    teamOwners = [], // 组长
    skus = [], // SKU
    cvpnCodes = [], // CVPN
    pnCodes = [], // PN
    mpnIds = [], // MPN
    matCatLvl1Codes = [], // 物料大类
    matCatLvl2Codes = [], // 物料中类
    matCatLvl3Codes = [], // 物料小类
    brandNames = [], // 品牌
  } = conditions || {}
  return {
    dates: onValueMap(dates),
    bizNames: onValueMap(bizNames),
    depts: onValueMap(depts),
    teamOwners: teamOwners.map((item) => ({ id: item.value, title: item.name })),
    skus: onValueMap(skus),
    cvpnCodes: onValueMap(cvpnCodes),
    pnCodes: onValueMap(pnCodes),
    mpnIds: onValueMap(mpnIds),
    matCatLvl1Codes: onValueMap(matCatLvl1Codes),
    matCatLvl2Codes: onValueMap(matCatLvl2Codes),
    matCatLvl3Codes: onValueMap(matCatLvl3Codes),
    brandNames: onValueMap(brandNames),
  }
}

export const MaterialPsiProvider: FC = ({ children }) => {
  const [tabId, setTabId] = useState<number>(1)
  const [tableTab, setTableTab] = useState<'cvpn' | 'mpn'>('mpn')
  const [filter, setFilter] = useState<CostPriceType>({})
  const [bomFilter, setBomFilter] = useState<CostPriceType>({})
  const [expanded, setExpanded] = useState<boolean>(true)
  const [conditionList, setConditionList] = useState<Record<string, SelectMergedItem[]>>({
    dates: [], // 数据版本
    bizNames: [], // 业务线
    depts: [], // 部门
    teamOwners: [], // 组长
    skus: [], // SKU
    cvpnCodes: [], // CVPN
    pnCodes: [], // PN
    mpnIds: [], // MPN
    matCatLvl1Codes: [], // 物料大类
    matCatLvl2Codes: [], // 物料中类
    matCatLvl3Codes: [], // 物料小类
    brandNames: [], // 品牌
  })
  const [bomConditionList, setBomConditionList] = useState<Record<string, SelectMergedItem[]>>({
    pros: [], // 项目
    sopcodes: [], // 业务线
    skus: [], // 部门
    isLcs: [], // 组长
    hasDemands: [], // CVPN
    chargeTypes: [], // PN
  })

  useRequest(getPsiConditions, {
    onSuccess: (res) => {
      setConditionList(formatCondition(res?.data || {}))
    },
  })

  useRequest(getBomConditions, {
    onSuccess: (res) => {
      setBomConditionList(
        Object.keys(res?.data || {}).reduce((a: AnyType, b: string) => {
          a[b] = onValueMap(res?.data[b])
          return a
        }, [])
      )
    },
  })

  return (
    <MaterialPsiContext.Provider
      value={{
        tabId,
        tableTab,
        expanded,
        filter,
        bomFilter,
        conditionList,
        bomConditionList,
        setTabId,
        setTableTab,
        setExpanded,
        setFilter,
        setBomFilter,
        setConditionList,
      }}
    >
      {children}
    </MaterialPsiContext.Provider>
  )
}
