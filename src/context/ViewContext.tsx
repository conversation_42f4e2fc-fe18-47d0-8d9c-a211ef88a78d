import React, { createContext, useEffect, useRef, useState } from 'react'
import { useCRUDVariant } from '@/views/gsc/pages/mat-logistics/hooks'
import { NOOP_FUNC } from '@/utils/noop'
import { AnyType } from '@/constants'

type SetType<T> = React.Dispatch<React.SetStateAction<T>>

export const ViewContext = createContext<{
  views: Record<string, string | number>[]
  activeViewId: number
  setActiveViewId: React.Dispatch<React.SetStateAction<number>>
  get: AnyType
  add: AnyType
  del: AnyType
  update: AnyType
  hoverVisible: boolean
  setHoverVisible: SetType<boolean>
  configFromView: Record<string, string | string[]>
  setConfigFromView: SetType<Record<string, string | string[]>>
}>({
  views: [],
  activeViewId: -1,
  setActiveViewId: NOOP_FUNC,
  get: NOOP_FUNC,
  add: NOOP_FUNC,
  del: NOOP_FUNC,
  update: NOOP_FUNC,
  hoverVisible: false,
  setHoverVisible: NOOP_FUNC,
  configFromView: {},
  setConfigFromView: NOOP_FUNC,
})

export const ViewProvider = ({ children, cachedKey }) => {
  const [views, setViews] = useState<Record<string, string | number>[]>([])
  const [activeViewId, setActiveViewId] = useState<number>(-1)
  const [hoverVisible, setHoverVisible] = useState<boolean>(false)
  const [configFromView, setConfigFromView] = useState<Record<string, string | string[]>>({})
  const { get, del, add, update, variantList: viewList } = useCRUDVariant()
  const onceDefault = useRef<boolean>(true)

  useEffect(() => {
    get({ pageType: cachedKey })
  }, [cachedKey, get])

  useEffect(() => {
    setViews(viewList)
    if (onceDefault.current) {
      const activeDefaultId = viewList.find((i) => i.isDefault === 1)?.id
      setActiveViewId(activeDefaultId)
      onceDefault.current = false
    }
  }, [setViews, setActiveViewId, viewList])

  return (
    <ViewContext.Provider
      value={{
        views,
        activeViewId,
        setActiveViewId,
        get,
        add,
        del,
        update,
        hoverVisible,
        setHoverVisible,
        configFromView,
        setConfigFromView,
      }}
    >
      {children}
    </ViewContext.Provider>
  )
}
