import React, { createContext, Dispatch, SetStateAction, useState } from 'react'

type SetType<T> = Dispatch<SetStateAction<T>>

export const GenerationalCostContext = createContext<{
  filter: Record<string, string | string[]>
  setFilter: SetType<Record<string, string | string[]>>
  expanded: boolean
  setExpanded: SetType<boolean>
  searched: boolean
  setSearched: SetType<boolean>
  baseline: string
  setBaseline: SetType<string>
}>({
  /** 过滤条件 */
  filter: {},
  setFilter: () => undefined,
  /** 表单是否展开 */
  expanded: false,
  setExpanded: () => undefined,
  /** 是否搜索 */
  searched: false,
  setSearched: () => undefined,
  /** 基准列 */
  baseline: '',
  setBaseline: () => undefined,
})

export const GenerationalCostProvider = ({ children }) => {
  const [filter, setFilter] = useState<Record<string, string | string[]>>({})
  const [expanded, setExpanded] = useState<boolean>(true)
  const [searched, setSearched] = useState<boolean>(false)
  const [baseline, setBaseline] = useState<string>('')

  return (
    <GenerationalCostContext.Provider
      value={{
        filter,
        setFilter,
        expanded,
        setExpanded,
        searched,
        setSearched,
        baseline,
        setBaseline,
      }}
    >
      {children}
    </GenerationalCostContext.Provider>
  )
}
