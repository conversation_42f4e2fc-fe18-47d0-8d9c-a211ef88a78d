import { EmptyFunctionType } from '@/types/type'
import { RebateDetailModule } from '@/views/gsc/cost/rebate/detail/config'
import { RebateLedgerModule } from '@/views/gsc/cost/rebate/ledger/config'
import React, { useState, createContext, Dispatch, SetStateAction, useCallback } from 'react'

type SetType<T> = Dispatch<SetStateAction<T>>

export enum RebateType {
  LEDGER = 'ledger',
  DETAIL = 'detail',
}

export enum Currency {
  CNY = 'CNY',
  USD = 'USD',
  // INR = 'INR',
  // IDR = 'IDR',
}

// 定义过滤信息的类型 - 以模块名为key
export interface UnionFilterInfo {
  dashboardOverview?: string
  postingMonthByYear?: string
  postingMonthByMonth?: string
  postingMonthTopFive?: string
  rebateBrandTopThree?: string
  rebateBrandTopTen?: string
  rebateBrandTopThreeTrend?: string
  overviewAnalysis?: string
  overviewAnalysisMat?: string
  totalList?: string
}

export const RebateLedgerContext = createContext<{
  filter: Record<string, string | string[]>
  setFilter: SetType<Record<string, string | string[]>>
  expanded: boolean
  setExpanded: SetType<boolean>
  topTab: RebateType
  setTopTab: SetType<RebateType>
  exChangeRateType: Currency
  setExChangeRateType: SetType<Currency>
  unionFilterInfo: UnionFilterInfo
  setUnionFilterInfo: SetType<UnionFilterInfo>
  clickedModule: RebateLedgerModule | RebateDetailModule | null
  setClickedModule: SetType<RebateLedgerModule | RebateDetailModule | null>
  resetAllUnionFilters: EmptyFunctionType
}>({
  filter: {},
  setFilter: () => undefined,
  expanded: true,
  setExpanded: () => undefined,
  topTab: RebateType.LEDGER,
  setTopTab: () => undefined,
  exChangeRateType: Currency.CNY,
  setExChangeRateType: () => undefined,
  unionFilterInfo: {},
  setUnionFilterInfo: () => undefined,
  clickedModule: null,
  setClickedModule: () => undefined,
  resetAllUnionFilters: () => undefined,
})

export const RebateLedgerProvider = ({ children }) => {
  const [filter, setFilter] = useState<Record<string, string | string[]>>({})
  const [expanded, setExpanded] = useState<boolean>(true)
  const [topTab, setTopTab] = useState<RebateType>(RebateType.LEDGER)
  const [exChangeRateType, setExChangeRateType] = useState<Currency>(Currency.CNY)
  // 模块点击提示信息
  const [unionFilterInfo, setUnionFilterInfo] = useState<UnionFilterInfo>({})
  // 模块点击互斥控制
  const [clickedModule, setClickedModule] = useState<
    RebateLedgerModule | RebateDetailModule | null
  >(null)

  // 重置所有联动过滤器状态
  const resetAllUnionFilters = useCallback(() => {
    setUnionFilterInfo({})
    setClickedModule(null)
    // 使用setTimeout确保状态更新完成后再触发事件
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('resetUnionFilters'))
    }, 0)
  }, [])

  return (
    <RebateLedgerContext.Provider
      value={{
        filter,
        setFilter,
        expanded,
        setExpanded,
        topTab,
        setTopTab,
        exChangeRateType,
        setExChangeRateType,
        unionFilterInfo,
        setUnionFilterInfo,
        clickedModule,
        setClickedModule,
        resetAllUnionFilters,
      }}
    >
      {children}
    </RebateLedgerContext.Provider>
  )
}
