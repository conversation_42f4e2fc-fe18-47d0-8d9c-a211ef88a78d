import React, { useState, createContext, Dispatch, SetStateAction } from 'react'

type SetType<T> = Dispatch<SetStateAction<T>>

interface IProps {
  filter: Record<string, string | string[]>
  setFilter: SetType<Record<string, AnyType>>
  expanded: boolean
  setExpanded: SetType<boolean>
}

export const EcoBomCostContext = createContext<IProps>({
  filter: {},
  setFilter: () => undefined,
  expanded: false,
  setExpanded: () => undefined,
})

export const EcoBomCostProvider = ({ children }) => {
  const [filter, setFilter] = useState<Record<string, string | string[]>>({})
  const [expanded, setExpanded] = useState<boolean>(false)

  return (
    <EcoBomCostContext.Provider
      value={{
        filter,
        setFilter,
        expanded,
        setExpanded,
      }}
    >
      {children}
    </EcoBomCostContext.Provider>
  )
}
