/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { createContext, FC, useEffect, useState } from 'react'
import { getCacheDefaultFilterValue } from '@/api/gsc/mat-logistics'
import { NOOP_OBJ } from '@/utils/noop'
import { useRequest } from 'ahooks'
import { LINK_VISIBLE_MAT_STORAGE_KEY } from '@/constants'

export const MatVisibleContext = createContext<{
  formConditions: Record<string, any> | undefined
}>({
  formConditions: undefined,
})

export const MatVisibleProvider: FC = ({ children }) => {
  const [formConditions, setFormConditions] = useState<Record<string, any>>()

  const { runAsync: getCache } = useRequest(() => getCacheDefaultFilterValue(), {
    manual: true,
  })

  useEffect(() => {
    const storage = localStorage.getItem(LINK_VISIBLE_MAT_STORAGE_KEY)
    storage && setFormConditions(JSON.parse(storage))
  }, [])

  useEffect(() => {
    getCache().then((res) => {
      const storage = localStorage.getItem(LINK_VISIBLE_MAT_STORAGE_KEY)
      const dataArrOrObj = res?.data ?? NOOP_OBJ
      !storage && setFormConditions(dataArrOrObj)
      localStorage.setItem(LINK_VISIBLE_MAT_STORAGE_KEY, JSON.stringify(dataArrOrObj))
    })
  }, [getCache])

  return (
    <MatVisibleContext.Provider value={{ formConditions }}>{children}</MatVisibleContext.Provider>
  )
}
