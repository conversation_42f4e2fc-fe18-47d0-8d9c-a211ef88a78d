import React, { createContext, Dispatch, SetStateAction, useState } from 'react'

type SetType<T> = Dispatch<SetStateAction<T>>

interface ISubjectUploadContext {
  filter: Record<string, string[]>
  setFilter: SetType<Record<string, string[]>>
  tab: string
  setTab: SetType<string>
}

export const SubjectUploadContext = createContext<ISubjectUploadContext>({
  filter: {},
  setFilter: () => undefined,
  tab: '0',
  setTab: () => undefined,
})

export const SubjectUploadProvider = ({ children }) => {
  const [filter, setFilter] = useState<Record<string, string[]>>({})
  const [tab, setTab] = useState<string>('0')

  return (
    <SubjectUploadContext.Provider value={{ filter, setFilter, tab, setTab }}>
      {children}
    </SubjectUploadContext.Provider>
  )
}
