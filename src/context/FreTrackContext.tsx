import React, { FC, createContext, useRef, useState } from 'react'
import { NOOP_FUNC } from '@/utils/noop'

type FilterType = Record<string, string | string[]>
type SetType<T> = React.Dispatch<React.SetStateAction<T>>

export const FreTrackContext = createContext<{
  filter: FilterType
  setFilter: SetType<FilterType>
  kpi: number | string
  setKpi: SetType<number | string>
  isRanking: React.MutableRefObject<boolean>
  nonMenuTitles: string[]
  setNonMenuTitles: SetType<string[]>
}>({
  filter: {},
  setFilter: NOOP_FUNC,
  kpi: '',
  setKpi: NOOP_FUNC,
  isRanking: { current: true },
  nonMenuTitles: [],
  setNonMenuTitles: NOOP_FUNC,
})

export const FreTrackProvider: FC = ({ children }) => {
  const [filter, setFilter] = useState<FilterType>({})
  const [kpi, setKpi] = useState<number | string>('')
  const isRanking = useRef<boolean>(true)
  const [nonMenuTitles, setNonMenuTitles] = useState<string[]>([])

  return (
    <FreTrackContext.Provider
      value={{
        filter,
        setFilter,
        kpi,
        setKpi,
        isRanking,
        nonMenuTitles,
        setNonMenuTitles,
      }}
    >
      {children}
    </FreTrackContext.Provider>
  )
}
