import React, { useState, createContext, Dispatch, SetStateAction } from 'react'
import {
  AlgorithmDenominator,
  defaultFormData,
  IFormData,
} from '@/views/qms/quality-analysis-report/material/search/search-config'
import {
  ChartForm,
  defaultChartForm,
} from '@/views/qms/quality-analysis-report/material/chart/components/query-form/config'
import { cloneDeep } from 'lodash'
import { CountryEnum, ZoneType } from '@/views/qms/quality-analysis-report/common/config'

type SetType<T> = Dispatch<SetStateAction<T>>

export const AnalysisMaterialContext = createContext<{
  /**
   * 当前用户是否有查看工单明细表的权限
   */
  authType: number
  setAuthType: SetType<number>
  /**
   * 当前区域
   */
  zoneType: ZoneType
  setZoneType: SetType<ZoneType>
  /**
   * 表单项
   */
  validateForm: IFormData
  setValidateForm: SetType<IFormData>
  /**
   * 图表表头表单项
   */
  chartForm: ChartForm
  setChartForm: SetType<ChartForm>
  /**
   * 基础信息&表单下拉列表
   */
  validateList: Record<string, AnyType>
  setValidateList: SetType<Record<string, AnyType>>
  /**
   * 算法模型中计算分母下拉列表
   */
  denominatorList: AlgorithmDenominator[]
  setDenominatorList: SetType<AlgorithmDenominator[]>
}>({
  validateForm: defaultFormData,
  setValidateForm: () => undefined,
  chartForm: defaultChartForm,
  setChartForm: () => undefined,
  validateList: {},
  setValidateList: () => undefined,
  denominatorList: [],
  setDenominatorList: () => undefined,
  authType: 0,
  setAuthType: () => undefined,
  zoneType: CountryEnum.国内,
  setZoneType: () => undefined,
})

export const AnalysisMaterialProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authType, setAuthType] = useState<number>(0)
  const [validateForm, setValidateForm] = useState<IFormData>(cloneDeep(defaultFormData))
  const [chartForm, setChartForm] = useState<ChartForm>(cloneDeep(defaultChartForm))
  const [validateList, setValidateList] = useState<Record<string, AnyType>>({})
  const [denominatorList, setDenominatorList] = useState<AlgorithmDenominator[]>([])
  const [zoneType, setZoneType] = useState<ZoneType>(CountryEnum.国内)

  return (
    <AnalysisMaterialContext.Provider
      value={{
        authType,
        setAuthType,
        zoneType,
        setZoneType,
        validateForm,
        setValidateForm,
        chartForm,
        setChartForm,
        validateList,
        setValidateList,
        denominatorList,
        setDenominatorList,
      }}
    >
      {children}
    </AnalysisMaterialContext.Provider>
  )
}
