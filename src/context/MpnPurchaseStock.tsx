import React, { createContext } from 'react'
import { NOOP_FUNC } from '@/utils/noop'
import { useSafeState } from 'ahooks'
import { FilterType, initialValues } from '@/views/gsc/pages/mpn-purchase-stock/config'

export type SetType<T> = React.Dispatch<React.SetStateAction<T>>

export const MpnPurchaseStockContext = createContext<{
  filter: FilterType
  setFilter: SetType<FilterType>
}>({
  filter: {},
  setFilter: NOOP_FUNC,
})

export const MpnPurchaseStockProvider = ({ children }) => {
  const [filter, setFilter] = useSafeState<FilterType>(initialValues)

  return (
    <MpnPurchaseStockContext.Provider
      value={{
        filter,
        setFilter,
      }}
    >
      {children}
    </MpnPurchaseStockContext.Provider>
  )
}
