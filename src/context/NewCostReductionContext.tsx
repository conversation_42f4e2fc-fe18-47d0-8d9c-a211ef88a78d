import React, { FC, createContext, useState, useEffect, useCallback, ReactText } from 'react'
import { NOOP_OBJ, NOOP_FUNC } from '@/utils/noop'
import { TableColumnItem } from '@hi-ui/table'
import { SelectMergedItem } from '@hi-ui/select'
import { AnyType } from '@/constants'
import {
  initialConfigCols,
  initialDimensionsAllCols,
  initialDimensions,
  initialHiddenCols,
  initialTypeList,
  DEFAULT_YEAR,
} from '@/views/gsc/cost/new-cost-reduction-v2/constants'
import { PROJECT_COLUMN_WIDTH } from '@/views/gsc/cost/project-cost/constants'

export const columnWidthKeys = Object.keys(PROJECT_COLUMN_WIDTH)

export interface CostPriceType {
  startDate?: number
  endDate?: number
  sourcingOprNameCns?: string[]
  projectNames?: string[]
  purchasePatternDescs?: string[]
  depts?: string[]
  deptOwners?: string[]
  teamOwnerEn?: string[]
  subjects?: string[]
  subProjectNames?: string[]
}

export type SetType<T> = React.Dispatch<React.SetStateAction<T>>
export type ColumnType = { title: string; id: string }[]
export const NewCostReductionContext = createContext<{
  userType: string
  searchCondition: Record<string, number | string | string[]>
  searchDescription: string
  detailSearchCondition: Record<string, boolean | number | string | string[]>
  tabId: number
  dimensions: string[]
  hiddenCols: string[]
  configCols: ColumnType
  typeList: string[]
  showCols: string[]
  dimensionConfig: string[]
  dimensionsAllCols: ColumnType
  costFilter: CostPriceType
  reductionTableColumn: TableColumnItem[]
  warehouseTableColumns: TableColumnItem[]
  conditionList: Record<string, SelectMergedItem[]>
  conditionDetailList: Record<string, SelectMergedItem[]>
  findFields: string[]
  first: boolean
  expanded: boolean
  expandedIndicate: boolean
  createDayAsc: boolean
  updateTime: string
  indicatePageState: { pageNum: number; pageSize: number }
  isFCST: boolean
  data: Record<string, string | number | Record<string, string | number>[]>[]
  isLoading: boolean
  newDimensions: AnyType[]
  newIndicators: AnyType[]
  tableList: AnyType[]
  detailSetFilter: AnyType
  clickInfo: Record<string, string> | null
  exchangeRateType: ReactText
  setIsFCST: SetType<boolean>
  setUserType: SetType<string>
  setSearchCondition: SetType<Record<string, boolean | number | string | string[]>>
  setSearchDescription: SetType<string>
  setDetailSearchCondition: SetType<Record<string, number | string | boolean | string[]>>
  setTabId: SetType<number>
  setDimensions: SetType<string[]>
  setHiddenCols: SetType<string[]>
  setConfigCols: SetType<ColumnType>
  setTypeList: SetType<string[]>
  setShowCols: SetType<string[]>
  setDimensionConfig: SetType<string[]>
  setDimensionsAllCols: SetType<ColumnType>
  setCostFilter: SetType<CostPriceType>
  setReductionTableColumn: SetType<TableColumnItem[]>
  setWarehouseTableColumns: SetType<TableColumnItem[]>
  setConditionList: SetType<Record<string, SelectMergedItem[]>>
  setDetailConditionList: SetType<Record<string, SelectMergedItem[]>>
  setFindFields: SetType<string[]>
  setFirst: SetType<boolean>
  setExpanded: SetType<boolean>
  setExpandedIndicate: SetType<boolean>
  setCreateDayAsc: SetType<boolean>
  setUpdateTime: SetType<string>
  setIndicatePageState: SetType<{ pageNum: number; pageSize: number }>
  setState: SetType<{
    isLoading: boolean
    data: Record<string, string | number | Record<string, string | number>[]>[]
  }>
  setNewDimensions: SetType<AnyType[]>
  setNewIndicators: SetType<AnyType[]>
  setTableList: SetType<AnyType[]>
  setDetailSetFilter: SetType<AnyType>
  assignFetchedResult: AnyType
  setClickInfo: SetType<Record<string, string> | null>
  setExchangeRateType: SetType<ReactText>
}>({
  userType: '',
  searchCondition: NOOP_OBJ,
  searchDescription: '',
  detailSearchCondition: NOOP_OBJ,
  tabId: 1,
  dimensions: [],

  dimensionsAllCols: [],
  hiddenCols: [],
  configCols: [],
  typeList: [],
  showCols: [],
  dimensionConfig: [],
  costFilter: NOOP_OBJ,
  reductionTableColumn: [],
  warehouseTableColumns: [],
  conditionList: NOOP_OBJ,
  conditionDetailList: NOOP_OBJ,
  findFields: [],
  first: true,
  expanded: true,
  expandedIndicate: true,
  createDayAsc: true,
  updateTime: '',
  indicatePageState: { pageNum: 1, pageSize: 30 },
  isFCST: false,
  data: [],
  isLoading: true,
  newDimensions: [],
  newIndicators: [],
  tableList: [],
  detailSetFilter: {},
  clickInfo: null,
  exchangeRateType: 'USD',
  setIsFCST: NOOP_FUNC,
  setUserType: NOOP_FUNC,
  setSearchCondition: NOOP_FUNC,
  setSearchDescription: NOOP_FUNC,
  setDetailSearchCondition: NOOP_FUNC,
  setTabId: NOOP_FUNC,
  setDimensions: NOOP_FUNC,
  setDimensionsAllCols: NOOP_FUNC,
  setHiddenCols: NOOP_FUNC,
  setConfigCols: NOOP_FUNC,
  setTypeList: NOOP_FUNC,
  setShowCols: NOOP_FUNC,
  setDimensionConfig: NOOP_FUNC,
  setReductionTableColumn: NOOP_FUNC,
  setWarehouseTableColumns: NOOP_FUNC,
  setCostFilter: NOOP_FUNC,
  setConditionList: NOOP_FUNC,
  setDetailConditionList: NOOP_FUNC,
  setFindFields: NOOP_FUNC,
  setFirst: NOOP_FUNC,
  setExpanded: NOOP_FUNC,
  setExpandedIndicate: NOOP_FUNC,
  setCreateDayAsc: NOOP_FUNC,
  setUpdateTime: NOOP_FUNC,
  setIndicatePageState: NOOP_FUNC,
  setState: NOOP_FUNC,
  setNewDimensions: NOOP_FUNC,
  setNewIndicators: NOOP_FUNC,
  setTableList: NOOP_FUNC,
  assignFetchedResult: NOOP_OBJ,
  setDetailSetFilter: NOOP_FUNC,
  setClickInfo: NOOP_FUNC,
  setExchangeRateType: NOOP_FUNC,
})

export const NewCostReductionProvider: FC = ({ children }) => {
  const [userType, setUserType] = useState('')
  const [tabId, setTabId] = useState(1)
  const [dimensions, setDimensions] = useState<string[]>(initialDimensions)
  const [dimensionsAllCols, setDimensionsAllCols] =
    useState<{ title: string; id: string }[]>(initialDimensionsAllCols)
  const [hiddenCols, setHiddenCols] = useState<string[]>(initialHiddenCols)
  const [configCols, setConfigCols] = useState<{ title: string; id: string }[]>(initialConfigCols)
  const [typeList, setTypeList] = useState(initialTypeList)
  const [showCols, setShowCols] = useState<string[]>(initialTypeList)
  const [dimensionConfig, setDimensionConfig] = useState<string[]>(initialTypeList)
  const [searchCondition, setSearchCondition] = useState({})
  const [isFCST, setIsFCST] = useState(false)
  const [searchDescription, setSearchDescription] = useState('')
  const [detailSearchCondition, setDetailSearchCondition] = useState({})
  const [costFilter, setCostFilter] = useState<CostPriceType>({})
  const [reductionTableColumn, setReductionTableColumn] = useState<TableColumnItem[]>([])
  const [warehouseTableColumns, setWarehouseTableColumns] = useState<TableColumnItem[]>([])
  const [first, setFirst] = useState(false)
  const [expanded, setExpanded] = useState<boolean>(false)
  const [expandedIndicate, setExpandedIndicate] = useState<boolean>(false)
  const [createDayAsc, setCreateDayAsc] = useState<boolean>(true)
  const [updateTime, setUpdateTime] = useState<string>('')
  const [findFields, setFindFields] = useState<string[]>([
    'dept',
    'deptOwner',
    'teamOwner',
    'matCat',
  ])
  const [conditionList, setConditionList] = useState<Record<string, SelectMergedItem[]>>({
    bizNames: [],
    calcYears: [{ id: DEFAULT_YEAR, title: DEFAULT_YEAR }],
    businessDate: [],
    depts: [],
    teamOwnerEn: [],
    matOprNameCns: [],
    matCats: [],
    cvpnCodes: [],
    mpnIds: [],
    pnCodes: [],
    matCatLvl1Codes: [],
    matCatLvl2Codes: [],
    matCatLvl3Codes: [],
    dates: [],
  })
  const [conditionDetailList, setDetailConditionList] = useState<
    Record<string, SelectMergedItem[]>
  >({
    bizNames: [],
    calcYears: [{ id: DEFAULT_YEAR, title: DEFAULT_YEAR }],
    businessDate: [],
    depts: [],
    teamOwnerEn: [],
    matOprNameCns: [],
    matCats: [],
    cvpnCodes: [],
    mpnIds: [],
    pnCodes: [],
    matCatLvl1Codes: [],
    matCatLvl2Codes: [],
    matCatLvl3Codes: [],
    dates: [],
  })
  const [indicatePageState, setIndicatePageState] = useState<{ pageNum: number; pageSize: number }>(
    {
      pageNum: 1,
      pageSize: 30,
    }
  )
  const [{ data, isLoading }, setState] = useState({ isLoading: true, data: [] as AnyType[] })
  const [newDimensions, setNewDimensions] = useState<AnyType[]>([])
  const [newIndicators, setNewIndicators] = useState<AnyType[]>([])
  const [tableList, setTableList] = useState<AnyType[]>([])
  const [detailSetFilter, setDetailSetFilter] = useState({})
  const [clickInfo, setClickInfo] = useState<Record<string, string> | null>(null)
  const [exchangeRateType, setExchangeRateType] = useState<ReactText>('USD')

  useEffect(() => {
    const descriptionParts: string[] = []
    const { subject, source, teamOwner, matOprNameCn } = searchCondition || ({} as AnyType)
    descriptionParts.push(
      `${source === '不限制' || source === '全年预测' ? '全年预测' : '实际'}${
        !subject ? '' : `：${subject}`
      }`
    )
    if (teamOwner) {
      const userTypeName =
        userType === '高级权限人员' || userType === '部门负责人'
          ? '各组'
          : userType === '组员'
            ? '团队'
            : '资源'
      descriptionParts.push(`${userTypeName}负责人: ${teamOwner}`)
    }
    if (matOprNameCn) {
      descriptionParts.push(`资源: ${matOprNameCn}`)
    }
    const formattedDescription = `(${descriptionParts.join('；')}`
    setSearchDescription(formattedDescription + '）')
  }, [searchCondition, userType])

  const getColumnsByType = useCallback(
    (allColumns, types) =>
      allColumns
        ?.filter((item) => types.includes(item?.type))
        ?.map((column) => {
          const { key, value } = column
          return { code: key, name: value, width: 130 }
        }) || [],
    []
  )

  const assignFetchedResult = useCallback(
    (result) => {
      const { columnList = [], rowPage } = result?.data || {}
      const { data = [] } = rowPage || {}
      const calcIndicators = getColumnsByType(columnList, ['6']).map((indicator, idx) => {
        return {
          ...indicator,
          width: 210,
          hidden: false,
          align: 'right' as const,
          expression: `SUM(a${idx + 1})`,
        }
      })
      const calcDimensions = getColumnsByType(columnList, ['1', '3'])
      setNewIndicators(calcIndicators)
      setNewDimensions(calcDimensions)
      setTableList(data)
    },
    [getColumnsByType, setNewIndicators, setTableList]
  )

  return (
    <NewCostReductionContext.Provider
      value={{
        userType,
        searchCondition,
        conditionDetailList,
        searchDescription,
        detailSearchCondition,
        tabId,
        dimensions,
        hiddenCols,
        configCols,
        typeList,
        showCols,
        dimensionConfig,
        dimensionsAllCols,
        reductionTableColumn,
        warehouseTableColumns,
        costFilter,
        conditionList,
        findFields,
        first,
        expanded,
        expandedIndicate,
        createDayAsc,
        updateTime,
        indicatePageState,
        isFCST,
        newDimensions,
        newIndicators,
        tableList,
        detailSetFilter,
        clickInfo,
        exchangeRateType,
        setIsFCST,
        setUserType,
        setSearchCondition,
        setSearchDescription,
        setDetailSearchCondition,
        setDetailConditionList,
        setTabId,
        setDimensions,
        setHiddenCols,
        setConfigCols,
        setTypeList,
        setShowCols,
        setDimensionConfig,
        setDimensionsAllCols,
        setReductionTableColumn,
        setWarehouseTableColumns,
        setCostFilter,
        setConditionList,
        setFindFields,
        setFirst,
        setExpanded,
        setExpandedIndicate,
        setCreateDayAsc,
        setUpdateTime,
        setIndicatePageState,
        data,
        isLoading,
        setState,
        setNewDimensions,
        setNewIndicators,
        setTableList,
        assignFetchedResult,
        setDetailSetFilter,
        setClickInfo,
        setExchangeRateType,
      }}
    >
      {children}
    </NewCostReductionContext.Provider>
  )
}
