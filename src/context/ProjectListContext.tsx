import React, { createContext, FC, useState } from 'react'
import { SetType } from './CostReductionContext'
import { NOOP_FUNC } from '@/utils/noop'
interface FilterType {
  bizNames?: string
  projectNames?: string[]
  milestones?: string[]
  states?: string[]
  saleSites?: string[]
  configs?: string[]
  colors?: string[]
  milestoneDates?: string[]
  exchangeRateType?: string
  startDate?: string
  endDate?: string
  exchangeRateTypes?: string
}

export const ProjectListContext = createContext<{
  costFilter: FilterType
  setCostFilter: SetType<FilterType>
  expanded: boolean
  setExpanded: SetType<boolean>
}>({
  costFilter: {},
  setCostFilter: NOOP_FUNC,
  expanded: false,
  setExpanded: NOOP_FUNC,
})
export const ProjectListProvider: FC = ({ children }) => {
  const [costFilter, setCostFilter] = useState<FilterType>({})
  const [expanded, setExpanded] = useState<boolean>(true)

  return (
    <ProjectListContext.Provider
      value={{
        costFilter,
        setCostFilter,
        expanded,
        setExpanded,
      }}
    >
      {children}
    </ProjectListContext.Provider>
  )
}
