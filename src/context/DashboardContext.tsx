/* eslint-disable @typescript-eslint/no-empty-function */
import { getUserBIList } from '@/api/gsc/bi'
import { EmptyFunctionType } from '@/types/type'
import { useMount, useSafeState } from 'ahooks'
import React, { FC, useCallback, useContext } from 'react'

type getUserBIListResType = Pick<Awaited<ReturnType<typeof getUserBIList>>, 'data'>
export type DashboardType = getUserBIListResType[keyof getUserBIListResType][number]['kanbanUrls']

export const DashboardContext = React.createContext<{
  dashboard: Record<string, DashboardType>
  initBIList: EmptyFunctionType
}>({
  dashboard: {},
  initBIList: () => {},
})

export const useDashboardCtx = () => {
  return useContext(DashboardContext)
}
export const DashboardProvider: FC = ({ children }) => {
  const [dashboard, setDashboard] = useSafeState<Record<string, DashboardType>>({})

  const initBIList = useCallback(() => {
    getUserBIList().then((res) => {
      const data = res.data
      const map = data.reduce((a, b) => {
        a[b.pageId] = b.kanbanUrls
        b.mainPageIds.forEach((mainPageId) => {
          a[mainPageId] = b.kanbanUrls
        })
        return a
      }, {})
      setDashboard(map)
    })
  }, [setDashboard])

  useMount(() => initBIList())

  return (
    <DashboardContext.Provider value={{ dashboard, initBIList }}>
      {children}
    </DashboardContext.Provider>
  )
}
DashboardProvider.displayName = 'DashboardProvider'
