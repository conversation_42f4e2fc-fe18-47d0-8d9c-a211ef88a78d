import React, { createContext, Dispatch, MutableRefObject, SetStateAction, useRef } from 'react'
import { useSafeState } from 'ahooks'

type SetType<T> = Dispatch<SetStateAction<T>>

export const AllLifeCostContext = createContext<{
  filter: Record<string, string | string[]>
  setFilter: SetType<Record<string, string | string[]>>
  changeFilter: Record<string, string[]>
  setChangeFilter: SetType<Record<string, string[]>>
  maxDigit: number
  setMaxDigit: SetType<number>
  fetchChartSwitch: MutableRefObject<boolean> | undefined
  selectObj: Record<string, AnyType>
  setSelectObj: SetType<Record<string, AnyType>>
}>({
  filter: {},
  setFilter: () => undefined,
  changeFilter: { chooseDimensions: [] },
  setChangeFilter: () => undefined,
  maxDigit: 0,
  setMaxDigit: () => undefined,
  fetchChartSwitch: undefined,
  selectObj: {},
  setSelectObj: () => undefined,
})

export const AllLifeCostProvider = ({ children }) => {
  const [filter, setFilter] = useSafeState<Record<string, string | string[]>>({})
  const [changeFilter, setChangeFilter] = useSafeState<Record<string, string[]>>({
    chooseDimensions: [],
  })
  const [maxDigit, setMaxDigit] = useSafeState<number>(0)
  const fetchChartSwitch = useRef<boolean>(true)
  const [selectObj, setSelectObj] = useSafeState<Record<string, AnyType>>({})

  return (
    <AllLifeCostContext.Provider
      value={{
        filter,
        setFilter,
        changeFilter,
        setChangeFilter,
        maxDigit,
        setMaxDigit,
        fetchChartSwitch,
        selectObj,
        setSelectObj,
      }}
    >
      {children}
    </AllLifeCostContext.Provider>
  )
}
