import React, { memo, useState, useCallback } from 'react'
import CheckSelect from '@hi-ui/check-select'
import { useRequest } from 'ahooks'
import { getNewProjectListCondition } from '@/api/gsc/project-list'
import { valuesToSearch } from '@/views/gsc/cost/project-list/config'
import Select from '@hi-ui/select'

export const OpenCheckSelect = memo<{
  selectKey: string
  filter: Record<string, string[] | string>
  api?: AnyType
  formatter?: AnyType
  defaultData?: { value: string; name: string }[]
  [key: string]: AnyType
}>((props) => {
  const { selectKey, filter, formatter, api, defaultData, ...rest } = props
  const [selectData, setSelectData] = useState<{ value: string; name: string }[]>([])

  const format = formatter || valuesToSearch

  const { runAsync: getCondition, loading } = useRequest(api || getNewProjectListCondition, {
    manual: true,
  })

  const onOpen = useCallback(() => {
    getCondition(
      format({
        ...filter,
        bizNames: filter.bizNames ? [filter.bizNames] : [],
        exchangeRateTypes: filter.exchangeRateTypes ? [filter.exchangeRateTypes] : [],
        [selectKey]: [],
        searchName: selectKey,
      })
    )
      .then((res: AnyType) => setSelectData(res?.data))
      .catch(() => {
        setSelectData([] as AnyType[])
      })
  }, [getCondition, format, filter, selectKey])

  return selectKey !== 'bizNames' && selectKey !== 'exchangeRateTypes' ? (
    <CheckSelect
      loading={loading}
      data={selectData as AnyType[]}
      onOpen={onOpen}
      fieldNames={{ id: 'value', title: 'name' }}
      {...rest}
    />
  ) : (
    <Select
      loading={loading}
      data={selectData.length ? (selectData as AnyType[]) : defaultData || []}
      onOpen={onOpen}
      fieldNames={{ id: 'value', title: 'name' }}
      {...rest}
    />
  )
})
OpenCheckSelect.displayName = 'OpenCheckSelect'
