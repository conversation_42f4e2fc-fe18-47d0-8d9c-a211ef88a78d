import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { OpenCheckSelect } from '../index'
import '@testing-library/jest-dom'

// mock 组件自动调用 onOpen
vi.mock('@hi-ui/check-select', () => ({
  __esModule: true,
  default: function MockCheckSelect(props) {
    React.useEffect(() => {
      if (typeof props.onOpen === 'function') {
        try {
          props.onOpen()
        } catch (e) {}
      }
    }, [props])
    return <div data-testid="check-select">mocked check-select</div>
  },
}))
vi.mock('@hi-ui/select', () => ({
  __esModule: true,
  default: function MockSelect(props) {
    React.useEffect(() => {
      if (typeof props.onOpen === 'function') {
        try {
          props.onOpen()
        } catch (e) {}
      }
    }, [props])
    return <div data-testid="select">mocked select</div>
  },
}))

// mock api
const mockApi = vi.fn()
const mockFormatter = vi.fn()
const defaultData = [
  { value: '1', name: 'A' },
  { value: '2', name: 'B' },
]

// mock ahooks useRequest，runAsync 实际调用 mockApi
vi.mock('ahooks', () => ({
  useRequest: (api) => ({
    runAsync: (...args) => api(...args),
    loading: false,
  }),
}))

describe('OpenCheckSelect', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockApi.mockReset && mockApi.mockReset()
  })

  it('selectKey 非 bizNames/exchangeRateTypes 时渲染 CheckSelect 并触发 onOpen 成功', async () => {
    mockApi.mockResolvedValueOnce({ data: defaultData })
    render(
      <OpenCheckSelect selectKey="other" filter={{}} api={mockApi} defaultData={defaultData} />
    )
    await waitFor(() => {
      expect(mockApi).toHaveBeenCalled()
      expect(screen.getByTestId('check-select')).toBeInTheDocument()
    })
  })

  it('selectKey 非 bizNames/exchangeRateTypes 时 onOpen 失败', async () => {
    mockApi.mockRejectedValueOnce(new Error('fail'))
    render(
      <OpenCheckSelect selectKey="other" filter={{}} api={mockApi} defaultData={defaultData} />
    )
    await waitFor(() => {
      expect(mockApi).toHaveBeenCalled()
      expect(screen.getByTestId('check-select')).toBeInTheDocument()
    })
  })

  it('selectKey 为 bizNames 时渲染 Select，优先用 defaultData', async () => {
    mockApi.mockResolvedValueOnce({ data: [] })
    render(
      <OpenCheckSelect selectKey="bizNames" filter={{}} api={mockApi} defaultData={defaultData} />
    )
    await waitFor(() => {
      expect(screen.getByTestId('select')).toBeInTheDocument()
    })
  })

  it('selectKey 为 exchangeRateTypes 时渲染 Select，接口有数据时优先用接口数据', async () => {
    mockApi.mockResolvedValueOnce({ data: [{ value: '3', name: 'C' }] })
    render(
      <OpenCheckSelect
        selectKey="exchangeRateTypes"
        filter={{}}
        api={mockApi}
        defaultData={defaultData}
      />
    )
    await waitFor(() => {
      expect(mockApi).toHaveBeenCalled()
      expect(screen.getByTestId('select')).toBeInTheDocument()
    })
  })

  it('支持自定义 formatter', async () => {
    mockApi.mockResolvedValueOnce({ data: defaultData })
    mockFormatter.mockImplementation((params: AnyType) => params)
    render(
      <OpenCheckSelect selectKey="other" filter={{}} api={mockApi} formatter={mockFormatter} />
    )
    await waitFor(() => {
      expect(mockFormatter).toHaveBeenCalled()
      expect(mockApi).toHaveBeenCalled()
    })
  })

  it('不传 api/formatter 时使用默认', async () => {
    render(<OpenCheckSelect selectKey="other" filter={{}} defaultData={defaultData} />)
    // 只需断言不报错即可
    await waitFor(() => {
      expect(screen.getByTestId('check-select')).toBeInTheDocument()
    })
  })
})
