.menu-settings {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 28px;
  height: 100%;

  .tree-item--disable {
    color: #b5bcc7;
  }

  .tree-item--enable {
    color: #1f2733;
  }

  .left {
    width: 100%;
    height: 100%;

    .left-up {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #f2f4f7;
      padding: 4px 8px 4px 16px;
      width: 100%;
      height: 40px;
      border: 1px solid #dfe2e8;
      border-radius: 4px 4px 0 0;
      box-sizing: border-box;

      .left-up-title {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #1f2733;
      }

      .left-up-num {
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: #929aa6;
      }
    }

    .left-down {
      width: 100%;
      height: calc(100% - 50px);
      padding-top: 12px;
      border-right: 1px solid #dfe2e8;
      border-bottom: 1px solid #dfe2e8;
      border-left: 1px solid #dfe2e8;

      .hi-v4-tree-searcher {
        padding: 0 6px;

        .hi-v4-input__inner {
          box-shadow: none;
          border: none;
        }

        .hi-v4-input__inner::after {
          content: '';
          position: absolute;
          height: 1px;
          width: 100%;
          left: 2px;
          bottom: 0;
          background-color: #dfe2e8;
        }
      }

      .hi-v4-tree {
        padding: 0 6px;
        height: calc(100% - 50px);
        overflow: overlay;

        .hi-v4-tree-node--disabled:hover {
          cursor: auto;
        }

        .hi-v4-tree-node__wrap {
          .hi-v4-tree-node__title {
            padding: 0 2px 0 6px;
            cursor: auto;

            .hi-v4-tree-node__title-text {
              color: #1f2733;
            }
          }
        }

        .hi-v4-tree-node--selected {
          .hi-v4-tree-node__wrap {
            background-color: #fff !important;

            &:hover {
              background-color: #fff;
            }
          }

          &:hover {
            background-color: #fff;
          }
        }

        .hi-v4-tree-node--disabled:not(.hi-v4-tree-node--checkbox-checked)
          > .hi-v4-tree-node__wrap {
          label > .hi-v4-checkbox__icon {
            display: none;
          }

          .hi-v4-tree-node__title {
            padding: 0 2px 0 0;
            cursor: auto;

            .hi-v4-tree-node__title-text {
              color: #1f2733;
            }
          }
        }
      }
    }
  }

  .middle {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 12px;

    .down {
      margin-top: 24px;
    }
  }

  .right {
    width: 100%;
    height: 100%;

    .right-up {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #f2f4f7;
      padding: 4px 8px 4px 16px;
      width: 100%;
      height: 40px;
      border: 1px solid #dfe2e8;
      border-radius: 4px 4px 0 0;
      box-sizing: border-box;

      .right-up-title {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #1f2733;
      }

      .right-up-num {
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: #929aa6;
      }
    }

    .right-down {
      width: 100%;
      height: calc(100% - 50px);
      padding-top: 12px;
      border-right: 1px solid #dfe2e8;
      border-bottom: 1px solid #dfe2e8;
      border-left: 1px solid #dfe2e8;
      overflow: hidden overlay;

      .hi-v4-input {
        width: 100%;
        padding: 0 6px;

        .hi-v4-input__outer {
          width: 100%;

          .hi-v4-input__inner {
            border: none;
            box-shadow: none;
          }

          .hi-v4-input__inner::after {
            content: '';
            position: absolute;
            height: 1px;
            width: 100%;
            left: 2px;
            bottom: 0;
            background-color: #dfe2e8;
          }
        }
      }

      .right-select {
        margin-top: 6px;
        padding: 0 6px;
        height: calc(100% - 50px);
        overflow: overlay;

        .empty-message {
          color: #929aa6;
          display: inline-block;
          margin: 6px 0 0 4px;
        }

        .right-item {
          width: 242px;
          height: 32px;
          border-radius: 4px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin: 6px 0;
          padding: 0 12px;

          .right-item-span {
            display: inline-block;
            padding-right: 8px;
          }
        }

        .right-item:hover {
          background-color: #f2f4f7;
          cursor: auto;
        }
      }
    }
  }
}
