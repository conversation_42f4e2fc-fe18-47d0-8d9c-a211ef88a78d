import React, { memo, ReactText, useState, useEffect, useCallback, useRef } from 'react'
import { RightOutlined, LeftOutlined, SearchOutlined } from '@hi-ui/icons'
import Tree, { useTreeSearch } from '@hi-ui/tree'
import { Checkbox } from '@hi-ui/checkbox'
import Button from '@hi-ui/button'
import Input from '@hi-ui/input'
import { countLeafNodes, findAllLeafNodes, getAllIds, deepCopy } from '@/utils/tree'
import './index.scss'

const TreeTransfer = memo<{
  treeData: AnyType
  rightItems: AnyType
  setRightItems: AnyType
  initSelectedItems
}>(({ treeData, rightItems, setRightItems, initSelectedItems }) => {
  const SearchTree = useTreeSearch(Tree)
  const [checkedNodeData, setCheckedNodeData] = useState<AnyType[]>([])
  const [configAllData, setConfigAllData] = useState<AnyType[]>([])
  const [checkedIds, setCheckedIds] = useState<ReactText[]>([])
  const [rightCheckedIds, setRightCheckedIds] = useState<ReactText[]>([])
  const [rightInputValue, setRightInputValue] = useState<string>('')
  const [expandedIds, setExpandedIds] = useState<string[]>([])
  // 缓存右侧区域可操作数据
  const cacheItemsRef = useRef(deepCopy(initSelectedItems))

  useEffect(() => {
    setConfigAllData(treeData)
    setExpandedIds(getAllIds(treeData, 'code', 1))
    setCheckedIds(initSelectedItems.map((item) => item.code))
  }, [initSelectedItems, setRightItems, treeData])

  useEffect(() => {
    cacheItemsRef.current = deepCopy(initSelectedItems)
  }, [initSelectedItems])

  const handleRemove = useCallback(() => {
    setRightItems(() => {
      const res = cacheItemsRef.current?.filter((item) => !rightCheckedIds.includes(item.code))

      cacheItemsRef.current = res

      return res
    })
    setRightCheckedIds([])
    setCheckedIds((prev) => prev.filter((id) => !rightCheckedIds.includes(id)))
    setRightInputValue('')
  }, [rightCheckedIds, setRightItems])

  const handleOnCheck = useCallback(
    (selectedIds, selectedObj) => {
      setCheckedIds(selectedIds)
      setCheckedNodeData(selectedObj.checkedNodes || [])
    },
    [setCheckedIds]
  )

  const handleClickToRight = useCallback(() => {
    const addedData = findAllLeafNodes(checkedNodeData).filter(
      (i) => !rightItems.map((item) => item.code).includes(i.code) && checkedIds.includes(i.code)
    )
    setRightItems((prev) => {
      const res = [...prev, ...addedData]

      cacheItemsRef.current = res

      return res
    })
  }, [checkedIds, checkedNodeData, rightItems, setRightItems])

  const handleRightSearch = useCallback(
    (e) => {
      const filterKey = e.target.value
      setRightInputValue(filterKey)
      setRightItems(
        filterKey
          ? cacheItemsRef.current.filter((item) => item.value.includes(filterKey))
          : cacheItemsRef.current
      )
    },
    [setRightItems]
  )

  return (
    <div className="menu-settings">
      <div className="left">
        <div className="left-up">
          <span className="left-up-title">全部菜单</span>
          <span className="left-up-num">{countLeafNodes(treeData) || 0}</span>
        </div>
        <div className="left-down">
          <SearchTree
            searchPlaceholder="搜索"
            checkable
            searchable
            data={configAllData || []}
            checkedIds={checkedIds}
            onCheck={handleOnCheck}
            fieldNames={{ id: 'code', title: 'value', children: 'children', disabled: 'disabled' }}
            expandedIds={expandedIds}
            onExpand={(...args) => {
              setExpandedIds(args[0] as string[])
            }}
            render={(item) => {
              return (
                <span
                  className={
                    item.disabled && !item?.children?.length
                      ? 'tree-item--disable'
                      : 'tree-item--enable'
                  }
                >
                  {item.title}
                </span>
              )
            }}
          ></SearchTree>
        </div>
      </div>
      <div className="middle">
        <div className="up">
          <Button
            type="default"
            shape="round"
            icon={<RightOutlined />}
            disabled={!checkedIds.length}
            onClick={handleClickToRight}
          />
        </div>
        <div className="down">
          <Button
            type="default"
            shape="round"
            icon={<LeftOutlined />}
            disabled={!rightCheckedIds.length}
            onClick={handleRemove}
          />
        </div>
      </div>
      <div className="right">
        <div className="right-up">
          <span className="right-up-title">已选</span>
          <span className="right-up-num">{`${cacheItemsRef.current.length || 0} / ${
            countLeafNodes(treeData) || 0
          }`}</span>
        </div>
        <div className="right-down">
          <Input
            placeholder="搜索"
            prefix={<SearchOutlined />}
            clearable
            value={rightInputValue}
            onChange={handleRightSearch}
          />
          <div className="right-select">
            {rightItems?.length ? (
              rightItems?.map((item, index) => {
                return (
                  <div className="right-item" key={`${item.code}_${index}`}>
                    <Checkbox
                      disabled={item.disabled}
                      checked={rightCheckedIds.includes(item.code)}
                      onChange={(e) => {
                        const { checked: newestValue = false } = e.target
                        if (newestValue) {
                          setRightCheckedIds((prev) => [...prev, item.code])
                        } else {
                          setRightCheckedIds((prev) =>
                            prev.filter((currentId) => currentId !== item.code)
                          )
                        }
                      }}
                    >
                      <span className="right-item-span">{item.value}</span>
                    </Checkbox>
                  </div>
                )
              })
            ) : rightInputValue ? (
              <span className="empty-message">未找到搜索结果</span>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  )
})
TreeTransfer.displayName = 'TreeTransfer'
export default TreeTransfer
