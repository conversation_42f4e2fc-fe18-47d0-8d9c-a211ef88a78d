import './index.scss'

import React from 'react'

import EllipsisTooltip from '@hi-ui/ellipsis-tooltip'

export const ellipses = (key: string, required = false, maxWidth = 0) => {
  const text = key || ''
  const tooltipProps = maxWidth ? { tooltipProps: { style: { maxWidth } } } : {}
  return required ? (
    <EllipsisTooltip className="form-text__required">{text}</EllipsisTooltip>
  ) : (
    // 一般 maxWidth 传入时，不需要 required 属性
    <EllipsisTooltip {...tooltipProps}>{text}</EllipsisTooltip>
  )
}
