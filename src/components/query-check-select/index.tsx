import React, { CSSProperties, memo } from 'react'
import { useDebounceFn } from 'ahooks'
import { formatInputString } from '@/utils/input-format'
import dayjs from 'dayjs'
import CheckSelect from '@hi-ui/check-select'
import { BaseResponse } from '@/utils/request'
import message from '@hi-ui/message'
import intl from 'react-intl-universal'
interface ParamsPropsType {
  startDate?: number
  endDate?: number
  type?: string
  prefix?: string
}
export type CommonType = Record<string, boolean | CSSProperties | undefined>
type FnType = (params: ParamsPropsType) => Promise<BaseResponse<Record<string, string | number>[]>>

export const QueryCheckSelect = memo<{
  fun: FnType
  type?: string
  formDate?: string | string[] | null
  commonSettings?: CommonType
  defaultData?: { id: string; title: string }[]
  // 无法准确知道所有 extra props 类型，暂用单行清零
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [k: string]: any
}>(({ type, formDate, commonSettings, defaultData = [], fun, ...props }) => {
  const { run: debouncedFetch } = useDebounceFn(
    (input, resolve) => {
      const finalQueryString = input ? formatInputString(input) : ''
      const startDate = dayjs(formDate?.[0])
        ?.startOf('day')
        ?.valueOf()
      const endDate = dayjs(formDate?.[1])
        ?.endOf('day')
        ?.valueOf()
      return fun({
        startDate,
        endDate,
        type,
        prefix: finalQueryString,
      })
        .then((res) => {
          const data = res?.data?.map((item) => {
            return { id: item?.value, title: item?.name }
          })
          return resolve(data)
        })
        .catch((err) => {
          message.open({
            title: err?.message || intl.get('搜索条件失败，请联系管理员'),
            type: 'error',
          })
        })
    },
    { wait: 300 }
  )
  return (
    <CheckSelect
      data={defaultData || []}
      height={260}
      dataSource={(keyword) => {
        return new Promise((resolve) => {
          return debouncedFetch(keyword, resolve)
        })
      }}
      {...commonSettings}
      {...props}
    />
  )
})
QueryCheckSelect.displayName = 'QueryCheckSelect'
