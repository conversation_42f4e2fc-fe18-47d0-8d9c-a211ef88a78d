import React from 'react'
import styled from 'styled-components'
import { makeRecursiveMapper, TablePipeline } from 'ali-react-table'
import { FilterFilled } from '@hi-ui/icons'
import Popover from '@hi-ui/popover'
import { gscTrack } from '@/utils/gscTrack-help'

const HeaderCellWithTips = styled.div`
  display: flex;
  align-items: center;

  .tip-icon-wrapper {
    margin-left: 2px;
  }

  .tip-icon {
    display: flex;
    fill: currentColor;
  }
`

const safeRenderHeader = (column: AnyType) => {
  return column.title || column.name || '-'
}

export const filter = (opts: AnyType = {}) => {
  return (pipeline: TablePipeline) => {
    const { genContent, ref, highlightColumnKeys } = opts
    return pipeline.mapColumns(
      makeRecursiveMapper((col) => {
        if (!col.features?.filter) {
          return col
        }
        const justifyContent =
          col.align === 'right' ? 'flex-end' : col.align === 'center' ? 'center' : 'flex-start'

        return {
          ...col,
          title: (
            <HeaderCellWithTips style={{ justifyContent }}>
              {safeRenderHeader(col)}
              <Popover
                placement="bottom-start"
                innerRef={ref}
                title={null}
                trigger="click"
                style={{ width: 200 }}
                arrow={false}
                content={genContent?.({ dataKey: col.code, ref })}
              >
                <FilterFilled
                  color={highlightColumnKeys?.includes(col.code) ? '#1890ff' : '#bfbfbf'}
                  size={15}
                  className="cursor-pointer ml-2"
                  onClick={() => {
                    gscTrack.pageElemClick('筛选器-首页筛选具体待办', 'filter', '01')
                  }}
                />
              </Popover>
            </HeaderCellWithTips>
          ),
        }
      })
    )
  }
}
