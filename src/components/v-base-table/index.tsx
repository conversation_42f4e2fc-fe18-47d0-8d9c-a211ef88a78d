import React from 'react'
import { BaseTable, BaseTableProps, Classes } from 'ali-react-table'
import styled from 'styled-components'
import cx from 'classnames'
import EmptyState, { EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL } from '@hi-ui/empty-state'
import intl from 'react-intl-universal'

export const StyledBaseTable = styled(BaseTable)`
  --font-size: 14px;
  --font-weight: 400;
  --row-height: 41px;
  --header-row-height: 41px;

  --lock-shadow: rgba(0, 0, 0, 0.2) 0 0 10px 0px;
  --border-color: #ebedf0;
  --color: rgba(0, 0, 0, 0.85);
  --bgcolor: white;
  --hover-bgcolor: #f2f4f7;
  --header-color: #1f2733;
  --header-bgcolor: #f5f7fa;

  td {
    transition: background 0.3s;
  }

  th {
    font-weight: 500;
  }

  .${Classes.lockShadowMask} {
    .${Classes.lockShadow} {
      transition: box-shadow 0.3s;
    }
  }

  &:not(.bordered) {
    --cell-border-vertical: none;
    --header-cell-border-vertical: none;

    thead > tr.first th {
      border-top: none;
    }
  }
` as unknown as typeof BaseTable

// 将 styled 组件创建移到组件外部，避免每次渲染都创建新组件
const FirstLineBoldStyledBaseTable = styled(StyledBaseTable)`
  .art-table-row.first.even {
    font-weight: 500;
  }
`

export const VBaseTable = React.forwardRef<BaseTable, BaseTableProps & { firstLineBold?: boolean }>(
  (props, ref) => {
    if (props?.firstLineBold !== false) {
      return <FirstLineBoldStyledBaseTable ref={ref} className={cx(props.className)} {...props} />
    }
    return (
      <StyledBaseTable
        ref={ref}
        className={cx(props.className)}
        components={{
          EmptyContent: () => (
            <EmptyState
              size="md"
              className="flex justify-center items-center"
              style={{ height: 458 }}
              title={intl.get('暂无数据')}
              indicator={EMPTY_STATE_IMAGE_NO_DATA_COLOURFUL}
            />
          ),
        }}
        {...props}
      />
    )
  }
)

VBaseTable.displayName = 'VBaseTable'
