import React, { memo, ReactNode } from 'react'
import Loading from '@hi-ui/loading'

import './track-card.scss'

type TransferType = (num: number) => string

const TrackCard = memo<{
  title: string
  count: number
  transfer: TransferType
  component?: ReactNode
  bgColor?: string
  loading?: boolean
  style?
}>(({ title, count, transfer, component = null, bgColor = '', loading = false, style }) => {
  const bgColorStyle = bgColor ? { backgroundColor: bgColor } : {}
  return (
    <div className="track-card" style={style}>
      <div>
        <div className="track-card__title">{title}</div>
        <Loading content={null} visible={loading}>
          <div className="track-card__count">{transfer(count)}</div>
        </Loading>
      </div>
      {component ? (
        <div className="track-card__icon" style={bgColorStyle}>
          {component}
        </div>
      ) : null}
    </div>
  )
})

TrackCard.displayName = 'TrackCard'

export default TrackCard
