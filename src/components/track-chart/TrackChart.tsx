import intl from 'react-intl-universal'
import React, { memo, useEffect, useRef, useState } from 'react'
import { DualAxes } from '@antv/g2plot'
import { useFuncTrack } from '@/hooks/useFuncTrack'

const colors30Arr = [
  '#4A9EFF',
  '#48D4CF',
  '#FEC833',
  '#38C3FE',
  '#38D677',
  '#6372FF',
  '#FE9560',
  // '#FF7A75', // 其他
  '#BE8CF1',
  '#7298D0',
  '#4A9EFFA6', // 0.65 transparency
  '#48D4CFA6',
  '#FEC833A6',
  '#38C3FEA6',
  '#38D677A6',
  '#6372FFA6',
  '#FE9560A6',
  '#FF7A75A6',
  '#BE8CF1A6',
  '#7298D0A6',
  '#4A9EFF4D', // 0.3 transparency
  '#48D4CF4D',
  '#FEC8334D',
  '#38C3FE4D',
  '#38D6774D',
  '#6372FF4D',
  '#FE95604D',
  '#FF7A754D',
  '#BE8CF14D',
  '#7298D04D',
]
const OTHER_COLOR = '#FF7A75'
let colorIndex = 0
const colorMap = new Map()
const FunColorList = ['#68acff', '#fee45b', '#7c89ff', '#799dd3', '#4a9eff']
const funcRankToColorMap = {
  无人使用功能: FunColorList[0],
  高频功能: FunColorList[1],
  中频功能: FunColorList[2],
  低频功能: FunColorList[3],
  uv: FunColorList[4],
  pv: FunColorList[4],
}

type RefreshType = (dept: string, nonMenuTitles: string[], chartCode?: string) => void

const TrackChart = memo<{
  xField: string
  yField: string[]
  barData: Record<string, string | number>[]
  lineData?: Record<string, string | number>[]
  refresh?: RefreshType
  canColumnClick?: boolean
  funcFlag?: boolean
  chartCode?: string
  chartHeight?: string
}>(
  ({
    xField,
    yField,
    barData,
    lineData,
    refresh,
    canColumnClick = false,
    funcFlag = false,
    chartCode,
    chartHeight = '520px',
  }) => {
    const { clickTitle, setClickTitle } = useFuncTrack()
    const [titleChangeFlag, setTitleChangeFlag] = useState<boolean>(false)
    const trackChartContainer = useRef<HTMLDivElement>(null)
    const [dualAxes, setDualAxes] = useState<DualAxes | null>(null)
    const init = useRef<boolean>(true)
    const cardRowStyle = {
      padding: '12px 16px',
      borderRadius: '6px',
      minHeight: chartHeight,
    }
    const funcYAxisConfig = {
      [yField[1]]: {
        min: 0,
        label: {
          formatter: (v) => `${v}次`,
        },
      },
    }
    const deptTotalMap = barData?.reduce((acc, obj) => {
      acc[obj.dept] = obj.total
      return acc
    }, {})

    const clickTitleRef = useRef(clickTitle)

    useEffect(() => {
      clickTitleRef.current = clickTitle
    }, [clickTitle])

    useEffect(() => {
      init.current = true
    }, [barData, lineData])

    useEffect(() => {
      if (!trackChartContainer.current || !init.current) return
      const arr = [barData, lineData || []]
      const yAxisConfig = lineData?.length
        ? funcFlag
          ? funcYAxisConfig
          : {
              [yField[1]]: {
                min: 0,
                max: 1,
                label: {
                  formatter: (v) => `${v * 100}%`,
                },
              },
            }
        : {}
      const res = new DualAxes(trackChartContainer.current, {
        data: arr,
        animation: false,
        xField,
        yField,
        yAxis: yAxisConfig,
        legend: {
          position: 'top',
        },
        interactions: [
          {
            type: 'active-region',
          },
        ],
        tooltip: {
          formatter: (datum) => {
            const { dept, rateType, type, value } = datum
            const total = deptTotalMap[datum?.dept]
            if (rateType) {
              return {
                name: rateType,
                value:
                  rateType === '达标次数'
                    ? datum['未使用率']
                    : rateType === '使用人数'
                      ? datum['使用人数']
                      : rateType === '使用频率'
                        ? datum['使用频率']
                        : `${(datum['未使用率'] * 100).toFixed(1)}%`,
              }
            } else {
              if (funcFlag && chartCode === 'menuTitles') {
                return {
                  title: `${dept}（总计：${total}）`,
                  name: type,
                  value,
                }
              } else if (chartCode === 'functionCodes') {
                return { name: '使用人数', value }
              } else {
                return { name: type, value }
              }
            }
          },
        },
        xAxis: {
          label: {
            autoRotate: true, // 自动旋转标签
            style: (item) => ({
              fill: funcFlag && item === clickTitleRef.current ? '#e13e34' : '#252525',
              fontWeight: funcFlag && item === clickTitleRef.current ? '600' : '400',
            }),
            formatter: (item) => (item.length > 25 ? `${item.slice(0, 25)}...` : item),
          },
        },
        geometryOptions: [
          {
            geometry: 'column',
            isStack: true,
            seriesField: 'type',
            columnWidthRatio: 0.16,
            color: ({ type }) => {
              if (type === '其他') {
                return OTHER_COLOR
              } else {
                if (colorMap.has(type)) {
                  return colorMap.get(type)
                } else {
                  let color = ''
                  if (!funcFlag) {
                    color = colors30Arr[colorIndex % (colors30Arr.length - 1)]
                  } else {
                    color = funcRankToColorMap[type]
                  }
                  colorMap.set(type, color)
                  colorIndex++
                  return color
                }
              }
            },
          },
          {
            geometry: 'line',
            seriesField: 'rateType',
            lineStyle: ({ rateType }) => {
              return [
                intl.get('达标率'),
                intl.get('总人数'),
                intl.get('使用人数比例'),
                intl.get('达标次数'),
                intl.get('使用次数比例'),
              ].includes(rateType)
                ? {
                    opacity: 0,
                  }
                : {
                    opacity: 1,
                  }
            },
            color: ({ rateType }) => {
              if ([intl.get('未使用率'), intl.get('达标次数')].includes(rateType)) {
                return '#FEC833'
              } else if ([intl.get('达标率'), intl.get('使用次数比例')].includes(rateType)) {
                return '#38C3FE'
              } else {
                return '#38D677'
              }
            },
          },
        ],
      })

      setDualAxes(res)
      init.current = false
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [barData, lineData, xField, yField, setDualAxes, funcFlag])

    useEffect(() => {
      if (!funcFlag) {
        dualAxes?.on('element:click', (args) => {
          const { data } = args
          const { nonMenuTitles, dept } = data.data
          if (canColumnClick && nonMenuTitles?.length && dept && refresh) {
            refresh(dept, nonMenuTitles)
          }
        })
      } else {
        dualAxes?.on('element:click', (args) => {
          const { data } = args
          if (canColumnClick && refresh) {
            const nonMenuTitles =
              chartCode === 'functionCodes' ? [data?.data?.functionCode] : [data?.data?.dept]
            if (nonMenuTitles?.[0]) {
              refresh(data?.data?.dept, nonMenuTitles, chartCode)
            }
          }
          if (clickTitleRef.current === data?.data?.dept) {
            setClickTitle('none')
            setTitleChangeFlag(true)
          } else {
            setClickTitle(data?.data?.dept)
            setTitleChangeFlag(true)
          }
        })
      }
      return () => {
        dualAxes?.off('element:click')
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [canColumnClick, chartCode, dualAxes, funcFlag, refresh])

    useEffect(() => {
      if (dualAxes && titleChangeFlag) {
        dualAxes.render()
        setTitleChangeFlag(false)
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [titleChangeFlag])

    useEffect(() => {
      if (!dualAxes) {
        return () => undefined
      } else {
        dualAxes.render()
        return () => {
          dualAxes.destroy()
          colorIndex = 0
          colorMap.clear()
        }
      }
    }, [dualAxes])

    return <div className="track-chart" ref={trackChartContainer} style={cardRowStyle}></div>
  }
)

TrackChart.displayName = 'TrackChart'

export default TrackChart
