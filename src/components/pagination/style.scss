@import '../../common/styles/vars';

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  padding: 12px 0 0;
}

.rc-pagination-options-quick-jumper {
  color: #1f2733;

  input {
    background: #f2f4f7;
    border-radius: 4px;
    border: none;
    outline: none;
    height: 24px;
    padding: 0 8px;
    box-sizing: border-box;
  }
}

.gsc-pagination {
  .rc-pagination-total-text {
    color: #1f2733;
    line-height: 24px;
    font-size: 14px;
    height: 24px;
  }

  .rc-pagination-item-active a {
    color: #fff !important;
    background-color: $primary-color;
  }

  .rc-pagination-item,
  .rc-pagination-prev,
  .rc-pagination-next {
    min-width: 24px;
    height: 24px;
    border: none;
    line-height: 24px;
    background: #f2f4f7;
    border-radius: 6px;
    color: #102837;

    a {
      border-radius: 6px;
      color: rgb(31 39 51 / 81.1%);
    }

    button {
      border: none;
      background: #f2f4f7;
      border-radius: 6px;
    }
  }
}

@media (max-width: 1366px) {
  .rc-pagination-options {
    margin-left: 2px;

    input {
      width: 30px;
    }
  }
}
