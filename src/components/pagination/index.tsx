import React, { <PERSON> } from 'react'
import RCPagination, { PaginationProps } from 'rc-pagination'
import 'rc-pagination/assets/index.css'
import './style.scss'

export const Pagination: FC<PaginationProps> = ({
  current,
  total,
  pageSize,
  onChange,
  showSizeChanger,
  ...props
}) => {
  return (
    <div className="pagination-wrapper">
      <RCPagination
        className="gsc-pagination"
        showQuickJumper
        showTotal={(total) => `共 ${total} 条`}
        total={total}
        pageSize={pageSize}
        current={current}
        onChange={onChange}
        showSizeChanger={showSizeChanger}
        {...props}
      ></RCPagination>
    </div>
  )
}
