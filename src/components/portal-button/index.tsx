import React, { useEffect, useState } from 'react'
import <PERSON><PERSON> from '@hi-ui/button'
import Portal from '@/components/portal'
import { HEADER_TOOLS_PORTAL } from '@/constants'

const PortalButton = ({ onClick, children, icon = null, disabled = false }) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, forceUpdate] = useState<number>(1)
  const portalRoot = document.getElementById(HEADER_TOOLS_PORTAL)

  useEffect(() => {
    const timer = setInterval(() => {
      if (document.getElementById(HEADER_TOOLS_PORTAL)) {
        forceUpdate((prev) => prev + 1)
        clearInterval(timer)
      }
    }, 100)
  }, [])

  return portalRoot ? (
    <Portal container={portalRoot}>
      <Button onClick={onClick} icon={icon} type="primary" disabled={disabled}>
        {children}
      </Button>
    </Portal>
  ) : null
}

export default PortalButton
