import { ReactElement, cloneElement, memo } from 'react'

interface Props {
  icon: ReactElement
  open: () => void
  close: () => void
  color?: string
}

const TableBaseTool = memo<Props>(({ icon, close, open, color = '#5F6A7A' }) =>
  cloneElement(icon, {
    color,
    onMouseEnter: open,
    onMouseLeave: close,
    onClick: close,
  })
)
TableBaseTool.displayName = 'TableBaseTool'
export default TableBaseTool
