import intl from 'react-intl-universal'
/* eslint-disable @typescript-eslint/ban-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { memo, FC, useRef, useCallback, useMemo, useEffect } from 'react'
import './index.scss'
import { Input } from '@hi-ui/input'
import { Checkbox } from '@hi-ui/checkbox'
import { Button } from '@hi-ui/button'
import { useSafeState, useDebounceFn } from 'ahooks'
import VirtualList from 'react-tiny-virtual-list'
import Tooltip from '@hi-ui/tooltip'
import Loading from '@hi-ui/loading'

const TableFilterAsync: FC<{
  setFilterDropdownVisible: Function // 控制过滤组件显隐
  filterColumnKey: string // 当前过滤的列值
  curSelectData: AnyType[] // 当前列可以选择的数据
  setMultiFilter: Function // 多条件查询
  reShow: object // 已设置的查询条件，用于回显
  setReShow: Function // reshow
  setIconClickObj: Function // 过滤过的按钮
  filterSameAsInit: AnyType // 精细化确定
  setIsReady: Function // 设置isReady关闭过滤组件
  realTimeSearch?: AnyType // 实时查询
  withCache?: boolean // 是否开启缓存
}> = memo(
  ({
    setFilterDropdownVisible,
    filterColumnKey,
    curSelectData,
    setMultiFilter,
    reShow,
    setReShow,
    setIconClickObj,
    filterSameAsInit,
    setIsReady,
    realTimeSearch,
    withCache,
  }) => {
    const initData = useRef(curSelectData)
    const [curDataState, setCurDataState] = useSafeState<AnyType[]>(curSelectData || [])
    const [inputValue, setInputValue] = useSafeState('')
    const [hasChecked, setHasChecked] = useSafeState<object>(
      Object.keys(reShow).length !== 0 ? { ...reShow } : {}
    )

    // 搜索下拉框中与输入值模糊匹配的值
    const searchFilterData = async (keyword) => {
      if (keyword.length !== 0) {
        if (realTimeSearch) {
          const { searchFunction, filter = {} } = realTimeSearch
          const columnRes = await searchFunction(
            Object.assign(filter, {
              columnName: filterColumnKey,
              searchKey: keyword,
              // 上面是售后概况、下面是物料快查的搜索参数
              content: [keyword],
              searchType: filterColumnKey,
            })
          )

          const allColumnResData = columnRes?.data ?? {}
          const columnData = allColumnResData.map((item) => {
            item.id = item.id || item.code
            item.title = (
              <Tooltip
                trigger="hover"
                placement="right"
                title={<p className="break-words flex justify-center items-center">{item.title}</p>}
              >
                <span className="truncate block" style={{ width: 144 }}>
                  {item.title || item.name}
                </span>
              </Tooltip>
            )
            return item
          })
          setCurDataState(columnData)
        } else {
          setCurDataState(initData.current.filter((item) => item?.id?.includes(keyword)))
        }
      } else {
        setCurDataState(initData.current)
      }
    }

    const { run: onSearch } = useDebounceFn(searchFilterData, {
      wait: 300,
    })

    // input框输入值改变时
    const searchInputChange = (e) => {
      const value = e.target.value
      setInputValue(value)
      onSearch(value.trim())
    }

    // 重置过滤组件
    const resetTableFilter = () => {
      setInputValue('')
      setHasChecked({})
    }

    // 通过所选的过滤条件更新表格数据
    const confirmTableFilter = useCallback(() => {
      // 所有弹窗的visible都更新为false
      setIsReady((prev) => Object.keys(prev).reduce((a, b) => ({ ...a, [b]: false }), {}))
      setFilterDropdownVisible(false)
      filterSameAsInit.current[filterColumnKey] = false
      setMultiFilter((prev) => {
        const res = Object.entries(hasChecked).reduce<string[]>((acc, [key, value]) => {
          if (value) acc.push(key)
          return acc
        }, [])
        return { ...prev, [filterColumnKey]: res }
      })

      const iconHighlight = Object.keys(hasChecked).some((item) => hasChecked[item])
      setIconClickObj((prev) => {
        return { ...prev, [filterColumnKey]: iconHighlight }
      })
      setReShow((prev) => {
        return {
          ...prev,
          [filterColumnKey]: hasChecked,
        }
      })
      if (Object.keys(hasChecked).length === 0) filterSameAsInit.current[filterColumnKey] = true
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [hasChecked])

    const checkAllOrCancelAll = (flag: boolean) => {
      const allSelect = curDataState
        ?.map((item) => item?.id)
        ?.reduce((a, b) => {
          a[b] = flag
          return a
        }, {})
      setHasChecked((prev) => {
        return { ...prev, ...allSelect }
      })
    }

    const checkedLength = useMemo(
      () => Object.values(hasChecked)?.filter((i) => i)?.length,
      [hasChecked]
    )

    useEffect(() => {
      if (curSelectData?.length && withCache) {
        setCurDataState(curSelectData)
        if (!initData.current) {
          initData.current = curSelectData
        }
      }
    }, [curSelectData, setCurDataState, withCache])

    return (
      <>
        <div className="filter_input">
          <Input
            clearable
            clearableTrigger="always"
            placeholder={intl.get('搜索')}
            value={inputValue}
            onChange={searchInputChange}
          ></Input>
        </div>

        <div className="filter_content">
          {curDataState.length !== 0 ? (
            <VirtualList
              style={{ overflowX: 'hidden' }}
              width={184}
              height={200}
              itemCount={curDataState.length || 0}
              itemSize={32}
              renderItem={({ index, style }) => (
                <div
                  key={index}
                  style={{
                    ...style,
                    display: 'flex',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    paddingLeft: 8,
                  }}
                >
                  <Checkbox
                    key={curDataState[index].id}
                    value={curDataState[index].id}
                    name={curDataState[index].title}
                    checked={!!hasChecked[curDataState[index].id]} // 一开始全没有
                    onChange={(e) => {
                      const { checked: newestValue = false } = e.target
                      setHasChecked((prev) => {
                        prev[curDataState?.[index]?.id] = newestValue
                        return { ...prev }
                      })
                    }}
                  >
                    {curDataState[index].title}
                  </Checkbox>
                </div>
              )}
            />
          ) : (
            <span className="no_data_span">
              <Loading visible={!curSelectData} size="sm">
                {curSelectData ? intl.get('暂无数据') : ''}
              </Loading>
            </span>
          )}
        </div>

        <div className="filter_foot_btns">
          {inputValue ? (
            <>
              <Button
                type="default"
                size="sm"
                disabled={checkedLength === 0}
                onClick={() => checkAllOrCancelAll(false)}
              >
                {intl.get('全不选')}
              </Button>
              <Button
                type="default"
                size="sm"
                disabled={checkedLength >= curDataState.length}
                onClick={() => checkAllOrCancelAll(true)}
              >
                {intl.get('全选')}
              </Button>
            </>
          ) : (
            <Button
              type="default"
              size="sm"
              disabled={checkedLength === 0}
              onClick={resetTableFilter}
            >
              {intl.get('重置')}
            </Button>
          )}
          <Button type="primary" size="sm" onClick={confirmTableFilter}>
            {intl.get('确定')}
          </Button>
        </div>
      </>
    )
  }
)

TableFilterAsync.displayName = 'TableFilterAsync'

export default TableFilterAsync
