@import '@/common/styles/vars';

.relation-table-custom-filter {
  padding: 8px 8px 12px;
  width: max-content;
  height: 300px;

  .filter_input {
    margin-bottom: 8px;

    .hi-v4-input__outer {
      .hi-v4-input__inner {
        border-top: none;
        border-left: none;
        border-right: none;
        border-radius: 0;

        input.hi-v4-input__text {
          position: relative;
          left: 22px;
          padding-top: 6px;
          padding-bottom: 6px;
        }

        span.hi-v4-input__suffix {
          position: relative;
          right: 0;
        }
      }
    }

    div.hi-v4-input__inner::before {
      content: '';
      position: absolute;
      display: block;
      height: 16px;
      width: 16px;
      left: 8px;
      top: 8px;
      background-image: url('#{$img-url}filter_search.svg');
      z-index: 1;
    }

    .hi-v4-input--appearance-line
      .hi-v4-input__inner:not(.hi-v4-inputinner--disabled).hi-v4-input__inner--focused {
      box-shadow: none !important;
    }
  }

  .filter_content {
    height: 200px;
    overflow: hidden overlay;

    .no_data_span {
      padding: 8px;
      font-size: 0.875rem;
      color: #929aa6;
      height: 16px;
      display: flex;
      align-items: center;
      margin-top: 12px;
      justify-content: center;
    }
  }

  .filter_foot_btns {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 8px;
  }
}
