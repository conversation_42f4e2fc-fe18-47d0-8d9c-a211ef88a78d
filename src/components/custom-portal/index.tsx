import React, { ReactNode, useCallback, useEffect, useRef } from 'react'
import Portal from '@/components/portal'

const CustomPortal = ({ children, domId }) => {
  const portalRoot = document.getElementById(domId)
  const initRender = useRef<boolean>(false)
  const titleCache = useRef<ReactNode | ReactNode[]>(null)

  const changeDisplay = useCallback((nodes, show) => {
    if (!nodes || !nodes?.length) return
    const style = show ? 'display: block' : 'display: none'
    for (const cur of nodes) {
      cur.style = style
    }
  }, [])

  useEffect(() => {
    if (portalRoot) {
      titleCache.current = portalRoot.children
      changeDisplay(titleCache.current, false)
      initRender.current = true
    }
    return () => {
      titleCache.current && changeDisplay(titleCache.current, true)
    }
  }, [portalRoot, changeDisplay])

  return portalRoot && initRender.current ? (
    <Portal container={portalRoot}>{children}</Portal>
  ) : null
}

export default CustomPortal
