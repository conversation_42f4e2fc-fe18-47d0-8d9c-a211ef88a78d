import React, { memo, ReactNode, useRef } from 'react'

import { useHover } from 'ahooks'

import { Tooltip } from '@hi-ui/tooltip'

import './index.scss'
type ClickType = (e: React.MouseEvent<HTMLElement, MouseEvent>) => void
interface ITableToolItemProps {
  title: string
  icon: ReactNode
  onClick: ClickType
  loading?: boolean
}

const TableToolItem = memo((props: ITableToolItemProps) => {
  const { title, icon, onClick, loading } = props
  const ref = useRef(null)
  const isHovering = useHover(ref)

  return (
    <Tooltip visible={isHovering} title={title} trigger="hover">
      <i
        ref={ref}
        className="table-tool__item"
        onClick={(e) => {
          if (loading) return
          onClick(e)
        }}
      >
        {icon}
      </i>
    </Tooltip>
  )
})

TableToolItem.displayName = 'TableToolItem'
export default TableToolItem
