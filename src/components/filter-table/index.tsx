import React, { useMemo, useState } from 'react'
import { Table, TableColumnItem } from '@hi-ui/table'
import { Select, SelectMergedItem } from '@hi-ui/select'
import { Input } from '@hi-ui/input'
import { ArrowUpOutlined, FilterOutlined } from '@hi-ui/icons'
import { Tooltip } from '@hi-ui/tooltip'
import './index.scss'

export interface FilterTableProps<T = AnyType> {
  columns: Array<TableColumnItem>
  data: T[]
  filterTypes?: Record<string, 'select' | 'input'>
  filterOptions?: Record<string, SelectMergedItem[]>
  onFilterChange?: (filters: Record<string, AnyType>) => void
  onSortChange?: (sortInfo: { field: string; order: 'asc' | 'desc' | undefined }) => void
}

export const FilterTable = <T extends Record<string, AnyType>>({
  columns,
  data,
  filterTypes = {},
  filterOptions = {},
  onFilterChange,
  onSortChange,
}: FilterTableProps<T>) => {
  const [filters, setFilters] = useState<Record<string, AnyType>>({})
  const [sortInfo, setSortInfo] = useState<{ field: string; order: 'asc' | 'desc' | undefined }>({
    field: '',
    order: undefined,
  })

  const enhancedColumns = useMemo(() => {
    return columns.map((column) => {
      if (!column.dataKey) return column

      const filterType = filterTypes[column.dataKey]
      if (!filterType) return column

      return {
        ...column,
        title: (
          <div className="filter-table__header">
            <span>{column.title}</span>
            <div className="filter-table__header-actions">
              {filterType && (
                <Tooltip
                  title={
                    filterType === 'select' ? (
                      <Select
                        clearable
                        data={filterOptions[column.dataKey] || []}
                        value={filters[column.dataKey]}
                        onChange={(value) => {
                          const newFilters = { ...filters, [column.dataKey as string]: value }
                          setFilters(newFilters)
                          onFilterChange?.(newFilters)
                        }}
                      />
                    ) : (
                      <Input
                        value={filters[column.dataKey]}
                        onChange={(e) => {
                          const newFilters = {
                            ...filters,
                            [column.dataKey as string]: e.target.value,
                          }
                          setFilters(newFilters)
                          onFilterChange?.(newFilters)
                        }}
                      />
                    )
                  }
                >
                  <FilterOutlined />
                </Tooltip>
              )}
              <span
                className={`filter-table__sort-icon ${
                  sortInfo.field === column.dataKey ? `active ${sortInfo.order}` : ''
                }`}
                onClick={() => {
                  const order: 'asc' | 'desc' | undefined =
                    sortInfo.field !== column.dataKey
                      ? 'asc'
                      : sortInfo.order === 'asc'
                        ? 'desc'
                        : undefined
                  const newSortInfo = order
                    ? { field: column.dataKey as string, order }
                    : { field: '', order: undefined }
                  setSortInfo(newSortInfo)
                  onSortChange?.(newSortInfo)
                }}
              >
                <ArrowUpOutlined />
              </span>
            </div>
          </div>
        ),
      }
    })
  }, [
    columns,
    filterTypes,
    filterOptions,
    filters,
    sortInfo.field,
    sortInfo.order,
    onFilterChange,
    onSortChange,
  ])

  return <Table columns={enhancedColumns} data={data} />
}
