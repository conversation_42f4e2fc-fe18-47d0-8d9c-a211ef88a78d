import intl from 'react-intl-universal'
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { FC, useCallback, useEffect, useRef, useState } from 'react'
import Button from '@hi-ui/button'
import Upload from '@hi-ui/upload'
import message from '@hi-ui/message'
import { generatePreUploadUrl, uploadEditTable } from '@/api/gsc/manual-todo'
import { EDIT_TABLE_UPLOAD } from './type'
import { useRequest } from 'ahooks'
import { customMessage } from '@/utils/custom-message'

const CustomUpload: FC<{
  category: string
  getTodoList: (params_0: { [key: string]: any; current: number; pageSize: number }) => void
  pagination: Record<string, any>
}> = ({ category, getTodoList, pagination }) => {
  const [uploadLoading, setUploadLoading] = useState<boolean>(false)
  const uploadedName = useRef<string>('')

  const { run: conductUploadEditTable, data: errorList } = useRequest(
    ({ category, objectName }) => uploadEditTable({ category, objectName }),
    {
      manual: true,
      onSuccess: () => {
        message.closeAll()
        getTodoList({ current: pagination.current, pageSize: pagination.pageSize })
        !errorList?.data?.length && customMessage('数据已更新至表格', 'success')
      },
    }
  )

  useEffect(() => {
    if (errorList?.data?.length > 0) {
      const messageInfo = errorList?.data?.join(',')
      customMessage(messageInfo, 'warning', 8000)
    }
  }, [errorList])

  const handleUpdate = useCallback(
    (files) => {
      const data = files?.[0]
      const name = data?.name
      setUploadLoading(true)
      generatePreUploadUrl({ category: EDIT_TABLE_UPLOAD, fileName: name }).then((res) => {
        const { objectName = '', presignedUri = '' } = res?.data || {}
        uploadedName.current = objectName
        fetch(presignedUri, {
          method: 'PUT',
          headers: {
            'Content-Type': '',
          },
          body: data,
        })
          .then((res) => {
            return res.json()
          })
          .then((res) => {
            // 更新文件列表
            const { objectName = '' } = res || {}
            customMessage('等待数据校验更新...', 'info')
            setUploadLoading(false)
            conductUploadEditTable({ category, objectName })
          })
      })
    },
    [category, conductUploadEditTable]
  )

  return (
    <div>
      <Upload
        type="default"
        accept="application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        headers={{ 'Content-Type': '' }}
        disabled={false}
        loading={uploadLoading}
        customUpload={(files) => handleUpdate(files)}
      >
        <Button type="primary">{intl.get('上传')}</Button>
      </Upload>
    </div>
  )
}

export default CustomUpload
