import React from 'react'
import Portal from '@/components/portal'
import But<PERSON> from '@hi-ui/button'
import { PlusOutlined } from '@hi-ui/icons'
import { HEADER_TOOLS_PORTAL } from '@/constants'

const NoticeAddButton = ({ onClick, children }) => {
  const portalRoot = document.getElementById(HEADER_TOOLS_PORTAL) || document.body

  return (
    <Portal container={portalRoot}>
      <Button onClick={onClick} icon={<PlusOutlined />} type="primary">
        {children}
      </Button>
    </Portal>
  )
}

export default NoticeAddButton
