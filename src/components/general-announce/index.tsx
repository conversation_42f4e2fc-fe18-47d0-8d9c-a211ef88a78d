import intl from 'react-intl-universal'
import React, { useMemo, useEffect, useCallback, useRef, ReactText } from 'react'
import * as Sentry from '@sentry/react'
import { Button } from '@hi-ui/button'
import { Card } from '@hi-ui/card'
import { ValueType, ProFormField } from '@mi/sc-ui-pro-form'
import { ProTable, ProTableColumnItem } from '@mi/sc-ui-pro-table'
import { CheckSelect } from '@hi-ui/check-select'
import { Switch } from '@hi-ui/switch'
import { useNavigate } from 'react-router-dom'
import PopConfirm from '@hi-ui/pop-confirm'
import Input from '@hi-ui/input'
import message from '@hi-ui/message'
import Tooltip from '@hi-ui/tooltip'
import ReactLoading from 'react-loading'
import { useRequest, useSafeState } from 'ahooks'
import {
  executeNoticeDelete,
  executeNoticeSearch,
  executeNoticeUpdate,
  getNoticeDefaultCondition,
} from '@/api/gsc/general-notice'
import { getTextWidth } from '@/utils/get-text-width'
import NoticeAddButton from './NoticeAddButton'
import { customMessage } from '@/utils/custom-message'
import { AnyType, MESSAGE_DURATION, isGSCWorkbench } from '@/constants'
import './index.scss'
import Select from '@hi-ui/select'
import { NOTIFY_DIMENSION } from './config'
import { ellipses } from '../ellipsis-tool'
import { ProLocaleProvider } from '@mi/sc-ui-i18n'

const GeneralAnnounce = () => {
  const navigate = useNavigate()
  const actionRef = useRef<AnyType>(null)
  const [switchLoading, setSwitchLoading] = useSafeState<object>({})
  const [switchCheck, setSwitchCheck] = useSafeState<object>({})
  const [deleteLoading, setDeleteLoading] = useSafeState<object>({})
  const [defaultSelectData, setDefaultSelectData] = useSafeState<AnyType>({})
  const [pagination, setPagination] = useSafeState({
    current: 1,
    pageSize: 10,
  })
  const [isBusinessDimension, setIsBusinessDimension] = useSafeState<ReactText>(0)

  useEffect(() => {
    const fn = async () => {
      const res = await getNoticeDefaultCondition()
      setDefaultSelectData(res?.data ?? {})
    }
    fn()
  }, [setDefaultSelectData])

  const { run: updateHandler } = useRequest(
    (
      id: string,
      name: string | null,
      content: string | null,
      url: string | null,
      enable: boolean,
      remark: string | null,
      roles: string[]
    ) =>
      executeNoticeUpdate({
        id,
        name,
        content,
        url,
        enable,
        remark,
        roles,
      }),
    {
      manual: true,
      onBefore: (params) => {
        setSwitchLoading((prev) => {
          return { ...prev, [`${params[0]}`]: true }
        })
      },
      onError: (err, params) => {
        const state = params?.[4] ?? 0
        Sentry.captureException(err)
        customMessage('修改当前状态失败，请联系管理员', 'error')
        setSwitchCheck((prev) => {
          return { ...prev, [`${params[0]}`]: !state }
        })
      },
      onSuccess: (_data, params) => {
        const state = params?.[4] ?? 0
        message.open({
          title: state ? intl.get('启用成功') : intl.get('禁用成功'),
          type: 'success',
          duration: MESSAGE_DURATION,
        })
      },
      onFinally: (params) => {
        setSwitchLoading((prev) => {
          return { ...prev, [`${params[0]}`]: false }
        })
      },
    }
  )

  const { run: deleteHandler } = useRequest((id: number) => executeNoticeDelete({ id }), {
    manual: true,
    onBefore: (params) => {
      setDeleteLoading((prev) => {
        return { ...prev, [`${params[0]}`]: true }
      })
    },
    onSuccess: () => {
      message.open({ title: intl.get('删除成功'), type: 'success' })
      const fn = async () => {
        const res = await getNoticeDefaultCondition()
        setDefaultSelectData(res?.data ?? {})
      }
      fn()
    },
    onError: (err) => {
      message.open({ title: err?.message || intl.get('删除失败，请联系管理员！'), type: 'error' })
    },
    onFinally: (params) => {
      setDeleteLoading((prev) => {
        return { ...prev, [`${params[0]}`]: false }
      })
      actionRef.current?.reload(false)
    },
  })

  const tableColumnRender = (text: string, textWidth: number) => {
    return text === '0' || !text || text === 'null' ? (
      '-'
    ) : getTextWidth(text) > textWidth - 36 ? (
      <Tooltip title={<div style={{ maxWidth: 550, wordWrap: 'break-word' }}>{text}</div>}>
        <span className="truncate block">{text}</span>
      </Tooltip>
    ) : (
      <span className="block">{text}</span>
    )
  }

  const dimensionCache = useRef<ReactText>(0)
  useEffect(() => {
    dimensionCache.current = isBusinessDimension
  }, [isBusinessDimension])

  const cols = useMemo<ProTableColumnItem[]>(() => {
    const dynamicCol = isGSCWorkbench
      ? [
          {
            dataKey: 'roleBoSet',
            title: ellipses(intl.get('职位')),
            width: 200,
            render: (arr) => {
              const str = arr?.map((i) => i?.name).join(',') ?? '-'
              return !arr?.length ? (
                '-'
              ) : str.length > 30 ? (
                <Tooltip title={<div style={{ maxWidth: 550, wordWrap: 'break-word' }}>{str}</div>}>
                  <span className="truncate block">{str}</span>
                </Tooltip>
              ) : (
                <span className="block">{str}</span>
              )
            },
            initialValue: [],
            renderFormItem: () => (
              <ProFormField field="roleBoSet" label={ellipses(intl.get('职位'))}>
                {(props) => (
                  <CheckSelect
                    className="announce-form-item"
                    searchable
                    searchMode="filter"
                    showCheckAll
                    placeholder={intl.get('请选择职位')}
                    fieldNames={{ id: 'code', title: 'name' }}
                    data={defaultSelectData?.roleBoSet?.filter((i) => i?.code && i?.name) || []}
                    {...props}
                  ></CheckSelect>
                )}
              </ProFormField>
            ),
          },
        ]
      : [
          {
            dataKey: 'isBusinessDimension',
            title: ellipses(intl.get('查询维度')),
            hideInTable: true,
            initialValue: 0,
            renderFormItem: () => (
              <ProFormField field="isBusinessDimension" label={ellipses(intl.get('查询维度'))}>
                {({ onChange, ...props }) => (
                  <Select
                    className="announce-form-item"
                    placeholder={intl.get('请选择查询维度')}
                    clearable={false}
                    data={NOTIFY_DIMENSION}
                    onChange={(a, b) => {
                      setIsBusinessDimension(a)
                      onChange(a, b)
                    }}
                    {...props}
                  ></Select>
                )}
              </ProFormField>
            ),
          },
          {
            dataKey: 'suppliers',
            title: ellipses(intl.get('供应商')),
            width: 300,
            render: (_str, rowItem) => {
              const { supplierBoSet = [] } = rowItem || {}
              const str = supplierBoSet?.map((i) => i?.name)?.join(',') || ''
              return !str ? (
                '-'
              ) : str.length > 30 ? (
                <Tooltip title={<div style={{ maxWidth: 550, wordWrap: 'break-word' }}>{str}</div>}>
                  <span className="truncate block">{str}</span>
                </Tooltip>
              ) : (
                <span className="block">{str}</span>
              )
            },
            initialValue: [],
            hideInTable: !!dimensionCache.current,
            renderFormItem: () => (
              <ProFormField field="suppliers" label={ellipses(intl.get('供应商'))}>
                {({ ...props }) => (
                  <CheckSelect
                    className="announce-form-item"
                    searchable
                    searchMode="filter"
                    showCheckAll
                    placeholder={intl.get('请选择供应商')}
                    fieldNames={{ id: 'code', title: 'name' }}
                    data={
                      defaultSelectData?.suppliersBoSet?.filter((i) => i?.code && i?.name) || []
                    }
                    {...props}
                  ></CheckSelect>
                )}
              </ProFormField>
            ),
          },
          {
            dataKey: 'businessCodes',
            title: ellipses(intl.get('业务线')),
            width: 300,
            render: (_str, rowItem) => {
              const { businessCodeBoSet = [] } = rowItem || {}
              const str = businessCodeBoSet?.map((i) => i?.name)?.join(',') || ''
              return !str ? (
                '-'
              ) : str.length > 30 ? (
                <Tooltip title={<div style={{ maxWidth: 550, wordWrap: 'break-word' }}>{str}</div>}>
                  <span className="truncate block">{str}</span>
                </Tooltip>
              ) : (
                <span className="block">{str}</span>
              )
            },
            initialValue: [],
            hideInTable: !dimensionCache.current,
            renderFormItem: () => (
              <ProFormField field="businessCodes" label={ellipses(intl.get('业务线'))}>
                {({ ...props }) => (
                  <CheckSelect
                    className="announce-form-item"
                    searchable
                    searchMode="filter"
                    showCheckAll
                    placeholder={intl.get('请选择业务线')}
                    fieldNames={{ id: 'code', title: 'name' }}
                    data={
                      defaultSelectData?.businessCodesBoSet?.filter((i) => i?.code && i?.name) || []
                    }
                    {...props}
                  ></CheckSelect>
                )}
              </ProFormField>
            ),
          },
        ]
    return [
      ...dynamicCol,
      {
        dataKey: 'name',
        title: ellipses(intl.get('公告栏名称')),
        width: 150,
        render: (text) => tableColumnRender(text, 150),
        initialValue: [],
        renderFormItem: () => (
          <ProFormField field="name" label={ellipses(intl.get('公告栏名称'))}>
            {(props) => (
              <CheckSelect
                className="announce-form-item"
                searchable
                searchMode="filter"
                showCheckAll
                placeholder={intl.get('请选择')}
                data={
                  defaultSelectData?.nameList
                    ?.filter((i) => !!i)
                    ?.map((item) => ({
                      title: item,
                      id: item,
                    })) ?? []
                }
                {...props}
              ></CheckSelect>
            )}
          </ProFormField>
        ),
      },
      {
        dataKey: 'content',
        title: ellipses(intl.get('通知内容')),
        width: 200,
        render: (text) => tableColumnRender(text, 200),
        initialValue: '',
        renderFormItem: () => (
          <ProFormField field="content" label={ellipses(intl.get('通知内容'))}>
            {(props) => (
              <Input
                className="announce-form-item"
                placeholder={intl.get('请输入')}
                {...props}
              ></Input>
            )}
          </ProFormField>
        ),
      },
      {
        dataKey: 'url',
        width: 200,
        title: ellipses(intl.get('通知链接')),
        hideInSearch: true,
        render: (text) => tableColumnRender(text, 200),
      },
      {
        dataKey: 'remark',
        width: 200,
        title: ellipses(intl.get('备注')),
        hideInSearch: true,
        render: (text) => tableColumnRender(text, 200),
      },
      {
        dataKey: 'startTime',
        hideInSearch: true,
        render: (text) => tableColumnRender(text, 180),
        width: 180,
        title: ellipses(intl.get('发送时间')),
      },
      {
        dataKey: 'endTime',
        width: 180,
        hideInSearch: true,
        render: (text) => tableColumnRender(text, 180),
        title: ellipses(intl.get('关闭时间')),
      },
      {
        dataKey: 'enable',
        title: ellipses(intl.get('状态')),
        valueType: ValueType.select,
        hideInSearch: true,
        width: 120,
        render: (text, rowItem) => {
          return (
            <Switch
              defaultChecked={!!text}
              checked={switchCheck?.[rowItem?.id]}
              content={[
                switchLoading?.[rowItem?.id] ? (
                  <ReactLoading
                    type="spokes"
                    color="#fff"
                    height={14}
                    width={18}
                    className="switch_loading"
                  />
                ) : (
                  intl.get('启用')
                ),
                switchLoading?.[rowItem?.id] ? (
                  <ReactLoading
                    type="spokes"
                    color="#fff"
                    height={14}
                    width={18}
                    className="switch_loading"
                  />
                ) : (
                  intl.get('禁用')
                ),
              ]}
              onChange={(newState) => {
                const { id, roles } = rowItem ?? {}
                updateHandler(id, null, null, null, newState, null, [roles])
              }}
            />
          )
        },
      },
      {
        dataKey: 'options',
        title: ellipses(intl.get('操作')),
        valueType: ValueType.select,
        hideInSearch: true,
        width: 120,
        render: (_text, rowItem) => (
          <>
            <Button
              onClick={() => {
                navigate(`/update-announce/${rowItem.id}`, {
                  state: {
                    rowItem: {
                      ...rowItem,
                      current: pagination.current,
                      pageSize: pagination.pageSize,
                    },
                  },
                })
              }}
              appearance="link"
              type="primary"
            >
              {intl.get('编辑')}
            </Button>
            <PopConfirm
              onConfirm={() => deleteHandler(rowItem.id)}
              title={intl.get('是否删除该配置?')}
            >
              <Button loading={deleteLoading[`${rowItem.id}`]} type="primary" appearance="link">
                {deleteLoading[`${rowItem.id}`] ? `${'\u00A0\u00A0\u00A0'}` : intl.get('删除')}
              </Button>
            </PopConfirm>
          </>
        ),
      },
    ]
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultSelectData, pagination])

  const handleAddNotice = useCallback(
    () =>
      navigate('/create-announce', {
        state: {
          rowItem: {
            current: pagination.current,
            pageSize: pagination.pageSize,
          },
        },
      }),
    [navigate, pagination]
  )

  return (
    <div>
      <Card bordered={false} className="general_notice_wrapper">
        <NoticeAddButton onClick={handleAddNotice}>{intl.get('新增通用公告')}</NoticeAddButton>
        <ProLocaleProvider locale={intl.getInitOptions().currentLocale as AnyType}>
          <ProTable
            showSplitLine={false}
            search={{
              labelWidth: 80,
              labelPlacement: 'right',
              showColon: false,
              rowProps: {
                gutter: 26,
              },
              defaultColHeight: 86,
            }}
            tableProps={{
              fieldKey: 'id',
            }}
            actionRef={actionRef}
            pagination={{
              showJumper: true,
              pageSizeOptions: [5, 10, 20],
              showTotal: true,
            }}
            columns={cols}
            toolbar={false}
            request={(params) => {
              const {
                current,
                pageSize,
                name,
                roleBoSet,
                suppliers,
                businessCodes,
                isBusinessDimension,
              } = params
              setPagination((prev) => ({
                ...prev,
                current,
                pageSize,
              }))
              const newParams: AnyType = {
                ...params,
                pageNo: current,
                names: name,
                roles: roleBoSet,
                isEmployees: [isGSCWorkbench],
                isBusinessDimension: [!!isBusinessDimension],
              }
              delete newParams.name
              delete newParams.roleBoSet
              delete newParams.current
              if (!isGSCWorkbench) {
                delete newParams.roles
                if (isBusinessDimension === 1 && suppliers) {
                  delete newParams.suppliers
                } else if (isBusinessDimension === 0 && businessCodes) {
                  delete newParams.businessCodes
                }
              } else {
                delete newParams.suppliers
                delete newParams.businessCodes
                delete newParams.isBusinessDimension
              }
              return executeNoticeSearch(newParams)
            }}
            onReset={() => {
              setIsBusinessDimension(0)
              setPagination((prev) => ({
                ...prev,
                current: 1,
              }))
            }}
          ></ProTable>
        </ProLocaleProvider>
      </Card>
    </div>
  )
}
export default GeneralAnnounce
