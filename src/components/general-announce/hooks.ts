import { getAllBusinessLines, getJobs, getNoticeDefaultCondition } from '@/api/gsc/general-notice'
import { useRequest } from 'ahooks'

export const useAnnounceDefaultSearch = () => {
  const { data: res } = useRequest(() => getNoticeDefaultCondition())
  return { defaultSelectData: res?.data ?? {} }
}

export const useDefaultJobs = () => {
  const { data: res } = useRequest(() => getJobs())
  return { defaultJobs: res?.data ?? [] }
}

export const useDefaultBusinessLines = () => {
  const { data: res } = useRequest(() => getAllBusinessLines())
  return { defaultBusinessLines: res?.data ?? [] }
}
