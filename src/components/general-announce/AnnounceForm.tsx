import intl from 'react-intl-universal'
/* eslint-disable n/no-callback-literal */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { FC, memo, useCallback, useEffect, useRef, useState } from 'react'
import * as Sentry from '@sentry/react'
import Button from '@hi-ui/button'
import Card from '@hi-ui/card'
import { Form, FormHelpers, FormItem } from '@hi-ui/form'
import Grid from '@hi-ui/grid'
import Input from '@hi-ui/input'
import Switch from '@hi-ui/switch'
import Loading from '@hi-ui/loading'
import CheckSelect from '@hi-ui/check-select'
import { ArrowLeftOutlined, CloseCircleOutlined, SendOutOutlined } from '@hi-ui/icons'
import { useDebounceFn, useRequest, useSafeState } from 'ahooks'
import { useLocation, useMatch, useNavigate } from 'react-router-dom'
import { useAnnounceDefaultSearch, useDefaultJobs } from './hooks'
import { FormItemChild } from '@/components/FormItemChild'
import {
  executeNoticeCreate,
  executeNoticeUpdate,
  getAllBusinessLines,
  getAllSupplier,
} from '@/api/gsc/general-notice'
import { customMessage } from '@/utils/custom-message'
import CustomPortal from '../custom-portal'
import { AnyType, HEADER_TITLE_PORTAL, isGSCWorkbench } from '@/constants'
import './announce-form.scss'
import Select from '@hi-ui/select'
import { NOTIFY_DIMENSION } from './config'
import { ellipses } from '../ellipsis-tool'
import TextArea from '@hi-ui/textarea'
import { getSelectData } from '@/utils/select-data'
import DatePicker from '@hi-ui/date-picker'
import dayjs from 'dayjs'
import message from '@hi-ui/message'

const AnnounceForm: FC = memo(() => {
  const { Row, Col } = Grid
  const loadingRef = useRef<HTMLDivElement>(null)
  const navigate = useNavigate()
  const location = useLocation()
  const match = useMatch({
    path: '/update-announce/:id',
  })
  const isUpdate = match && match.params.id
  const firstRender = useRef<boolean>(true)
  const formRef = useRef<FormHelpers<any>>(null)
  const [editDataLoading, setEditDataLoading] = useState<boolean>(true)
  const [editId, setEditId] = useSafeState<string>('')
  const [formData, setFormData] = useState<any>({
    roles: [],
    name: '',
    content: '',
    url: '',
    remark: '',
    enable: false,
    isBusinessDimension: 0,
  })
  const { defaultJobs } = useDefaultJobs()
  const { defaultSelectData } = useAnnounceDefaultSearch()
  const rowGapStyle = { maxWidth: 280 }
  const [businessLines, setBusinessLines] = useState<AnyType[]>([])
  const [supplierSelectData, setSupplierSelectData] = useState([])

  const formatEditingData = useCallback(
    (data) => {
      const {
        id,
        roles = '',
        name = '',
        content = '',
        url = '',
        remark = '',
        enable = '',
        startTime = '',
        endTime = '',
        supplierBoSet = [],
        businessCodeBoSet = [],
        isBusinessDimension = false,
      } = data
      setEditId(id)
      const newData: any = {}
      newData.roles = roles.split(',') ?? []
      newData.name = name
      newData.content = content
      newData.url = url
      newData.remark = remark
      newData.enable = enable
      newData.startTime = startTime
      newData.endTime = endTime
      newData.suppliers = supplierBoSet?.map((item) => item.code) || []
      newData.businessCodes = businessCodeBoSet?.map((item) => item.code) || []
      newData.isBusinessDimension = isBusinessDimension ? 1 : 0
      return newData
    },
    [setEditId]
  )

  const uploadedName = useRef<string>('')

  useEffect(() => {
    if (location?.state) {
      const { rowItem } = location?.state as Record<string, any>
      if (!!isUpdate && firstRender.current && Object.keys(defaultSelectData).length !== 0) {
        // 编辑
        const formattedData: any = formatEditingData(rowItem)
        firstRender.current = false
        setFormData((prev) => {
          return {
            ...prev,
            ...formattedData,
          }
        })
        formRef.current?.setFieldsValue(formattedData)
        setEditDataLoading(false)
      }
    }
  }, [location.state, defaultSelectData, formatEditingData, isUpdate])

  useEffect(() => {
    if (location?.state) {
      const {
        rowItem: { supplierBoSet },
      } = (location?.state as Record<string, AnyType>) || {}
      setSupplierSelectData(supplierBoSet || [])
    }
  }, [location.state])

  const { loading: isNotifySaving, run: save } = useRequest(
    (data: {
      id: string
      roles: string[]
      name: string
      content: string
      url: string
      remark: string
      enable: boolean
      startTime: string
      endTime: string
    }) => executeNoticeUpdate(data),
    {
      manual: true,
      onSuccess() {
        customMessage('保存成功', 'success')
        navigate('/announce-list', {
          state: (location as any)?.state?.rowItem ?? null,
        })
      },
      onError(err) {
        Sentry.captureException(err)
        customMessage('保存失败', 'error')
      },
    }
  )

  const { loading: isAnnounceAdding, run: add } = useRequest<any, any>(
    (data: {
      roles: string[]
      name: string
      content: string
      url: string
      remark: string
      enable: boolean
      startTime: string
      endTime: string
    }) => executeNoticeCreate(data),
    {
      manual: true,
      onSuccess: () => {
        customMessage('新增通知成功', 'success')
        navigate('/announce-list', {
          state: (location as any)?.state?.rowItem ?? null,
        })
      },
      onError(err) {
        Sentry.captureException(err)
        customMessage('新增失败', 'error')
      },
    }
  )

  const onAddNotifySubmit = useCallback(() => {
    formRef.current
      ?.validate()
      .then((res) => {
        const { isBusinessDimension } = formData
        if (formData?.startTime && formData?.endTime) {
          const startTime = dayjs(formData.startTime)
          const endTime = dayjs(formData.endTime)
          if (endTime.isBefore(startTime)) {
            message.closeAll()
            message.open({
              title: intl.get('关闭时间不能早于发送时间'),
              type: 'error',
            })
            return
          }
        }
        formData.startTime = formData?.startTime
          ? dayjs(formData?.startTime).format('YYYY-MM-DD HH:mm:ss')
          : ''
        formData.endTime = formData?.endTime
          ? dayjs(formData?.endTime).format('YYYY-MM-DD HH:mm:ss')
          : ''
        const isEmployee = !!isGSCWorkbench
        const extra = isGSCWorkbench
          ? {
              isEmployee,
            }
          : {
              isEmployee,
              isBusinessDimension: isBusinessDimension === 1,
            }
        const params = {
          ...res,
          objectName: uploadedName.current,
          id: editId,
          ...extra,
          startTime: formData.startTime,
          endTime: formData.endTime,
        }
        if (!isGSCWorkbench) {
          if (isBusinessDimension === 0 && params.businessCodes) {
            delete params.businessCodes
          } else if (isBusinessDimension === 1 && params.suppliers) {
            delete params.suppliers
          }
          if (params.roles) {
            delete params.roles
          }
        }
        isUpdate ? save(params) : add(params)
      })
      .catch((err) => {
        console.error(err)
      })
  }, [formData, editId, isUpdate, save, add])

  const { run: debouncedFetch } = useDebounceFn(
    (input, resolve) => {
      return getAllSupplier({
        supplierName: input,
      }).then((res) => resolve(res?.data))
    },
    { wait: 200 }
  )

  useEffect(() => {
    if (!isGSCWorkbench) {
      const fn = async () => {
        getAllBusinessLines().then((res) => setBusinessLines(res?.data || []))
      }
      fn()
    }
  }, [])

  return (
    <>
      <CustomPortal domId={HEADER_TITLE_PORTAL}>
        <ArrowLeftOutlined className="gsc-back-icon" onClick={() => navigate('/announce-list')} />
        <span className="ml-3">{`${isUpdate ? '编辑' : '新增'}公告`}</span>
      </CustomPortal>
      <div ref={loadingRef} className="form-content-wrapper rounded-3 pt-5">
        <Loading visible={isUpdate ? editDataLoading : false}>
          <Card bordered={false} className="content-body">
            <Form
              validateTrigger="onChange"
              innerRef={formRef}
              labelPlacement="right"
              showColon={false}
              labelWidth={130}
              onValuesChange={(_, allValues) => {
                setFormData(allValues)
              }}
              initialValues={formData}
            >
              <Row gutter>
                {isGSCWorkbench ? (
                  <Col span={8}>
                    <FormItem
                      field="roles"
                      valueType="array"
                      label={ellipses(intl.get('职位'), true)}
                      rules={[
                        {
                          required: true,
                          message: intl.get('请至少选择一个职位'),
                        },
                      ]}
                    >
                      <FormItemChild className="flex items-center h-16">
                        <CheckSelect
                          data={defaultJobs}
                          fieldNames={{ id: 'code', title: 'name' }}
                          searchable
                          searchMode="filter"
                          showCheckAll
                          placeholder={intl.get('请选择职位')}
                          style={rowGapStyle}
                        />
                      </FormItemChild>
                    </FormItem>
                  </Col>
                ) : (
                  <Col span={8}>
                    <FormItem
                      field="isBusinessDimension"
                      valueType="number"
                      label={ellipses(intl.get('配置维度'), true)}
                      rules={[
                        {
                          required: true,
                          message: intl.get('请至少选择一个维度'),
                        },
                      ]}
                    >
                      <FormItemChild className="flex items-center h-16">
                        <Select
                          data={NOTIFY_DIMENSION}
                          placeholder={intl.get('请选择配置维度')}
                          style={rowGapStyle}
                        />
                      </FormItemChild>
                    </FormItem>
                  </Col>
                )}
                <Col span={8}>
                  <FormItem
                    field="name"
                    valueType="string"
                    label={ellipses(intl.get('公告栏名称'), true)}
                    rules={[
                      {
                        validator: (_rule, value, cb) => {
                          !value?.trim()
                            ? cb(intl.get('请输入公告栏名称'))
                            : value.length > 20
                              ? cb(intl.get('公告栏名称长度不能超过20'))
                              : cb()
                        },
                      },
                    ]}
                  >
                    <FormItemChild className="flex items-center h-16">
                      <Input placeholder={intl.get('请输入')} style={rowGapStyle} />
                    </FormItemChild>
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem
                    field="enable"
                    valueType="boolean"
                    label={ellipses(intl.get('公告栏状态'), true)}
                    valuePropName="checked"
                  >
                    <FormItemChild className="flex items-center h-16">
                      <Switch content={[intl.get('启用'), intl.get('禁用')]} />
                    </FormItemChild>
                  </FormItem>
                </Col>
              </Row>
              <Row gutter>
                <Col span={8}>
                  <FormItem
                    field="url"
                    valueType="string"
                    label={ellipses(intl.get('通知链接'))}
                    rules={[
                      {
                        validator: (_rule, value, cb) => {
                          if (!value) return cb()
                          // 校验是否为网址
                          const urlReg = /^(https?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w\-./?%&=]*)?$/
                          if (!urlReg.test(value)) {
                            return cb(intl.get('请输入正确的网址'))
                          }
                          if (value.length > 500) {
                            return cb(intl.get('通知链接长度不能超过500'))
                          }
                          cb()
                        },
                      },
                    ]}
                  >
                    <FormItemChild className="flex items-center h-16">
                      <Input
                        disabled={!!isUpdate}
                        placeholder={intl.get('请输入')}
                        style={rowGapStyle}
                      />
                    </FormItemChild>
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem
                    field="remark"
                    valueType="string"
                    label={ellipses(intl.get('备注'))}
                    rules={[
                      {
                        validator: (_rule, value, cb) => {
                          if (!value) cb()
                          value.length > 200 ? cb(intl.get('备注长度不能超过200')) : cb()
                        },
                      },
                    ]}
                  >
                    <FormItemChild className="flex items-center h-16">
                      <Input placeholder={intl.get('可输入备注')} style={rowGapStyle} />
                    </FormItemChild>
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem
                    field="content"
                    valueType="string"
                    label={ellipses(intl.get('通知内容'), true)}
                    rules={[
                      {
                        validator: (_rule, value, cb) => {
                          !value?.trim()
                            ? cb(intl.get('请输入通知内容'))
                            : value.length > 300
                              ? cb(intl.get('通知内容长度不能超过300'))
                              : cb()
                        },
                      },
                    ]}
                  >
                    <FormItemChild className="flex h-16 mb-9.5">
                      <TextArea
                        style={{ padding: '4px 10px', maxWidth: 395 }}
                        minRows={2}
                        maxRows={2}
                        placeholder={intl.get('请输入')}
                        appearance="line"
                      />
                    </FormItemChild>
                  </FormItem>
                </Col>
              </Row>
              <Row gutter>
                <Col span={8}>
                  <FormItem
                    field="startTime"
                    valueType="string"
                    label={ellipses(intl.get('发送时间'))}
                    rules={[
                      {
                        validator: (_rule, value, cb) => {
                          if (!value) cb()
                          const timestamp = dayjs(value, 'YYYY-MM-DD HH:mm:ss').valueOf()
                          const currentTime = new Date().getTime()
                          timestamp < currentTime ? cb(intl.get('设置时间已过期')) : cb()
                        },
                      },
                    ]}
                  >
                    <FormItemChild className="flex items-center h-16">
                      <DatePicker
                        style={rowGapStyle}
                        showTime={true}
                        format="YYYY-MM-DD HH:mm:ss"
                        minDate={new Date(new Date().getTime() - 1 * 24 * 60 * 60 * 1000)}
                      />
                    </FormItemChild>
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem
                    field="endTime"
                    valueType="string"
                    label={ellipses(intl.get('关闭时间'))}
                    rules={[
                      {
                        validator: (_rule, value, cb) => {
                          if (!value) cb()
                          const timestamp = dayjs(value, 'YYYY-MM-DD HH:mm:ss').valueOf()
                          const currentTime = new Date().getTime()
                          timestamp < currentTime ? cb(intl.get('设置时间已过期')) : cb()
                        },
                      },
                    ]}
                  >
                    <FormItemChild className="flex items-center h-16">
                      <DatePicker
                        minuteStep={30}
                        secondStep={60}
                        style={rowGapStyle}
                        showTime={true}
                        minDate={new Date(new Date().getTime() - 1 * 24 * 60 * 60 * 1000)}
                        format="YYYY-MM-DD HH:mm:ss"
                      />
                    </FormItemChild>
                  </FormItem>
                </Col>
                {!isGSCWorkbench && (
                  <>
                    {formData.isBusinessDimension === 0 && (
                      <Col span={8}>
                        <FormItem
                          field="suppliers"
                          valueType="array"
                          label={ellipses(intl.get('供应商'), true)}
                          rules={[
                            {
                              required: true,
                              message: intl.get('请至少选择一个供应商'),
                            },
                          ]}
                        >
                          <FormItemChild className="flex items-center h-16">
                            <CheckSelect
                              data={supplierSelectData as AnyType}
                              fieldNames={{ id: 'code', title: 'name' }}
                              searchable
                              searchMode="filter"
                              showCheckAll
                              placeholder={intl.get('请选择供应商')}
                              style={rowGapStyle}
                              onOpen={() => {
                                getAllSupplier({ supplierName: ' ' }).then((res) => {
                                  const { data } = res
                                  setSupplierSelectData(data)
                                })
                              }}
                              dataSource={(keyword) => {
                                return new Promise((resolve) => {
                                  return debouncedFetch(keyword, resolve)
                                })
                              }}
                            />
                          </FormItemChild>
                        </FormItem>
                      </Col>
                    )}
                    {formData.isBusinessDimension === 1 && (
                      <Col span={8}>
                        <FormItem
                          field="businessCodes"
                          valueType="array"
                          label={ellipses(intl.get('业务线'))}
                          required
                          rules={[
                            {
                              required: true,
                              message: intl.get('请至少选择一个业务线'),
                            },
                          ]}
                        >
                          <FormItemChild className="flex items-center h-16">
                            <CheckSelect
                              data={getSelectData(businessLines)}
                              fieldNames={{ id: 'code', title: 'name' }}
                              placeholder={intl.get('请选择业务线')}
                              searchable
                              searchMode="filter"
                              showCheckAll
                              style={rowGapStyle}
                            />
                          </FormItemChild>
                        </FormItem>
                      </Col>
                    )}
                  </>
                )}
              </Row>
            </Form>
          </Card>
        </Loading>
      </div>
      <div className="announce-content-footer">
        <div className="flex justify-center">
          <Button
            onClick={() =>
              navigate('/announce-list', {
                state: (location as any)?.state?.rowItem ?? null,
              })
            }
            size="md"
            type="default"
            icon={<CloseCircleOutlined />}
          >
            {intl.get('取消')}
          </Button>
          <Button
            loading={isUpdate ? isNotifySaving : isAnnounceAdding}
            onClick={onAddNotifySubmit}
            size="md"
            type="primary"
            icon={<SendOutOutlined />}
          >
            {intl.get('提交')}
          </Button>
        </div>
      </div>
    </>
  )
})

AnnounceForm.displayName = 'AnnounceForm'

export default AnnounceForm
