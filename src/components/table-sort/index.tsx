/* eslint-disable @typescript-eslint/ban-types */
import React, { memo, FC, useState, useEffect } from 'react'
import './index.scss'

const TableSort: FC<{
  code: string
  name: string
  sortCode: string
  setSort: (sortKey: string[]) => void
}> = memo(({ code, sortCode, name, setSort }) => {
  const [clickNum, setClickNum] = useState<number>(0)
  const ascPattern = new RegExp(`^${code}\\.[a-zA-Z]+\\.asc$`)
  const descPattern = new RegExp(`^${code}\\.[a-zA-Z]+\\.desc$`)
  useEffect(() => {
    if (ascPattern.test(sortCode)) {
      setClickNum(1)
    } else if (descPattern.test(sortCode)) {
      setClickNum(2)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [code, sortCode])

  return (
    <div className="table-sort-wrapper">
      {name}
      <svg
        focusable="false"
        width="18"
        height="18"
        viewBox="0 0 32 32"
        aria-hidden="true"
        style={{ userSelect: 'none', marginLeft: '2px', flexShrink: 0 }}
        onClick={() => {
          let num = clickNum + 1
          if (num > 2) num = 0
          let sortArr
          if (num === 1) sortArr = [code, 'asc']
          if (num === 2) sortArr = [code, 'desc']
          setClickNum(num)
          setSort && setSort(sortArr ?? [])
        }}
      >
        <path
          data-type="asc"
          fill={clickNum === 1 ? '#237ffa' : '#929aa6'}
          d="M16 4 L24 14 L8 14 Z"
        ></path>
        <path
          fill={clickNum === 2 ? '#237ffa' : '#929aa6'}
          d="M8 18 L24 18 L16 28 Z"
          data-type="desc"
        ></path>
      </svg>
    </div>
  )
})
TableSort.displayName = 'TableSort'
export default TableSort
