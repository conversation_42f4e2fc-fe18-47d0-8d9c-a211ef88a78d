import Button from '@hi-ui/button'
import Checkbox from '@hi-ui/checkbox'
import EllipsisTooltip from '@hi-ui/ellipsis-tooltip'
import {
  CloseOutlined,
  CloseCircleOutlined,
  SendOutOutlined,
  ArrowLeftOutlined,
} from '@hi-ui/icons'
import Input from '@hi-ui/input'
import Pagination from '@hi-ui/pagination'
import Select from '@hi-ui/select'
import Table from '@hi-ui/table'
import message from '@hi-ui/message'
import React, { useCallback, useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { addClassScene, getCategoryClass, getSelectCondition } from '@/api/gsc/todo-config'
import './index.scss'
import CustomPortal from '../custom-portal'
import { HEADER_TITLE_PORTAL } from '@/constants'
import intl from 'react-intl-universal'

const pageSizeOption = [
  { id: 10, title: '10' },
  { id: 20, title: '20' },
  { id: 50, title: '50' },
  { id: 100, title: '100' },
  { id: 200, title: '200' },
  { id: 500, title: '500' },
]

const getCheckState = (checked, all) => {
  let count = 0
  all.forEach((item) => {
    if (checked.includes(item)) {
      count = count + 1
    }
  })
  if (count === all.length) {
    return 'checkAll'
  } else if (count > 0 && count < all.length) {
    return 'semiChecked'
  } else {
    return 'none'
  }
}
interface AddPanelProps {
  categoryClassId?: string | number
}
const AddPanel: React.FC<AddPanelProps> = ({ categoryClassId }) => {
  const [flatData, setFlatData] = useState<AnyType[]>([])
  const navigate = useNavigate()
  const [keywords, setKeywords] = useState<string>('')
  const [loading, setLoading] = useState<boolean>(false)
  const [allTotal, setAllTotal] = useState<number>(0)
  const [addKeywords, setAddKeywords] = useState<string>('')
  const [pageNum, setPageNum] = useState<number>(1)
  const [addPageNum, setAddPageNum] = useState<number>(1)
  const [addedRow, setAddedRow] = useState<AnyType[]>([])
  const [allPageSize, setAllPageSize] = useState(100)
  const [addPageSize, setAddPageSize] = useState(100)
  const [isSaving, setIsSaving] = useState<boolean>(false)
  // 查询所有平铺资源
  const getAllFlatData = useCallback(async () => {
    try {
      setLoading(true)
      const res = await getSelectCondition({
        conditionType: '2',
        categoryClass: [],
        category: [],
      })
      const data = res?.data || []
      setFlatData(data)
      setAllTotal(data.length)
      setLoading(false)
    } catch (error) {
      setLoading(false)
    }
  }, [])
  useEffect(() => {
    getAllFlatData()
  }, [getAllFlatData])
  useEffect(() => {
    getCategoryClass({ categoryId: categoryClassId }).then((res) => {
      const data = res?.data?.map((item) => {
        return { name: item.categoryName, code: item.category }
      })
      setAddedRow(data || [])
    })
  }, [categoryClassId])

  const allColumns = [
    {
      title: '待办场景',
      dataKey: 'name',
      width: 200,
      render: (text) => {
        const textRender = <EllipsisTooltip>{text}</EllipsisTooltip>
        return textRender
      },
    },
    {
      title: 'code',
      dataKey: 'code',
      render: (text) => {
        const textRender = <EllipsisTooltip>{text}</EllipsisTooltip>
        return textRender
      },
    },
  ]
  const addColumns = [
    {
      title: '待办场景',
      dataKey: 'name',
      width: 170,
      render: (text) => {
        const textRender = <div className="text">{text}</div>
        return textRender
      },
    },
    {
      title: 'code',
      dataKey: 'code',
      render: (text) => {
        const textRender = <EllipsisTooltip>{text}</EllipsisTooltip>
        return textRender
      },
    },
    {
      title: '操作',
      dataKey: 'opt',
      width: 60,
      render: (text, record) => {
        const btn = (
          <Button
            size="sm"
            icon={<CloseOutlined />}
            onClick={() => {
              const _r = addedRow.filter((r) => r.code !== record?.code)
              setAddedRow(_r)
              setFlatData((pre) => {
                return [...pre, record]
              })
            }}
          />
        )
        return btn
      },
    },
  ]
  const onSubmit = useCallback(() => {
    setIsSaving(true)
    // 新增操作
    const classScenes = addedRow.map((item) => {
      return { categoryClass: item.name, category: item.code }
    })
    addClassScene({ categoryClassId, classScenes })
      .then(() => {
        setIsSaving(false)
        message.open({ type: 'success', title: '编辑成功！' })
        navigate('/outer-todo-config/list')
      })
      .finally(() => {
        setIsSaving(false)
      })
  }, [addedRow, categoryClassId, navigate])
  return (
    <div className="add-panel-container">
      <CustomPortal domId={HEADER_TITLE_PORTAL}>
        <ArrowLeftOutlined
          className="gsc-back-icon"
          onClick={() => navigate('/outer-todo-config/list')}
        />
        <span className="ml-3">{intl.get('编辑待办分类')}</span>
      </CustomPortal>
      <div className="left">
        <div className="header">
          <span>全部</span>
          <div>
            <span style={{ verticalAlign: 'bottom', marginLeft: 10 }}>{`${
              addedRow.length || 0
            }/${allTotal}`}</span>
          </div>
        </div>
        <div className="search">
          <Input
            appearance="underline"
            placeholder="搜索"
            clearable
            value={keywords}
            onChange={(e) => {
              setKeywords(e.target.value)
            }}
          />
        </div>

        <div style={{ height: 'calc(100% - 60px)' }} className="inner">
          <Table
            columns={allColumns}
            data={flatData
              .slice((pageNum - 1) * allPageSize, pageNum * allPageSize)
              .filter((r) => r?.name?.includes(keywords) || r?.code?.includes(keywords))}
            maxHeight={380}
            striped
            fieldKey="code"
            size="sm"
            loading={loading}
            rowSelection={{
              selectedRowKeys: addedRow.map((item) => item.code),
              onChange: (keys, targetRow: AnyType, shouldChecked) => {
                const _rows = shouldChecked
                  ? addedRow.concat(targetRow)
                  : addedRow.filter((r) => r.code !== targetRow.code)
                setAddedRow(_rows)
              },
              getCheckboxConfig: (rowData: {
                permissionStateEnum?: number
                permissionAdminAdminStateEnum?: number
              }) => ({
                disabled:
                  rowData?.permissionStateEnum === 1 ||
                  rowData?.permissionAdminAdminStateEnum === 0,
              }),
            }}
          />
        </div>

        <div className="footer">
          <Checkbox
            checked={
              getCheckState(
                addedRow.map((item) => item.code),
                flatData.map((item) => item.code)
              ) === 'checkAll'
            }
            indeterminate={
              getCheckState(
                addedRow.map((item) => item.code),
                flatData.map((item) => item.code)
              ) === 'semiChecked'
            }
            onChange={(e) => {
              const _rows = e.target.checked
                ? addedRow?.concat(
                    flatData?.filter(
                      (item) =>
                        item?.permissionStateEnum !== 1 &&
                        item?.permissionAdminAdminStateEnum !== 0 &&
                        !addedRow?.map((ar) => ar?.code).includes(item?.code)
                    )
                  )
                : addedRow?.filter((r) => {
                    return !flatData?.map((item) => item?.code).includes(r?.code)
                  })
              setAddedRow(_rows)
            }}
          >
            全选
          </Checkbox>
          <div>
            <Pagination
              type="shrink"
              total={flatData.length}
              style={{ verticalAlign: 'bottom' }}
              pageSize={allPageSize}
              current={pageNum}
              onChange={(current) => {
                setPageNum(current)
              }}
            />
            <Select
              clearable={false}
              style={{ width: 80, marginLeft: 12 }}
              value={allPageSize}
              data={pageSizeOption}
              onChange={(value) => {
                setAllPageSize(value as number)
                setPageNum(1)
                setPageNum(1)
              }}
            />
          </div>
        </div>
      </div>
      <div className="right">
        <div className="header">
          <span>已选</span>
          <span>{addedRow.length || 0}</span>
        </div>
        <div className="search">
          <Input
            clearable
            appearance="underline"
            placeholder="搜索"
            value={addKeywords}
            onChange={(e) => {
              setAddKeywords(e.target.value)
              setAddPageNum(1)
            }}
          />
        </div>

        <div className="inner">
          <Table
            columns={addColumns}
            data={addedRow
              .slice((addPageNum - 1) * addPageSize, addPageNum * addPageSize)
              .filter((r) => r?.name?.includes(addKeywords) || r?.code?.includes(addKeywords))}
            maxHeight={380}
            striped
            fieldKey="code"
            size="sm"
          />
        </div>

        <div className="footer" style={{ justifyContent: 'flex-end' }}>
          <Pagination
            style={{ verticalAlign: 'bottom' }}
            type="shrink"
            total={addedRow.length}
            pageSize={addPageSize}
            current={addPageNum}
            onChange={(current) => {
              setAddPageNum(current)
            }}
          />
          <Select
            clearable={false}
            style={{ width: 80, marginLeft: 12 }}
            value={addPageSize}
            data={pageSizeOption}
            onChange={(value) => {
              setAddPageSize(value as number)
              setAddPageNum(1)
            }}
          />
        </div>
      </div>
      <div className="notify-content-footer">
        <div className="flex justify-center">
          <Button
            onClick={() => navigate('/outer-todo-config/list')}
            size="md"
            type="default"
            icon={<CloseCircleOutlined />}
          >
            取消
          </Button>
          <Button
            loading={isSaving}
            onClick={onSubmit}
            size="md"
            type="primary"
            icon={<SendOutOutlined />}
          >
            提交
          </Button>
        </div>
      </div>
    </div>
  )
}
export default AddPanel
