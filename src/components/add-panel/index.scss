.add-panel-container {
  position: relative;
  height: 100%;
  border: 1px solid #dfe2ee;
  display: flex;
  border-radius: 4px;
  box-sizing: border-box;
  padding-bottom: 60px;

  .hi-v4-table-header {
    display: none;
  }

  .hi-v4-table-cell {
    background-color: #f5f7fa;
  }

  .hi-v4-table-cell:not(.hi-v4-table__selection-col) {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .hi-v4-table-row--striped > .hi-v4-table-cell {
    background-color: #fff;
  }

  .header {
    padding: 10px;
    border-bottom: 1px solid #dfe2ee;
    display: flex;
    justify-content: space-between;
    background-color: #f5f7fa;
  }

  .search {
    padding: 10px 10px 0;
    display: flex;
    align-items: center;
  }

  .left {
    width: 50%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #dfe2ee;
  }

  .right {
    width: 50%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }

  .footer {
    border-top: 1px solid #dfe2ee;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .inner {
    flex: 1;
    overflow: auto;
    padding: 16px 10px;
  }

  .text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 100%;
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .disable-text {
    color: #ccc;
  }
}
