import React, { forwardRef, useCallback, useRef, useEffect, useMemo, Fragment } from 'react'
import { useSafeState } from 'ahooks'
import { cx } from '@hi-ui/classname'
import { useLatestCallback } from '@hi-ui/use-latest'
import { useLocaleContext } from '@hi-ui/core'
import { Drawer } from '@hi-ui/drawer'
import { Button } from '@hi-ui/button'
import { useUncontrolledState } from '@hi-ui/use-uncontrolled-state'
import { isEmpty } from 'lodash'
import { SettingDrawerProps, TableColumnItem, _prefix } from './config'
import { SettingContent } from './SettingContent'
import './style.scss'
/**
 * 设置抽屉
 */
export const SettingDrawer = forwardRef<HTMLDivElement | null, SettingDrawerProps>(
  (
    {
      prefixCls = _prefix,
      className,
      visible: visibleProp,
      onClose,
      columns: columnsProp = [],
      hiddenColKeys: hiddenKeysBeforeVerify = [],
      sortedColKeys: sortedKeysBeforeVerify = [],
      onSetColKeysChange,
      checkDisabledColKeys = [],
      drawerProps,
    },
    ref
  ) => {
    const i18n = useLocaleContext()
    const cls = cx(`${prefixCls}-drawer`, className)
    const [hiddenColKeys, setHiddenColKeys] = useSafeState<string[]>(hiddenKeysBeforeVerify)
    const [sortedColKeys, setSortedColKeys] = useSafeState<string[][]>(sortedKeysBeforeVerify)

    useEffect(() => {
      setHiddenColKeys(hiddenKeysBeforeVerify)
    }, [hiddenKeysBeforeVerify, setHiddenColKeys])
    useEffect(() => {
      setSortedColKeys(sortedKeysBeforeVerify)
    }, [sortedKeysBeforeVerify, setSortedColKeys])

    const columnList = useMemo(() => {
      const newColumnList = [...columnsProp].map((key, index) => {
        return (key || []).sort((a, b) => {
          const indexA = (sortedColKeys[index] || []).indexOf(a.dataKey)
          const indexB = (sortedColKeys[index] || []).indexOf(b.dataKey)
          if (indexA < indexB) {
            return -1
          } else if (indexA > indexB) {
            return 1
          } else {
            return 0
          }
        })
      })
      return newColumnList
    }, [columnsProp, sortedColKeys])

    const resetLatest = useLatestCallback(() => {
      setHiddenColKeys(hiddenKeysBeforeVerify)
      setSortedColKeys(sortedKeysBeforeVerify)
    })
    const onHiddenColKeysChange = useCallback(
      (hiddenColKeys: string[]) => {
        setHiddenColKeys(hiddenColKeys)
      },
      [setHiddenColKeys]
    )
    const [visible, setVisible] = useUncontrolledState<boolean>(false, visibleProp, onClose)
    // 当 visible 由 false 变为 true 时触发
    const prevShowPopperRef = useRef(!visible)
    useEffect(() => {
      if (!prevShowPopperRef.current && visible) {
        resetLatest()
      }
      prevShowPopperRef.current = visible
    }, [visible, resetLatest])

    const onConfirm = () => {
      let newSortedColKeys: string[][] = [...sortedColKeys]
      if (isEmpty(newSortedColKeys)) {
        newSortedColKeys = columnsProp.map((key: TableColumnItem[]) =>
          key.map(({ dataKey }) => dataKey)
        )
      } else {
        newSortedColKeys = columnsProp.map((key: TableColumnItem[], index: number) =>
          key
            .map(({ dataKey }) => dataKey)
            .sort((a, b) => sortedColKeys[index].indexOf(a) - sortedColKeys[index].indexOf(b))
        )
      }
      onSetColKeysChange?.(newSortedColKeys, hiddenColKeys)
      setVisible(false)
    }
    return (
      <Drawer
        ref={ref}
        className={cls}
        title={i18n.get('table.fieldExplorer')}
        visible={visibleProp}
        onClose={() => onClose?.()}
        width={304}
        footer={
          <div className={`${prefixCls}__btn-group`}>
            <Button key="reset" className={`${prefixCls}__btn-cancel`} onClick={resetLatest}>
              {i18n.get('table.reset')}
            </Button>
            <Button
              key="confirm"
              className={`${prefixCls}__btn-confirm`}
              onClick={onConfirm}
              type="primary"
            >
              {i18n.get('table.confirm')}
            </Button>
          </div>
        }
        {...drawerProps}
      >
        {columnList.map((key: TableColumnItem[], index: number) => (
          <Fragment key={index}>
            {index !== 0 ? <div className="line" /> : null}
            <SettingContent
              key={index}
              columns={key}
              hiddenColKeys={hiddenColKeys}
              sortedColKeys={sortedColKeys[index] || []}
              checkDisabledColKeys={checkDisabledColKeys}
              onHiddenColKeysChange={onHiddenColKeysChange}
              onSortedColKeysChange={(sortedColKeys) => {
                setSortedColKeys((prev: string[][]) => {
                  const newSorted = [...prev]
                  newSorted[index] = [...sortedColKeys]
                  return newSorted
                })
              }}
            />
          </Fragment>
        ))}
      </Drawer>
    )
  }
)
SettingDrawer.displayName = 'SettingDrawer'
