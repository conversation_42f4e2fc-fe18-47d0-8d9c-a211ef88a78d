import React, { memo, useMemo } from 'react'
import { cx } from '@hi-ui/classname'
import { useDrag, useDrop } from '@hi-ui/use-drag-sorter'
import { Checkbox } from '@hi-ui/checkbox'
import { MoveOutlined } from '@hi-ui/icons'
import { runIfFunc } from '@hi-ui/func-utils'
import { SettingItemProps, SettingContentProps, TableColumnItem, _prefix } from './config'

const SettingItem = memo<SettingItemProps>(
  ({
    prefixCls = _prefix,
    column,
    cacheHiddenColKeys = [],
    setCacheHiddenColKeys,
    dropProps,
    index,
    checkDisabled,
  }) => {
    const { dataKey, title } = column
    const { dragging, direction, getDragTriggerProps, getDropTriggerProps } = useDrag({
      ...dropProps,
      item: column,
      index,
      idFieldName: 'dataKey',
      dataTransferKey: 'table-setting-data',
    })
    return (
      <div
        className={cx(`${prefixCls}-item`, {
          [`${prefixCls}-item--dragging`]: dragging,
          [`${prefixCls}-item--direction-${direction}`]: direction,
        })}
        {...getDragTriggerProps()}
        {...getDropTriggerProps()}
      >
        <div className={`${prefixCls}-item__wrap`}>
          <Checkbox
            disabled={checkDisabled}
            checked={!cacheHiddenColKeys.includes(dataKey)}
            onChange={(evt) => {
              const shouldChecked = evt.target.checked
              const nextCacheHiddenColKeys = shouldChecked
                ? cacheHiddenColKeys.filter((col: string) => col !== dataKey)
                : cacheHiddenColKeys.concat(dataKey)

              setCacheHiddenColKeys(nextCacheHiddenColKeys)
            }}
          >
            <span>{runIfFunc(title)}</span>
          </Checkbox>
          <MoveOutlined />
        </div>
      </div>
    )
  }
)
SettingItem.displayName = 'SettingItem'

export const SettingContent = memo<SettingContentProps>(
  ({
    prefixCls = _prefix,
    columns: columnsProp = [],
    hiddenColKeys: hiddenColKeysPropBeforeVerify = [],
    onHiddenColKeysChange,
    sortedColKeys: sortedColKeysPropBeforeVerify = [],
    onSortedColKeysChange,
    checkDisabledColKeys = [],
  }) => {
    const cacheSortedCols = useMemo(() => {
      return [...columnsProp].sort(
        (a: TableColumnItem, b: TableColumnItem) =>
          sortedColKeysPropBeforeVerify.indexOf(a?.dataKey) -
          sortedColKeysPropBeforeVerify.indexOf(b?.dataKey)
      )
    }, [columnsProp, sortedColKeysPropBeforeVerify])
    const dropProps = useDrop({
      draggable: true,
      idFieldName: 'dataKey',
      onSwap: async (dragItem, dropItem, direction, info) => {
        const nextCacheSortCols = [...cacheSortedCols]
        const [removed] = nextCacheSortCols.splice(info.dragIndex, 1)
        nextCacheSortCols.splice(info.dropIndex, 0, removed)
        onSortedColKeysChange?.(nextCacheSortCols.map((key) => key.dataKey))
        return true
      },
    })

    return (
      <div className={`${prefixCls}__content`}>
        {cacheSortedCols.map((col: TableColumnItem, index: number) => {
          return (
            <SettingItem
              key={col.dataKey}
              prefixCls={prefixCls}
              column={col}
              index={index}
              dropProps={dropProps}
              cacheHiddenColKeys={hiddenColKeysPropBeforeVerify}
              setCacheHiddenColKeys={onHiddenColKeysChange}
              checkDisabled={checkDisabledColKeys.includes(col.dataKey)}
            />
          )
        })}
      </div>
    )
  }
)
SettingContent.displayName = 'SettingContent'
