import { HiBaseHTMLProps } from '@hi-ui/core'
import { DrawerProps } from '@hi-ui/drawer'
import { UseDropPropsReturn } from '@hi-ui/use-drag-sorter'
import { getPrefixCls } from '@hi-ui/classname'

export interface TableColumnItem {
  title: React.ReactNode
  dataKey: string
}
export interface SettingDrawerProps extends HiBaseHTMLProps<'div'> {
  prefixCls?: string
  visible?: boolean
  columns?: TableColumnItem[][]
  onClose?: () => void
  checkDisabledColKeys?: string[]
  onSetColKeysChange?: (sortedColKeys: string[][], hiddenColKeys: string[]) => void
  hiddenColKeys?: string[]
  sortedColKeys?: string[][]
  drawerProps?: Omit<DrawerProps, 'className'>
}
export interface SettingContentProps extends HiBaseHTMLProps<'div'> {
  prefixCls?: string
  visible?: boolean
  columns?: TableColumnItem[]
  checkDisabledColKeys?: string[]
  hiddenColKeys?: string[]
  onHiddenColKeysChange: (hiddenColKeys: string[]) => void
  sortedColKeys?: string[]
  onSortedColKeysChange: (sortedColKeys: string[]) => void
}
export interface SettingItemProps extends HiBaseHTMLProps<'div'> {
  prefixCls?: string
  column: TableColumnItem
  cacheHiddenColKeys: string[]
  setCacheHiddenColKeys: (hiddenColKeys: string[]) => void
  index: number
  checkDisabled?: boolean
  dropProps: UseDropPropsReturn
}
export const _prefix = getPrefixCls('setting')
