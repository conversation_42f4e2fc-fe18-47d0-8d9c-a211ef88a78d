/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react'
import { Chart, registerShape } from '@antv/g2'
import { useSafeState, useUnmount } from 'ahooks'
import { customMessage } from '@/utils/custom-message'
import { PIE_COLORS } from './config'

import './move-price-trend.scss'

const MovePriceTrend = memo<{
  data: any[]
  xLineName: string
  title: string
  isPercent: boolean
  allY: string[]
}>(({ data, xLineName, title, isPercent, allY }) => {
  const firstRenderLine = useRef<boolean>(true)
  const antVLineContainer = useRef<HTMLDivElement>(null)
  const [chart, setChart] = useSafeState<any>(null)

  const processTooltipItems = useCallback((items) => {
    const hoverItem = [...items]?.[0]
    hoverItem.value = `${hoverItem.value}%` || '0%'
    return [hoverItem]
  }, [])

  const titleTip = useMemo(
    () => (isPercent && data?.[0]?.cost !== 0 ? '相对值' : '绝对值'),
    [isPercent, data]
  )

  const tooltipOption = useMemo(
    () =>
      isPercent
        ? {
            customItems: (items) => {
              return processTooltipItems(items)
            },
          }
        : {},
    [isPercent, processTooltipItems]
  )

  const renderDataMemo = useMemo(
    () =>
      data.reduce((a, b) => {
        const currentObj: Record<string, string | number> = {}
        const { date, movingAvgPriceNodeDtoMap } = b || {}
        Object.keys(movingAvgPriceNodeDtoMap)?.forEach((key) => {
          currentObj[key] = movingAvgPriceNodeDtoMap?.[key]?.cost
          currentObj[`isPoint${key}`] = movingAvgPriceNodeDtoMap?.[key]?.isPoint
        })
        currentObj.date = date
        a.push(currentObj)
        return a
      }, []),
    [data]
  )

  const maxY = useMemo(() => {
    const max = renderDataMemo.reduce((a, b) => {
      Object.keys(b).forEach((key) => {
        if (key !== 'date' && !key.includes('isPoint')) {
          a = a > b[key] ? a : b[key]
        }
      })
      return a
    }, 0)
    return max * 1.1
  }, [renderDataMemo])

  const minOption = useMemo(() => (isPercent ? {} : { min: 0, max: maxY }), [isPercent, maxY])

  const settledExtraFlag = useMemo(() => data?.[0]?.type === '1', [data])

  const settledLinePoints = useMemo(
    () =>
      settledExtraFlag
        ? renderDataMemo?.reduce((a, b) => {
            Object.keys(b).forEach((key) => {
              if (key.startsWith('isPoint') && b[key] === 1) {
                const val = key.slice(7)
                const prev = a?.[val] || []
                a[val] = [...prev, b.date]
              }
            })
            return a
          }, {})
        : {},
    [renderDataMemo, settledExtraFlag]
  )

  useEffect(() => {
    if (!antVLineContainer.current) return
    const res = new Chart({
      container: antVLineContainer.current as HTMLElement,
      autoFit: true,
      padding: [10, 0, 20, 60],
    })
    setChart(res)
  }, [antVLineContainer, setChart])

  useEffect(() => {
    if (!chart || !data?.length) return
    let renderData = renderDataMemo
    if (isPercent) {
      const { cost: firstCost } = data?.[0]?.movingAvgPriceNodeDtoMap?.[allY[0]] || {}
      if (!firstCost) {
        customMessage('首个价格为0，暂无法切换相对变化曲线', 'info')
      } else {
        renderData = renderData?.map((item) => ({
          ...item,
          [allY[0]]: Number(((item?.[allY[0]] / firstCost - 1) * 100).toFixed(2)),
        }))
      }
    }
    if (firstRenderLine.current) {
      chart.data(renderData)
      firstRenderLine.current = false
    } else {
      chart.clear()
      chart.data(renderData)
    }
    chart.coordinate() // 默认创建直角坐标系

    const scaleObj = allY?.reduce((a, b) => {
      a[b] = {
        sync: true,
        nice: true,
        ...minOption,
      }
      return a
    }, {})

    chart.scale({
      [xLineName]: {
        sync: true,
        nice: true,
      },
      ...scaleObj,
    })

    allY?.forEach((yLine) => {
      if (!settledExtraFlag || allY.length > 1) return // 预估不需要算点
      registerShape('point', 'draw-point', {
        draw(cfg, container) {
          const currentData = cfg.data as Record<string, string[] | number[]>
          const point = { x: cfg.x, y: cfg.y }
          const group = container.addGroup()
          if (settledLinePoints[yLine]?.includes(currentData?.date)) {
            group.addShape('circle', {
              attrs: {
                x: point.x as number,
                y: point.y as number,
                r: 3,
                fill: '#1890ff',
                opacity: 1,
              },
            })
          }
          return group
        },
      })
      chart.point().position(`${xLineName}*${yLine}`).shape('draw-point')
    })

    allY
      ?.sort((a, b) => renderData?.[0]?.[b] - renderData?.[0]?.[a])
      ?.forEach((yLine, idx) => {
        if (idx !== 0) {
          chart.axis(yLine, false)
        }
        chart
          .line()
          .position(`${xLineName}*${yLine}`)
          .shape('smooth')
          .color(PIE_COLORS[idx % 10])
      })

    if (isPercent) {
      allY?.forEach((yLine) => {
        chart.axis([yLine], {
          label: {
            formatter: (val) => {
              return val + '%'
            },
          },
        })
      })
    } else {
      allY?.forEach((yLine) => {
        chart.axis([yLine], {
          label: {
            formatter: (val) => val,
          },
        })
      })
    }
    chart.tooltip({
      showCrosshairs: true, // 展示 Tooltip 辅助线
      shared: true, // 共享折线数据展示
      ...tooltipOption,
    })
    chart.forceFit()
    chart.render()
  }, [
    allY,
    chart,
    data,
    isPercent,
    minOption,
    renderDataMemo,
    settledExtraFlag,
    settledLinePoints,
    tooltipOption,
    xLineName,
  ])

  useUnmount(() => chart.clear())

  return (
    <div className="antv-line-wrapper" ref={antVLineContainer}>
      <div className="antv-content">{`${title}（${titleTip}）`}</div>
    </div>
  )
})

MovePriceTrend.displayName = 'MovePriceTrend'

export default MovePriceTrend
