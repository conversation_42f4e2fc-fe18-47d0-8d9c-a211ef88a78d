import intl from 'react-intl-universal'
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { memo, useEffect, useRef } from 'react'
import { Chart } from '@antv/g2'
import { useSafeState, useUnmount } from 'ahooks'
import { LINE_X_TITLE, LinePropsType } from './config'

import './antv-line.scss'

const AntVLine = memo<{
  data: LinePropsType[]
}>(({ data }) => {
  const firstRenderLine = useRef<boolean>(true)
  const antVLineContainer = useRef<HTMLDivElement>(null)
  const [chart, setChart] = useSafeState<any>(null)

  useEffect(() => {
    if (!antVLineContainer.current) return
    const res: any = new Chart({
      container: antVLineContainer.current as HTMLElement,
      autoFit: true,
      height: 500,
    })
    setChart(res)
  }, [antVLineContainer, setChart])

  useEffect(() => {
    if (!chart || !data?.length) return
    const finalInputData = data?.map(({ zVersion, ...left }) => {
      const regex = /WK(\d+)/
      const derivedVersion = `W${zVersion.match(regex)?.[1]}`
      left[`${LINE_X_TITLE}`] = left.zPrice
      return { ...left, zVersion: derivedVersion }
    })
    if (firstRenderLine.current) {
      chart.data(finalInputData)
      firstRenderLine.current = false
    } else {
      chart.clear()
      chart.data(finalInputData)
    }
    chart.scale({
      zVersion: {
        tickCount: 10,
        sync: true,
        nice: true,
      },
      [LINE_X_TITLE]: {
        sync: true,
        nice: true,
        min: 0,
      },
    })
    chart.axis('zVersion', {
      label: {
        formatter: (text) => text,
      },
    })
    chart.line().position(`zVersion*${LINE_X_TITLE}`).shape('smooth')
    const arr = finalInputData.filter((i) => i.zPhase) || []
    arr.forEach((item) => {
      chart.annotation().dataMarker({
        position: [`${item.zVersion}`, `${item[`${LINE_X_TITLE}`]}`],
        text: {
          content: `${item.zPhase}`,
          style: { textAlign: 'left', fontSize: 14, color: '#929AA6' },
        },
        line: {
          length: 0,
        },
        point: {
          style: {
            stroke: '#4A9EFF',
          },
        },
        direction: 'downward',
      })
    })
    chart.tooltip({
      showCrosshairs: true,
    })
    chart.render()
  }, [chart, data])

  useUnmount(() => chart.clear())

  return (
    <div className="new-project-cost-antv-line-wrapper" ref={antVLineContainer}>
      <div className="antv-content">{intl.get('成本阶段性对比')}</div>
    </div>
  )
})

AntVLine.displayName = 'AntVLine'

export default AntVLine
