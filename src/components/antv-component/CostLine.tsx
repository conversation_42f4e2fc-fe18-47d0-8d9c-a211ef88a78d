import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react'
import { useSize, useUnmount } from 'ahooks'
import { Chart, registerInteraction } from '@antv/g2'
import './cost-line.scss'
import { findMonth, MILESTONE_MAP } from '@/views/gsc/cost/all-life-cost/constants'
import dayjs from 'dayjs'
import { useAllLifeCost } from '@/hooks/useAllLifeCost'

interface LineProps {
  fcstMonth?: string
  cost?: number | string
  recordVersion?: string
  projectStepType?: number
}

registerInteraction('legend-visible-filter', {
  showEnable: [
    { trigger: 'legend-item:mouseenter', action: 'cursor:pointer' },
    { trigger: 'legend-item:mouseleave', action: 'cursor:default' },
  ],
  start: [
    { trigger: 'legend-item:click', action: 'list-unchecked:toggle' },
    { trigger: 'legend-item:click', action: 'element-filter:filter' },
  ],
})

const CostLine = memo<{
  data: LineProps[]
  x: string
  y: string
  diffKey: string
  chartColor: Record<string, string>
  maxYValue: number
  minYValue: number
}>(({ data, x, y, diffKey, chartColor, minYValue, maxYValue }) => {
  const { filter } = useAllLifeCost()
  const firstRender = useRef<boolean>(true)
  const antVLineContainer = useRef<HTMLDivElement>(null)
  const chart = useRef<Chart | null>(null)
  const size = useSize(antVLineContainer)
  const chartRegionRef = useRef<AnyType[]>([])

  const chartRegion = useMemo(() => {
    const singleLine = data || []
    const allMonthsSet = new Set<string>()
    singleLine.forEach((item) => {
      const x = item.fcstMonth
      if (!allMonthsSet.has(x as string) && !isNaN(x as unknown as AnyType)) {
        allMonthsSet.add(x as string)
      }
    })
    const allMonths = Array.from(allMonthsSet)
    const currentMonth = dayjs().format('YYYYMM')
    const dynamicX = singleLine.find((item) => item.fcstMonth === currentMonth) ? currentMonth : ''
    const reverseSingleLine = [...singleLine].reverse()

    const temp1 = findMonth(reverseSingleLine, 1)
    const temp2 = findMonth(reverseSingleLine, 2)

    const x1 = findMonth(singleLine, 0)
    const x2 = findMonth(reverseSingleLine, 0)
    const x3 = x2 || findMonth(singleLine, 1)
    const x4 = temp2 ? temp1 : dynamicX || temp1
    const x5 = x4 || x2 || findMonth(singleLine, 2)
    const x6 = dynamicX || temp2
    const x7 = dynamicX || x4 || x2
    const x8 = allMonths[allMonths.length - 1]
    const sArr = (filter.milestone as string[]).map((item) => Number(MILESTONE_MAP[item]) - 1) || []
    sArr.sort((a, b) => a - b)

    const res = [] as AnyType
    ;(
      [
        { start: x1, end: x2 },
        { start: x3, end: x4 },
        { start: x5, end: x6 },
        { start: x7, end: x8 },
      ] as AnyType
    ).forEach((item, index) => {
      const idx = res.findIndex((i) => i.start === item.start)
      if (sArr.includes(index) && item.start && item.end && idx === -1) {
        res.push(item)
      } else if (idx !== -1) {
        res.splice(idx, 1)
        res.push(item)
      }
    })

    const firstX = singleLine?.[0]?.fcstMonth

    // 如果没有数据，从第一个点连到最后一个点
    if (res.length === 0 && firstX && x8) {
      res.push({ start: firstX, end: x8 })
    }

    // 如果数据长度为 2，将第一个点的 end 设置为第二个点的 start
    if (res.length === 2) {
      res[0].end = res?.[1]?.start
    }

    // 如果数据长度为 3，将第二个点的 end 设置为当前月份
    if (res.length === 3 && res[2].start === currentMonth) {
      res[1].end = currentMonth
    }

    return res
  }, [data, filter.milestone])

  useEffect(() => {
    chartRegionRef.current = chartRegion
  }, [chartRegion])

  const createChart = useCallback(() => {
    if (chart.current) {
      chart.current.destroy()
    }

    if (!antVLineContainer.current) return
    chart.current = new Chart({
      container: antVLineContainer.current as HTMLElement,
      autoFit: true,
      padding: 'auto',
    })

    const finalInputData = data
    if (firstRender.current) {
      chart.current.data(finalInputData)
      firstRender.current = false
    } else {
      chart.current.clear()
      chart.current.changeData(finalInputData)
    }
    chart.current.forceFit()
    chart.current.axis(x, {
      label: {
        autoRotate: true, // 自动旋转标签
      },
    })
    chart.current.scale({
      [x]: {
        sync: true,
        nice: true,
        range: finalInputData.length !== 1 ? [0.01, 0.97] : undefined,
        min: 0,
      },
      [y]: {
        sync: true,
        nice: true,
        min: minYValue || 0,
        max: maxYValue || 100,
      },
    })
    chart.current.tooltip({
      showCrosshairs: true,
      shared: true,
    })
    chartRegionRef.current.forEach(({ start, end }, index) => {
      chart.current?.annotation().region({
        start: [start as string, 'min'],
        end: [end as string, 'max'],
        style: {
          fill: chartColor[index + 1],
          fillOpacity: 0.999, // 1显示的不是指定颜色
        },
      })
    })

    chart.current.line().position(`${x}*${y}`).color(`${diffKey}`).shape('smooth').label('cost')
    chart.current.point().position(`${x}*${y}`).color(`${diffKey}`).shape('circle').label('cost')
    chart.current.legend({
      position: 'top',
      marker: { symbol: 'hyphen' },
    })

    chart.current.removeInteraction('legend-filter')
    chart.current.interaction('legend-visible-filter')
    chart.current.render()
  }, [chartColor, data, diffKey, maxYValue, minYValue, x, y])

  useEffect(() => {
    chart?.current && chart?.current.forceFit()
  }, [chart, size?.width])

  useUnmount(() => {
    chart?.current && chart?.current.clear()
  })

  useEffect(() => {
    createChart()
  }, [createChart])

  return <div className="cost-line__container" ref={antVLineContainer}></div>
})
CostLine.displayName = 'CostLine'
export default CostLine
