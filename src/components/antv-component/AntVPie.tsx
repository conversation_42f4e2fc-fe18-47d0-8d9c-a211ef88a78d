import intl from 'react-intl-universal'
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { memo, useEffect, useRef } from 'react'
import { Chart, Util } from '@antv/g2'
import { useSafeState, useUnmount } from 'ahooks'
import { PIE_COLORS, PiePropsType } from './config'

import './antv-pie.scss'

const AntVPie = memo<{
  data: PiePropsType[]
  allCost: number
  percentType: boolean
}>(({ data, allCost, percentType }) => {
  const container = useRef<HTMLDivElement>(null)
  const [chart, setChart] = useSafeState<any>(null)
  const firstRenderPie = useRef<boolean>(true)

  useEffect(() => {
    if (!container.current) return
    const res: any = new Chart({
      container: container.current as HTMLElement,
      autoFit: true,
      height: 500,
    })
    setChart(res)
  }, [container, setChart])

  useEffect(() => {
    if (!chart || !data?.length) return
    if (firstRenderPie.current) {
      chart.data(data)
      firstRenderPie.current = false
    } else {
      chart.clear()
      chart.data(data)
    }
    chart.coordinate('theta', {
      radius: 0.8,
    })
    chart.tooltip({
      showMarkers: true,
      title: intl.get('物料成本（USD）'),
    })
    chart
      .interval()
      .adjust('stack')
      .position('value')
      .color('type', PIE_COLORS)
      .style({ opacity: 0.4 })
      .state({
        active: {
          style: (element) => {
            const shape = element.shape
            return {
              matrix: Util.zoom(shape, 1.1),
            }
          },
        },
      })
      .label('name', () => {
        return {
          content: (obj) => {
            if (!percentType) {
              return obj.value
            } else {
              return ((obj.value / allCost) * 100).toFixed(2) + '%'
            }
          },
          offset: 12,
          style: {
            fill: '#1f2733',
          },
        }
      })
    chart.interaction('tooltip')
    chart.legend({
      position: 'bottom',
      flipPage: true,
      marker: { symbol: 'square' },
    })
    chart.render()
  }, [chart, data, allCost, percentType])

  useUnmount(() => chart.clear())

  return (
    <div className="antv-pie-wrapper" ref={container}>
      <span className="pie-content">{intl.get('成本TOP10')}</span>
    </div>
  )
})

AntVPie.displayName = 'AntVPie'

export default AntVPie
