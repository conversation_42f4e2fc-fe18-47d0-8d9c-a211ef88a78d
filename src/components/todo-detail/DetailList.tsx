import React, { FC, memo, useMemo } from 'react'
import Table, { TableColumnItem } from '@hi-ui/table'
import Tooltip from '@hi-ui/tooltip'
import { useSafeState } from 'ahooks'
import { TODO_STATE_NAME_MAP } from './config'
import { gscTrack } from '@/utils/gscTrack-help'
import { ellipses } from '../ellipsis-tool'
import intl from 'react-intl-universal'

const DetailList: FC<{
  paginationState
  detailList
  detailTableLoading: boolean
}> = memo(({ paginationState, detailList, detailTableLoading }) => {
  const [dynamicCols, setDynamicCols] = useSafeState<string[]>([])
  const tableColumnRender = (text: string, textWidth: number) => {
    if (!text || text === '0' || text === 'null') {
      return '-'
    }
    if (text.length * 14 + 32 > textWidth) {
      return (
        <Tooltip title={<p className="break-words flex justify-center items-center">{text}</p>}>
          <div className="truncate" style={{ maxWidth: `${textWidth}px` }}>
            {text}
          </div>
        </Tooltip>
      )
    }
    return <div className="whitespace-nowrap">{text}</div>
  }

  const derivedList = useMemo(() => {
    const dynamicColsArr = detailList?.[0]?.summary?.items?.map((i) => `${i.label}`)
    setDynamicCols(Array.from(new Set(dynamicColsArr)))
    return detailList?.reduce((a, b) => {
      const {
        deptLevel234Name = '-',
        categoryName = '-',
        concatName = '-',
        startTime = '-',
        status = '-',
        deleteReason = '-',
        taskId = '-',
        purchaseOrg = '-',
        stockOrg = '-',
        id,
        endTime = '-',
      } = b ?? {}
      const curData: Record<string, string> = {
        deptLevel234Name,
        categoryName,
        concatName,
        startTime,
        status,
        deleteReason,
        taskId,
        purchaseOrg,
        stockOrg,
        id,
        endTime,
      }
      let dynamicObj = {}
      if (b?.summary?.items) {
        dynamicObj = Object.assign(
          {},
          ...b?.summary?.items?.map((i) => {
            return {
              [`${i.label}`]: i?.value ?? '-',
            }
          })
        )
      } else {
        dynamicColsArr?.forEach((i) => {
          dynamicObj[`${i}`] = '-'
        })
      }
      a.push(Object.assign(curData, dynamicObj))
      return a
    }, [])
  }, [detailList, setDynamicCols])

  const detailTableCols = useMemo<TableColumnItem[]>(() => {
    const initCols: TableColumnItem[] = [
      {
        title: ellipses(intl.get('序号')),
        dataKey: 'indexNum',
        width: 90,
        align: 'center',
        render: (_text, _item, index) =>
          index + 1 + paginationState?.pageSize * (paginationState?.current - 1),
      },
      {
        title: ellipses(intl.get('部门')),
        dataKey: 'deptLevel234Name',
        width: 320,
        render: (text) => tableColumnRender(text, 320),
      },
      {
        title: ellipses(intl.get('待办场景')),
        dataKey: 'categoryName',
        width: 180,
        render: (text) => tableColumnRender(text, 180),
      },
      {
        title: ellipses(intl.get('执行人')),
        dataKey: 'concatName',
        width: 180,
      },
      {
        title: ellipses(intl.get('创建时间')),
        dataKey: 'startTime',
        width: 180,
      },
      {
        title: ellipses(intl.get('任务ID')),
        dataKey: 'taskId',
        width: 200,
      },
      {
        title: ellipses(intl.get('待办状态')),
        dataKey: 'status',
        width: 150,
        render: (text) => TODO_STATE_NAME_MAP[text.toString()],
      },
    ]
    dynamicCols?.length !== 0 &&
      initCols.push(
        ...dynamicCols.map((i) => {
          return {
            title: `${i}`,
            dataKey: `${i}`,
            width: i === '流程发起人' ? 150 : 180,
            render: (text) => tableColumnRender(text, i === '流程发起人' ? 150 : 180),
          }
        })
      )
    initCols.push({
      title: ellipses(intl.get('删除理由')),
      dataKey: 'deleteReason',
      width: 200,
      render: (text) => tableColumnRender(text, 200),
    })
    initCols.push({
      title: ellipses(intl.get('完成时间')),
      dataKey: 'endTime',
      width: 180,
    })
    return initCols
  }, [dynamicCols, paginationState])

  return (
    <div className="table-pagination__wrap" style={{ minWidth: 660, marginTop: 16 }}>
      <Table
        pagination={{
          showTotal: true,
          showJumper: true,
          pageSize: paginationState.pageSize,
          pageSizeOptions: [10, 20, 30, 50, 100],
          onPageSizeChange: (pageSize) => {
            paginationState.changePageSize(pageSize)
            gscTrack.pageElemClick('每页条目-待办明细', 'entryPerPage', '01')
          },
          total: !detailList?.length || !paginationState?.total ? 0 : paginationState?.total,
          current: paginationState?.current ?? 0,
          onChange: (page, _, size) => {
            paginationState.onChange(page, size)
            gscTrack.pageElemClick('翻页-待办明细', 'flip', '01')
          },
        }}
        columns={detailTableCols}
        data={derivedList}
        fieldKey="id"
        loading={detailTableLoading}
        maxHeight={600}
      />
    </div>
  )
})

DetailList.displayName = 'DetailList'

export default DetailList
