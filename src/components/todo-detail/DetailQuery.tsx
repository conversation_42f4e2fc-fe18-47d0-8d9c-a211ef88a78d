import './detail-query.scss'

import { useSafeState } from 'ahooks'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'
import React, { CSSProperties, memo, useCallback, useEffect, useMemo, useRef } from 'react'
import intl from 'react-intl-universal'
import { useLocation } from 'react-router-dom'

import * as API from '@/api/gsc/form-view'
import { getTodoCondition } from '@/api/gsc/todo-detail'
import { FORM_VIEWS_CACHE, TODO_LIST_PAGE } from '@/constants'
import { formatStartObj, formatTimeObj } from '@/utils/format-time-obj'
import { gscSearchTrack, gscTrack } from '@/utils/gscTrack-help'
import { FORM_WIDTH } from '@/utils/select-data'
import Button from '@hi-ui/button'
import CheckSelect from '@hi-ui/check-select'
import DatePicker from '@hi-ui/date-picker'
import { Form, FormHelpers, FormItem } from '@hi-ui/form'
import { Col, Row } from '@hi-ui/grid'
import { DownOutlined } from '@hi-ui/icons'
import Select from '@hi-ui/select'

import { ellipses } from '../ellipsis-tool'
import FormView from '../form-view'
import {
  COMMON_VALUES,
  DetailCallBackType,
  DetailFilterType,
  FROM_HOME_VALUES,
  ITEM_COMMON_WIDTH,
  keysToSelect,
  keyToSelect,
  NumberToKeyMap,
  SELECT_STATUS,
  TODO_DETAIL_URL,
  TODO_TYPE,
  TODO_TYPE_SELECT_DATA,
  TodoDashboardClick,
} from './config'
import { QueryCodeName } from './track'

const DetailQuery = memo<{
  onSearch: React.Dispatch<React.SetStateAction<DetailFilterType>>
  formState: TodoDashboardClick // 跳转或弹框通用表单状态
  getTabsAllData: DetailCallBackType
  setUpdateTab: React.Dispatch<React.SetStateAction<boolean>>
}>(({ onSearch, formState, getTabsAllData, setUpdateTab }) => {
  const formRef = useRef<FormHelpers>(null)
  const defaultSearchValue = useRef<Record<string, string | string[]>>({
    startTime: '',
    endTime: '',
    dept3NameList: [],
    dept4NameList: [],
    categoryNameList: [],
    concatNameList: [],
    statusList: [],
    todoTypeList: [],
    purchaseOrgList: [],
    stockOrgList: [],
    taskIdList: [],
  })
  const location = useLocation()
  const isCheckDetailUrl = useMemo(() => location.pathname === TODO_DETAIL_URL, [location.pathname])
  const commonStyle = useRef<CSSProperties | undefined>({
    width: '100%',
    minWidth: ITEM_COMMON_WIDTH,
  })
  const [expanded, setExpanded] = useSafeState(false) // 控制表单折叠与否
  const formEleHeightRef = useRef<number>(33) // 表单默认高度
  const [selectState, setSelectState] = useSafeState<
    Record<string, { id: string; title: string }[]>
  >({
    dept3NameList: [],
    dept4NameList: [],
    categoryNameList: [],
    concatNameList: [],
    statusList: SELECT_STATUS,
    todoTypeList: [],
    purchaseOrgList: [],
    stockOrgList: [],
    taskIdList: [],
  })

  const [formData, setFormData] = useSafeState<Record<string, string | string[]>>({
    startTime: [],
    dept3NameList: [],
    dept4NameList: [],
    categoryNameList: [],
    concatNameList: [],
    statusList: [],
    todoTypeList: [],
    purchaseOrgList: [],
    stockOrgList: [],
    taskIdList: [],
  })

  const [selectStateLoading, setSelectStateLoading] = useSafeState({
    dept3NameList: true,
    dept4NameList: true,
    categoryNameList: true,
    concatNameList: true,
    statusList: true,
    todoTypeList: true,
    purchaseOrgList: true,
    stockOrgList: true,
    taskIdList: true,
  })

  const detailQueryFormInitial = useRef({
    // 表单初始条件
    startTime: [],
    dept3NameList: '',
    dept4NameList: [],
    categoryNameList: [],
    concatNameList: [],
    statusList: [],
    todoTypeList: [],
    purchaseOrgList: [],
    stockOrgList: [],
    taskIdList: [],
  })

  const commonProcess = (values) => {
    const newValues: Record<string, string | string[]> = Object.keys(values).reduce((a, b) => {
      if (['dept3NameList'].includes(b)) {
        a[b] = !values[b] ? [] : [values[b]]
      } else {
        a[b] = values[b]
      }
      return a
    }, {})
    formatTimeObj(newValues)
    newValues.todoTypeList = (newValues.todoTypeList as string[])?.map((i) => TODO_TYPE[`${i}`])
    return newValues
  }

  const handleSearch = useCallback(() => {
    setUpdateTab(false)
    formRef.current?.validate()?.then((val) => {
      const params = commonProcess(val)
      getTabsAllData(params)
      onSearch(params)
      gscSearchTrack({ filters: val, queryCodeName: QueryCodeName })
    })
  }, [getTabsAllData, onSearch, setUpdateTab])

  const formatter = useCallback(
    (filter, getTab = true) => {
      const res = { ...filter }
      formRef.current?.setFieldsValue(res)
      setFormData(res)
      const params = commonProcess(res)
      // tip
      if (getTab) {
        getTabsAllData(params)
      }
      return params
    },
    [getTabsAllData, setFormData]
  )

  useEffect(() => {
    let queryObj: Record<string, string | string[]> = {}
    if (!isCheckDetailUrl) {
      // 弹框
      const {
        dept3NameList = [],
        dept4NameList = [],
        categoryNameList = [],
        startTime = [],
        concatNameList = [],
        todoTypeList = [],
        purchaseOrgList = [],
        stockOrgList = [],
        taskIdList = [],
      } = formState ?? {}
      const obj = {
        dept3NameList,
        dept4NameList,
        categoryNameList,
        concatNameList,
        purchaseOrgList,
        stockOrgList,
        taskIdList,
      }
      queryObj = { ...obj, todoTypeList: (todoTypeList as string[])?.map((i) => TODO_TYPE[`${i}`]) }
      if (!!formState && 'concatNameList' in formState) {
        queryObj.concatNameList = concatNameList
      }
      if (!!formState && 'startTime' in formState) {
        formatStartObj(startTime, queryObj)
      }
      setSelectState((prev) => {
        prev.dept3NameList = dept3NameList.map((i) => ({ id: i, title: i }))
        prev.dept4NameList = dept4NameList.map((i) => ({ id: i, title: i }))
        prev.categoryNameList = categoryNameList.map((i) => ({
          id: i,
          title: i,
        }))
        prev.todoTypeList = (todoTypeList as string[])?.map((i) => ({ id: i, title: i }))
        prev.purchaseOrgList = (purchaseOrgList as string[])?.map((i) => ({ id: i, title: i }))
        prev.stockOrgList = (stockOrgList as string[])?.map((i) => ({ id: i, title: i }))
        prev.taskIdList = (taskIdList as string[])?.map((i) => ({ id: i, title: i }))
        !!formState &&
          'concatNameList' in formState &&
          (prev.concatNameList = concatNameList.map((i) => ({
            id: i,
            title: i,
          })))
        return { ...prev }
      })
      setFormData((prev) => {
        prev.dept3NameList = dept3NameList
        prev.dept4NameList = dept4NameList
        prev.categoryNameList = categoryNameList
        prev.todoTypeList = todoTypeList
        prev.purchaseOrgList = purchaseOrgList
        prev.stockOrgList = stockOrgList
        prev.taskIdList = taskIdList
        !!formState && 'concatNameList' in formState && (prev.concatNameList = concatNameList ?? [])
        return { ...prev }
      })
      formRef.current?.setFieldsValue({
        dept3NameList: dept3NameList[0],
        dept4NameList,
        categoryNameList,
        startTime,
        concatNameList,
        todoTypeList,
        purchaseOrgList,
        stockOrgList,
        taskIdList,
      })
      onSearch({ ...defaultSearchValue.current, ...queryObj })
    } else {
      // 跳转
      let config
      if (location.search === '?from=notify') {
        config = {
          ...COMMON_VALUES,
          dept3NameList: '',
        }
      } else if (location.search === '?from=home') {
        config = {
          ...FROM_HOME_VALUES,
          dept3NameList: '',
        }
      } else {
        const cache = localStorage.getItem(FORM_VIEWS_CACHE) || '[]'
        const content =
          JSON.parse(cache)?.find((item) => item?.pageType === TODO_LIST_PAGE)?.content || ''
        if (content) {
          const { formConfig } = JSON.parse(content)
          config = formConfig
        } else {
          config = {
            dept3NameList: '手机部_手机供应链',
            dept4NameList: [],
            categoryNameList: [],
            startTime: [],
            todoTypeList: [],
            statusList: ['2', '3'],
            purchaseOrgList: [],
            stockOrgList: [],
            taskIdList: [],
          }
        }
      }
      setSelectState((prev) => {
        prev.dept3NameList = keyToSelect(config.dept3NameList)
        prev.dept4NameList = keysToSelect(config.dept4Name)
        prev.categoryNameList = keysToSelect(config.categoryNameList)
        prev.todoTypeList = keysToSelect(config.todoTypeList)
        prev.purchaseOrgList = keysToSelect(config.purchaseOrgList)
        prev.stockOrgList = keysToSelect(config.stockOrgList)
        prev.taskIdList = keysToSelect(config.taskIdList)
        return { ...prev }
      })
      setFormData({ ...config })
      onSearch(formatter(config, false))
    }
  }, [
    formState,
    formatter,
    isCheckDetailUrl,
    location.search,
    onSearch,
    setFormData,
    setSelectState,
  ])

  const updateCondition = useCallback(
    (type) => {
      const newFormData = cloneDeep(formData)
      const [start, end] = newFormData?.startTime || ['', '']
      if (newFormData?.startTime?.length !== 0 && start && end) {
        newFormData.endTime = dayjs(end).format('YYYY-MM-DD')
        newFormData.startTime = dayjs(start).format('YYYY-MM-DD')
      } else {
        newFormData.endTime = ''
        newFormData.startTime = ''
      }
      typeof newFormData.dept3NameList === 'string' &&
        (newFormData.dept3NameList =
          newFormData.dept3NameList?.length === 0 ? [] : [newFormData.dept3NameList])
      newFormData.todoTypeList = (newFormData?.todoTypeList as string[])?.map(
        (i) => TODO_TYPE[`${i}`]
      )
      setSelectStateLoading((prev) => {
        prev[`${NumberToKeyMap[`${type}`]}`] = true
        return { ...prev }
      })
      const fn = async () => {
        const res = await getTodoCondition({
          type,
          ...newFormData,
        })
        setSelectState((prev) => {
          prev[`${NumberToKeyMap[`${type}`]}`] = res?.data?.map((i) => {
            return {
              id: i?.code,
              title: i?.name,
            }
          })
          return { ...prev }
        })
        setSelectStateLoading((prev) => {
          prev[`${NumberToKeyMap[`${type}`]}`] = false
          return { ...prev }
        })
      }
      fn()
    },
    [formData, setSelectState, setSelectStateLoading]
  )

  return (
    <div className="detail_query_wrapper">
      <div
        className="overflow-hidden transition-[height] flex justify-between"
        style={{
          height: expanded ? formEleHeightRef.current : 130,
        }}
      >
        <Form
          style={{ width: FORM_WIDTH }}
          innerRef={formRef}
          onValuesChange={(_changeValue, allValues) => {
            setFormData(allValues)
          }}
          initialValues={detailQueryFormInitial.current}
          labelWidth="75"
          labelPlacement="right"
          showColon={false}
        >
          <Row gutter={false}>
            <Col span={8}>
              <FormItem field="startTime" label={ellipses(intl.get('创建时间'))}>
                <DatePicker type="daterange" style={commonStyle.current} format="YYYY-MM-DD" />
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem field="dept3NameList" label={ellipses(intl.get('二级部门'))}>
                <Select
                  style={commonStyle.current}
                  data={selectState.dept3NameList ?? []}
                  onOpen={() => updateCondition('1')}
                  searchable
                  loading={selectStateLoading.dept3NameList}
                ></Select>
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem field="dept4NameList" label={ellipses(intl.get('三级部门'))}>
                <CheckSelect
                  style={commonStyle.current}
                  data={selectState.dept4NameList ?? []}
                  showCheckAll
                  searchable
                  onOpen={() => updateCondition('2')}
                  loading={selectStateLoading.dept4NameList}
                ></CheckSelect>
              </FormItem>
            </Col>
          </Row>
          <Row gutter={false}>
            <Col span={8}>
              <FormItem field="todoTypeList" label={ellipses(intl.get('待办类型'))}>
                <CheckSelect
                  loading={selectStateLoading.todoTypeList}
                  style={commonStyle.current}
                  data={TODO_TYPE_SELECT_DATA}
                  onOpen={() => updateCondition('6')}
                  showCheckAll
                ></CheckSelect>
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem field="purchaseOrgList" label={ellipses(intl.get('采购组织'))}>
                <CheckSelect
                  loading={selectStateLoading.purchaseOrgList}
                  style={commonStyle.current}
                  data={selectState.purchaseOrgList ?? []}
                  onOpen={() => updateCondition('7')}
                  showCheckAll
                ></CheckSelect>
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem field="categoryNameList" label={ellipses(intl.get('待办场景'))}>
                <CheckSelect
                  loading={selectStateLoading.categoryNameList}
                  searchable
                  style={commonStyle.current}
                  data={selectState.categoryNameList ?? []}
                  onOpen={() => updateCondition('3')}
                  showCheckAll
                ></CheckSelect>
              </FormItem>
            </Col>
          </Row>
          <Row gutter={false}>
            <Col span={8}>
              <FormItem field="concatNameList" label={ellipses(intl.get('执行人'))}>
                <CheckSelect
                  loading={selectStateLoading.concatNameList}
                  searchable
                  style={commonStyle.current}
                  data={selectState.concatNameList ?? []}
                  onOpen={() => updateCondition('4')}
                  showCheckAll
                ></CheckSelect>
              </FormItem>
            </Col>
            <Col justify="flex-start" span={8}>
              <FormItem field="statusList" label={ellipses(intl.get('待办状态'))}>
                <CheckSelect
                  loading={selectStateLoading.statusList}
                  style={commonStyle.current}
                  data={selectState.statusList ?? []}
                  onOpen={() => updateCondition('5')}
                  showCheckAll
                ></CheckSelect>
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div className="ml-6 pl-6">
          <div className="w-max">
            <Button
              appearance="link"
              onClick={() => {
                setExpanded((expanded) => !expanded)
                gscTrack.pageElemClick('展开/收起-待办明细', 'on/off', '01')
              }}
            >
              {expanded ? intl.get('展开') : intl.get('收起')}
              <DownOutlined
                className="transition-transform ml-2"
                color="#9d9d9f"
                style={{
                  transform: expanded ? 'rotateZ(0)' : 'rotateZ(180deg)',
                }}
              />
            </Button>
            <Button
              type="default"
              onClick={() => {
                formRef.current?.reset()
                setFormData(detailQueryFormInitial.current)
                gscTrack.pageElemClick('重置-待办明细', 'reset', '01')
              }}
            >
              {intl.get('重置')}
            </Button>
            <Button type="secondary" onClick={handleSearch} className="search-button">
              {intl.get('查询')}
            </Button>
            <FormView
              api={API}
              filter={formData}
              pageType={TODO_LIST_PAGE}
              onSearch={onSearch}
              clickFormatter={formatter}
            />
          </div>
        </div>
      </div>
    </div>
  )
})

DetailQuery.displayName = 'DetailQuery'

export default DetailQuery
