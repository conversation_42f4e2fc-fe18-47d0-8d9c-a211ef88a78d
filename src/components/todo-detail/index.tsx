import React, { FC, memo, ReactText, useEffect, useMemo, useRef } from 'react'
import DetailQuery from './DetailQuery'
import TabsLine from './TabsLine'
import DetailList from './DetailList'
import { ErrorBoundary } from '@sentry/react'
import { usePagination, useSafeState, useRequest } from 'ahooks'
import { useLocation } from 'react-router-dom'
import { getTodoDetailList, getTodoScenes } from '@/api/gsc/todo-detail'
import {
  COMMON_VALUES,
  DEFAULT_TABS_QUERY_CONDITION,
  DetailFilterType,
  FROM_HOME_VALUES,
  initFormConfig,
  NOTIFY_ITEMS,
  TODO_DETAIL_URL,
  TODO_TYPE,
  TodoDashboardClick,
} from './config'
import { cloneDeep } from 'lodash'
import dayjs from 'dayjs'
import { getQueryStr } from '@/utils/query-string'
import cx from 'classnames'

import './index.scss'
import { FORM_VIEWS_CACHE, TODO_LIST_PAGE } from '@/constants'

const paramsToArray = (params = {}) => {
  const newParams = { ...params }
  for (const key in newParams) {
    if (!Array.isArray(newParams[key])) {
      newParams[key] = [newParams[key]]
    }
  }
  return newParams
}

const TodoDetail: FC<{ modalFormState?: TodoDashboardClick }> = memo(({ modalFormState }) => {
  const [detailFilter, setDetailFilter] = useSafeState<DetailFilterType>({})
  const [currentId, setCurrentId] = useSafeState<ReactText>('')
  // 用于标记tab接口是否正在请求状态
  const [updateTab, setUpdateTab] = useSafeState<boolean>(false)
  const location = useLocation()
  const initRender = useRef<boolean>(true)
  const isCheckDetailUrl = useMemo(() => location.pathname === TODO_DETAIL_URL, [location.pathname])

  const formState = useMemo(() => {
    let newState
    if (!isCheckDetailUrl) {
      newState = isCheckDetailUrl
        ? location.state || paramsToArray(getQueryStr(location.search.substring(1)))
        : modalFormState ?? {}
      if (!!newState && 'type' in newState) delete newState?.type
      newState.startTime =
        !newState?.startTime?.[0] || !newState?.startTime?.[1] ? [] : newState.startTime
    } else {
      if (location.search === '?from=notify') {
        newState = {
          ...COMMON_VALUES,
          dept3NameList: [],
        }
      } else if (location.search === '?from=home') {
        newState = {
          ...COMMON_VALUES,
          ...FROM_HOME_VALUES,
          dept3NameList: [],
          todoTypeList: [],
        }
      } else {
        const cache = localStorage.getItem(FORM_VIEWS_CACHE) || '[]'
        const content =
          JSON.parse(cache)?.find((item) => item?.pageType === TODO_LIST_PAGE)?.content || ''
        if (content) {
          const { formConfig } = JSON.parse(content)
          const { dept3NameList, ...left } = formConfig
          newState = { ...left, dept3NameList: [dept3NameList] }
        } else {
          newState = {
            dept3NameList: ['手机部_手机供应链'],
            dept4NameList: [],
            categoryNameList: [],
            startTime: [],
            todoTypeList: [],
            statusList: ['2', '3'],
            purchaseOrgList: [],
            stockOrgList: [],
            taskIdList: [],
          }
        }
      }
    }
    return newState
  }, [isCheckDetailUrl, modalFormState, location.state, location.search])

  const {
    data: tabsAllData,
    run: getTabsAllData,
    loading: tabsLoading,
  } = useRequest((data) => getTodoScenes(data), {
    manual: true,
  })

  const {
    data: detailList,
    run: getDetailList,
    loading: detailTableLoading,
    pagination,
  } = usePagination(
    ({ current, pageSize, chooseArray }) => {
      let param = initFormConfig as AnyType
      if (location.search === '?from=notify') {
        param = { ...initFormConfig, ...NOTIFY_ITEMS }
      } else if (location.search === '?from=home') {
        param = { ...initFormConfig, ...FROM_HOME_VALUES }
      }
      return getTodoDetailList({
        pageNum: current,
        pageSize,
        ...param,
        ...detailFilter,
        categoryNameList: chooseArray,
      })
    },
    {
      manual: true,
      refreshDeps: [detailFilter],
    }
  )

  useEffect(() => {
    // 通过默认状态查询 tabs 数据
    const newFormState = cloneDeep(formState) as Record<string, string | string[]>
    const { todoTypeList = [] } = newFormState
    if (!!newFormState && 'startTime' in newFormState) {
      if (newFormState?.startTime?.length !== 0) {
        newFormState.endTime = dayjs(newFormState?.startTime?.[1])?.format('YYYY-MM-DD')
        newFormState.startTime = dayjs(newFormState?.startTime?.[0])?.format('YYYY-MM-DD')
      } else {
        newFormState.endTime = ''
        newFormState.startTime = ''
      }
    }
    newFormState.todoTypeList = (todoTypeList as string[])?.map((i) => TODO_TYPE[`${i}`])
    getTabsAllData({ ...DEFAULT_TABS_QUERY_CONDITION, ...newFormState })
  }, [formState, getTabsAllData])

  useEffect(() => {
    // tab请求完成，且选中了某一个tab后再请求列表
    if (currentId && updateTab) {
      getDetailList({
        current: 1,
        pageSize: pagination.pageSize,
        chooseArray: [currentId],
      })

      initRender.current = false
    }
  }, [currentId, pagination.pageSize, getDetailList, detailFilter, updateTab])

  return (
    <div style={{ minWidth: 1212 }}>
      <ErrorBoundary>
        <DetailQuery
          onSearch={setDetailFilter}
          formState={formState}
          getTabsAllData={getTabsAllData}
          setUpdateTab={setUpdateTab}
        />
      </ErrorBoundary>
      <div className={cx('detail_info_wrapper', { 'is-percent-height': !isCheckDetailUrl })}>
        <ErrorBoundary>
          <TabsLine
            tabsData={tabsAllData?.data ?? []}
            setCurrentId={setCurrentId}
            detailFilter={detailFilter}
            currentId={currentId}
            tabsLoading={tabsLoading}
            setUpdateTab={setUpdateTab}
          />
        </ErrorBoundary>
        <ErrorBoundary>
          <DetailList
            paginationState={pagination}
            detailList={
              !tabsAllData?.data?.length || !detailList?.list?.length ? [] : detailList?.list
            }
            detailTableLoading={detailTableLoading}
          />
        </ErrorBoundary>
      </div>
    </div>
  )
})

TodoDetail.displayName = 'TodoDetail'

export default TodoDetail
