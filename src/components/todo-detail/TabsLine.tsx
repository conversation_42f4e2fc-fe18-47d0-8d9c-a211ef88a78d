import intl from 'react-intl-universal'
import React, { FC, memo, ReactText, useCallback, useMemo, useRef } from 'react'
import './tabs-line.scss'
import Button from '@hi-ui/button'
import { TabList, TabPaneProps } from '@hi-ui/tabs'
import { useSafeState } from 'ahooks'
import { DetailFilterType } from './config'
import message from '@hi-ui/message'
import { exportTodoDetail } from '@/api/gsc/todo-detail'
import { gscTrack } from '@/utils/gscTrack-help'
import { customMessage } from '@/utils/custom-message'
import * as Sentry from '@sentry/react'
import { ResponseType } from '@/types/type'

const TabsLine: FC<{
  tabsData
  currentId: ReactText
  setCurrentId: React.Dispatch<React.SetStateAction<React.ReactText>>
  detailFilter: DetailFilterType
  tabsLoading: boolean
  setUpdateTab: React.Dispatch<React.SetStateAction<boolean>>
}> = memo(({ tabsData, currentId, setCurrentId, detailFilter, tabsLoading, setUpdateTab }) => {
  const [detailExportLoading, setDetailExportLoading] = useSafeState<boolean>(false)
  const lastCurrentId = useRef<ReactText>('')

  const getDerivedTabData = useCallback((name: string) => {
    return {
      tabId: name,
      tabTitle: <div style={{ height: '20px', lineHeight: '20px' }}>{name}</div>,
    }
  }, [])

  const setupCategory = useCallback(
    (categoryName: string) => {
      setCurrentId(categoryName)
      lastCurrentId.current = categoryName
      setUpdateTab(true)
    },
    [setCurrentId, setUpdateTab]
  )

  const tabListData = useMemo(() => {
    if (tabsData.length === 0) {
      setupCategory('')
      return []
    }
    // 切换条件后是否存在当前分类
    let categoryExist = false
    // 第一个非空categoryName
    let firstCategoryName = ''
    const listData: TabPaneProps[] = []
    tabsData.forEach((tab) => {
      const { categoryName = '' } = tab || {}
      if (!categoryName) {
        return
      }

      if (!firstCategoryName) {
        firstCategoryName = categoryName
      }
      if (!lastCurrentId.current || lastCurrentId.current === categoryName) {
        setupCategory(categoryName)
        categoryExist = true
      }

      listData.push(getDerivedTabData(categoryName))
    })
    if (!categoryExist) {
      setupCategory(firstCategoryName)
    }

    return listData
  }, [tabsData, getDerivedTabData, setupCategory])

  const handleTodoDetailExport = useCallback(() => {
    if (!tabsData?.length) {
      message.open({
        title: intl.get('表格数据为空，请查询得到数据后导出'),
        type: 'info',
      })
    } else {
      gscTrack.pageElemClick('导出-待办明细', 'export', '01')
      setDetailExportLoading(true)
      message.open({ title: intl.get('导出中...'), type: 'info', autoClose: false })
      const fn = async () => {
        try {
          const res = await exportTodoDetail({
            ...detailFilter,
            categoryNameList: tabsData?.map((i) => i?.categoryName) ?? [],
            showTaskId: true,
          })
          if (res?.data) {
            window.open(res?.data)
            message.closeAll()
            customMessage(intl.get('导出成功'), 'success')
          } else {
            message.closeAll()
            customMessage(res?.msg || intl.get('导出失败'), 'error')
          }
        } catch (err) {
          Sentry.captureException(err)
          message.closeAll()
          customMessage((err as ResponseType)?.msg || intl.get('导出失败'), 'error')
        } finally {
          setDetailExportLoading(false)
        }
      }
      fn()
    }
  }, [detailFilter, setDetailExportLoading, tabsData])

  return (
    <div className="info_wrapper flex justify-between">
      <div className="tabs-basic__wrap">
        {!tabsLoading && (
          <TabList
            data={tabListData}
            onChange={(tabId) => {
              setCurrentId(tabId)
              lastCurrentId.current = tabId
              gscTrack.pageElemClick('查看-待办明细', 'view', '01')
            }}
            activeId={currentId}
          />
        )}
      </div>
      <div className="flex items-center">
        <Button type="primary" onClick={handleTodoDetailExport} loading={detailExportLoading}>
          {intl.get('导出')}
        </Button>
      </div>
    </div>
  )
})

TabsLine.displayName = 'TabsLine'

export default TabsLine
