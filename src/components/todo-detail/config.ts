import { getObjectStorage } from '@/utils/cache'
import intl from 'react-intl-universal'
export const TODO_STATE = [
  intl.get('已完成'),
  intl.get('未完成-未逾期'),
  intl.get('未完成-已逾期'),
  intl.get('已删除'),
].map((state) => {
  return { id: state, title: state }
})

export const TODO_STATE_NUMBER_MAP = {
  已完成: '1',
  '未完成-未逾期': '2',
  '未完成-已逾期': '3',
  已删除: '4',
}

export const TODO_STATE_NAME_MAP = {
  '1': intl.get('已完成'),
  '2': intl.get('未完成-未逾期'),
  '3': intl.get('未完成-已逾期'),
  '4': intl.get('已删除'),
}

export const TODO_INITIAL_VALUES = []
export const TODO_TYPE_OPTIONS = []
export const FORM_COMMON_WIDTH = 316
export const FORM_COMMON_HEIGHT = 48
export const ITEM_COMMON_WIDTH = 220
export const TABS_FONT_SIZE = 14

export const DEFAULT_TABS_QUERY_CONDITION = {
  startTime: '',
  endTime: '',
  dept3NameList: ['手机部_手机供应链'],
  dept4NameList: [],
  categoryNameList: [],
  concatNameList: [],
  statusList: [],
  todoTypeList: [],
}

export const NumberToKeyMap = {
  '1': 'dept3NameList',
  '2': 'dept4NameList',
  '3': 'categoryNameList',
  '4': 'concatNameList',
  '5': 'statusList',
  '6': 'todoTypeList',
  '7': 'purchaseOrgList',
  '8': 'stockOrgList',
  '9': 'taskIdList',
}

export const WHALE_FORM_STATE_NUM = 4

export const TODO_DETAIL_URL = '/todo/detail'

export const TODO_TYPE = {
  审批: '1',
  待办: '2',
  通知: '3',
}

export const TODO_TYPE_SELECT_DATA = [intl.get('审批'), intl.get('待办'), intl.get('通知')].map(
  (i) => ({
    id: i,
    title: i,
  })
)

export interface TodoDashboardClick {
  categoryNameList?: string[]
  concatNameList?: string[]
  dept4NameList?: string[]
  startTime?: string[]
  todoTypeList?: string | string[]
  type?: string | string[]
  dept3NameList?: string[]
  purchaseOrgList?: string[]
  stockOrgList?: string[]
  taskIdList?: string[]
}

export interface DetailFilterType {
  categoryNameList?: string[]
  concatNameList?: string[]
  dept3NameList?: string[]
  dept4NameList?: string[]
  startTime?: string
  endTime?: string
  statusList?: string[]
  todoTypeList?: string[]
  purchaseOrgList?: string[]
  stockOrgList?: string[]
}

export type DetailCallBackType = (data: DetailFilterType) => void

export const keyToSelect = (key: string) => [{ id: key, title: key }]
export const keysToSelect = (keys: string[]) => keys?.map((key) => ({ id: key, title: key })) || []

export const SELECT_STATUS = [
  { id: '1', title: '已完成' },
  { id: '2', title: '未完成-未逾期' },
  { id: '3', title: '未完成-已逾期' },
  { id: '1', title: '已删除' },
]

export const initFormConfig = {
  dept3NameList: ['手机部_手机供应链'],
  dept4NameList: [],
  categoryNameList: [],
  todoTypeList: [],
  startTime: '',
  endTime: '',
  statusList: ['2', '3'],
  purchaseOrgList: [],
  stockOrgList: [],
  taskIdList: [],
}

export const getCombineName = () => {
  const cache = getObjectStorage('WORKBENCH_USER_INFO')
  const { displayName = '', userName = '' } = cache || {}
  if (displayName && userName) {
    return `${displayName}_${userName}`
  } else {
    return ''
  }
}

export const NOTIFY_ITEMS = {
  dept3NameList: [],
  concatNameList: [getCombineName()],
  todoTypeList: ['3'],
  statusList: [],
}

export const COMMON_VALUES = {
  dept4NameList: [],
  categoryNameList: [],
  startTime: [],
  concatNameList: [getCombineName()],
  todoTypeList: ['通知'],
  statusList: [],
  purchaseOrgList: [],
  stockOrgList: [],
  taskIdList: [],
}

export const FROM_HOME_VALUES = {
  statusList: ['2', '3'],
  concatNameList: [getCombineName()],
  dept3NameList: [],
}
