import React, { memo, useCallback } from 'react'
import './style.scss'
import Slider from '@hi-ui/slider'
import Button from '@hi-ui/button'
import { MinusOutlined, PlusOutlined } from '@hi-ui/icons'

const ZoomSlider = memo<{ sliderValue: number; setSliderValue }>(
  ({ sliderValue, setSliderValue }) => {
    const handleSubValue = useCallback(() => {
      setSliderValue((prev) => prev - 10)
    }, [setSliderValue])

    const handleAddValue = useCallback(() => {
      setSliderValue((prev) => prev + 10)
    }, [setSliderValue])

    return (
      <div className="zoom_slider_wrapper">
        <Button
          icon={<MinusOutlined />}
          className="relative top-2 left-4"
          size="sm"
          onClick={handleSubValue}
          disabled={sliderValue === 50}
        />
        <Slider
          value={sliderValue}
          onChange={setSliderValue}
          style={{ width: 200 }}
          min={50}
          max={150}
          tipFormatter={(num) => <span>{`${num}%`}</span>}
        />
        <Button
          icon={<PlusOutlined />}
          className="relative top-2 left-3"
          size="sm"
          disabled={sliderValue === 150}
          onClick={handleAddValue}
        />
      </div>
    )
  }
)

ZoomSlider.displayName = 'ZoomSlider'

export default ZoomSlider
