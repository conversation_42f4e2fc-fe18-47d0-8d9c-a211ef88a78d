import React, { useEffect } from 'react'
import { resolvePath, useLocation, useNavigate, useParams } from 'react-router-dom'
import cx from 'classnames'
import { useCreation, useMemoizedFn, useSafeState } from 'ahooks'
import { isNil, omit, pick } from 'lodash'
import Drawer from '@hi-ui/drawer'
import Loading from '@hi-ui/loading'
import { useDrawerResponsive } from '@/hooks/useDrawerResponsive'
import './index.scss'

const cls = (...classnames: string[]) => cx(classnames.map((name) => `gsc-${name}`))

export function useInitialValues<T extends string = string>(
  fields: T[] = [],
  transform?: (values: AnyObject, allValues: AnyObject) => AnyObject
) {
  const values = useLocation().state || {}

  // 先移除完全不能要的，[可选]再取出必须要的
  const picked = pick(omit(values, ['$rowKey']), ['id', ...fields])

  return (transform ? transform(picked, values) : picked) as AnyObject &
    Record<ArrayToUnion<T[]>, AnyType>
}

// 没有携带state时自动返回指定页面
export function useAutoBack(to?: string) {
  const state = useLocation().state
  const navigate = useNavigate()

  if (!state) {
    setTimeout(() => navigate(to || '..'), 32)
    return null
  }

  return <></> // 实际上返回值没有意义
}

export function usePageType() {
  const { id } = useParams<{ id: string }>()

  if (isNil(id) || id === '') return { isEditPage: false as const }

  const type = id === 'add' ? ('add' as const) : ('edit' as const)

  return {
    type,
    id,
    /** 标识组件是否符合新建/编辑页的约束 */
    isEditPage: true as const,
    /** 标识是否为编辑页 */
    isEditing: type === 'edit',
  }
}

export const SideDrawerContainerCtx = React.createContext({
  close: () => {
    /** close placeholder */
  },
})

export function useSideDrawerContainerCtx() {
  return React.useContext(SideDrawerContainerCtx)
}

type SideDrawerContainerProps = {
  title?: string
  /** 指当前操作的数据实体名称，用来生成title */
  name?: string
  /** 开启窄抽屉(420px) */
  mini?: boolean
  backTo?: string
  search?: string
  children: React.ReactElement
  maskClosable?: boolean
  /** 设置为true时，抽屉的背景会透明 */
  isOverlayTransparent?: boolean
  className?: string
}

export function SideDrawerContainer(props: SideDrawerContainerProps) {
  const [visible, setVisible] = useSafeState(false)

  const navigate = useNavigate()
  const { isEditPage, ...pageInfo } = usePageType()
  const dftBackTo = isEditPage ? '../../' : '../'
  const backTo = resolvePath(props.backTo || dftBackTo, useLocation().pathname).pathname
  const { isOverlayTransparent = false } = props

  useEffect(() => {
    setTimeout(() => setVisible(true), 0)
  }, [setVisible])

  const loading = (
    <Loading visible>
      <div style={{ height: '400px' }}></div>
    </Loading>
  )

  const miniWidth = 420 // mini类型时的宽度
  const responsiveWidth = useDrawerResponsive()
  const width = props.mini ? miniWidth : responsiveWidth

  const title = (
    <div className="flex flex-space-between flex-align-center">
      <span>
        {
          // title 存在用 title
          props.title ||
            // name 存在根据页面类型拼接
            (props.name ? `${pageInfo.type === 'add' ? '新建' : '编辑'}${props.name}` : undefined)
        }
      </span>
    </div>
  )

  const handleClose = useMemoizedFn(() => {
    setVisible(false)
    setTimeout(() => navigate(backTo + (props.search || '')), 200)
  })

  const ctxPayload = useCreation(() => {
    return {
      close: handleClose,
    }
  }, [handleClose])

  return (
    <SideDrawerContainerCtx.Provider value={ctxPayload}>
      <Drawer
        onClose={handleClose}
        title={title}
        width={width}
        visible={visible}
        closeOnEsc
        maskClosable={props.maskClosable ?? true}
        showMask={true}
        unmountOnClose
        className={cx(
          cls('side-drawer'),
          isOverlayTransparent ? cls('transparent-overlay') : undefined,
          props.className
        )}
      >
        <React.Suspense fallback={loading}>{props.children}</React.Suspense>
      </Drawer>
    </SideDrawerContainerCtx.Provider>
  )
}
