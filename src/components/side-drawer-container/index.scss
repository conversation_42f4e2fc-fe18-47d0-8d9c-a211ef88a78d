$RootClassPrefix: 'gsc-' !default;
$prefix: 'hi-v4-' !default;

.#{$RootClassPrefix}side-drawer {
  &_fullscreen {
    font-size: 14px;
    color: #5f6a7a;
    margin-right: 16px;
    cursor: pointer;
  }

  .#{$prefix}drawer__header {
    padding-top: 14px;
    padding-bottom: 14px;
  }

  .#{$prefix}drawer__wrapper {
    transition: width, 0.3s;
    .#{$prefix}drawer__title {
      font-weight: 600;
    }
  }

  // pro-form 底部增加一点间距，防止底部提交按钮遮挡表单项
  .#{$prefix}drawer__body {
    & > .#{$prefix}loading__wrapper > .sc-ui__pro-form {
      margin-bottom: 32px;
    }

    // 移除内部的内外边距
    .sc-ui__pro-form__form {
      & > .#{$prefix}grid-row.sc-ui__pro-form__fields {
        margin-left: unset;
        margin-right: unset;

        & > .#{$prefix}grid-col {
          padding-left: unset;
          padding-right: unset;
        }
      }
    }

    // 取消及提交按钮固定在底部
    div.sc-ui__pro-form__submitter {
      width: 100%;
      box-sizing: border-box;

      & > div {
        $marginRight: 20px !default;

        position: fixed;
        bottom: 0;
        right: 0;
        box-sizing: border-box;
        width: calc(100% - #{$marginRight});
        height: 56px;
        margin-right: $marginRight;
        padding: 12px 4px 12px 24px;
        background-color: #fff;
        text-align: end;
      }
    }
  }
}

.#{$RootClassPrefix}transparent-overlay {
  .#{$prefix}drawer__overlay {
    background-color: rgb(0 0 0 / 0%) !important;
  }
}
