import React, { memo, useCallback } from 'react'
import { CloudUploadOutlined } from '@hi-ui/icons'
import Button from '@hi-ui/button'
import Upload from '@hi-ui/upload'
import { generatePreUploadUrl } from '@/api/cost/new-project-cost'
import { useDownload, useUpload } from '@/hooks/useExcelOp'
import { EmptyFunctionType } from '@/types/type'
import { AnyType } from '@/constants'
import intl from 'react-intl-universal'
const ExcelOpButtons = memo<{
  category: string
  uploadKey?: string // 后端可能用不同的参数名
  uploadKeyObj?: AnyType
  isTodo?: boolean
  downloadAPI?: AnyType
  downloadId?: string
  downloadKey?: string
  uploadAPI?: AnyType
  updateData?: EmptyFunctionType
  children?: string[]
}>(
  ({
    category,
    downloadAPI,
    uploadAPI,
    updateData,
    uploadKeyObj,
    downloadId,
    uploadKey,
    downloadKey,
    children,
  }) => {
    const { run: download, loading: downloading } = useDownload(downloadAPI)
    const { run: upload, loading: uploading } = useUpload(uploadAPI, updateData)
    const key = uploadKey || 'objName'
    const downKey = downloadKey || 'categoryId'
    const handleUpdate = useCallback(
      (files) => {
        const data = files?.[0]
        const name = data?.name
        generatePreUploadUrl({ category, fileName: name }).then((res) => {
          const { presignedUri = '' } = res?.data || {}
          fetch(presignedUri, {
            method: 'PUT',
            headers: {
              'Content-Type': '',
            },
            body: data,
          })
            .then((res) => {
              return res.json()
            })
            .then((res) => {
              const { objectName = '' } = res || {}
              uploadKeyObj
                ? upload(
                    Object.assign(
                      {
                        [key]: objectName,
                      },
                      uploadKeyObj
                    )
                  )
                : upload({
                    [key]: objectName,
                  })
            })
        })
      },
      [category, key, upload, uploadKeyObj]
    )
    const handleDownload = useCallback(() => {
      downloadId
        ? download({
            [downKey]: downloadId,
          })
        : download()
    }, [downKey, download, downloadId])
    const clickUpload = useCallback((files) => handleUpdate(files), [handleUpdate])
    return (
      <div className="flex gap-4">
        <Button type="default" onClick={handleDownload} disabled={downloading}>
          {children?.[0] || intl.get('下载模板')}
        </Button>
        <Upload
          type="default"
          accept="application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          headers={{ 'Content-Type': '' }}
          disabled={false}
          loading={uploading}
          customUpload={clickUpload}
        >
          <Button icon={<CloudUploadOutlined />} type="primary">
            {children?.[1] || intl.get('上传数据')}
          </Button>
        </Upload>
      </div>
    )
  }
)
ExcelOpButtons.displayName = 'ExcelOpButtons'
export default ExcelOpButtons
