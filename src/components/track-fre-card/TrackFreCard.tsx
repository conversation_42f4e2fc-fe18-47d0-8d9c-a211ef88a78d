import React, { memo } from 'react'
import Loading from '@hi-ui/loading'
import Tooltip from '@hi-ui/tooltip'
import './track-fre-card.scss'

type TransferType = (num: number) => string

const TrackFreCard = memo<{
  title: string
  count: number
  transfer: TransferType
  type?: string
  loading?: boolean
  DropStar?
  style?
}>(({ title, count, transfer, type, loading, DropStar, style }) => {
  return (
    <div className="track-fre-card" style={style}>
      <Loading content={null} visible={loading}>
        <div className="card-left">
          <div className={`track-fre-card_title ${type}`}>
            <Tooltip
              trigger="hover"
              placement="top"
              title={<p className="break-words flex justify-center items-center">{title}</p>}
            >
              <div className="tooltip_title">{title}</div>
            </Tooltip>
          </div>
          <div className="track-fre-card_count">{transfer(count)}</div>
        </div>
      </Loading>
      <DropStar />
    </div>
  )
})

TrackFreCard.displayName = 'TrackFreCard'

export default TrackFreCard
