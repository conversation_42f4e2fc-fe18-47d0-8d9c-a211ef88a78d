import EmptyState from '@hi-ui/empty-state'
import Loading from '@hi-ui/loading'
import { BaseTable, BaseTableProps, Classes } from 'ali-react-table'
import React from 'react'
import styled from 'styled-components'

const StyledBaseTable = styled(BaseTable)`
  --line-height: 16px;
  --font-size: 14px;
  --row-height: 48px;
  --header-row-height: 36px;
  --cell-padding: 14px 16px;
  --lock-shadow: rgba(0, 0, 0, 0.2) 0 0 10px 0px;
  --border-color: #f6f8fc;
  --color: rgba(0, 0, 0, 0.85);
  --bgcolor: white;
  --hover-bgcolor: #f4f7fa;
  --highlight-bgcolor: #fafafa;
  --header-color: #1f2733;
  --header-bgcolor: #f5f7fa;
  --header-hover-bgcolor: #f5f5f5;
  --header-highlight-bgcolor: #f5f5f5;
  .art-table-header {
    border-radius: 6px;
  }
  .art-table-header-cell .hi-v4-dropdown button {
    font-weight: 500;
  }
  &,
  .art-horizontal-scroll-container {
    ::-webkit-scrollbar {
      width: 10px;
      height: 10px;
    }

    ::-webkit-scrollbar-thumb {
      background: rgba(170, 170, 170, 0.7);
      border: 1px solid #fff;
      border-radius: 100px;

      &:hover {
        /* background: #6e6e6e; */
      }
    }

    ::-webkit-scrollbar-track {
      background: #fff;
    }
  }
  &.compact {
    --cell-padding: 12px 8px;
  }
  td {
    transition: background 0.3s;
  }
  th {
    font-weight: 500;
  }
  .${Classes.lockShadowMask} {
    .${Classes.lockShadow} {
      transition: box-shadow 0.3s;
    }
  }
  &:not(.bordered) {
    --cell-border-vertical: none;
    --header-cell-border-vertical: none;
    thead > tr.first th {
      border-top: none;
    }
  }
` as unknown as typeof BaseTable

export const GSCTable = React.forwardRef<BaseTable, BaseTableProps>((props, ref) => (
  <StyledBaseTable
    ref={ref}
    {...props}
    stickyScrollHeight={10}
    components={{
      EmptyContent: EmptyState,
      LoadingIcon: Loading,
      ...props.components,
    }}
  />
))

GSCTable.displayName = 'GSCTable'
