import intl from 'react-intl-universal'
/* eslint-disable @typescript-eslint/ban-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, memo, FC, useRef } from 'react'
import './index.scss'
import { Input } from '@hi-ui/input'
import { CheckboxGroup } from '@hi-ui/checkbox'
import { Button } from '@hi-ui/button'
import Tooltip from '@hi-ui/tooltip'
import { useSafeState } from 'ahooks'
import { getTextWidth } from '@/utils/get-text-width'

const TableFilter: FC<{
  setFilterDropdownVisible: Function // 控制过滤组件显隐
  filterColumnKey: string // 当前过滤的列值
  curTableData: any[] // 当前详情表格数据
  allTableData: any[] // 所有表格数据
  setSelectedList: Function // 根据过滤条件修改表格数据的方法
  setDetailIconObj: Function // 点击确定后，再设置 icon 颜色高亮
  trackFun?: Function // 点击确定后，触发埋点
}> = memo(
  ({
    setFilterDropdownVisible,
    filterColumnKey,
    curTableData,
    allTableData,
    setSelectedList,
    setDetailIconObj,
    trackFun,
  }) => {
    const initRender = useRef<boolean>(true)
    const selectedRef = useRef<any[]>(
      allTableData?.reduce((a, b) => {
        // all select
        if (
          !!b[filterColumnKey] &&
          a.findIndex((innerItem) => innerItem.id === b[filterColumnKey]) === -1
        ) {
          a.push({
            id: b[filterColumnKey],
            title:
              Math.floor(getTextWidth(b[filterColumnKey], 14)) > 144 ? (
                <Tooltip
                  trigger="hover"
                  placement="right"
                  title={
                    <p className="break-words flex justify-center items-center">
                      {b[filterColumnKey]}
                    </p>
                  }
                >
                  <span className="truncate block" style={{ width: 144 }}>
                    {b[filterColumnKey]}
                  </span>
                </Tooltip>
              ) : (
                <span className="block">{b[filterColumnKey]}</span>
              ),
          })
        }
        return a
      }, [])
    )
    const [showList, setShowList] = useSafeState(selectedRef.current)
    const [selectedListNew, setSelectedListNew] = useSafeState<any[]>(
      allTableData?.reduce((a, b) => {
        // 选择项根据全部数据和当前数据的差值构成
        if (
          !!b[filterColumnKey] &&
          a.findIndex((innerItem) => innerItem.id === b[filterColumnKey]) === -1 &&
          curTableData.findIndex(
            (tableItem) => tableItem[filterColumnKey] === b[filterColumnKey]
          ) !== -1
        ) {
          a.push(b[filterColumnKey])
        }
        return a
      }, [])
    )
    const lastSelectedList = useRef<any[]>(selectedListNew)
    const [inputValue, setInputValue] = useSafeState('')
    const getShowListFromData = (data: any[] = []) => {
      return data?.reduce((a, b) => {
        if (
          !!filterColumnKey &&
          !!b[filterColumnKey] &&
          a.findIndex((item) => item === b[filterColumnKey]) === -1
        ) {
          a.push(b[filterColumnKey])
        }
        return a
      }, [])
    }
    const [sameAsInit, setSameAsInit] = useSafeState(
      getShowListFromData(allTableData)?.length === getShowListFromData(curTableData)?.length
    )

    // 搜索下拉框中与输入值模糊匹配的值
    const searchFilterData = (keyword) => {
      if (keyword.length !== 0) {
        setShowList((prev) => {
          const newArr = prev.filter((item) => item?.id?.includes(keyword))
          return [...newArr]
        })
        setSameAsInit(true)
      } else {
        setShowList(selectedRef.current)
        setSelectedListNew((prev) => {
          return Array.from(new Set(lastSelectedList.current.concat(prev)))
        }) // 初始化
        setSameAsInit(true)
      }
    }

    // input框输入值改变时
    const searchInputChange = (e) => {
      setInputValue(e.target.value)
      searchFilterData(e.target.value)
    }

    // 重置过滤组件
    const resetTableFilter = () => {
      setSameAsInit(true)
      setInputValue('')
      setShowList(selectedRef.current)
      setSelectedListNew(selectedRef.current.map((item) => item?.id))
      lastSelectedList.current = selectedRef.current.map((item) => item?.id)
    }

    // 通过所选的过滤条件更新表格数据
    const confirmTableFilter = () => {
      setSelectedList((prev) => {
        return { ...prev, [filterColumnKey]: selectedListNew }
      })
      setDetailIconObj((prev) => {
        return { ...prev, [filterColumnKey]: true }
      })
      setFilterDropdownVisible(false)
      trackFun?.()
    }

    useEffect(() => {
      // 更新重置按钮状态
      const initArr = selectedRef.current.map((item) => item?.id) || []
      !initRender.current && setSameAsInit(selectedListNew?.length === initArr?.length)
      initRender.current = false
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedListNew])

    return (
      <>
        <div className="filter_input">
          <Input
            clearable
            clearableTrigger="always"
            placeholder={intl.get('搜索')}
            value={inputValue}
            onChange={(e) => searchInputChange(e)}
          ></Input>
        </div>

        <div className="filter_content">
          {showList.length !== 0 ? (
            <CheckboxGroup
              data={showList}
              placement="vertical"
              value={selectedListNew}
              onChange={(value) => {
                const newValue = Array.from(new Set(value))
                setSelectedListNew(newValue)
                lastSelectedList.current = newValue
              }}
            />
          ) : (
            <span className="no_data_span">{intl.get('暂无数据')}</span>
          )}
        </div>

        <div className="filter_foot_btns">
          <Button type="default" size="sm" disabled={sameAsInit} onClick={resetTableFilter}>
            {intl.get('全选')}
          </Button>
          <Button type="primary" size="sm" onClick={confirmTableFilter}>
            {intl.get('确定')}
          </Button>
        </div>
      </>
    )
  }
)

TableFilter.displayName = 'TableFilter'

export default TableFilter
