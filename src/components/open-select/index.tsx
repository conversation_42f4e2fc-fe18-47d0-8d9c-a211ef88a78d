import { getNewProjectListCondition } from '@/api/gsc/project-list'
import { valuesToSearch } from '@/views/gsc/cost/project-list/config'
import Select from '@hi-ui/select'
import { useRequest } from 'ahooks'
import React, { memo, useState, useCallback } from 'react'

export const OpenSelect = memo<{
  selectKey: string
  filter: Record<string, string[] | string>
  formatter?: AnyType
  api?: AnyType
  [key: string]: AnyType
}>((props) => {
  const { selectKey, filter, formatter, api, ...rest } = props
  const [selectData, setSelectData] = useState<{ value: string; name: string }[]>([
    { value: 'USD', name: '美元' },
  ])

  const format = formatter || valuesToSearch

  const { runAsync: getCondition, loading } = useRequest(api || getNewProjectListCondition, {
    manual: true,
  })

  const onOpen = useCallback(() => {
    getCondition(format({ ...filter, [selectKey]: '', searchName: selectKey })).then(
      (res: AnyType) => setSelectData(res.data)
    )
  }, [getCondition, format, filter, selectKey])

  return (
    <Select
      loading={loading}
      data={selectData as AnyType[]}
      onOpen={onOpen}
      fieldNames={{ id: 'value', title: 'name' }}
      {...rest}
    />
  )
})
OpenSelect.displayName = 'OpenSelect'
