import './index.scss'

import React, { Dispatch, memo, SetStateAction } from 'react'
import intl from 'react-intl-universal'

import * as API from '@/api/gsc/form-view'
import { EmptyFunctionType } from '@/constants'
import Button from '@hi-ui/button'
import { DownOutlined } from '@hi-ui/icons'

import FormView from '../form-view'

interface IFormSearchProps {
  onReset: EmptyFunctionType
  onSearch: EmptyFunctionType
  formValues: AnyType // 根据实际情况定义类型
  setFilter: Dispatch<SetStateAction<AnyType>> // 根据实际情况定义类型
  formatFormValues: (values: AnyType) => AnyType // 根据实际情况定义类型
  pageType: string
  expanded?: boolean
  setExpanded?: Dispatch<SetStateAction<boolean>>
  showExpand?: boolean
}

const FormSearch = memo<IFormSearchProps>((props) => {
  const {
    onReset,
    onSearch,
    formValues,
    setFilter,
    formatFormValues,
    pageType,
    expanded,
    setExpanded,
    showExpand = true,
  } = props

  return (
    <div className="form-search__wrapper w-max">
      {showExpand && (
        <Button appearance="link" onClick={() => setExpanded?.((expanded) => !expanded)}>
          {expanded ? intl.get('展开') : intl.get('收起')}
          <DownOutlined
            color="#9d9d9f"
            style={{
              transform: expanded ? 'rotateZ(0)' : 'rotateZ(180deg)',
            }}
          />
        </Button>
      )}
      <Button type="default" onClick={onReset}>
        {intl.get('重置')}
      </Button>
      <div style={{ display: 'flex' }}>
        <Button className="form-search__button" type="secondary" onClick={onSearch}>
          {intl.get('查询')}
        </Button>
        <FormView
          api={API}
          filter={formValues}
          pageType={pageType}
          onSearch={setFilter}
          clickFormatter={formatFormValues}
        />
      </div>
    </div>
  )
})
FormSearch.displayName = 'FormSearch'
export default FormSearch
