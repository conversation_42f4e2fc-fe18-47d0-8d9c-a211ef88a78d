import intl from 'react-intl-universal'
import React, { FC, useCallback, useRef } from 'react'
import { CheckOutlined } from '@hi-ui/icons'
import { Button } from '@hi-ui/button'
import message from '@hi-ui/message'
import { Tag } from '@hi-ui/tag'
import { useHover, useRequest } from 'ahooks'
import cx from 'classnames'
import { getProjectCostList } from '@/api/cost/project-cost'
import { useProjectCost } from '@/hooks/useProjectCost'
import { useIndicator } from '@/hooks/useIndicator'
import { PROJECT_COST } from '@/constants'

import './view-item.scss'

const ViewItem: FC<{
  view: Record<string, string | number>
}> = ({ view }) => {
  const { activeViewId, setActiveViewId, del, get, update, setRateType } = useProjectCost()
  const { assignFetchedResult, setState, setConfigFromView } = useIndicator()
  const { id, title, isDefault, content } = view
  const ref = useRef(null)
  const isHovering = useHover(ref)

  const { runAsync: getList } = useRequest((params) => getProjectCostList(params), {
    manual: true,
    onBefore: () => {
      setState({ data: [], isLoading: true })
    },
  })

  const clickView = useCallback(() => {
    try {
      const { formConfig, tableColumnConfig } = JSON.parse(content as string)
      const { startMonth = '', endMonth = '', exchangeRateType = 'CNY' } = formConfig
      const formatFormConfig = { ...formConfig, range: [startMonth, endMonth] }
      setConfigFromView(formatFormConfig)
      getList({ ...formConfig, tableColumns: tableColumnConfig }).then((res) => {
        assignFetchedResult(res)
        setActiveViewId(id as number)
        setRateType(exchangeRateType)
      })
    } catch (err) {
      message.open({
        title: (err as Error)?.message || intl.get('获取视图信息失败'),
        type: 'error',
      })
    }
  }, [assignFetchedResult, content, getList, id, setActiveViewId, setConfigFromView, setRateType])

  const clickSetDefault = useCallback(
    (e) => {
      e.stopPropagation()
      update({
        isDefault: 1,
        pageType: PROJECT_COST,
        id,
      }).then(() => {
        get({ pageType: PROJECT_COST })
      })
    },
    [get, id, update]
  )

  const clickDelete = useCallback(
    (e) => {
      e.stopPropagation()
      del(id).then(() => get({ pageType: PROJECT_COST }))
    },
    [del, id, get]
  )

  return (
    <div className={cx('view-item-wrapper', { isHovering })} ref={ref} onClick={clickView}>
      <div>
        <span>{title}</span>
        {!!isDefault && (
          <Tag size="md" type="primary" appearance="filled" className="tag-content">
            {intl.get('默认')}
          </Tag>
        )}
      </div>
      <div>
        {activeViewId === id && <CheckOutlined color="#237FFA" />}
        {isHovering && activeViewId !== id && (
          <Button appearance="link" type="primary" onClick={clickDelete}>
            {intl.get('删除')}
          </Button>
        )}
        {isHovering && !isDefault && activeViewId !== id && (
          <Button appearance="link" type="primary" onClick={clickSetDefault}>
            {intl.get('设为默认')}
          </Button>
        )}
      </div>
    </div>
  )
}

export default ViewItem
