import intl from 'react-intl-universal'
import React, { memo, useCallback, useMemo, useState } from 'react'
import { CloseOutlined, PlusOutlined } from '@hi-ui/icons'
import { Checkbox } from '@hi-ui/checkbox'
import { message } from '@hi-ui/message'
import { Button } from '@hi-ui/button'
import Input from '@hi-ui/input'
import cx from 'classnames'
import { useProjectCost } from '@/hooks/useProjectCost'
import { useIndicator } from '@/hooks/useIndicator'
import { MESSAGE_DURATION, PROJECT_COST } from '@/constants'
import ViewItem from './ViewItem'

import './index.scss'

const MAX_VIEWS_SIZE = 7

const EditView = memo<{
  filter: Record<string, number | string | string[]>
}>(({ filter }) => {
  const { views, get, add } = useProjectCost()
  const [justClickAdd, setJustClickAdd] = useState<boolean>(false)
  const [isDefaultCheckbox, setIsDefaultCheckbox] = useState<boolean>(false)
  const { allColumns, hiddenColumns, configFromView } = useIndicator()
  const [viewName, setViewName] = useState<string>('')

  const clickAdd = useCallback(() => {
    if (views.length >= MAX_VIEWS_SIZE) {
      message.open({ type: 'info', title: intl.get('视图容量达上限，请删除后添加') })
    } else {
      setJustClickAdd(true)
    }
  }, [views])

  const clear = useCallback(() => {
    setIsDefaultCheckbox(false)
    setViewName('')
  }, [])

  const changeCheckbox = useCallback((evt) => setIsDefaultCheckbox(evt.target.checked), [])

  const changeViewName = useCallback((e) => {
    const name = e.target.value.trim() || ''
    setViewName(name)
  }, [])

  const allColumnsMemo = useMemo(() => allColumns.map((i) => i.code), [allColumns])
  const hiddenColumnsMemo = useMemo(() => hiddenColumns.map((i) => i.code), [hiddenColumns])

  const saveAdd = useCallback(() => {
    const lastFilter = Object.keys(filter).length ? filter : configFromView
    const { type, projectNames } = lastFilter
    if (!viewName) {
      message.open({ title: intl.get('请填写视图名称'), type: 'info', duration: MESSAGE_DURATION })
    } else if (!type) {
      message.open({
        title: intl.get('请选择维度查询后保存视图'),
        type: 'info',
        duration: MESSAGE_DURATION,
      })
    } else if (!projectNames?.[0]) {
      message.open({
        title: intl.get('请选择项目查询后保存视图'),
        type: 'info',
        duration: MESSAGE_DURATION,
      })
    } else {
      const columnConfig = allColumnsMemo.reduce((a: Record<string, string | boolean>[], b) => {
        const show = !hiddenColumnsMemo.includes(b)
        a.push({ column: b as string, show })
        return a
      }, [])

      const objToString =
        JSON.stringify({ formConfig: lastFilter, tableColumnConfig: columnConfig }) || ''
      const addItem = {
        pageType: PROJECT_COST,
        title: viewName,
        isDefault: isDefaultCheckbox ? 1 : 0,
        content: objToString,
      }
      add(addItem).then(() => {
        get({ pageType: PROJECT_COST })
        setJustClickAdd(false)
      })
    }
  }, [
    add,
    allColumnsMemo,
    configFromView,
    filter,
    get,
    hiddenColumnsMemo,
    isDefaultCheckbox,
    viewName,
  ])

  const cancelAdd = useCallback(() => {
    setJustClickAdd(false)
    clear()
  }, [clear])

  return (
    <div className={cx('edit-view-wrapper', { adding: justClickAdd })}>
      {justClickAdd ? (
        <div className="add-modal">
          <div className="title">
            {intl.get('添加视图')}
            <Button icon={<CloseOutlined />} appearance="link" onClick={cancelAdd}></Button>
          </div>
          <div className="form">
            <Input
              placeholder={intl.get('请填写视图名称')}
              value={viewName}
              onChange={changeViewName}
            />
          </div>
          <div className="footer">
            <Checkbox checked={isDefaultCheckbox} onChange={changeCheckbox}>
              {intl.get('设为默认')}
            </Checkbox>
            <div className="footer-button">
              <Button type="default" onClick={cancelAdd}>
                {intl.get('取消')}
              </Button>
              <Button type="primary" onClick={saveAdd}>
                {intl.get('保存')}
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div>
          {views?.length > 0 && (
            <div className="views-wrapper">
              {views.map((view, index) => (
                <ViewItem view={view} key={index} />
              ))}
            </div>
          )}
          <div className="add-button">
            <Button type="primary" icon={<PlusOutlined />} appearance="link" onClick={clickAdd}>
              {intl.get('添加视图')}
            </Button>
          </div>
        </div>
      )}
    </div>
  )
})

EditView.displayName = 'EditView'

export default EditView
