import Button from '@hi-ui/button'
import { CloseOutlined } from '@hi-ui/icons'
import message from '@hi-ui/message'
import React, { ReactText } from 'react'

export const alwaysMessage = (
  arr: { type: string; message: string }[],
  type: 'info' | 'success' | 'error' | 'warning' = 'info'
) => {
  if (arr?.length > 0) {
    const toastId = message.open({
      type,
      autoClose: false,
      title: (
        <div style={{ display: 'flex', alignItems: 'flex-start' }}>
          <div>
            {arr.map((item, index) => (
              <div key={item.message}>
                {index + 1}. {item.message}
              </div>
            ))}
          </div>
          <Button
            style={{ marginLeft: 12 }}
            type="primary"
            appearance="link"
            onClick={() => message.close(toastId as unknown as ReactText)}
            icon={<CloseOutlined />}
          />
        </div>
      ),
    })
  }
}
