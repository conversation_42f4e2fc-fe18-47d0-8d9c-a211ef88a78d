import intl from 'react-intl-universal'
import React, { useCallback, useEffect, useRef } from 'react'
import { DashboardConfig } from '@/api/gsc/bi'
import { usePBIConfig } from '@/hooks/usePBIConfig'
import dayjs from 'dayjs'
import trackHelp from '@/utils/track-help'
import { PBI_DASHBOARD } from '@/constants/track'
import { getDepart } from '@/utils/get-department'
import { message } from '@hi-ui/message'
import { useUnmount } from 'ahooks'
import { pbiEmbed, pbiReport, pbiReset } from '@/utils/pbi'

export const PBIContainer = (props: { config: DashboardConfig }) => {
  const enterTime = useRef<number>(dayjs().valueOf())
  const domRef = useRef(document.querySelector('.power-bi-container') as HTMLElement)
  const containerRef = useRef(document.querySelector('.gsc-content-container') as HTMLElement)
  const maskRef = useRef(document.querySelector('.pbi-wrapper-mask') as HTMLElement)
  const trackTime = useRef({})
  const { PBIConfig } = usePBIConfig(props.config.id)
  const calculateGap = useCallback(() => dayjs().valueOf() - enterTime.current, [])

  useEffect(() => {
    enterTime.current = dayjs().valueOf()
    trackTime.current = {
      dashboard_id: props.config.id,
      page_enter_time: enterTime.current,
      page_enter_format_time: dayjs(enterTime.current).format('YYYY-MM-DD HH:mm:ss'),
    }
  }, [props.config.id])

  useUnmount(() => {
    const container = containerRef.current
    domRef.current && (domRef.current.style.display = 'none')
    maskRef.current && (maskRef.current.style.display = 'block')
    if (container) {
      container.style.display = 'flex'
      container.parentElement && (container.parentElement.style.height = '100%')
      container.parentElement?.parentElement &&
        (container.parentElement.parentElement.style.height = '100%')
    }
  })

  const updateTimeTrack = useCallback(
    (timeType, isFinish = false) => {
      trackTime.current = {
        ...trackTime.current,
        [timeType]: calculateGap(),
      }
      if (isFinish) {
        trackHelp.trackEvent(PBI_DASHBOARD.event, {
          tip: PBI_DASHBOARD.tip,
          ...trackTime.current,
          ...getDepart(),
        })
      }
    },
    [calculateGap]
  )

  const showPBIContainer = useCallback(() => {
    domRef.current && (domRef.current.style.display = 'block')
    if (containerRef.current) {
      containerRef.current.style.display = 'none'
      const content = containerRef.current.parentElement as HTMLElement
      content && (content.style.height = 'auto')
      content?.parentElement && (content.parentElement.style.height = 'auto')
    }
  }, [])

  const embedPBI = useCallback(
    (config) => {
      if (!domRef.current || !config.accessToken) {
        console.warn('DOM IS NOT READY YET', domRef.current)
        const timer = setTimeout(() => {
          domRef.current = document.querySelector('.power-bi-container') as HTMLElement
          containerRef.current = document.querySelector('.gsc-content-container') as HTMLElement
          maskRef.current = document.querySelector('.pbi-wrapper-mask') as HTMLElement
          embedPBI(config)
          clearTimeout(timer)
        }, 100)
        return
      }

      const domNode = domRef.current?.firstChild as HTMLElement
      const isLoaded = domNode?.childNodes?.length
      // pbi嵌入配置
      const report = pbiEmbed(domNode, config)

      showPBIContainer()

      if (report) {
        report.on('loaded', () => {
          updateTimeTrack('loaded_time')
          report.off('loaded')
          if (isLoaded) {
            maskRef.current && (maskRef.current.style.display = 'none')
          }
        })
        report.on('rendered', () => {
          updateTimeTrack('rendered_time', true)
          report.off('rendered')
        })
      } else {
        message.open({ title: intl.get('PBI看板加载异常，请刷新重试'), type: 'error' })
        console.error({ pbi: window.powerbi, config })
      }
    },
    [domRef, updateTimeTrack, showPBIContainer]
  )

  useEffect(() => {
    const { accessToken } = window.__POWER_BI_CONFIG__ || {}
    const { accessToken: newToken } = PBIConfig || {}
    const { iframeLoaded } = pbiReport || {}
    // 同一个看板直接展示，不重复渲染
    if (newToken && accessToken !== newToken) {
      updateTimeTrack('token_time')
      // 如果预加载未完成就先reset再加载
      if (iframeLoaded === false) {
        pbiReset(domRef.current?.firstChild as HTMLElement)
      }
      embedPBI(PBIConfig)
    } else {
      showPBIContainer()
      accessToken === newToken && maskRef.current && (maskRef.current.style.display = 'none')
    }
  }, [PBIConfig, embedPBI, updateTimeTrack, showPBIContainer])

  return <></>
}
