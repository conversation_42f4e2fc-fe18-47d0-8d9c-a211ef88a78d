import { mockDefaultHandlers } from '@hi-ui/dom-utils'
import { omitPopperOverlayProps } from '@hi-ui/popper'
import { mergeRefs, withDefaultProps } from '@hi-ui/react-utils'
import { useLatestCallback } from '@hi-ui/use-latest'
import { useUncontrolledToggle } from '@hi-ui/use-toggle'
import { useCallback, useState } from 'react'

interface UsePopConfirmProps {
  visible?: boolean
  onOpen?: () => void
  onClose?: () => void
  onCancel?: () => void
  onConfirm?: () => Promise<boolean> | void
}

export const usePopConfirm = ({
  visible: visibleProp,
  onCancel: onCancelProp,
  onConfirm: onConfirmProp,
  onOpen,
  onClose,
  ...restProps
}: UsePopConfirmProps) => {
  const [popper, rest] = omitPopperOverlayProps(restProps)

  const [visible, visibleAction] = useUncontrolledToggle({
    defaultVisible: false,
    visible: visibleProp,
    onOpen,
    onClose: () => {
      onClose?.()
      onCancelProp?.()
    },
  })

  const onCancel = useCallback(() => {
    visibleAction.off()
  }, [visibleAction])

  const onConfirmLatest = useLatestCallback(onConfirmProp)

  const onConfirm = useCallback(async () => {
    const confirmRes = await onConfirmLatest()
    if (confirmRes) {
      visibleAction.off()
    }
  }, [visibleAction, onConfirmLatest])

  const [targetEl, setTargetEl] = useState<HTMLElement | null>(null)

  const getTriggerProps = useCallback(
    (props, ref) => {
      return {
        ref: mergeRefs(setTargetEl, ref),
        onClick: mockDefaultHandlers(props.onClick, visibleAction.not),
      }
    },
    [visibleAction, setTargetEl]
  )

  const getPopperProps = useCallback(() => {
    const popperProps = withDefaultProps(popper, {
      arrow: true,
      placement: 'top',
      gutterGap: 14,
    })

    return {
      ...popperProps,
      visible,
      attachEl: targetEl,
      onClose: visibleAction.off,
    }
  }, [visible, targetEl, popper, visibleAction])

  const rootProps = {
    role: 'alert-dialog',
    ...rest,
  }

  return {
    rootProps,
    getTriggerProps,
    getPopperProps,
    onCancel,
    onConfirm,
  }
}
