import Button from '@hi-ui/button'
import Popper from '@hi-ui/popper'
import React, { forwardRef } from 'react'

import { usePopConfirm } from './use-pop-confirm'
import { CloseOutlined } from '@hi-ui/icons'
import './index.scss'
import intl from 'react-intl-universal'
export interface PopConfirmProps {
  title: React.ReactNode
  content: React.ReactNode
  confirmText?: React.ReactNode
  children?: React.ReactNode
  footer?: React.ReactNode
  loading?: boolean
  visible?: boolean
  onClose?: () => void
  onCancel?: () => void
  onConfirm?: () => void
  placement?: string
  width?: string | number
}

const PopModal = forwardRef<HTMLDivElement | null, PopConfirmProps>(
  ({ children, content, title, confirmText, loading, footer, width, ...rest }, ref) => {
    const { rootProps, getPopperProps, getTriggerProps, onCancel, onConfirm } = usePopConfirm(rest)

    const hasFooter = footer !== null

    return (
      <>
        {React.isValidElement(children)
          ? React.cloneElement(
              children,
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              getTriggerProps(children.props, children.ref)
            )
          : null}
        <Popper {...getPopperProps()}>
          <div ref={ref} className="pop-modal modal-header" style={{ width }} {...rootProps}>
            <section className="modal-wrap">
              <div className="modal-header ">
                <div className="title">{title}</div>
                <div className="extra">
                  <CloseOutlined className="close-icon" onClick={onCancel} />
                </div>
              </div>
              <div className="modal-content">{content}</div>
            </section>

            {hasFooter ? (
              <footer className="footer">
                {footer === undefined
                  ? [
                      <Button key="1" type="default" onClick={onCancel}>
                        {intl.get('取消')}
                      </Button>,
                      <Button key="2" type="primary" onClick={onConfirm} loading={loading}>
                        {confirmText || intl.get('保存')}
                      </Button>,
                    ]
                  : footer}
              </footer>
            ) : null}
          </div>
        </Popper>
      </>
    )
  }
)

PopModal.displayName = 'PopModal'

export default PopModal
