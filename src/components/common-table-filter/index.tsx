import intl from 'react-intl-universal'
import React, { Fragment, ReactNode, memo, useCallback, useMemo, useRef, useState } from 'react'
import VirtualList from 'react-tiny-virtual-list'
import { Checkbox } from '@hi-ui/checkbox'
import Button from '@hi-ui/button'
import Input from '@hi-ui/input'
import './index.scss'

export type SetType<T> = React.Dispatch<React.SetStateAction<T>>

interface Props {
  setVisible: (visible: boolean) => void
  columnKey: string
  data: { id: string; title: ReactNode }[]
  reShow: Record<string, boolean>
  setReShow: SetType<Record<string, Record<string, boolean>>>
  setMultiFilter: SetType<Record<string, string[]>>
  filterSameAsInit: React.MutableRefObject<Record<string, boolean>>
  setIconClickObj: SetType<Record<string, boolean>>
}

const CommonTableFilter = memo<Props>(
  ({
    setVisible,
    columnKey,
    data,
    reShow,
    setReShow,
    setMultiFilter,
    filterSameAsInit,
    setIconClickObj,
  }) => {
    const initData = useRef(data)
    const [curDataState, setCurDataState] = useState(data)
    const [searchValue, setSearchValue] = useState<string>('')
    const [hasChecked, setHasChecked] = useState(Object.keys(reShow).length !== 0 ? reShow : {})

    const confirmTableFilter = useCallback(() => {
      setVisible(false)
      filterSameAsInit.current[columnKey] = false
      setMultiFilter((prev) => {
        const arr =
          data
            .filter((item) =>
              Object.entries(hasChecked)
                .filter((item) => item?.[1])
                .map((arrItem) => arrItem?.[0])
                .includes(item.id)
            )
            .map((i) => i.id) || []
        return { ...prev, [columnKey]: arr }
      })

      setIconClickObj((prev) => {
        return { ...prev, [columnKey]: Object.keys(hasChecked).some((item) => hasChecked[item]) }
      })

      setReShow((prev) => {
        return {
          ...prev,
          [columnKey]: hasChecked,
        }
      })
      if (Object.keys(hasChecked).length === 0) filterSameAsInit.current[columnKey] = true
    }, [
      columnKey,
      data,
      filterSameAsInit,
      hasChecked,
      setIconClickObj,
      setMultiFilter,
      setReShow,
      setVisible,
    ])

    // 搜索下拉框中与输入值模糊匹配的值
    const searchFilterData = (keyword) => {
      if (keyword.length !== 0) {
        setCurDataState(initData.current.filter((item) => item.id.includes(keyword)))
      } else {
        setCurDataState(initData.current)
      }
    }

    // input框输入值改变时
    const onInputChange = (e) => {
      const value = e.target.value
      setSearchValue(value)
      searchFilterData(value.trim())
    }

    const checkAllOrCancelAll = (flag: boolean) => {
      const allSelect = {}
      curDataState.forEach((item) => {
        allSelect[item.id] = flag
      })
      setHasChecked((prev) => {
        return { ...prev, ...allSelect }
      })
    }

    const checkedLength = useMemo(
      () => Object.values(hasChecked).filter((i) => i).length,
      [hasChecked]
    )

    const resetTableFilter = () => {
      setSearchValue('')
      setHasChecked({})
    }

    return (
      <Fragment>
        <div className="filter-input">
          <Input
            clearable
            clearableTrigger="always"
            placeholder={intl.get('搜索')}
            value={searchValue}
            onChange={onInputChange}
          />
        </div>
        <div className="filter-content">
          {curDataState.length !== 0 ? (
            <VirtualList
              style={{ overflowX: 'hidden' }}
              width={184}
              height={200}
              itemCount={curDataState.length || 0}
              itemSize={32}
              renderItem={({ index, style }) => {
                const id = curDataState?.[index]?.id
                return (
                  <div key={index} className="filter-content__item" style={style}>
                    <Checkbox
                      key={id}
                      value={id}
                      checked={!!hasChecked[id]}
                      onChange={(e) => {
                        const { checked: newestValue = false } = e.target
                        setHasChecked((prev) => {
                          prev[id] = newestValue
                          return { ...prev }
                        })
                      }}
                    >
                      {curDataState?.[index]?.title || '-'}
                    </Checkbox>
                  </div>
                )
              }}
            />
          ) : (
            <span className="filter-content__empty">{intl.get('暂无数据')}</span>
          )}
        </div>

        <div className="filter-footer">
          {searchValue && curDataState.length !== 0 ? (
            <>
              <Button
                type="default"
                size="sm"
                disabled={checkedLength === 0}
                onClick={() => checkAllOrCancelAll(false)}
              >
                {intl.get('全不选')}
              </Button>
              <Button
                type="default"
                size="sm"
                disabled={checkedLength >= curDataState.length}
                onClick={() => checkAllOrCancelAll(true)}
              >
                {intl.get('全选')}
              </Button>
            </>
          ) : (
            <Button
              type="default"
              size="sm"
              disabled={checkedLength === 0}
              onClick={resetTableFilter}
            >
              {intl.get('重置')}
            </Button>
          )}
          <Button
            type="primary"
            size="sm"
            onClick={confirmTableFilter}
            disabled={
              checkedLength === 0 &&
              (!Object.prototype.hasOwnProperty.call(filterSameAsInit.current, columnKey) ||
                filterSameAsInit.current?.[columnKey])
            }
          >
            {intl.get('确定')}
          </Button>
        </div>
      </Fragment>
    )
  }
)

CommonTableFilter.displayName = 'CommonTableFilter'
export default CommonTableFilter
