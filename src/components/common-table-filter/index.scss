@import '@/common/styles/vars';

.todo-table-custom__filter {
  width: 184px;
  max-height: 300px;
  min-height: 100px;
  padding: 8px 8px 12px;

  .filter-input {
    margin-bottom: 8px;

    .hi-v4-input__outer {
      .hi-v4-input__inner {
        border-top: none;
        border-left: none;
        border-right: none;
        border-radius: 0;

        input.hi-v4-input__text {
          position: relative;
          left: 22px;
          padding-top: 6px;
          padding-bottom: 6px;
        }

        span.hi-v4-input__suffix {
          position: relative;
          right: 0;
        }
      }
    }

    div.hi-v4-input__inner::before {
      content: '';
      position: absolute;
      display: block;
      height: 16px;
      width: 16px;
      left: 8px;
      top: 8px;
      background-image: url('#{$img-url}filter_search.svg');
      z-index: 1;
    }

    .hi-v4-input--appearance-line
      .hi-v4-input__inner:not(.hi-v4-inputinner--disabled).hi-v4-input__inner--focused {
      box-shadow: none;
    }
  }

  .filter-content {
    min-height: 32px;
    max-height: 200px;
    overflow: hidden overlay;

    .filter-content__item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding-left: 8px;
    }

    .filter-content__empty {
      height: 16px;
      padding: 8px;
      font-size: 0.875rem;
      color: #929aa6;
      display: flex;
      align-items: center;
    }
  }

  .filter-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 8px;
  }
}
