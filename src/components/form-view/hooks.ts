import { useEffect, useRef, useState } from 'react'
import message from '@hi-ui/message'
import { useRequest } from 'ahooks'
import { AnyType } from '@/constants'

export const useCRUDView = (API) => {
  const [views, setViews] = useState([])
  const [activeViewId, setActiveViewId] = useState('')
  const initActive = useRef<boolean>(true)

  const handleError = (msg: string) => {
    return {
      onError: (err) => {
        message.open({
          type: 'error',
          title: err.message || `${msg}失败，请重试`,
        })
      },
    }
  }

  const { run: get, loading: isGetting } = useRequest((params) => API.get(params), {
    manual: true,
    onSuccess: (res: AnyType) => setViews(res?.data || []),
    ...handleError('获取'),
  })

  const { runAsync: add, loading: isAdding } = useRequest((data) => API.add(data), {
    manual: true,
    ...handleError('新增'),
  })

  const { runAsync: update, loading: isUpdating } = useRequest((data) => API.update(data), {
    manual: true,
    ...handleError('更新'),
  })

  const { runAsync: del, loading: isDeleting } = useRequest((id) => API.del(id), {
    manual: true,
    ...handleError('删除'),
  })

  useEffect(() => {
    const viewId = (views.find((item: AnyType) => item?.isDefault === 1) as AnyType)?.id || ''
    if (initActive.current && viewId) {
      setActiveViewId(viewId)
      initActive.current = false
    }
  }, [views])

  return {
    get,
    add,
    update,
    del,
    views,
    isGetting,
    isAdding,
    isUpdating,
    isDeleting,
    activeViewId,
    setActiveViewId,
  }
}
