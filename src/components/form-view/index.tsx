import intl from 'react-intl-universal'
import React, { ReactNode, ReactText, memo, useState } from 'react'
import Tooltip from '@hi-ui/tooltip'
import Popover from '@hi-ui/popover'
import Button from '@hi-ui/button'
import { FilterFilled } from '@hi-ui/icons'
import { useCRUDView } from './hooks'
import View from './View'
import './index.scss'

const FormView = memo<{
  pageType: string
  filter: Record<string, string | string[] | ReactText>
  api
  onSearch?
  clickFormatter?
  icon?: ReactNode
  trackCode?: string
  buttonText?: string
}>(
  ({
    pageType,
    filter,
    api,
    onSearch,
    clickFormatter,
    icon = <FilterFilled />,
    trackCode,
    buttonText,
  }) => {
    const CRUDView = useCRUDView(api)
    const [hoverVisible, setHoverVisible] = useState<boolean>(false)

    return (
      <Tooltip
        title={intl.get('筛选视图')}
        trigger="hover"
        visible={buttonText ? false : hoverVisible}
      >
        <span>
          <Popover
            placement="bottom-end"
            content={
              <View
                pageType={pageType}
                filter={filter}
                onSearch={onSearch}
                clickFormatter={clickFormatter}
                trackCode={trackCode}
                CRUDView={CRUDView}
              />
            }
            arrow={false}
            gutterGap={9}
            className="form-view__popover"
          >
            <Button
              className={buttonText ? 'form-view-button-margin' : 'form-view-button'}
              type={buttonText ? 'default' : 'secondary'}
              icon={buttonText ? undefined : icon}
              onMouseEnter={() => setHoverVisible(true)}
              onMouseLeave={() => setHoverVisible(false)}
              onClick={() => setHoverVisible(false)}
            >
              {buttonText}
            </Button>
          </Popover>
        </span>
      </Tooltip>
    )
  }
)

FormView.displayName = 'FormView'

export default FormView
