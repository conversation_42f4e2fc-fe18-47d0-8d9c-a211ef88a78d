import intl from 'react-intl-universal'
import React, { memo, useCallback, useRef, useState } from 'react'
import { CheckOutlined, EditOutlined } from '@hi-ui/icons'
import { Button } from '@hi-ui/button'
import message from '@hi-ui/message'
import Input from '@hi-ui/input'
import { Tag } from '@hi-ui/tag'
import { useHover } from 'ahooks'
import cx from 'classnames'
import { refreshViewCache } from '@/utils/refresh-cache'
import { AnyType } from '@/constants'

import './view-item.scss'

const ViewItem = memo<{
  view: Record<string, string | number>
  update
  del
  get: AnyType
  activeViewId
  setActiveViewId
  pageType
  onSearch
  clickFormatter
}>(
  ({
    view,
    update,
    del,
    get,
    activeViewId,
    setActiveViewId,
    pageType,
    onSearch,
    clickFormatter,
  }) => {
    const { id, title, isDefault, content } = view
    const ref = useRef<HTMLDivElement>(null)
    const isHovering = useHover(ref)
    const [isEditing, setIsEditing] = useState<boolean>(false)
    const [inputValue, setInputValue] = useState<string>(title as string)

    const clickView = useCallback(() => {
      try {
        const { formConfig } = JSON.parse(content as string)
        onSearch(clickFormatter(formConfig))
        setActiveViewId(id as number)
      } catch (err) {
        message.open({
          title: (err as Error)?.message || intl.get('获取视图信息失败'),
          type: 'error',
        })
      }
    }, [content, onSearch, clickFormatter, setActiveViewId, id])

    const onDefault = useCallback(
      (e) => {
        e.stopPropagation()
        update({
          isDefault: 1,
          pageType,
          title,
          id,
        }).then(() => {
          get({ pageType })
          refreshViewCache()
        })
      },
      [get, id, pageType, title, update]
    )

    const onDelete = useCallback(
      (e) => {
        e.stopPropagation()
        del(id).then(() => {
          get({ pageType })
          message.open({ title: intl.get('删除成功'), type: 'success' })
          refreshViewCache()
        })
      },
      [del, id, get, pageType]
    )

    const onSave = useCallback(
      (e) => {
        e.stopPropagation()
        if (!inputValue) {
          message.open({ title: intl.get('请填写视图名称'), type: 'info' })
        } else if (inputValue?.length > 12) {
          message.open({ title: intl.get('视图名称最多12个字符'), type: 'info' })
        } else {
          update({ title: inputValue, id, pageType, isDefault }).then(() => {
            get({ pageType })
            message.open({ title: intl.get('修改成功'), type: 'success' })
            setIsEditing(false)
            refreshViewCache()
          })
        }
      },
      [get, id, inputValue, isDefault, pageType, update]
    )

    const onCancel = useCallback(
      (e) => {
        e.stopPropagation()
        setIsEditing(false)
        setInputValue(title as string)
      },
      [title]
    )

    const onEdit = useCallback(
      (e) => {
        e.stopPropagation()
        setIsEditing(true)
        setInputValue(title as string)
      },
      [title]
    )

    return (
      <div className={cx('view-item-wrapper', { isHovering })} ref={ref} onClick={clickView}>
        <div>
          {isEditing ? (
            <Input
              className="h-14 w-90"
              placeholder={intl.get('请输入')}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onClick={(e) => e.stopPropagation()}
            />
          ) : (
            <span className="mr-2">{title}</span>
          )}
          {isHovering && !isEditing && title !== '公共视图' && <EditOutlined onClick={onEdit} />}
          {isDefault && !isEditing ? (
            <Tag size="md" type="primary" appearance="filled" className="tag-content">
              {intl.get('默认')}
            </Tag>
          ) : null}
        </div>
        <div>
          {activeViewId === id && !isHovering && !isEditing && <CheckOutlined color="#237FFA" />}
          {isEditing && (
            <>
              <Button appearance="link" type="primary" onClick={onSave}>
                {intl.get('保存')}
              </Button>
              <Button appearance="link" type="primary" onClick={onCancel}>
                {intl.get('取消')}
              </Button>
            </>
          )}
          {isHovering && activeViewId !== id && !isEditing && title !== '公共视图' && (
            <Button appearance="link" type="primary" onClick={onDelete}>
              {intl.get('删除')}
            </Button>
          )}
          {isHovering && !isDefault && !isEditing && (
            <Button appearance="link" type="primary" onClick={onDefault}>
              {intl.get('默认')}
            </Button>
          )}
        </div>
      </div>
    )
  }
)

ViewItem.displayName = 'ViewItem'

export default ViewItem
