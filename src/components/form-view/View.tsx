import intl from 'react-intl-universal'
import React, { memo, useCallback, useState } from 'react'
import { CloseOutlined, PlusOutlined } from '@hi-ui/icons'
import { Checkbox } from '@hi-ui/checkbox'
import { Loading } from '@hi-ui/loading'
import { message } from '@hi-ui/message'
import { Button } from '@hi-ui/button'
import Input from '@hi-ui/input'
import cx from 'classnames'
import { refreshViewCache } from '@/utils/refresh-cache'
import { useMount } from 'ahooks'
import ViewItem from './ViewItem'
import { gscTrack } from '@/utils/gscTrack-help'
import { AnyType } from '@/constants'
import './view.scss'

type filterType = Record<string, number | string | string[]>
type ClickFormatterFunctionType = (filter: filterType) => filterType

const View = memo<{
  pageType: string
  filter: filterType
  onSearch
  clickFormatter: ClickFormatterFunctionType
  maxSize?: number
  CRUDView: Record<string, AnyType>
  trackCode?: string
}>(({ pageType, filter, onSearch, clickFormatter, maxSize = 7, CRUDView, trackCode }) => {
  const { views, get, add, isGetting, update, del, activeViewId, setActiveViewId } = CRUDView
  const [isAdd, setIsAdd] = useState<boolean>(false)
  const [isDefaultCheck, setIsDefaultCheck] = useState<boolean>(false)
  const [viewName, setViewName] = useState<string>('')

  useMount(() => get({ pageType }))

  const clickAdd = useCallback(() => {
    if (views.length >= maxSize) {
      message.open({ type: 'info', title: intl.get('自定义视图容量达上限，请删除后添加') })
    } else {
      setIsAdd(true)
    }
  }, [maxSize, views.length])

  const changeCheckbox = useCallback((evt) => setIsDefaultCheck(evt.target.checked), [])

  const changeViewName = useCallback((e) => {
    const name = e.target.value.trim() || ''
    setViewName(name)
  }, [])

  const saveAdd = useCallback(() => {
    const lastFilter = Object.keys(filter).length ? filter : {}
    if (!viewName) {
      message.open({ title: intl.get('请填写视图名称'), type: 'info' })
    } else if (viewName?.length > 12) {
      message.open({ title: intl.get('视图名称最多12个字符'), type: 'info' })
    } else {
      try {
        const filterToString = JSON.stringify({ formConfig: lastFilter }) || ''
        const addItem = {
          pageType,
          title: viewName,
          isDefault: isDefaultCheck ? 1 : 0,
          content: filterToString,
        }
        add(addItem).then(() => {
          get({ pageType })
          message.open({ title: intl.get('添加成功'), type: 'success' })
          setIsAdd(false)
          setIsDefaultCheck(false)
          setViewName('')
          refreshViewCache()
          trackCode && gscTrack.pageElemClick(trackCode, 'save', '01')
        })
      } catch (err) {}
    }
  }, [pageType, filter, isDefaultCheck, trackCode, viewName, get, add])

  const cancelAdd = useCallback(() => {
    setIsAdd(false)
    setIsDefaultCheck(false)
    setViewName('')
  }, [])

  return (
    <div className={cx('edit-view-wrapper', { adding: isAdd })}>
      <Loading content="" visible={isGetting} delay={300}>
        {isAdd ? (
          <div className="add-modal">
            <div className="title">
              {intl.get('添加视图')}
              <Button icon={<CloseOutlined />} appearance="link" onClick={cancelAdd}></Button>
            </div>
            <div className="form">
              <Input
                placeholder={intl.get('请填写视图名称')}
                value={viewName}
                onChange={changeViewName}
              />
            </div>
            <div className="footer">
              <Checkbox checked={isDefaultCheck} onChange={changeCheckbox}>
                {intl.get('设为默认')}
              </Checkbox>
              <div className="footer-button">
                <Button type="default" onClick={cancelAdd}>
                  {intl.get('取消')}
                </Button>
                <Button type="primary" onClick={saveAdd}>
                  {intl.get('保存')}
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div>
            {views?.length > 0 && (
              <div className="views-wrapper">
                {views.map((view, index) => (
                  <ViewItem
                    key={index}
                    view={view}
                    update={update}
                    del={del}
                    get={get}
                    activeViewId={activeViewId}
                    setActiveViewId={setActiveViewId}
                    pageType={pageType}
                    onSearch={onSearch}
                    clickFormatter={clickFormatter}
                  />
                ))}
              </div>
            )}
            <div className="add-button">
              <Button type="primary" icon={<PlusOutlined />} appearance="link" onClick={clickAdd}>
                {intl.get('添加视图')}
              </Button>
            </div>
          </div>
        )}
      </Loading>
    </div>
  )
})

View.displayName = 'View'

export default View
