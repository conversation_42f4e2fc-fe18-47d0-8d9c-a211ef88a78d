import React, { memo, ReactText } from 'react'
import cx from 'classnames'
import './index.scss'
import { SetType } from '@/types/type'

interface CustomTabProps {
  tabItems: {
    tabId: string
    tabTitle: string
  }[]
  currentTab: string
  setCurrentTab: SetType<ReactText>
  className?: string
  position?: 'left' | 'right'
}

const CustomTab = memo<CustomTabProps>(
  ({ tabItems, currentTab, setCurrentTab, className, position = 'left' }) => {
    return (
      <div className={cx('custom-tab__wrapper', className)}>
        {tabItems.map((tab, index) => {
          const len = tabItems.length
          return len === 1 ? (
            <span key={tab.tabId} className={cx('single', { active: currentTab === tab.tabId })}>
              {tab.tabTitle}
            </span>
          ) : (
            <span
              key={tab.tabId}
              className={cx(
                { start: index === 0 },
                { mid: index > 0 && index < tabItems.length - 1 },
                { end: index === tabItems.length - 1 },
                { active: currentTab === tab.tabId },
                { right: position === 'right' }
              )}
              onClick={() => setCurrentTab(tab.tabId)}
            >
              {tab.tabTitle}
            </span>
          )
        })}
      </div>
    )
  }
)
CustomTab.displayName = 'CustomTab'
export default CustomTab
