// 定义通用样式变量
$tab-height: 32px !default;
$tab-padding: 0 12px !default;
$tab-bg-color: #f2f4f7 !default;
$tab-text-color: #1f2733 !default;
$tab-active-color: #237ffa !default;
$tab-active-bg: #e2f3fe !default;
$divider-color: #dfe2e8 !default;

// 通用标签基础样式混合
@mixin tab-base {
  padding: $tab-padding;
  height: $tab-height;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: $tab-bg-color;
  color: $tab-text-color;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;

  &:hover {
    cursor: pointer;
  }

  &.active {
    color: $tab-active-color;
    background-color: $tab-active-bg;
  }
}

// 分隔线混合
@mixin tab-divider {
  &::after {
    content: '';
    position: absolute;
    width: 1px;
    height: 16px;
    background-color: $divider-color;
    right: 0;
    top: 8px;
  }
}

.custom-tab__wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background-color: #fff;

  .single {
    padding: 0 12px;
    height: 32px;
    border-radius: 4px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f2f4f7;
    color: #1f2733;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;

    &:hover {
      cursor: pointer;
    }
  }

  .single.active {
    color: #237ffa;
    background-color: #e2f3fe;
  }

  .start {
    @include tab-base;

    border-radius: 4px 0 0 4px;

    @include tab-divider;
  }

  .mid {
    @include tab-base;
    @include tab-divider;
  }

  .end {
    @include tab-base;

    margin-right: 18px;
    border-radius: 0 4px 4px 0;
  }

  .end.right {
    margin-right: 0;
  }
}
