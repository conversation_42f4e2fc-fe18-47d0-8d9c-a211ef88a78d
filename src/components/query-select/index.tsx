import { useDebounceFn } from 'ahooks'
import React, { CSSProperties, memo } from 'react'
import intl from 'react-intl-universal'

import { EllipsisTooltip } from '@hi-ui/ellipsis-tooltip'
import message from '@hi-ui/message'
import Select from '@hi-ui/select'

const QuerySelect = memo<{
  api: AnyType
  data: AnyType[]
  type: string
  clearable?: boolean
  fieldNames?: { id: string; title: string }
  style?: CSSProperties
  [k: string]: AnyType
}>(({ style, data, clearable = true, api, fieldNames, ...props }) => {
  const { run: debouncedFetch } = useDebounceFn(
    (input, resolve) => {
      return api({ type: props.type, content: input })
        .then((res) => resolve(res?.data))
        .catch((err) => {
          message.open({
            title: err.message || intl.get('搜索条件失败，请联系管理员'),
            type: 'error',
          })
          return resolve([])
        })
    },
    { wait: 200 }
  )
  return (
    <Select
      style={style}
      clearable={clearable}
      data={data}
      height={260}
      closeOnEsc
      fieldNames={fieldNames || { id: 'code', title: 'name' }}
      dataSource={(keyword) => new Promise((resolve) => debouncedFetch(keyword, resolve))}
      searchable
      {...props}
      render={(item) => {
        return (
          <EllipsisTooltip tooltipProps={{ placement: 'right' }}>
            {(item?.title as string) || ''}
          </EllipsisTooltip>
        )
      }}
    />
  )
})

QuerySelect.displayName = 'QuerySelect'

export default QuerySelect
