import React, { memo } from 'react'
import Button from '@hi-ui/button'
import { GetTypeFromCategory, Task } from '@/views/home/<USER>'
import { ParamOneFunctionType } from '@/types/type'

import './index.scss'

export const WBTabContent = memo<{
  task: Task
  category: string
  handleClick: ParamOneFunctionType
}>(({ task, category, handleClick }) => {
  const taskArr = []
  task?.childCategory?.forEach((outerItem) => {
    outerItem?.childCategory?.forEach((innerItem) => {
      taskArr.push(Object.assign(innerItem, { type: GetTypeFromCategory[category] }) as never)
    })
  })

  return (
    <div className="link_button_wrapper" style={{ paddingBottom: !taskArr?.length ? 0 : 14 }}>
      {task && task?.childCategory?.length !== 0 ? (
        taskArr.map((item, idx: number) => {
          let { categoryName, processedNum } = item || {}
          categoryName = categoryName || '未知待办场景'
          processedNum = processedNum || 0
          return (
            <div key={idx}>
              <Button
                appearance="link"
                className="link_button_item"
                onClick={() => handleClick(item)}
              >
                <span className="link_button_title">{categoryName}</span>
                <span className="link_button_num">{processedNum}</span>
              </Button>
            </div>
          )
        })
      ) : (
        <></>
      )}
    </div>
  )
})

WBTabContent.displayName = 'WBTabContent'
