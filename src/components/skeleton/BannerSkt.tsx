import React from 'react'
import { Rect } from './Rect'

export const BannerSkt: React.FC<{ loading: boolean }> = ({ children, loading }) => {
  if (loading) {
    return (
      <div
        className="skt"
        style={{
          padding: '22px 80px',
        }}
      >
        <div className="skt-flex">
          <Rect width={240}></Rect>
          <Rect width={240}></Rect>
        </div>
        <div className="skt-flex">
          <div className="skt-flex">
            <Rect width={434}></Rect>
            <Rect width={56}></Rect>
          </div>
          <Rect width={240}></Rect>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
