import React from 'react'
import { Rect } from './Rect'

export const PageBookmarkSkt: React.FC<{ loading: boolean }> = ({ children, loading }) => {
  if (loading)
    return (
      <div className="skt skt-flex">
        <div
          className="skt-flex skt-col"
          style={{
            width: '60%',
          }}
        >
          <Rect width={'20%'}></Rect>
          <Rect width={40} height={40}></Rect>
          <Rect width={'60%'}></Rect>
          <Rect width={'60%'}></Rect>
          <Rect width={'60%'}></Rect>
          <Rect width={'60%'}></Rect>
          <Rect width={'60%'}></Rect>
          <Rect width={'60%'}></Rect>
          <Rect width={'60%'}></Rect>
        </div>
        <div
          className="skt-flex skt-col"
          style={{
            width: '40%',
            alignItems: 'flex-end',
          }}
        >
          <Rect width={'10%'}></Rect>
          <Rect width={'30%'}></Rect>
          <Rect width={'60%'}></Rect>
          <Rect width={'60%'}></Rect>
          <Rect width={'60%'}></Rect>
          <Rect width={'60%'}></Rect>
          <Rect width={'60%'}></Rect>
          <Rect width={'60%'}></Rect>
          <Rect width={'60%'}></Rect>
        </div>
      </div>
    )
  return <>{children}</>
}
