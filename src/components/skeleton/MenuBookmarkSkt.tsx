import React from 'react'
import { Rect } from './Rect'

export const MenuBookmarkSkt: React.FC<{ loading: boolean }> = ({ loading, children }) => {
  if (loading)
    return (
      <div className="skt">
        <div className="skt-flex">
          <Rect width={'30%'}></Rect>
          <Rect width={'20%'}></Rect>
        </div>
        {[1, 2, 3].map((i) => (
          <div
            style={{
              marginTop: 20,
            }}
            key={i}
            className="skt-flex"
          >
            <div className="skt-flex skt-col">
              <Rect width={40} height={40}></Rect>
              <Rect width={40}></Rect>
            </div>
            <div className="skt-flex skt-col">
              <Rect width={40} height={40}></Rect>
              <Rect width={40}></Rect>
            </div>
            <div className="skt-flex skt-col">
              <Rect width={40} height={40}></Rect>
              <Rect width={40}></Rect>
            </div>
          </div>
        ))}
      </div>
    )
  return <>{children}</>
}
