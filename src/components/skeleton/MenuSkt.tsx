import React from 'react'
import { Rect } from './Rect'

export const MenuSkt: React.FC<{ loading: boolean; isFold: boolean }> = ({
  loading,
  isFold,
  children,
}) => {
  if (loading)
    return (
      <div
        className="skt skt-flex"
        style={{
          justifyContent: 'flex-start',
          paddingTop: 20,
          paddingLeft: isFold ? 18 : 23,
          alignItems: 'flex-start',
          overflow: 'hidden',
          maxHeight: 600,
        }}
      >
        {Array(12)
          .fill(1)
          .map((_, index) => (
            <div key={index} className="skt-flex">
              <Rect width={20} height={20}></Rect>
              {!isFold && <Rect width={84}></Rect>}
            </div>
          ))}
      </div>
    )
  return <>{children}</>
}
