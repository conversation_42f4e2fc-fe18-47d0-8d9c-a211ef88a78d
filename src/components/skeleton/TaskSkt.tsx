import React from 'react'
import { Rect } from './Rect'
import { Ring } from './Ring'

export const TaskSkt: React.FC<{ loading: boolean }> = ({ loading, children }) => {
  if (loading) {
    return (
      <div
        className="skt skt-flex"
        style={{
          flexWrap: 'nowrap',
        }}
      >
        <div
          style={{
            width: 250,
          }}
          className="skt-flex"
        >
          <Rect width={64}></Rect>
          <Ring></Ring>
          <div className="skt-flex skt-w100">
            <Rect width={54}></Rect>
            <Rect width={54}></Rect>
            <Rect width={54}></Rect>
          </div>
        </div>
        <div
          style={{
            width: 'calc(100% - 450px)',
            alignContent: 'space-between',
          }}
          className="skt-flex"
        >
          <div className="skt-flex skt-w100">
            <Rect width={'15%'}></Rect>
            <Rect width={'15%'}></Rect>
            <Rect width={'15%'}></Rect>
          </div>
          <div className="skt-flex skt-w100">
            <Rect width={'25%'}></Rect>
            <Rect width={'25%'}></Rect>
            <Rect width={'25%'}></Rect>
          </div>
          <div className="skt-flex skt-w100">
            <Rect width={'20%'}></Rect>
            <Rect width={'10%'}></Rect>
          </div>
          <div className="skt-flex skt-w100">
            <Rect width={'60%'}></Rect>
            <Rect width={'10%'}></Rect>
          </div>
          <div className="skt-flex skt-w100">
            <Rect width={'60%'}></Rect>
            <Rect width={'10%'}></Rect>
          </div>
          <div className="skt-flex skt-w100">
            <Rect width={'60%'}></Rect>
            <Rect width={'10%'}></Rect>
          </div>
        </div>
      </div>
    )
  }
  return <>{children}</>
}
