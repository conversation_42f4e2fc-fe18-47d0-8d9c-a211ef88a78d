import React from 'react'
import { Rect } from './Rect'

export const TableSkt: React.FC<{ loading: boolean }> = ({ loading, children }) => {
  if (loading)
    return (
      <div className="skt skt-flex">
        <div
          className="skt-flex skt-col"
          style={{
            width: '20%',
            justifyContent: 'space-between',
          }}
        >
          <Rect width={'40%'}></Rect>
          <Rect width={'40%'}></Rect>
          <Rect width={'70%'}></Rect>
          <Rect width={'70%'}></Rect>
          <Rect width={'70%'}></Rect>
          <Rect width={'70%'}></Rect>
          <Rect width={'70%'}></Rect>
        </div>
        <div
          className="skt-flex skt-col"
          style={{
            width: '20%',
          }}
        >
          <Rect placeholder width={'40%'}></Rect>
          <Rect width={'40%'}></Rect>
          <Rect width={'40%'}></Rect>
          <Rect width={'40%'}></Rect>
          <Rect width={'40%'}></Rect>
          <Rect width={'40%'}></Rect>
          <Rect width={'40%'}></Rect>
        </div>
        <div
          className="skt-flex skt-col"
          style={{
            width: '20%',
          }}
        >
          <Rect placeholder width={'40%'}></Rect>
          <Rect width={'40%'}></Rect>
          <Rect width={'70%'}></Rect>
          <Rect width={'70%'}></Rect>
          <Rect width={'70%'}></Rect>
          <Rect width={'70%'}></Rect>
          <Rect width={'70%'}></Rect>
        </div>
        <div
          className="skt-flex skt-col"
          style={{
            width: '20%',
          }}
        >
          <Rect placeholder width={'40%'}></Rect>
          <Rect width={'40%'}></Rect>
          <Rect width={'70%'}></Rect>
          <Rect width={'70%'}></Rect>
          <Rect width={'70%'}></Rect>
          <Rect width={'70%'}></Rect>
          <Rect width={'70%'}></Rect>
        </div>
        <div
          className="skt-flex skt-col"
          style={{
            width: '20%',
            alignItems: 'flex-end',
          }}
        >
          <Rect width={'20%'}></Rect>
          <Rect width={'40%'}></Rect>
          <Rect width={'40%'}></Rect>
          <Rect width={'40%'}></Rect>
          <Rect width={'40%'}></Rect>
          <Rect width={'40%'}></Rect>
          <Rect width={'40%'}></Rect>
        </div>
      </div>
    )
  return <>{children}</>
}
