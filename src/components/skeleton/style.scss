.skt-bg {
  background: linear-gradient(0.25turn, transparent, #f2f4f7, transparent),
    linear-gradient(#f2f4f7, #f2f4f7),
    radial-gradient(38px circle at 19px 19px, #f2f4f7 50%, transparent 51%),
    linear-gradient(#f2f4f7, #f2f4f7);
  background-repeat: no-repeat;
}

.skt {
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-sizing: border-box;
}

.skt-rect {
  height: 20px;
  border-radius: 2px;
  margin-bottom: 8px;
  margin-right: 12px;
}

.skt-ring {
  border: 14px solid #f2f4f7;
  width: 92px;
  height: 92px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;

  .skt-rect {
    margin: 0;
    padding: 0;
  }
}

.skt-flex {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  &.h-center {
    justify-content: center;
  }
}

.skt-w100 {
  width: 100%;
}

.skt-col {
  flex-direction: column;
  justify-content: space-between;
}
