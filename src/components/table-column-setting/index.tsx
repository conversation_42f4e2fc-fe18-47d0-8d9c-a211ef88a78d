import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Reorder } from 'framer-motion'
import Button from '@hi-ui/button'
import { Checkbox } from '@hi-ui/checkbox'
import Drawer from '@hi-ui/drawer'
import Highlighter from '@hi-ui/highlighter'
import { SearchOutlined } from '@hi-ui/icons'
import Search from '@hi-ui/search'
import { cloneDeep } from 'lodash'
import TableEmpty from '../table-empty'
import './index.css'
import { cx } from '@mi/sc-ui-common'
import intl from 'react-intl-universal'

type TableColumn = Array<Record<string, AnyType>>

interface Column {
  label: string
  value: string
  checked: boolean
  disable: boolean
}

function getOriginColumns(
  columns: TableColumn,
  options: Partial<{
    labelKey: string
    valueKey: string
    visibleList: string[]
  }> = {}
): Column[] {
  const { labelKey = 'title', valueKey = 'dataKey', visibleList = [] } = options

  return (columns || []).map((item) => ({
    label: item[labelKey],
    value: item[valueKey],
    checked: visibleList.includes(item[valueKey]),
    disable: false,
  }))
}

interface TableColumnSettingProps {
  /**
   * 抽屉visible状态
   */
  visible: boolean
  /**
   * 初始列
   */
  columns: TableColumn
  /**
   * 表头label对应的key(列表中显示的文案)
   */
  labelKey?: string
  /**
   * 表头的value(列表中选项对应的ID)
   */
  valueKey?: string
  /**
   * 忽略的列(用于过滤类似操作列)
   */
  ignoreList?: string[]
  /**
   * 可见的列
   */
  visibleList?: string[]
  /**
   * 禁止操作的列
   */
  disableList?: string[]
  /**
   * 弹窗关闭
   */
  onClose: () => void
  /**
   * 表格配置项更新
   */
  onChange: (visibleList: string[]) => void
}

const defaultList = []

export default function TableColumnSetting(props: TableColumnSettingProps) {
  const {
    visible,
    columns,
    labelKey = 'title',
    valueKey = 'dataKey',
    visibleList = defaultList,
    disableList = defaultList,
    ignoreList = defaultList,
    onClose,
    onChange,
  } = props
  // 初始列列表(用于取消时数据回退)
  const originColumnsList = useRef<Column[]>(
    getOriginColumns(columns, { labelKey, valueKey, visibleList })
  )
  // 缓存所有数据列表(后续操作均基于该列表)
  const columnsList = useRef<Column[]>(cloneDeep(originColumnsList.current))
  // 抽屉中的列
  const [list, setList] = useState<Column[]>(cloneDeep(columnsList.current))
  // 搜索关键词
  const [searchKey, setSearchKey] = useState<string>('')
  const searchRef = useRef<HTMLInputElement>(null)

  // 切换选中状态
  const changeChecked = useCallback((id: string, checked: boolean) => {
    setList((prevList) => prevList.map((item) => (item.value === id ? { ...item, checked } : item)))

    const changeItem = columnsList.current.find((item) => item.value === id)

    if (changeItem) {
      changeItem.checked = checked
    }
  }, [])

  // 搜索
  const handleSearch = useCallback((searchKey) => {
    const searchList = columnsList.current.filter((item) => item.label.includes(searchKey))

    setList(cloneDeep(searchList))
    setSearchKey(searchKey)
  }, [])

  // 重置
  const reset = useCallback(() => {
    columnsList.current = cloneDeep(originColumnsList.current)
    setList(cloneDeep(columnsList.current))
    setSearchKey('')
  }, [])

  // 全选状态
  const allChecked = useMemo(() => list.every((item) => item.checked), [list])

  // 部分选中
  const someChecked = useMemo(
    () => list.some((item) => item.checked) && !allChecked,
    [allChecked, list]
  )

  // 全选操作
  const checkAll = useCallback(() => {
    let nextList

    if (allChecked) {
      nextList = list.map((item) => ({ ...item, checked: item.disable }))

      columnsList.current = columnsList.current.map((item) => {
        if (item.label.includes(searchKey)) {
          return { ...item, checked: item.disable }
        }

        return item
      })
    } else {
      nextList = list.map((item) => ({ ...item, checked: true }))

      columnsList.current = columnsList.current.map((item) => {
        if (item.label.includes(searchKey)) {
          return { ...item, checked: true }
        }

        return item
      })
    }

    setList(nextList)
  }, [allChecked, list, searchKey])

  // 拖拽排序
  const onReorder = useCallback((recordList) => {
    setList(recordList)
    columnsList.current = cloneDeep(recordList)
  }, [])

  // 确认提交
  const onSure = useCallback(() => {
    const visibleList = columnsList.current
      .filter((item) => item.checked && !ignoreList.includes(item.value))
      .map((item) => item.value)

    onChange([...new Set([...disableList, ...visibleList])])
    onClose()

    setList(cloneDeep(columnsList.current))
    setSearchKey('')
  }, [disableList, ignoreList, onChange, onClose])

  // 处理固定列的切换
  useEffect(() => {
    const visibleList = columnsList.current
      .filter(
        (item) =>
          !disableList.includes(item.value) && !ignoreList.includes(item.value) && item.checked
      )
      .map((item) => item.value)

    onChange([...new Set([...disableList, ...visibleList])])
  }, [disableList, ignoreList, onChange])

  return (
    <Drawer
      title={intl.get('字段设置')}
      width={400}
      visible={visible}
      closeable={true}
      onClose={() => {
        handleSearch('')
        onClose()
      }}
      footer={
        <div className="column-setting__footer">
          <Checkbox indeterminate={someChecked} checked={allChecked} onChange={checkAll}>
            {intl.get('全选')}
          </Checkbox>
          <div>
            <Button type="default" key={1} onClick={reset}>
              {intl.get('重置')}
            </Button>
            <Button type="primary" key={0} onClick={onSure}>
              {intl.get('确定')}
            </Button>
          </div>
        </div>
      }
    >
      <div style={{ marginBottom: 16 }}>
        <Search
          ref={searchRef}
          prefix={<SearchOutlined />}
          append={null}
          placeholder={intl.get('搜索')}
          value={searchKey}
          onChange={handleSearch}
        />
      </div>
      {disableList.map((code) => {
        const item = originColumnsList.current?.find((item) => item.value === code)

        if (item && item?.label?.includes(searchKey)) {
          return (
            <div className="hi-v4-setting-item" key={item.value}>
              <div className="hi-v4-setting-item__wrap">
                <Checkbox checked disabled>
                  <Highlighter keyword={searchKey}>{item.label}</Highlighter>
                </Checkbox>
              </div>
            </div>
          )
        }

        return null
      })}
      <Reorder.Group axis="y" values={list} onReorder={onReorder}>
        {list.map((item) => {
          return (
            <Reorder.Item
              className={cx({
                'column-setting__hidden':
                  disableList.includes(item.value) || ignoreList.includes(item.value),
              })}
              drag={!searchKey}
              key={item.value}
              value={item}
            >
              <div className="hi-v4-setting-item" key={item.value}>
                <div className="hi-v4-setting-item__wrap">
                  <Checkbox
                    checked={item.checked}
                    disabled={item.disable}
                    onChange={(e) => {
                      changeChecked(item.value, e.target.checked)
                    }}
                  >
                    <Highlighter keyword={searchKey}>{item.label}</Highlighter>
                  </Checkbox>
                </div>
              </div>
            </Reorder.Item>
          )
        })}
      </Reorder.Group>
      {list.length === 0 && <TableEmpty className="column-setting__empty" />}
    </Drawer>
  )
}
