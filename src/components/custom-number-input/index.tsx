import Input from '@hi-ui/input'
import React, { useState, useRef, memo } from 'react'

interface CustomNumberInputProps {
  initialValue: string
  validator: (value: string) => boolean
  customOnBlur: (e: React.FocusEvent<HTMLInputElement>) => void
}

const CustomNumberInput = memo((props: CustomNumberInputProps) => {
  const { initialValue, validator, customOnBlur } = props
  const [value, setValue] = useState<string>(initialValue)
  const previousValue = useRef<string>(initialValue)

  const handleChange = (e) => {
    setValue(e.target.value)
  }

  const handleBlur = (e) => {
    const newValue = e.target.value

    // 执行验证逻辑
    if (!validator(newValue)) {
      // 回退到上一个有效值
      setValue(previousValue.current)
    } else {
      previousValue.current = newValue
      customOnBlur(e)
    }
  }

  return <Input value={value as string} onChange={handleChange} onBlur={handleBlur} />
})

CustomNumberInput.displayName = 'CustomNumberInput'
export default CustomNumberInput
