import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import trackHelp from '@/utils/track-help'

import { useLocation } from 'react-router-dom'
import * as Sentry from '@sentry/react'
import { gscTrack } from '@/utils/gscTrack-help'
import { GSC_TRACK_DETAIL_URL, GSC_TRACK_EDIT_URL } from '@/constants/track'

export const Track = () => {
  const location = useLocation()
  const { user } = useAuth()
  const [hasTrackParams, setHasTrackParams] = useState(false)

  useEffect(() => {
    if (user) {
      const { email, displayName, userId, department, orgTreeCode } = user
      Sentry.setUser({ email, username: displayName, dept: department })
      trackHelp.login(displayName, 'xiaomi')
      trackHelp.setUser(user)
      gscTrack.setPublicParams({
        systemName: 'GSC',
        workbenchName: 'GSC',
        email,
        username: displayName,
        userId,
        orgTreeCode,
      })
      setHasTrackParams(true)
    }
  }, [user])

  useEffect(() => {
    if (!hasTrackParams) {
      return
    }
    const { pathname: fullPathname, search: fullSearch } = window.location
    if (GSC_TRACK_DETAIL_URL.some((key) => (fullPathname + fullSearch).includes(key))) {
      gscTrack.setTempPathPageView({ pageType: '02' })
      return
    }
    if (GSC_TRACK_EDIT_URL.some((key) => (fullPathname + fullSearch).includes(key))) {
      gscTrack.setTempPathPageView({ pageType: '03' })
      return
    }
    gscTrack.setTempPathPageView({ pageType: '01' })
  }, [location.pathname, location.search, hasTrackParams])
  return null
}
