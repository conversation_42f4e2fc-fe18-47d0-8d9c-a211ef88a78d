import intl from 'react-intl-universal'
const BODY_CELL_WIDTH = 90

export const CUSTOM_STORE_COLUMNS_CONFIG: Record<string, number> = [
  intl.get('物料分类编码'),
  intl.get('A-浅红'),
  intl.get('A-深红'),
  intl.get('A-绿'),
  intl.get('B-浅红'),
  intl.get('B-深红'),
  intl.get('B-绿'),
  intl.get('C-浅红'),
  intl.get('C-深红'),
  intl.get('C-绿'),
  intl.get('A+B-浅红'),
  intl.get('A+B-深红'),
  intl.get('A+B-绿'),
].reduce((a, b) => {
  a[b] = BODY_CELL_WIDTH
  return a
}, {})

export const HEADER_COLORS: Record<string, string> = {
  浅红: '#facccc',
  深红: '#e60000',
  绿: '#008a00',
}

export const HOVERED_FIRST_COLUMN = '物料分类编码'
