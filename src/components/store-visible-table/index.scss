.store-table-wrapper {
  overflow-x: scroll;
  height: 84px;
  padding: 12px 16px 6px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 2px 2px 16px 2px rgb(31 39 51 / 10%);
  border: 1px solid rgb(201 206 214);

  .custom-header {
    display: flex;

    .header-cell {
      color: #25262e;
      font-size: 13px;
      line-height: 17px;
      font-weight: 600;
      padding: 0;
      height: 37px;
      border: 1px solid rgb(240 240 240);
      border-right: none;
      display: flex;
      align-items: center;

      .header-cell-content {
        padding: 8px 12px;
      }
    }

    .header-cell:last-child {
      border-right: 1px solid rgb(240 240 240);
    }
  }

  .custom-body {
    display: flex;

    .body-cell {
      word-break: break-word;
      border-bottom: 1px solid rgb(240 240 240);
      border-left: 1px solid rgb(240 240 240);
      font-size: 14px;
      line-height: 20px;
      font-weight: 500;
      color: #333;
      background: rgb(245 247 250);
      padding: 0;
      height: 37px;
      display: flex;
      align-items: center;

      .body-cell-content {
        padding: 8px 12px;
      }
    }

    .body-cell:last-child {
      border-right: 1px solid rgb(240 240 240);
    }
  }
}
