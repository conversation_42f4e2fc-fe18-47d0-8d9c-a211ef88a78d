import React, { memo, FC, useEffect, useMemo, useCallback } from 'react'
import { useRequest } from 'ahooks'
import { getDashboardTips } from '@/api/gsc/dashboard'
import { HEADER_COLORS, CUSTOM_STORE_COLUMNS_CONFIG, HOVERED_FIRST_COLUMN } from './config'
import Loading from '@hi-ui/loading'

import './index.scss'

const StoreVisibleTable: FC<{
  storeCategory: {
    category: string
    businessLine: string
    hoverKey: string
    isJoint: boolean
  }
}> = memo(({ storeCategory }) => {
  const {
    run: getTips,
    data: tipsResult,
    loading: tipLoading,
  } = useRequest(
    ({ category, businessLine }) =>
      getDashboardTips({
        category,
        businessLine,
      }),
    {
      manual: true,
    }
  )

  const getColor = useCallback((title) => {
    const matched = Object.keys(HEADER_COLORS).find((item) => title.includes(item))
    if (!matched) {
      return '#E1CEFE'
    } else {
      return HEADER_COLORS[matched]
    }
  }, [])

  useEffect(() => {
    if (storeCategory.category) {
      getTips({ category: storeCategory.category, businessLine: storeCategory.businessLine })
    }
  }, [storeCategory.category, storeCategory.businessLine, getTips])

  const memoColumns = useMemo(() => {
    if (!tipsResult?.data?.columnList?.length) return []
    const firstColumn = {
      dataKey: HOVERED_FIRST_COLUMN,
      title: HOVERED_FIRST_COLUMN,
      width: 90,
      color: getColor(HOVERED_FIRST_COLUMN),
    }
    const filterRes = tipsResult?.data?.columnList
      ?.filter((i) => {
        if (storeCategory.isJoint) {
          return i?.key?.startsWith('A+B')
        } else {
          return !i?.key?.includes('+') && i?.key?.startsWith(storeCategory.hoverKey)
        }
      })
      .map((item) => {
        const { key, value } = item
        return {
          dataKey: key,
          title: value,
          width: CUSTOM_STORE_COLUMNS_CONFIG[key],
          color: getColor(value),
        }
      })
    filterRes.unshift(firstColumn)
    return filterRes
  }, [tipsResult?.data?.columnList, getColor, storeCategory.hoverKey, storeCategory.isJoint])

  const memoData = useMemo(() => {
    if (!tipsResult?.data?.rowPage?.length) return []
    const oneRowData = tipsResult?.data?.rowPage?.[0]
    return memoColumns.map((item) => {
      const { dataKey } = item
      return { value: oneRowData[dataKey], width: CUSTOM_STORE_COLUMNS_CONFIG[dataKey] }
    })
  }, [tipsResult?.data?.rowPage, memoColumns])

  return (
    <Loading visible={tipLoading} delay={500}>
      <div className="store-table-wrapper">
        <div className="custom-header">
          {memoColumns.map((item, idx) => {
            const { color, width, title } = item || {}
            return (
              <div key={idx} className="header-cell" style={{ background: color }}>
                <div className="header-cell-content" style={{ width }}>
                  {title}
                </div>
              </div>
            )
          })}
        </div>
        <div className="custom-body">
          {memoData.map((item, idx) => {
            const { width, value } = item || {}
            return (
              <div key={idx} className="body-cell">
                <div className="body-cell-content" style={{ width }}>
                  {value}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </Loading>
  )
})

StoreVisibleTable.displayName = 'StoreVisibleTable'

export default StoreVisibleTable
