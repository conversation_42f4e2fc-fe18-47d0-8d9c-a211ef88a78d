import React, { FC, memo, ReactNode } from 'react'
import cx from 'classnames'
import './index.scss'

interface IBlockProps {
  title: string
  className?: string
  extra?: ReactNode
  showLine?: boolean
  disabled?: boolean
  children?: ReactNode
}

const InfoBlock: FC<IBlockProps> = memo((props) => {
  const { title, className, extra, showLine = true, disabled = false, children } = props

  return (
    <div className={`info-block ${className || ''}`}>
      <div className={cx('info-block__title', { line: showLine })}>
        <span>{title}</span>
        {extra}
      </div>
      {children}
      {disabled && <div className="disabled-mode__wrapper"></div>}
    </div>
  )
})

InfoBlock.displayName = 'InfoBlock'
export default InfoBlock
