import intl from 'react-intl-universal'
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { memo, FC, useRef, useCallback, useMemo, ReactNode, MutableRefObject } from 'react'
import { Input } from '@hi-ui/input'
import { Checkbox } from '@hi-ui/checkbox'
import { Button } from '@hi-ui/button'
import { useSafeState } from 'ahooks'
import VirtualList from 'react-tiny-virtual-list'
import { SetType, TableIconReshowType } from '@/views/gsc/pages/manual-todo-edit/type'
import { excelToInput } from '@/utils/input-format'

import './index.scss'

const EditTableFilterAsync: FC<{
  setFilterDropdownVisible: (visible: boolean) => void // 控制过滤组件显隐
  filterColumnKey: string // 当前过滤的列值
  curSelectData: { id: string; title: string | ReactNode }[] // 当前列可以选择的数据
  setMultiFilter: SetType<{
    items: {
      label: string
      value: string
    }[]
  }> // 多条件查询
  reShow: Record<string, boolean> // 已设置的查询条件，用于回显
  setReShow: SetType<TableIconReshowType>
  setIconClickObj: SetType<Record<string, boolean>> // 过滤过的按钮
  filterSameAsInit: MutableRefObject<Record<string, boolean>> // 精细化确定
}> = memo(
  ({
    setFilterDropdownVisible,
    filterColumnKey,
    curSelectData,
    setMultiFilter,
    reShow,
    setReShow,
    setIconClickObj,
    filterSameAsInit,
  }) => {
    const initData = useRef(curSelectData)
    const [curDataState, setCurDataState] = useSafeState<any[]>(curSelectData)
    const [inputValue, setInputValue] = useSafeState('')
    const [hasChecked, setHasChecked] = useSafeState<object>(
      Object.keys(reShow).length !== 0 ? reShow : {}
    )
    // 搜索下拉框中与输入值模糊匹配的值
    const searchFilterData = (keyword) => {
      const queryInput = excelToInput(keyword)
      const finalQuery = queryInput.includes(',')
        ? queryInput // Excel 贴入
        : queryInput.trim().split(' ').join(',') // 手输
      const queryArr = finalQuery.split(',')
      if (finalQuery && queryArr.length === 1) {
        setCurDataState(initData.current.filter((item) => item?.id?.includes(finalQuery)))
      } else if (finalQuery && queryArr.length > 1) {
        setCurDataState(
          initData.current.filter((item) => queryArr.some((key) => item?.id?.includes(key)))
        )
      } else {
        setCurDataState(initData.current)
      }
    }

    // input框输入值改变时
    const searchInputChange = (e) => {
      setInputValue(e.target.value)
      searchFilterData(e.target.value)
    }

    // 重置过滤组件
    const resetTableFilter = () => {
      setInputValue('')
      setHasChecked({})
    }

    // 通过所选的过滤条件更新表格数据
    const confirmTableFilter = useCallback(() => {
      setFilterDropdownVisible(false)
      filterSameAsInit.current[filterColumnKey] = false
      setMultiFilter((prev) => {
        const { items: prevItems } = prev || {}
        let prevItemsTemp = [...prevItems]
        const newestCurrentCheck =
          Object.keys(hasChecked)?.reduce((a: { label: string; value: string }[], b) => {
            if (hasChecked[b]) {
              a.push({ label: filterColumnKey, value: b })
            }
            return a
          }, []) || []
        prevItemsTemp = prevItemsTemp?.filter((item) => item.label !== filterColumnKey)
        return { items: [...prevItemsTemp, ...newestCurrentCheck] }
      })
      setIconClickObj((prev) => {
        return { ...prev, [filterColumnKey]: true }
      })
      setReShow((prev) => {
        return {
          ...prev,
          [filterColumnKey]: hasChecked,
        } as TableIconReshowType
      })
      if (Object.keys(hasChecked).length === 0) filterSameAsInit.current[filterColumnKey] = true
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [hasChecked])

    const checkAllOrCancelAll = (flag: boolean) => {
      const allSelect = curDataState
        ?.map((item) => item?.id)
        ?.reduce((a, b) => {
          a[b] = flag
          return a
        }, {})
      setHasChecked((prev) => {
        return { ...prev, ...allSelect }
      })
    }

    const checkedLength = useMemo(
      () => Object.values(hasChecked)?.filter((i) => i)?.length,
      [hasChecked]
    )
    const isCheckedZero = useMemo(() => checkedLength === 0, [checkedLength])

    return (
      <>
        <div className="filter_input">
          <Input
            clearable
            clearableTrigger="always"
            placeholder={intl.get('搜索')}
            value={inputValue}
            onChange={searchInputChange}
          ></Input>
        </div>

        <div className="filter_content">
          {curDataState.length !== 0 ? (
            <VirtualList
              style={{ overflowX: 'hidden' }}
              width={184}
              height={200}
              itemCount={curDataState.length || 0}
              itemSize={32}
              renderItem={({ index, style }) => (
                <div key={index} className="filter_content_item" style={style}>
                  <Checkbox
                    key={curDataState[index].id}
                    value={curDataState[index].id}
                    name={curDataState[index].title}
                    checked={!!hasChecked[curDataState[index].id]} // 一开始全没有
                    onChange={(e) => {
                      const { checked: newestValue = false } = e.target
                      setHasChecked((prev) => {
                        prev[curDataState[index].id] = newestValue
                        return { ...prev }
                      })
                    }}
                  >
                    {curDataState[index].title}
                  </Checkbox>
                </div>
              )}
            />
          ) : (
            <span className="no_data_span">{intl.get('暂无数据')}</span>
          )}
        </div>

        <div className="filter_foot_btns">
          {inputValue ? (
            <>
              <Button
                type="default"
                size="sm"
                disabled={isCheckedZero}
                onClick={() => checkAllOrCancelAll(false)}
              >
                {intl.get('全不选')}
              </Button>
              <Button
                type="default"
                size="sm"
                disabled={checkedLength >= curDataState.length}
                onClick={() => checkAllOrCancelAll(true)}
              >
                {intl.get('全选')}
              </Button>
            </>
          ) : (
            <Button type="default" size="sm" disabled={isCheckedZero} onClick={resetTableFilter}>
              {intl.get('重置')}
            </Button>
          )}
          <Button
            type="primary"
            size="sm"
            onClick={confirmTableFilter}
            disabled={
              isCheckedZero &&
              (!Object.prototype.hasOwnProperty.call(filterSameAsInit.current, filterColumnKey) ||
                filterSameAsInit.current?.[filterColumnKey])
            }
          >
            {intl.get('确定')}
          </Button>
        </div>
      </>
    )
  }
)

EditTableFilterAsync.displayName = 'EditTableFilterAsync'

export default EditTableFilterAsync
