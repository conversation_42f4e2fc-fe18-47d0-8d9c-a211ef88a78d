.query-top-search {
  display: flex;
  width: 100%;
  box-sizing: border-box;
  justify-content: space-between;

  .query-top-form {
    flex-grow: 1;
    overflow: hidden;

    .hi-v4-form-label {
      margin-bottom: 12px;
      // padding-right: 10px;
      .hi-v4-form-label__text {
        padding-right: 10px;
      }
    }

    .hi-v4-form-label__control {
      min-width: 190px;
      max-width: 300px;

      .hi-v4-date-picker__input-selector {
        padding: 0;
      }
    }
  }

  .search-btns {
    display: flex;
    flex-wrap: nowrap;
    align-items: flex-start;
    margin-left: 8px;

    .hi-v4-button--appearance-link.hi-v4-button--type-default {
      height: 32px;
      line-height: 32px;
    }

    .search-second-btn {
      margin-left: 10px;
    }

    .hi-v4-button--type-secondary {
      margin-left: 10px;
    }
  }
}
