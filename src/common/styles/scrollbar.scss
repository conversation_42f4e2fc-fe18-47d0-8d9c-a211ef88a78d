::-webkit-scrollbar {
  width: 6px;

  /* 1px wider than <PERSON>. */

  /* This is more usable for users trying to click it. */
  background-color: rgb(0 0 0 / 0%);
  border-radius: 100px;
}

/* hover effect for both scrollbar area, and scrollbar 'thumb' */
::-webkit-scrollbar:hover {
  background-color: rgb(0 0 0 / 9%);
}

::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 0 5px #c9ced6;
}

/* The scrollbar 'thumb' ...that marque oval shape in a scrollbar */
::-webkit-scrollbar-thumb:vertical {
  /* This is the EXACT color of Mac OS scrollbars.
     Yes, I pulled out digital color meter */
  background: rgb(170 170 170 / 70%);
  border-radius: 100px;
}

::-webkit-scrollbar-thumb:vertical:active {
  background: rgb(170 170 170 / 70%);

  /* Some darker color when you click it */
  border-radius: 100px;
}
