import { MiTag, dftTagPrefixMap } from '@mi/workflow-tag'
import { getCurrentBranch, exit } from '@mi/workflow-utils'

const tagPrefixMap = {
  test: '', // 禁止在 test 分支打 tag
  pre: '', // 禁止在 pre 分支打 tag
  'feature/test': dftTagPrefixMap.test, // test-test
  'feature/pre': dftTagPrefixMap.pre, // test-pre
  'feature/canary': 'pro-canary',
  master: dftTagPrefixMap.master, // pro
}

const currentBranch = await getCurrentBranch()

if (tagPrefixMap[currentBranch]) MiTag({ tagPrefixMap })
else exit('错误：禁止在开发分支执行 Tag 脚本！')
